package service

import (
	"context"
	"time"

	"git.woa.com/fanniliu/output_error_qq_photos/internal/handler"
	"git.woa.com/trpc-go/tnet/log"
	pb "git.woa.com/trpcprotocol/zy_socail/qq_photos"
)

type QzonePhotoToolImpl struct {
	pb.UnimplementedQzonePhotoTool
	//todo 注入Handler
	QzonePhotoToolHandler *handler.QzonePhotoToolHandlerImpl
}

func (s *QzonePhotoToolImpl) RawFixTool(
	ctx context.Context,
	req *pb.RawFixToolRequest,
) (*pb.RawFixToolReply, error) {
	// 计算耗时
	defer func(start time.Time) {
		log.Debugf("RawFixTool cost:%v", time.Since(start))
	}(time.Now())

	uins := req.Uids
	concurrency := req.Concurrency
	if concurrency <= 0 {
		concurrency = 1
	}
	errList, unprocessedUids, photoUnprocessedList, err := s.QzonePhotoToolHandler.FindErrorPhotosByUins(uins, concurrency)

	if err != nil {
		log.Errorf("findErrorPhotosByUin err:%v", err)
		return nil, err
	}

	rsp := &pb.RawFixToolReply{
		ErrorList:            errList,
		UnprocessedUids:      unprocessedUids,
		PhotoUnprocessedList: photoUnprocessedList,
	}
	return rsp, nil
}

func NewQzonePhotoToolImpl() *QzonePhotoToolImpl {
	result := &QzonePhotoToolImpl{
		QzonePhotoToolHandler: handler.NewQzonePhotoToolHandlerImpl(),
	}
	return result
}
