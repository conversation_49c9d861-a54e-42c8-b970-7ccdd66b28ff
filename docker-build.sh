#!/bin/bash

# Docker 构建和部署脚本 - OpenCV 优化版本

set -e

# 配置变量
IMAGE_NAME="output_error_qq_photos"
TAG="${1:-latest}"  # 支持命令行参数指定 tag
TAR_FILE="${IMAGE_NAME}_${TAG}.tar"
DOCKERFILE="Dockerfile.optimized"
BUILD_CONTEXT="."

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要文件
check_prerequisites() {
    log_info "检查构建前置条件..."

    if [ ! -f "$DOCKERFILE" ]; then
        log_error "Dockerfile 不存在: $DOCKERFILE"
        exit 1
    fi

    if [ ! -f "go.mod" ]; then
        log_error "go.mod 文件不存在"
        exit 1
    fi

    if [ ! -d "vendor" ]; then
        log_warning "vendor 目录不存在，建议运行 'go mod vendor'"
        read -p "是否继续构建? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi

    if [ ! -f "application.yaml" ]; then
        log_warning "application.yaml 配置文件不存在"
    fi

    log_success "前置条件检查完成"
}

# 清理旧的构建缓存
cleanup_build_cache() {
    log_info "清理 Docker 构建缓存..."
    docker builder prune -f > /dev/null 2>&1 || true
}

# 构建镜像
build_image() {
    log_info "开始构建 Docker 镜像..."
    log_info "镜像名称: ${IMAGE_NAME}:${TAG}"
    log_info "Dockerfile: ${DOCKERFILE}"

    # 记录构建开始时间
    BUILD_START=$(date +%s)

    # 构建镜像，显示详细输出
    if docker build \
        -f "${DOCKERFILE}" \
        -t "${IMAGE_NAME}:${TAG}" \
        "${BUILD_CONTEXT}"; then

        BUILD_END=$(date +%s)
        BUILD_TIME=$((BUILD_END - BUILD_START))
        log_success "镜像构建完成！耗时: ${BUILD_TIME}s"
    else
        log_error "镜像构建失败！"
        exit 1
    fi
}

# 检查镜像大小
check_image_size() {
    log_info "检查镜像信息..."
    IMAGE_SIZE=$(docker images "${IMAGE_NAME}:${TAG}" --format "table {{.Size}}" | tail -n 1)
    IMAGE_ID=$(docker images "${IMAGE_NAME}:${TAG}" --format "{{.ID}}")

    log_info "镜像 ID: ${IMAGE_ID}"
    log_info "镜像大小: ${IMAGE_SIZE}"

    # 显示镜像层信息
    log_info "镜像层信息:"
    docker history "${IMAGE_NAME}:${TAG}" --format "table {{.CreatedBy}}\t{{.Size}}" | head -10
}

# 测试镜像和 OpenCV 依赖
test_image() {
    log_info "测试镜像和 OpenCV 依赖..."

    # 启动测试容器
    TEST_CONTAINER="${IMAGE_NAME}_test"

    # 清理可能存在的测试容器
    docker rm -f "${TEST_CONTAINER}" > /dev/null 2>&1 || true

    # 启动容器进行测试
    if docker run -d --name "${TEST_CONTAINER}" "${IMAGE_NAME}:${TAG}"; then
        sleep 10  # 增加等待时间，确保应用完全启动

        # 检查容器状态
        if docker ps | grep -q "${TEST_CONTAINER}"; then
            log_success "容器启动成功！"

            # 测试 OpenCV 依赖
            log_info "检查 OpenCV 依赖..."
            docker exec "${TEST_CONTAINER}" /bin/bash -c "
                echo '=== 检查 OpenCV 库文件 ==='
                find /usr/local/lib -name '*opencv*' | head -10
                echo ''
                echo '=== 检查环境变量 ==='
                echo 'LD_LIBRARY_PATH: '\$LD_LIBRARY_PATH
                echo 'PKG_CONFIG_PATH: '\$PKG_CONFIG_PATH
                echo ''
                echo '=== 测试应用程序依赖 ==='
                ldd /app/output_error_qq_photos | grep -E '(opencv|not found)' || echo '依赖检查完成'
                echo ''
                echo '=== 测试程序是否可以执行 ==='
                timeout 5s /app/output_error_qq_photos --help 2>&1 || echo '程序可以执行（或超时退出）'
            "

            if [ $? -eq 0 ]; then
                log_success "OpenCV 依赖检查通过！"
            else
                log_warning "OpenCV 依赖检查有警告，但容器可以启动"
            fi

        else
            log_error "容器启动后立即退出，请检查日志:"
            docker logs "${TEST_CONTAINER}"

            # 显示更详细的错误信息
            log_info "尝试直接运行程序查看错误："
            docker run --rm "${IMAGE_NAME}:${TAG}" /bin/bash -c "
                echo '=== 检查程序文件 ==='
                ls -la /app/
                echo ''
                echo '=== 检查库依赖 ==='
                ldd /app/output_error_qq_photos
                echo ''
                echo '=== 尝试运行程序 ==='
                /app/output_error_qq_photos 2>&1 || echo '程序运行失败'
            "
        fi

        # 清理测试容器
        docker rm -f "${TEST_CONTAINER}" > /dev/null 2>&1
    else
        log_error "镜像测试失败！"
    fi
}

# 导出镜像
export_image() {
    log_info "正在导出镜像到 ${TAR_FILE}..."

    # 删除旧的 tar 文件
    [ -f "${TAR_FILE}" ] && rm -f "${TAR_FILE}"

    # 导出镜像
    if docker save -o "${TAR_FILE}" "${IMAGE_NAME}:${TAG}"; then
        TAR_SIZE=$(du -h "${TAR_FILE}" | cut -f1)
        log_success "镜像已导出到: ${TAR_FILE}"
        log_info "文件大小: ${TAR_SIZE}"

        # 压缩 tar 文件以减小传输大小
        log_info "压缩镜像文件..."
        if gzip -f "${TAR_FILE}"; then
            COMPRESSED_SIZE=$(du -h "${TAR_FILE}.gz" | cut -f1)
            log_success "压缩完成: ${TAR_FILE}.gz"
            log_info "压缩后大小: ${COMPRESSED_SIZE}"
        else
            log_warning "压缩失败，使用原始 tar 文件"
        fi
    else
        log_error "镜像导出失败！"
        exit 1
    fi
}

# 显示部署说明
show_deployment_info() {
    echo ""
    log_success "构建完成！部署说明："
    echo ""

    # 确定使用哪个文件
    if [ -f "${TAR_FILE}.gz" ]; then
        DEPLOY_FILE="${TAR_FILE}.gz"
        LOAD_CMD="gunzip -c ${DEPLOY_FILE} | docker load"
    else
        DEPLOY_FILE="${TAR_FILE}"
        LOAD_CMD="docker load -i ${DEPLOY_FILE}"
    fi

    echo "📦 传输到服务器:"
    echo "   scp ${DEPLOY_FILE} user@server:/path/to/destination/"
    echo ""
    echo "🚀 在服务器上部署:"
    echo "   # 加载镜像"
    echo "   ${LOAD_CMD}"
    echo ""
    echo "   # 运行容器（注意 OpenCV 环境变量）"
    echo "   docker run -d \\"
    echo "     --name ${IMAGE_NAME} \\"
    echo "     -p 6610:6610 \\"
    echo "     -v \$(pwd)/application.yaml:/app/application.yaml:ro \\"
    echo "     -v \$(pwd)/logs:/app/logs \\"
    echo "     -e LD_LIBRARY_PATH=/usr/local/lib \\"
    echo "     -e PKG_CONFIG_PATH=/usr/local/lib/pkgconfig \\"
    echo "     --restart unless-stopped \\"
    echo "     ${IMAGE_NAME}:${TAG}"
    echo ""
    echo "   # 或使用 docker-compose"
    echo "   docker-compose up -d"
    echo ""
    echo "🔍 查看日志:"
    echo "   docker logs -f ${IMAGE_NAME}"
    echo ""
    echo "⚙️  进入容器调试:"
    echo "   docker exec -it ${IMAGE_NAME} /bin/bash"
    echo ""
    echo "🔧 OpenCV 故障排查:"
    echo "   # 检查 OpenCV 库"
    echo "   docker exec ${IMAGE_NAME} find /usr/local/lib -name '*opencv*'"
    echo "   # 检查程序依赖"
    echo "   docker exec ${IMAGE_NAME} ldd /app/output_error_qq_photos"
}

# 主函数
main() {
    echo "========================================"
    echo "  Docker 构建脚本 - OpenCV 优化版本"
    echo "========================================"
    echo ""

    check_prerequisites
    build_image
    check_image_size
    test_image
    export_image
    show_deployment_info

    log_success "所有操作完成！"
}

# 脚本使用说明
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "使用方法: $0 [TAG]"
    echo ""
    echo "参数:"
    echo "  TAG    镜像标签 (默认: latest)"
    echo ""
    echo "示例:"
    echo "  $0           # 构建 latest 标签"
    echo "  $0 v1.0.0    # 构建 v1.0.0 标签"
    echo "  $0 dev       # 构建 dev 标签"
    echo ""
    echo "特性:"
    echo "  ✅ 自动检查 OpenCV 依赖"
    echo "  ✅ 镜像压缩以减小传输大小"
    echo "  ✅ 详细的部署说明"
    echo "  ✅ 故障排查指导"
    exit 0
fi

# 执行主函数
main
