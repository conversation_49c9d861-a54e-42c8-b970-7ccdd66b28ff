// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v3.19.5
// source: trpc.proto

package trpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 框架协议头里的魔数
type TrpcMagic int32

const (
	// trpc不用这个值，为了提供给pb工具生成代码
	TrpcMagic_TRPC_DEFAULT_NONE TrpcMagic = 0
	// trpc协议默认使用这个魔数
	TrpcMagic_TRPC_MAGIC_VALUE TrpcMagic = 2352
)

// Enum value maps for TrpcMagic.
var (
	TrpcMagic_name = map[int32]string{
		0:    "TRPC_DEFAULT_NONE",
		2352: "TRPC_MAGIC_VALUE",
	}
	TrpcMagic_value = map[string]int32{
		"TRPC_DEFAULT_NONE": 0,
		"TRPC_MAGIC_VALUE":  2352,
	}
)

func (x TrpcMagic) Enum() *TrpcMagic {
	p := new(TrpcMagic)
	*p = x
	return p
}

func (x TrpcMagic) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrpcMagic) Descriptor() protoreflect.EnumDescriptor {
	return file_trpc_proto_enumTypes[0].Descriptor()
}

func (TrpcMagic) Type() protoreflect.EnumType {
	return &file_trpc_proto_enumTypes[0]
}

func (x TrpcMagic) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrpcMagic.Descriptor instead.
func (TrpcMagic) EnumDescriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{0}
}

// trpc协议的二进制数据帧类型
// 目前支持两种类型的二进制数据帧：
// 1. 一应一答模式的二进制数据帧类型
// 2. 流式模式的二进制数据帧类型
type TrpcDataFrameType int32

const (
	// trpc一应一答模式的二进制数据帧类型
	TrpcDataFrameType_TRPC_UNARY_FRAME TrpcDataFrameType = 0
	// trpc流式模式的二进制数据帧类型
	TrpcDataFrameType_TRPC_STREAM_FRAME TrpcDataFrameType = 1
)

// Enum value maps for TrpcDataFrameType.
var (
	TrpcDataFrameType_name = map[int32]string{
		0: "TRPC_UNARY_FRAME",
		1: "TRPC_STREAM_FRAME",
	}
	TrpcDataFrameType_value = map[string]int32{
		"TRPC_UNARY_FRAME":  0,
		"TRPC_STREAM_FRAME": 1,
	}
)

func (x TrpcDataFrameType) Enum() *TrpcDataFrameType {
	p := new(TrpcDataFrameType)
	*p = x
	return p
}

func (x TrpcDataFrameType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrpcDataFrameType) Descriptor() protoreflect.EnumDescriptor {
	return file_trpc_proto_enumTypes[1].Descriptor()
}

func (TrpcDataFrameType) Type() protoreflect.EnumType {
	return &file_trpc_proto_enumTypes[1]
}

func (x TrpcDataFrameType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrpcDataFrameType.Descriptor instead.
func (TrpcDataFrameType) EnumDescriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{1}
}

// trpc协议流式的二进制数据帧类型
// 目前流式帧类型分4种：INIT/DATA/FEEDBACK/CLOSE，其中CLOSE帧不带业务数据
// INIT帧：FIXHEADER + TrpcStreamInitMeta
// DATA帧：FIXHEADER + body(业务序列化的数据)
// FEEDBACK帧：FIXHEADER + TrpcStreamFeedBackMeta（触发策略，高低水位+定时）
// CLOSE帧：FIXHEADER + TrpcStreamCloseMeta
// 连接和流空闲超时的回收机制不考虑
type TrpcStreamFrameType int32

const (
	// 一应一答的默认取值
	TrpcStreamFrameType_TRPC_UNARY TrpcStreamFrameType = 0
	// 流式INIT帧类型
	TrpcStreamFrameType_TRPC_STREAM_FRAME_INIT TrpcStreamFrameType = 1
	// 流式DATA帧类型
	TrpcStreamFrameType_TRPC_STREAM_FRAME_DATA TrpcStreamFrameType = 2
	// 流式FEEDBACK帧类型
	TrpcStreamFrameType_TRPC_STREAM_FRAME_FEEDBACK TrpcStreamFrameType = 3
	// 流式CLOSE帧类型
	TrpcStreamFrameType_TRPC_STREAM_FRAME_CLOSE TrpcStreamFrameType = 4
)

// Enum value maps for TrpcStreamFrameType.
var (
	TrpcStreamFrameType_name = map[int32]string{
		0: "TRPC_UNARY",
		1: "TRPC_STREAM_FRAME_INIT",
		2: "TRPC_STREAM_FRAME_DATA",
		3: "TRPC_STREAM_FRAME_FEEDBACK",
		4: "TRPC_STREAM_FRAME_CLOSE",
	}
	TrpcStreamFrameType_value = map[string]int32{
		"TRPC_UNARY":                 0,
		"TRPC_STREAM_FRAME_INIT":     1,
		"TRPC_STREAM_FRAME_DATA":     2,
		"TRPC_STREAM_FRAME_FEEDBACK": 3,
		"TRPC_STREAM_FRAME_CLOSE":    4,
	}
)

func (x TrpcStreamFrameType) Enum() *TrpcStreamFrameType {
	p := new(TrpcStreamFrameType)
	*p = x
	return p
}

func (x TrpcStreamFrameType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrpcStreamFrameType) Descriptor() protoreflect.EnumDescriptor {
	return file_trpc_proto_enumTypes[2].Descriptor()
}

func (TrpcStreamFrameType) Type() protoreflect.EnumType {
	return &file_trpc_proto_enumTypes[2]
}

func (x TrpcStreamFrameType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrpcStreamFrameType.Descriptor instead.
func (TrpcStreamFrameType) EnumDescriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{2}
}

// trpc流式关闭类型
type TrpcStreamCloseType int32

const (
	// 正常单向流关闭
	TrpcStreamCloseType_TRPC_STREAM_CLOSE TrpcStreamCloseType = 0
	// 异常关闭双向流
	TrpcStreamCloseType_TRPC_STREAM_RESET TrpcStreamCloseType = 1
)

// Enum value maps for TrpcStreamCloseType.
var (
	TrpcStreamCloseType_name = map[int32]string{
		0: "TRPC_STREAM_CLOSE",
		1: "TRPC_STREAM_RESET",
	}
	TrpcStreamCloseType_value = map[string]int32{
		"TRPC_STREAM_CLOSE": 0,
		"TRPC_STREAM_RESET": 1,
	}
)

func (x TrpcStreamCloseType) Enum() *TrpcStreamCloseType {
	p := new(TrpcStreamCloseType)
	*p = x
	return p
}

func (x TrpcStreamCloseType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrpcStreamCloseType) Descriptor() protoreflect.EnumDescriptor {
	return file_trpc_proto_enumTypes[3].Descriptor()
}

func (TrpcStreamCloseType) Type() protoreflect.EnumType {
	return &file_trpc_proto_enumTypes[3]
}

func (x TrpcStreamCloseType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrpcStreamCloseType.Descriptor instead.
func (TrpcStreamCloseType) EnumDescriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{3}
}

// trpc协议版本
type TrpcProtoVersion int32

const (
	// 默认版本
	TrpcProtoVersion_TRPC_PROTO_V1 TrpcProtoVersion = 0
)

// Enum value maps for TrpcProtoVersion.
var (
	TrpcProtoVersion_name = map[int32]string{
		0: "TRPC_PROTO_V1",
	}
	TrpcProtoVersion_value = map[string]int32{
		"TRPC_PROTO_V1": 0,
	}
)

func (x TrpcProtoVersion) Enum() *TrpcProtoVersion {
	p := new(TrpcProtoVersion)
	*p = x
	return p
}

func (x TrpcProtoVersion) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrpcProtoVersion) Descriptor() protoreflect.EnumDescriptor {
	return file_trpc_proto_enumTypes[4].Descriptor()
}

func (TrpcProtoVersion) Type() protoreflect.EnumType {
	return &file_trpc_proto_enumTypes[4]
}

func (x TrpcProtoVersion) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrpcProtoVersion.Descriptor instead.
func (TrpcProtoVersion) EnumDescriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{4}
}

// trpc协议中的调用类型
type TrpcCallType int32

const (
	// 一应一答调用，包括同步、异步
	TrpcCallType_TRPC_UNARY_CALL TrpcCallType = 0
	// 单向调用
	TrpcCallType_TRPC_ONEWAY_CALL TrpcCallType = 1
)

// Enum value maps for TrpcCallType.
var (
	TrpcCallType_name = map[int32]string{
		0: "TRPC_UNARY_CALL",
		1: "TRPC_ONEWAY_CALL",
	}
	TrpcCallType_value = map[string]int32{
		"TRPC_UNARY_CALL":  0,
		"TRPC_ONEWAY_CALL": 1,
	}
)

func (x TrpcCallType) Enum() *TrpcCallType {
	p := new(TrpcCallType)
	*p = x
	return p
}

func (x TrpcCallType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrpcCallType) Descriptor() protoreflect.EnumDescriptor {
	return file_trpc_proto_enumTypes[5].Descriptor()
}

func (TrpcCallType) Type() protoreflect.EnumType {
	return &file_trpc_proto_enumTypes[5]
}

func (x TrpcCallType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrpcCallType.Descriptor instead.
func (TrpcCallType) EnumDescriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{5}
}

// trpc协议中的消息透传支持的类型
type TrpcMessageType int32

const (
	// trpc 不用这个值，为了提供给 pb 工具生成代码
	TrpcMessageType_TRPC_DEFAULT TrpcMessageType = 0
	// 染色
	TrpcMessageType_TRPC_DYEING_MESSAGE TrpcMessageType = 1
	// 调用链
	TrpcMessageType_TRPC_TRACE_MESSAGE TrpcMessageType = 2
	// 多环境
	TrpcMessageType_TRPC_MULTI_ENV_MESSAGE TrpcMessageType = 4
	// 灰度
	TrpcMessageType_TRPC_GRID_MESSAGE TrpcMessageType = 8
	// set名
	TrpcMessageType_TRPC_SETNAME_MESSAGE TrpcMessageType = 16
)

// Enum value maps for TrpcMessageType.
var (
	TrpcMessageType_name = map[int32]string{
		0:  "TRPC_DEFAULT",
		1:  "TRPC_DYEING_MESSAGE",
		2:  "TRPC_TRACE_MESSAGE",
		4:  "TRPC_MULTI_ENV_MESSAGE",
		8:  "TRPC_GRID_MESSAGE",
		16: "TRPC_SETNAME_MESSAGE",
	}
	TrpcMessageType_value = map[string]int32{
		"TRPC_DEFAULT":           0,
		"TRPC_DYEING_MESSAGE":    1,
		"TRPC_TRACE_MESSAGE":     2,
		"TRPC_MULTI_ENV_MESSAGE": 4,
		"TRPC_GRID_MESSAGE":      8,
		"TRPC_SETNAME_MESSAGE":   16,
	}
)

func (x TrpcMessageType) Enum() *TrpcMessageType {
	p := new(TrpcMessageType)
	*p = x
	return p
}

func (x TrpcMessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrpcMessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_trpc_proto_enumTypes[6].Descriptor()
}

func (TrpcMessageType) Type() protoreflect.EnumType {
	return &file_trpc_proto_enumTypes[6]
}

func (x TrpcMessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrpcMessageType.Descriptor instead.
func (TrpcMessageType) EnumDescriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{6}
}

// trpc协议中 data 内容的编码类型
// 默认使用pb
// 目前约定 0-127 范围的取值为框架规范的序列化方式,框架使用
type TrpcContentEncodeType int32

const (
	// pb
	TrpcContentEncodeType_TRPC_PROTO_ENCODE TrpcContentEncodeType = 0
	// jce
	TrpcContentEncodeType_TRPC_JCE_ENCODE TrpcContentEncodeType = 1
	// json
	TrpcContentEncodeType_TRPC_JSON_ENCODE TrpcContentEncodeType = 2
	// flatbuffer
	TrpcContentEncodeType_TRPC_FLATBUFFER_ENCODE TrpcContentEncodeType = 3
	// 不序列化
	TrpcContentEncodeType_TRPC_NOOP_ENCODE TrpcContentEncodeType = 4
	// xml
	TrpcContentEncodeType_TRPC_XML_ENCODE TrpcContentEncodeType = 5
	// thrift
	// 由于历史原因，早期实现的 thrift 使用的是二进制编码
	// 因此这里的 thrift 代表 thrift-binary
	TrpcContentEncodeType_TRPC_THRIFT_ENCODE TrpcContentEncodeType = 6
	// thrift-compact
	TrpcContentEncodeType_TRPC_THRIFT_COMPACT_ENCODE TrpcContentEncodeType = 7
	// text/xml
	TrpcContentEncodeType_TRPC_TEXT_XML_ENCODE TrpcContentEncodeType = 8
)

// Enum value maps for TrpcContentEncodeType.
var (
	TrpcContentEncodeType_name = map[int32]string{
		0: "TRPC_PROTO_ENCODE",
		1: "TRPC_JCE_ENCODE",
		2: "TRPC_JSON_ENCODE",
		3: "TRPC_FLATBUFFER_ENCODE",
		4: "TRPC_NOOP_ENCODE",
		5: "TRPC_XML_ENCODE",
		6: "TRPC_THRIFT_ENCODE",
		7: "TRPC_THRIFT_COMPACT_ENCODE",
		8: "TRPC_TEXT_XML_ENCODE",
	}
	TrpcContentEncodeType_value = map[string]int32{
		"TRPC_PROTO_ENCODE":          0,
		"TRPC_JCE_ENCODE":            1,
		"TRPC_JSON_ENCODE":           2,
		"TRPC_FLATBUFFER_ENCODE":     3,
		"TRPC_NOOP_ENCODE":           4,
		"TRPC_XML_ENCODE":            5,
		"TRPC_THRIFT_ENCODE":         6,
		"TRPC_THRIFT_COMPACT_ENCODE": 7,
		"TRPC_TEXT_XML_ENCODE":       8,
	}
)

func (x TrpcContentEncodeType) Enum() *TrpcContentEncodeType {
	p := new(TrpcContentEncodeType)
	*p = x
	return p
}

func (x TrpcContentEncodeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrpcContentEncodeType) Descriptor() protoreflect.EnumDescriptor {
	return file_trpc_proto_enumTypes[7].Descriptor()
}

func (TrpcContentEncodeType) Type() protoreflect.EnumType {
	return &file_trpc_proto_enumTypes[7]
}

func (x TrpcContentEncodeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrpcContentEncodeType.Descriptor instead.
func (TrpcContentEncodeType) EnumDescriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{7}
}

// trpc协议中 data 内容的压缩类型
// 默认使用不压缩
type TrpcCompressType int32

const (
	// 默认不使用压缩
	TrpcCompressType_TRPC_DEFAULT_COMPRESS TrpcCompressType = 0
	// 使用gzip
	TrpcCompressType_TRPC_GZIP_COMPRESS TrpcCompressType = 1
	// 使用snappy
	//
	// Deprecated: 建议使用 TRPC_SNAPPY_STREAM_COMPRESS/TRPC_SNAPPY_BLOCK_COMPRESS, 因为现在
	// trpc-go 和 trpc-cpp 分别的使用的是 stream、block 模式，二者不兼容，跨语言调用会出错
	TrpcCompressType_TRPC_SNAPPY_COMPRESS TrpcCompressType = 2
	// 使用zlib
	TrpcCompressType_TRPC_ZLIB_COMPRESS TrpcCompressType = 3
	// 使用 stream 模式的 snappy
	TrpcCompressType_TRPC_SNAPPY_STREAM_COMPRESS TrpcCompressType = 4
	// 使用 block 模式的 snappy
	TrpcCompressType_TRPC_SNAPPY_BLOCK_COMPRESS TrpcCompressType = 5
	// 使用 frame 模式的 lz4
	TrpcCompressType_TRPC_LZ4_FRAME_COMPRESS TrpcCompressType = 6
	// 使用 block 模式的 lz4
	TrpcCompressType_TRPC_LZ4_BLOCK_COMPRESS TrpcCompressType = 7
)

// Enum value maps for TrpcCompressType.
var (
	TrpcCompressType_name = map[int32]string{
		0: "TRPC_DEFAULT_COMPRESS",
		1: "TRPC_GZIP_COMPRESS",
		2: "TRPC_SNAPPY_COMPRESS",
		3: "TRPC_ZLIB_COMPRESS",
		4: "TRPC_SNAPPY_STREAM_COMPRESS",
		5: "TRPC_SNAPPY_BLOCK_COMPRESS",
		6: "TRPC_LZ4_FRAME_COMPRESS",
		7: "TRPC_LZ4_BLOCK_COMPRESS",
	}
	TrpcCompressType_value = map[string]int32{
		"TRPC_DEFAULT_COMPRESS":       0,
		"TRPC_GZIP_COMPRESS":          1,
		"TRPC_SNAPPY_COMPRESS":        2,
		"TRPC_ZLIB_COMPRESS":          3,
		"TRPC_SNAPPY_STREAM_COMPRESS": 4,
		"TRPC_SNAPPY_BLOCK_COMPRESS":  5,
		"TRPC_LZ4_FRAME_COMPRESS":     6,
		"TRPC_LZ4_BLOCK_COMPRESS":     7,
	}
)

func (x TrpcCompressType) Enum() *TrpcCompressType {
	p := new(TrpcCompressType)
	*p = x
	return p
}

func (x TrpcCompressType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrpcCompressType) Descriptor() protoreflect.EnumDescriptor {
	return file_trpc_proto_enumTypes[8].Descriptor()
}

func (TrpcCompressType) Type() protoreflect.EnumType {
	return &file_trpc_proto_enumTypes[8]
}

func (x TrpcCompressType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrpcCompressType.Descriptor instead.
func (TrpcCompressType) EnumDescriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{8}
}

// 框架层接口调用的返回码定义
type TrpcRetCode int32

const (
	// 调用成功
	TrpcRetCode_TRPC_INVOKE_SUCCESS TrpcRetCode = 0
	// 协议错误码
	// 服务端解码错误
	TrpcRetCode_TRPC_SERVER_DECODE_ERR TrpcRetCode = 1
	// 服务端编码错误
	TrpcRetCode_TRPC_SERVER_ENCODE_ERR TrpcRetCode = 2
	// service或者func路由错误码
	// 服务端没有调用相应的service实现
	TrpcRetCode_TRPC_SERVER_NOSERVICE_ERR TrpcRetCode = 11
	// 服务端没有调用相应的接口实现
	TrpcRetCode_TRPC_SERVER_NOFUNC_ERR TrpcRetCode = 12
	// 超时/过载/限流错误码
	// 请求在服务端超时
	TrpcRetCode_TRPC_SERVER_TIMEOUT_ERR TrpcRetCode = 21
	// 请求在服务端被过载保护而丢弃请求
	// 主要用在框架内部实现的过载保护插件上
	TrpcRetCode_TRPC_SERVER_OVERLOAD_ERR TrpcRetCode = 22
	// 请求在服务端被限流
	// 主要用在外部服务治理系统的插件或者业务自定义的限流插件上，比如: 北极星限流
	TrpcRetCode_TRPC_SERVER_LIMITED_ERR TrpcRetCode = 23
	// 请求在服务端因全链路超时时间而超时
	TrpcRetCode_TRPC_SERVER_FULL_LINK_TIMEOUT_ERR TrpcRetCode = 24
	// 服务端系统错误
	TrpcRetCode_TRPC_SERVER_SYSTEM_ERR TrpcRetCode = 31
	// 服务端鉴权失败错误
	TrpcRetCode_TRPC_SERVER_AUTH_ERR TrpcRetCode = 41
	// 服务端请求参数自动校验失败错误
	TrpcRetCode_TRPC_SERVER_VALIDATE_ERR TrpcRetCode = 51
	// 超时错误码
	// 请求在客户端调用超时
	TrpcRetCode_TRPC_CLIENT_INVOKE_TIMEOUT_ERR TrpcRetCode = 101
	// 请求在客户端因全链路超时时间而超时
	TrpcRetCode_TRPC_CLIENT_FULL_LINK_TIMEOUT_ERR TrpcRetCode = 102
	// 网络相关错误码
	// 客户端连接错误
	TrpcRetCode_TRPC_CLIENT_CONNECT_ERR TrpcRetCode = 111
	// 协议相关错误码
	// 客户端编码错误
	TrpcRetCode_TRPC_CLIENT_ENCODE_ERR TrpcRetCode = 121
	// 客户端解码错误
	TrpcRetCode_TRPC_CLIENT_DECODE_ERR TrpcRetCode = 122
	// 过载保护/限流相关错误码
	// 请求在客户端被限流
	// 主要用在外部服务治理系统的插件或者业务自定义的限流插件上，比如: 北极星限流
	TrpcRetCode_TRPC_CLIENT_LIMITED_ERR TrpcRetCode = 123
	// 请求在客户端被过载保护而丢弃请求
	// 主要用在框架内部实现的过载保护插件上
	TrpcRetCode_TRPC_CLIENT_OVERLOAD_ERR TrpcRetCode = 124
	// 路由相关错误码
	// 客户端选ip路由错误
	TrpcRetCode_TRPC_CLIENT_ROUTER_ERR TrpcRetCode = 131
	// 客户端网络错误
	TrpcRetCode_TRPC_CLIENT_NETWORK_ERR TrpcRetCode = 141
	// 客户端响应参数自动校验失败错误
	TrpcRetCode_TRPC_CLIENT_VALIDATE_ERR TrpcRetCode = 151
	// 上游主动断开连接，提前取消请求错误
	TrpcRetCode_TRPC_CLIENT_CANCELED_ERR TrpcRetCode = 161
	// 客户端读取 Frame 错误
	TrpcRetCode_TRPC_CLIENT_READ_FRAME_ERR TrpcRetCode = 171
	// 服务端流式网络错误, 详细错误码需要在实现过程中再梳理
	TrpcRetCode_TRPC_STREAM_SERVER_NETWORK_ERR TrpcRetCode = 201
	// 服务端流式传输错误, 详细错误码需要在实现过程中再梳理
	// 比如：流消息过大等
	TrpcRetCode_TRPC_STREAM_SERVER_MSG_EXCEED_LIMIT_ERR TrpcRetCode = 211
	// 服务端流式编码错误
	TrpcRetCode_TRPC_STREAM_SERVER_ENCODE_ERR TrpcRetCode = 221
	// 客户端流式编解码错误
	TrpcRetCode_TRPC_STREAM_SERVER_DECODE_ERR TrpcRetCode = 222
	// 服务端流式写错误, 详细错误码需要在实现过程中再梳理
	TrpcRetCode_TRPC_STREAM_SERVER_WRITE_END          TrpcRetCode = 231
	TrpcRetCode_TRPC_STREAM_SERVER_WRITE_OVERFLOW_ERR TrpcRetCode = 232
	TrpcRetCode_TRPC_STREAM_SERVER_WRITE_CLOSE_ERR    TrpcRetCode = 233
	TrpcRetCode_TRPC_STREAM_SERVER_WRITE_TIMEOUT_ERR  TrpcRetCode = 234
	// 服务端流式读错误, 详细错误码需要在实现过程中再梳理
	TrpcRetCode_TRPC_STREAM_SERVER_READ_END         TrpcRetCode = 251
	TrpcRetCode_TRPC_STREAM_SERVER_READ_CLOSE_ERR   TrpcRetCode = 252
	TrpcRetCode_TRPC_STREAM_SERVER_READ_EMPTY_ERR   TrpcRetCode = 253
	TrpcRetCode_TRPC_STREAM_SERVER_READ_TIMEOUT_ERR TrpcRetCode = 254
	// 服务端流空闲超时错误
	TrpcRetCode_TRPC_STREAM_SERVER_IDLE_TIMEOUT_ERR TrpcRetCode = 255
	// 客户端流式网络错误, 详细错误码需要在实现过程中再梳理
	TrpcRetCode_TRPC_STREAM_CLIENT_NETWORK_ERR TrpcRetCode = 301
	// 客户端流式传输错误, 详细错误码需要在实现过程中再梳理
	// 比如：流消息过大等
	TrpcRetCode_TRPC_STREAM_CLIENT_MSG_EXCEED_LIMIT_ERR TrpcRetCode = 311
	// 客户端流式编码错误
	TrpcRetCode_TRPC_STREAM_CLIENT_ENCODE_ERR TrpcRetCode = 321
	// 客户端流式编解码错误
	TrpcRetCode_TRPC_STREAM_CLIENT_DECODE_ERR TrpcRetCode = 322
	// 客户端流式写错误, 详细错误码需要在实现过程中再梳理
	TrpcRetCode_TRPC_STREAM_CLIENT_WRITE_END          TrpcRetCode = 331
	TrpcRetCode_TRPC_STREAM_CLIENT_WRITE_OVERFLOW_ERR TrpcRetCode = 332
	TrpcRetCode_TRPC_STREAM_CLIENT_WRITE_CLOSE_ERR    TrpcRetCode = 333
	TrpcRetCode_TRPC_STREAM_CLIENT_WRITE_TIMEOUT_ERR  TrpcRetCode = 334
	// 客户端流式读错误, 详细错误码需要在实现过程中再梳理
	TrpcRetCode_TRPC_STREAM_CLIENT_READ_END         TrpcRetCode = 351
	TrpcRetCode_TRPC_STREAM_CLIENT_READ_CLOSE_ERR   TrpcRetCode = 352
	TrpcRetCode_TRPC_STREAM_CLIENT_READ_EMPTY_ERR   TrpcRetCode = 353
	TrpcRetCode_TRPC_STREAM_CLIENT_READ_TIMEOUT_ERR TrpcRetCode = 354
	// 客户端流空闲超时错误
	TrpcRetCode_TRPC_STREAM_CLIENT_IDLE_TIMEOUT_ERR TrpcRetCode = 355
	// 客户端流初始化错误
	TrpcRetCode_TRPC_STREAM_CLIENT_INIT_ERR TrpcRetCode = 361
	// 未明确的错误
	TrpcRetCode_TRPC_INVOKE_UNKNOWN_ERR TrpcRetCode = 999
	// 未明确的错误
	TrpcRetCode_TRPC_STREAM_UNKNOWN_ERR TrpcRetCode = 1000
)

// Enum value maps for TrpcRetCode.
var (
	TrpcRetCode_name = map[int32]string{
		0:    "TRPC_INVOKE_SUCCESS",
		1:    "TRPC_SERVER_DECODE_ERR",
		2:    "TRPC_SERVER_ENCODE_ERR",
		11:   "TRPC_SERVER_NOSERVICE_ERR",
		12:   "TRPC_SERVER_NOFUNC_ERR",
		21:   "TRPC_SERVER_TIMEOUT_ERR",
		22:   "TRPC_SERVER_OVERLOAD_ERR",
		23:   "TRPC_SERVER_LIMITED_ERR",
		24:   "TRPC_SERVER_FULL_LINK_TIMEOUT_ERR",
		31:   "TRPC_SERVER_SYSTEM_ERR",
		41:   "TRPC_SERVER_AUTH_ERR",
		51:   "TRPC_SERVER_VALIDATE_ERR",
		101:  "TRPC_CLIENT_INVOKE_TIMEOUT_ERR",
		102:  "TRPC_CLIENT_FULL_LINK_TIMEOUT_ERR",
		111:  "TRPC_CLIENT_CONNECT_ERR",
		121:  "TRPC_CLIENT_ENCODE_ERR",
		122:  "TRPC_CLIENT_DECODE_ERR",
		123:  "TRPC_CLIENT_LIMITED_ERR",
		124:  "TRPC_CLIENT_OVERLOAD_ERR",
		131:  "TRPC_CLIENT_ROUTER_ERR",
		141:  "TRPC_CLIENT_NETWORK_ERR",
		151:  "TRPC_CLIENT_VALIDATE_ERR",
		161:  "TRPC_CLIENT_CANCELED_ERR",
		171:  "TRPC_CLIENT_READ_FRAME_ERR",
		201:  "TRPC_STREAM_SERVER_NETWORK_ERR",
		211:  "TRPC_STREAM_SERVER_MSG_EXCEED_LIMIT_ERR",
		221:  "TRPC_STREAM_SERVER_ENCODE_ERR",
		222:  "TRPC_STREAM_SERVER_DECODE_ERR",
		231:  "TRPC_STREAM_SERVER_WRITE_END",
		232:  "TRPC_STREAM_SERVER_WRITE_OVERFLOW_ERR",
		233:  "TRPC_STREAM_SERVER_WRITE_CLOSE_ERR",
		234:  "TRPC_STREAM_SERVER_WRITE_TIMEOUT_ERR",
		251:  "TRPC_STREAM_SERVER_READ_END",
		252:  "TRPC_STREAM_SERVER_READ_CLOSE_ERR",
		253:  "TRPC_STREAM_SERVER_READ_EMPTY_ERR",
		254:  "TRPC_STREAM_SERVER_READ_TIMEOUT_ERR",
		255:  "TRPC_STREAM_SERVER_IDLE_TIMEOUT_ERR",
		301:  "TRPC_STREAM_CLIENT_NETWORK_ERR",
		311:  "TRPC_STREAM_CLIENT_MSG_EXCEED_LIMIT_ERR",
		321:  "TRPC_STREAM_CLIENT_ENCODE_ERR",
		322:  "TRPC_STREAM_CLIENT_DECODE_ERR",
		331:  "TRPC_STREAM_CLIENT_WRITE_END",
		332:  "TRPC_STREAM_CLIENT_WRITE_OVERFLOW_ERR",
		333:  "TRPC_STREAM_CLIENT_WRITE_CLOSE_ERR",
		334:  "TRPC_STREAM_CLIENT_WRITE_TIMEOUT_ERR",
		351:  "TRPC_STREAM_CLIENT_READ_END",
		352:  "TRPC_STREAM_CLIENT_READ_CLOSE_ERR",
		353:  "TRPC_STREAM_CLIENT_READ_EMPTY_ERR",
		354:  "TRPC_STREAM_CLIENT_READ_TIMEOUT_ERR",
		355:  "TRPC_STREAM_CLIENT_IDLE_TIMEOUT_ERR",
		361:  "TRPC_STREAM_CLIENT_INIT_ERR",
		999:  "TRPC_INVOKE_UNKNOWN_ERR",
		1000: "TRPC_STREAM_UNKNOWN_ERR",
	}
	TrpcRetCode_value = map[string]int32{
		"TRPC_INVOKE_SUCCESS":                     0,
		"TRPC_SERVER_DECODE_ERR":                  1,
		"TRPC_SERVER_ENCODE_ERR":                  2,
		"TRPC_SERVER_NOSERVICE_ERR":               11,
		"TRPC_SERVER_NOFUNC_ERR":                  12,
		"TRPC_SERVER_TIMEOUT_ERR":                 21,
		"TRPC_SERVER_OVERLOAD_ERR":                22,
		"TRPC_SERVER_LIMITED_ERR":                 23,
		"TRPC_SERVER_FULL_LINK_TIMEOUT_ERR":       24,
		"TRPC_SERVER_SYSTEM_ERR":                  31,
		"TRPC_SERVER_AUTH_ERR":                    41,
		"TRPC_SERVER_VALIDATE_ERR":                51,
		"TRPC_CLIENT_INVOKE_TIMEOUT_ERR":          101,
		"TRPC_CLIENT_FULL_LINK_TIMEOUT_ERR":       102,
		"TRPC_CLIENT_CONNECT_ERR":                 111,
		"TRPC_CLIENT_ENCODE_ERR":                  121,
		"TRPC_CLIENT_DECODE_ERR":                  122,
		"TRPC_CLIENT_LIMITED_ERR":                 123,
		"TRPC_CLIENT_OVERLOAD_ERR":                124,
		"TRPC_CLIENT_ROUTER_ERR":                  131,
		"TRPC_CLIENT_NETWORK_ERR":                 141,
		"TRPC_CLIENT_VALIDATE_ERR":                151,
		"TRPC_CLIENT_CANCELED_ERR":                161,
		"TRPC_CLIENT_READ_FRAME_ERR":              171,
		"TRPC_STREAM_SERVER_NETWORK_ERR":          201,
		"TRPC_STREAM_SERVER_MSG_EXCEED_LIMIT_ERR": 211,
		"TRPC_STREAM_SERVER_ENCODE_ERR":           221,
		"TRPC_STREAM_SERVER_DECODE_ERR":           222,
		"TRPC_STREAM_SERVER_WRITE_END":            231,
		"TRPC_STREAM_SERVER_WRITE_OVERFLOW_ERR":   232,
		"TRPC_STREAM_SERVER_WRITE_CLOSE_ERR":      233,
		"TRPC_STREAM_SERVER_WRITE_TIMEOUT_ERR":    234,
		"TRPC_STREAM_SERVER_READ_END":             251,
		"TRPC_STREAM_SERVER_READ_CLOSE_ERR":       252,
		"TRPC_STREAM_SERVER_READ_EMPTY_ERR":       253,
		"TRPC_STREAM_SERVER_READ_TIMEOUT_ERR":     254,
		"TRPC_STREAM_SERVER_IDLE_TIMEOUT_ERR":     255,
		"TRPC_STREAM_CLIENT_NETWORK_ERR":          301,
		"TRPC_STREAM_CLIENT_MSG_EXCEED_LIMIT_ERR": 311,
		"TRPC_STREAM_CLIENT_ENCODE_ERR":           321,
		"TRPC_STREAM_CLIENT_DECODE_ERR":           322,
		"TRPC_STREAM_CLIENT_WRITE_END":            331,
		"TRPC_STREAM_CLIENT_WRITE_OVERFLOW_ERR":   332,
		"TRPC_STREAM_CLIENT_WRITE_CLOSE_ERR":      333,
		"TRPC_STREAM_CLIENT_WRITE_TIMEOUT_ERR":    334,
		"TRPC_STREAM_CLIENT_READ_END":             351,
		"TRPC_STREAM_CLIENT_READ_CLOSE_ERR":       352,
		"TRPC_STREAM_CLIENT_READ_EMPTY_ERR":       353,
		"TRPC_STREAM_CLIENT_READ_TIMEOUT_ERR":     354,
		"TRPC_STREAM_CLIENT_IDLE_TIMEOUT_ERR":     355,
		"TRPC_STREAM_CLIENT_INIT_ERR":             361,
		"TRPC_INVOKE_UNKNOWN_ERR":                 999,
		"TRPC_STREAM_UNKNOWN_ERR":                 1000,
	}
)

func (x TrpcRetCode) Enum() *TrpcRetCode {
	p := new(TrpcRetCode)
	*p = x
	return p
}

func (x TrpcRetCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrpcRetCode) Descriptor() protoreflect.EnumDescriptor {
	return file_trpc_proto_enumTypes[9].Descriptor()
}

func (TrpcRetCode) Type() protoreflect.EnumType {
	return &file_trpc_proto_enumTypes[9]
}

func (x TrpcRetCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrpcRetCode.Descriptor instead.
func (TrpcRetCode) EnumDescriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{9}
}

// trpc流式的流控帧头消息定义
type TrpcStreamInitMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// init请求元信息
	RequestMeta *TrpcStreamInitRequestMeta `protobuf:"bytes,1,opt,name=request_meta,json=requestMeta,proto3" json:"request_meta,omitempty"`
	// init响应元信息
	ResponseMeta *TrpcStreamInitResponseMeta `protobuf:"bytes,2,opt,name=response_meta,json=responseMeta,proto3" json:"response_meta,omitempty"`
	// 由接收端告知发送端初始的发送窗口大小
	InitWindowSize uint32 `protobuf:"varint,3,opt,name=init_window_size,json=initWindowSize,proto3" json:"init_window_size,omitempty"`
	// 请求数据的序列化类型
	// 比如: proto/jce/json, 默认proto
	// 具体值与TrpcContentEncodeType对应
	ContentType uint32 `protobuf:"varint,4,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	// 请求数据使用的压缩方式
	// 比如: gzip/snappy/..., 默认不使用
	// 具体值与TrpcCompressType对应
	ContentEncoding uint32 `protobuf:"varint,5,opt,name=content_encoding,json=contentEncoding,proto3" json:"content_encoding,omitempty"`
}

func (x *TrpcStreamInitMeta) Reset() {
	*x = TrpcStreamInitMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrpcStreamInitMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrpcStreamInitMeta) ProtoMessage() {}

func (x *TrpcStreamInitMeta) ProtoReflect() protoreflect.Message {
	mi := &file_trpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrpcStreamInitMeta.ProtoReflect.Descriptor instead.
func (*TrpcStreamInitMeta) Descriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{0}
}

func (x *TrpcStreamInitMeta) GetRequestMeta() *TrpcStreamInitRequestMeta {
	if x != nil {
		return x.RequestMeta
	}
	return nil
}

func (x *TrpcStreamInitMeta) GetResponseMeta() *TrpcStreamInitResponseMeta {
	if x != nil {
		return x.ResponseMeta
	}
	return nil
}

func (x *TrpcStreamInitMeta) GetInitWindowSize() uint32 {
	if x != nil {
		return x.InitWindowSize
	}
	return 0
}

func (x *TrpcStreamInitMeta) GetContentType() uint32 {
	if x != nil {
		return x.ContentType
	}
	return 0
}

func (x *TrpcStreamInitMeta) GetContentEncoding() uint32 {
	if x != nil {
		return x.ContentEncoding
	}
	return 0
}

// trpc流式init头的请求元信息
type TrpcStreamInitRequestMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 主调服务的名称
	// trpc协议下的规范格式: trpc.应用名.服务名.pb的service名, 4段
	Caller []byte `protobuf:"bytes,1,opt,name=caller,proto3" json:"caller,omitempty"`
	// 被调服务的路由名称
	// trpc协议下的规范格式，trpc.应用名.服务名.pb的service名[.接口名]
	// 前4段是必须有，接口可选。
	Callee []byte `protobuf:"bytes,2,opt,name=callee,proto3" json:"callee,omitempty"`
	// 调用服务的接口名
	// 规范格式: /package.Service名称/接口名
	Func []byte `protobuf:"bytes,3,opt,name=func,proto3" json:"func,omitempty"`
	// 框架信息透传的消息类型
	// 比如调用链、染色key、灰度、鉴权、多环境、set名称等的标识
	// 具体值与TrpcMessageType对应
	MessageType uint32 `protobuf:"varint,4,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty"`
	// 框架透传的信息key-value对，目前分两部分
	// 1是框架层要透传的信息，key的名字要以trpc-开头
	// 2是业务层要透传的信息，业务可以自行设置
	// 注意: trans_info中的key-value对会全链路透传，业务请谨慎使用！
	TransInfo map[string][]byte `protobuf:"bytes,5,rep,name=trans_info,json=transInfo,proto3" json:"trans_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *TrpcStreamInitRequestMeta) Reset() {
	*x = TrpcStreamInitRequestMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrpcStreamInitRequestMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrpcStreamInitRequestMeta) ProtoMessage() {}

func (x *TrpcStreamInitRequestMeta) ProtoReflect() protoreflect.Message {
	mi := &file_trpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrpcStreamInitRequestMeta.ProtoReflect.Descriptor instead.
func (*TrpcStreamInitRequestMeta) Descriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{1}
}

func (x *TrpcStreamInitRequestMeta) GetCaller() []byte {
	if x != nil {
		return x.Caller
	}
	return nil
}

func (x *TrpcStreamInitRequestMeta) GetCallee() []byte {
	if x != nil {
		return x.Callee
	}
	return nil
}

func (x *TrpcStreamInitRequestMeta) GetFunc() []byte {
	if x != nil {
		return x.Func
	}
	return nil
}

func (x *TrpcStreamInitRequestMeta) GetMessageType() uint32 {
	if x != nil {
		return x.MessageType
	}
	return 0
}

func (x *TrpcStreamInitRequestMeta) GetTransInfo() map[string][]byte {
	if x != nil {
		return x.TransInfo
	}
	return nil
}

// trpc流式init头的响应元信息
type TrpcStreamInitResponseMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 请求在框架层的错误返回码
	// 具体值与TrpcRetCode对应
	Ret int32 `protobuf:"varint,1,opt,name=ret,proto3" json:"ret,omitempty"`
	// 调用结果信息描述
	// 失败的时候用
	ErrorMsg []byte `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
}

func (x *TrpcStreamInitResponseMeta) Reset() {
	*x = TrpcStreamInitResponseMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trpc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrpcStreamInitResponseMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrpcStreamInitResponseMeta) ProtoMessage() {}

func (x *TrpcStreamInitResponseMeta) ProtoReflect() protoreflect.Message {
	mi := &file_trpc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrpcStreamInitResponseMeta.ProtoReflect.Descriptor instead.
func (*TrpcStreamInitResponseMeta) Descriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{2}
}

func (x *TrpcStreamInitResponseMeta) GetRet() int32 {
	if x != nil {
		return x.Ret
	}
	return 0
}

func (x *TrpcStreamInitResponseMeta) GetErrorMsg() []byte {
	if x != nil {
		return x.ErrorMsg
	}
	return nil
}

// trpc流式的流控帧头元信息定义
type TrpcStreamFeedBackMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 增加的窗口大小
	WindowSizeIncrement uint32 `protobuf:"varint,1,opt,name=window_size_increment,json=windowSizeIncrement,proto3" json:"window_size_increment,omitempty"`
}

func (x *TrpcStreamFeedBackMeta) Reset() {
	*x = TrpcStreamFeedBackMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trpc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrpcStreamFeedBackMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrpcStreamFeedBackMeta) ProtoMessage() {}

func (x *TrpcStreamFeedBackMeta) ProtoReflect() protoreflect.Message {
	mi := &file_trpc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrpcStreamFeedBackMeta.ProtoReflect.Descriptor instead.
func (*TrpcStreamFeedBackMeta) Descriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{3}
}

func (x *TrpcStreamFeedBackMeta) GetWindowSizeIncrement() uint32 {
	if x != nil {
		return x.WindowSizeIncrement
	}
	return 0
}

// trpc流式的RESET帧头消息定义
type TrpcStreamCloseMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 关闭的类型，关闭一端，还是全部关闭
	CloseType int32 `protobuf:"varint,1,opt,name=close_type,json=closeType,proto3" json:"close_type,omitempty"`
	// close返回码
	// 代表框架层的错误
	Ret int32 `protobuf:"varint,2,opt,name=ret,proto3" json:"ret,omitempty"`
	// close信息描述
	Msg []byte `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
	// 框架信息透传的消息类型
	// 比如调用链、染色key、灰度、鉴权、多环境、set名称等的标识
	// 具体值与TrpcMessageType对应
	MessageType uint32 `protobuf:"varint,4,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty"`
	// 框架透传的信息key-value对，目前分两部分
	// 1是框架层要透传的信息，key的名字要以trpc-开头
	// 2是业务层要透传的信息，业务可以自行设置
	TransInfo map[string][]byte `protobuf:"bytes,5,rep,name=trans_info,json=transInfo,proto3" json:"trans_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 接口的错误返回码
	// 建议业务在使用时，标识成功和失败，0代表成功，其它代表失败
	FuncRet int32 `protobuf:"varint,6,opt,name=func_ret,json=funcRet,proto3" json:"func_ret,omitempty"`
}

func (x *TrpcStreamCloseMeta) Reset() {
	*x = TrpcStreamCloseMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trpc_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrpcStreamCloseMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrpcStreamCloseMeta) ProtoMessage() {}

func (x *TrpcStreamCloseMeta) ProtoReflect() protoreflect.Message {
	mi := &file_trpc_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrpcStreamCloseMeta.ProtoReflect.Descriptor instead.
func (*TrpcStreamCloseMeta) Descriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{4}
}

func (x *TrpcStreamCloseMeta) GetCloseType() int32 {
	if x != nil {
		return x.CloseType
	}
	return 0
}

func (x *TrpcStreamCloseMeta) GetRet() int32 {
	if x != nil {
		return x.Ret
	}
	return 0
}

func (x *TrpcStreamCloseMeta) GetMsg() []byte {
	if x != nil {
		return x.Msg
	}
	return nil
}

func (x *TrpcStreamCloseMeta) GetMessageType() uint32 {
	if x != nil {
		return x.MessageType
	}
	return 0
}

func (x *TrpcStreamCloseMeta) GetTransInfo() map[string][]byte {
	if x != nil {
		return x.TransInfo
	}
	return nil
}

func (x *TrpcStreamCloseMeta) GetFuncRet() int32 {
	if x != nil {
		return x.FuncRet
	}
	return 0
}

// 请求协议头
type RequestProtocol struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 协议版本
	// 具体值与TrpcProtoVersion对应
	Version uint32 `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	// 请求的调用类型
	// 比如: 普通调用，单向调用
	// 具体值与TrpcCallType对应
	CallType uint32 `protobuf:"varint,2,opt,name=call_type,json=callType,proto3" json:"call_type,omitempty"`
	// 请求唯一id
	RequestId uint32 `protobuf:"varint,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// 请求的超时时间，单位ms
	Timeout uint32 `protobuf:"varint,4,opt,name=timeout,proto3" json:"timeout,omitempty"`
	// 主调服务的名称
	// trpc协议下的规范格式: trpc.应用名.服务名.pb的service名, 4段
	Caller []byte `protobuf:"bytes,5,opt,name=caller,proto3" json:"caller,omitempty"`
	// 被调服务的路由名称
	// trpc协议下的规范格式，trpc.应用名.服务名.pb的service名[.接口名]
	// 前4段是必须有，接口可选。
	Callee []byte `protobuf:"bytes,6,opt,name=callee,proto3" json:"callee,omitempty"`
	// 调用服务的接口名
	// 规范格式: /package.Service名称/接口名
	Func []byte `protobuf:"bytes,7,opt,name=func,proto3" json:"func,omitempty"`
	// 框架信息透传的消息类型
	// 比如调用链、染色key、灰度、鉴权、多环境、set名称等的标识
	// 具体值与TrpcMessageType对应
	MessageType uint32 `protobuf:"varint,8,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty"`
	// 框架透传的信息key-value对，目前分两部分
	// 1是框架层要透传的信息，key的名字要以trpc-开头
	// 2是业务层要透传的信息，业务可以自行设置
	TransInfo map[string][]byte `protobuf:"bytes,9,rep,name=trans_info,json=transInfo,proto3" json:"trans_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 请求数据的序列化类型
	// 比如: proto/jce/json, 默认proto
	// 具体值与TrpcContentEncodeType对应
	ContentType uint32 `protobuf:"varint,10,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	// 请求数据使用的压缩方式
	// 比如: gzip/snappy/..., 默认不使用
	// 具体值与TrpcCompressType对应
	ContentEncoding uint32 `protobuf:"varint,11,opt,name=content_encoding,json=contentEncoding,proto3" json:"content_encoding,omitempty"`
	// attachment大小
	AttachmentSize uint32 `protobuf:"varint,12,opt,name=attachment_size,json=attachmentSize,proto3" json:"attachment_size,omitempty"`
}

func (x *RequestProtocol) Reset() {
	*x = RequestProtocol{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trpc_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestProtocol) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestProtocol) ProtoMessage() {}

func (x *RequestProtocol) ProtoReflect() protoreflect.Message {
	mi := &file_trpc_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestProtocol.ProtoReflect.Descriptor instead.
func (*RequestProtocol) Descriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{5}
}

func (x *RequestProtocol) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *RequestProtocol) GetCallType() uint32 {
	if x != nil {
		return x.CallType
	}
	return 0
}

func (x *RequestProtocol) GetRequestId() uint32 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

func (x *RequestProtocol) GetTimeout() uint32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *RequestProtocol) GetCaller() []byte {
	if x != nil {
		return x.Caller
	}
	return nil
}

func (x *RequestProtocol) GetCallee() []byte {
	if x != nil {
		return x.Callee
	}
	return nil
}

func (x *RequestProtocol) GetFunc() []byte {
	if x != nil {
		return x.Func
	}
	return nil
}

func (x *RequestProtocol) GetMessageType() uint32 {
	if x != nil {
		return x.MessageType
	}
	return 0
}

func (x *RequestProtocol) GetTransInfo() map[string][]byte {
	if x != nil {
		return x.TransInfo
	}
	return nil
}

func (x *RequestProtocol) GetContentType() uint32 {
	if x != nil {
		return x.ContentType
	}
	return 0
}

func (x *RequestProtocol) GetContentEncoding() uint32 {
	if x != nil {
		return x.ContentEncoding
	}
	return 0
}

func (x *RequestProtocol) GetAttachmentSize() uint32 {
	if x != nil {
		return x.AttachmentSize
	}
	return 0
}

// 响应协议头
type ResponseProtocol struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 协议版本
	// 具体值与TrpcProtoVersion对应
	Version uint32 `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	// 请求的调用类型
	// 比如: 普通调用，单向调用
	// 具体值与TrpcCallType对应
	CallType uint32 `protobuf:"varint,2,opt,name=call_type,json=callType,proto3" json:"call_type,omitempty"`
	// 请求唯一id
	RequestId uint32 `protobuf:"varint,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// 请求在框架层的错误返回码
	// 具体值与TrpcRetCode对应
	Ret int32 `protobuf:"varint,4,opt,name=ret,proto3" json:"ret,omitempty"`
	// 接口的错误返回码
	// 建议业务在使用时，标识成功和失败，0代表成功，其它代表失败
	FuncRet int32 `protobuf:"varint,5,opt,name=func_ret,json=funcRet,proto3" json:"func_ret,omitempty"`
	// 调用结果信息描述
	// 失败的时候用
	ErrorMsg []byte `protobuf:"bytes,6,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	// 框架信息透传的消息类型
	// 比如调用链、染色key、灰度、鉴权、多环境、set名称等的标识
	// 具体值与TrpcMessageType对应
	MessageType uint32 `protobuf:"varint,7,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty"`
	// 框架透传回来的信息key-value对，
	// 目前分两部分
	// 1是框架层透传回来的信息，key的名字要以trpc-开头
	// 2是业务层透传回来的信息，业务可以自行设置
	TransInfo map[string][]byte `protobuf:"bytes,8,rep,name=trans_info,json=transInfo,proto3" json:"trans_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 响应数据的编码类型
	// 比如: proto/jce/json, 默认proto
	// 具体值与TrpcContentEncodeType对应
	ContentType uint32 `protobuf:"varint,9,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	// 响应数据使用的压缩方式
	// 比如: gzip/snappy/..., 默认不使用
	// 具体值与TrpcCompressType对应
	ContentEncoding uint32 `protobuf:"varint,10,opt,name=content_encoding,json=contentEncoding,proto3" json:"content_encoding,omitempty"`
	// attachment大小
	AttachmentSize uint32 `protobuf:"varint,12,opt,name=attachment_size,json=attachmentSize,proto3" json:"attachment_size,omitempty"`
}

func (x *ResponseProtocol) Reset() {
	*x = ResponseProtocol{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trpc_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResponseProtocol) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseProtocol) ProtoMessage() {}

func (x *ResponseProtocol) ProtoReflect() protoreflect.Message {
	mi := &file_trpc_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseProtocol.ProtoReflect.Descriptor instead.
func (*ResponseProtocol) Descriptor() ([]byte, []int) {
	return file_trpc_proto_rawDescGZIP(), []int{6}
}

func (x *ResponseProtocol) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *ResponseProtocol) GetCallType() uint32 {
	if x != nil {
		return x.CallType
	}
	return 0
}

func (x *ResponseProtocol) GetRequestId() uint32 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

func (x *ResponseProtocol) GetRet() int32 {
	if x != nil {
		return x.Ret
	}
	return 0
}

func (x *ResponseProtocol) GetFuncRet() int32 {
	if x != nil {
		return x.FuncRet
	}
	return 0
}

func (x *ResponseProtocol) GetErrorMsg() []byte {
	if x != nil {
		return x.ErrorMsg
	}
	return nil
}

func (x *ResponseProtocol) GetMessageType() uint32 {
	if x != nil {
		return x.MessageType
	}
	return 0
}

func (x *ResponseProtocol) GetTransInfo() map[string][]byte {
	if x != nil {
		return x.TransInfo
	}
	return nil
}

func (x *ResponseProtocol) GetContentType() uint32 {
	if x != nil {
		return x.ContentType
	}
	return 0
}

func (x *ResponseProtocol) GetContentEncoding() uint32 {
	if x != nil {
		return x.ContentEncoding
	}
	return 0
}

func (x *ResponseProtocol) GetAttachmentSize() uint32 {
	if x != nil {
		return x.AttachmentSize
	}
	return 0
}

var File_trpc_proto protoreflect.FileDescriptor

var file_trpc_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x74, 0x72,
	0x70, 0x63, 0x22, 0x97, 0x02, 0x0a, 0x12, 0x54, 0x72, 0x70, 0x63, 0x53, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x49, 0x6e, 0x69, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x42, 0x0a, 0x0c, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x54, 0x72, 0x70, 0x63, 0x53, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x45, 0x0a,
	0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x54, 0x72, 0x70, 0x63,
	0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x77, 0x69, 0x6e,
	0x64, 0x6f, 0x77, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e,
	0x69, 0x6e, 0x69, 0x74, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x63,
	0x6f, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x22, 0x8f, 0x02, 0x0a,
	0x19, 0x54, 0x72, 0x70, 0x63, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x69, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x61,
	0x6c, 0x6c, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c,
	0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x75,
	0x6e, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x66, 0x75, 0x6e, 0x63, 0x12, 0x21,
	0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x4d, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x54, 0x72, 0x70,
	0x63, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x1a, 0x3c, 0x0a, 0x0e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x4b,
	0x0a, 0x1a, 0x54, 0x72, 0x70, 0x63, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x69, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03,
	0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x22, 0x4c, 0x0a, 0x16, 0x54,
	0x72, 0x70, 0x63, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x46, 0x65, 0x65, 0x64, 0x42, 0x61, 0x63,
	0x6b, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x32, 0x0a, 0x15, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x5f, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x53, 0x69, 0x7a, 0x65,
	0x49, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x9d, 0x02, 0x0a, 0x13, 0x54, 0x72,
	0x70, 0x63, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x72,
	0x65, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x54, 0x72, 0x70, 0x63, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x43, 0x6c, 0x6f,
	0x73, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x19, 0x0a, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x5f, 0x72, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x66, 0x75, 0x6e, 0x63, 0x52, 0x65, 0x74, 0x1a, 0x3c, 0x0a, 0x0e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xe2, 0x03, 0x0a, 0x0f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x6c, 0x6c, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x61, 0x6c, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x63,
	0x61, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x66, 0x75, 0x6e, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x66, 0x75, 0x6e,
	0x63, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x45,
	0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0e, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x7a, 0x65,
	0x1a, 0x3c, 0x0a, 0x0e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xd0,
	0x03, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x66,
	0x75, 0x6e, 0x63, 0x5f, 0x72, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x66,
	0x75, 0x6e, 0x63, 0x52, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x6d, 0x73, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x4d, 0x73, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64,
	0x69, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0e, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x69, 0x7a, 0x65, 0x1a, 0x3c, 0x0a, 0x0e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x2a, 0x39, 0x0a, 0x09, 0x54, 0x72, 0x70, 0x63, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x12, 0x15,
	0x0a, 0x11, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x4e,
	0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x10, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x4d, 0x41,
	0x47, 0x49, 0x43, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0xb0, 0x12, 0x2a, 0x40, 0x0a, 0x11,
	0x54, 0x72, 0x70, 0x63, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x55, 0x4e, 0x41, 0x52, 0x59, 0x5f,
	0x46, 0x52, 0x41, 0x4d, 0x45, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x50, 0x43, 0x5f,
	0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x46, 0x52, 0x41, 0x4d, 0x45, 0x10, 0x01, 0x2a, 0x9a,
	0x01, 0x0a, 0x13, 0x54, 0x72, 0x70, 0x63, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x46, 0x72, 0x61,
	0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x55,
	0x4e, 0x41, 0x52, 0x59, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53,
	0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x46, 0x52, 0x41, 0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x49, 0x54,
	0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41,
	0x4d, 0x5f, 0x46, 0x52, 0x41, 0x4d, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x02, 0x12, 0x1e,
	0x0a, 0x1a, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x46, 0x52,
	0x41, 0x4d, 0x45, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x03, 0x12, 0x1b,
	0x0a, 0x17, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x46, 0x52,
	0x41, 0x4d, 0x45, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x10, 0x04, 0x2a, 0x43, 0x0a, 0x13, 0x54,
	0x72, 0x70, 0x63, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41,
	0x4d, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x50,
	0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x54, 0x10, 0x01,
	0x2a, 0x25, 0x0a, 0x10, 0x54, 0x72, 0x70, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x50, 0x52, 0x4f,
	0x54, 0x4f, 0x5f, 0x56, 0x31, 0x10, 0x00, 0x2a, 0x39, 0x0a, 0x0c, 0x54, 0x72, 0x70, 0x63, 0x43,
	0x61, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x52, 0x50, 0x43, 0x5f,
	0x55, 0x4e, 0x41, 0x52, 0x59, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10,
	0x54, 0x52, 0x50, 0x43, 0x5f, 0x4f, 0x4e, 0x45, 0x57, 0x41, 0x59, 0x5f, 0x43, 0x41, 0x4c, 0x4c,
	0x10, 0x01, 0x2a, 0xa1, 0x01, 0x0a, 0x0f, 0x54, 0x72, 0x70, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x44,
	0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x52, 0x50, 0x43,
	0x5f, 0x44, 0x59, 0x45, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10,
	0x01, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x45, 0x5f,
	0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x52, 0x50,
	0x43, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f, 0x45, 0x4e, 0x56, 0x5f, 0x4d, 0x45, 0x53, 0x53,
	0x41, 0x47, 0x45, 0x10, 0x04, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x47, 0x52,
	0x49, 0x44, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0x08, 0x12, 0x18, 0x0a, 0x14,
	0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x45, 0x54, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x45, 0x53,
	0x53, 0x41, 0x47, 0x45, 0x10, 0x10, 0x2a, 0xf2, 0x01, 0x0a, 0x15, 0x54, 0x72, 0x70, 0x63, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x5f, 0x45,
	0x4e, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x52, 0x50, 0x43, 0x5f,
	0x4a, 0x43, 0x45, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10,
	0x54, 0x52, 0x50, 0x43, 0x5f, 0x4a, 0x53, 0x4f, 0x4e, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45,
	0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x46, 0x4c, 0x41, 0x54, 0x42,
	0x55, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x03, 0x12, 0x14,
	0x0a, 0x10, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x4e, 0x4f, 0x4f, 0x50, 0x5f, 0x45, 0x4e, 0x43, 0x4f,
	0x44, 0x45, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x58, 0x4d, 0x4c,
	0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x52, 0x50,
	0x43, 0x5f, 0x54, 0x48, 0x52, 0x49, 0x46, 0x54, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x10,
	0x06, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x54, 0x48, 0x52, 0x49, 0x46, 0x54,
	0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x43, 0x54, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x10,
	0x07, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x58,
	0x4d, 0x4c, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x08, 0x2a, 0xf2, 0x01, 0x0a, 0x10,
	0x54, 0x72, 0x70, 0x63, 0x43, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x19, 0x0a, 0x15, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54,
	0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x52, 0x45, 0x53, 0x53, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x54,
	0x52, 0x50, 0x43, 0x5f, 0x47, 0x5a, 0x49, 0x50, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x52, 0x45, 0x53,
	0x53, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x4e, 0x41, 0x50,
	0x50, 0x59, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x16, 0x0a,
	0x12, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x5a, 0x4c, 0x49, 0x42, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x52,
	0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x4e,
	0x41, 0x50, 0x50, 0x59, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x52, 0x45, 0x53, 0x53, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53,
	0x4e, 0x41, 0x50, 0x50, 0x59, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x52, 0x45, 0x53, 0x53, 0x10, 0x05, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x4c,
	0x5a, 0x34, 0x5f, 0x46, 0x52, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x52, 0x45, 0x53,
	0x53, 0x10, 0x06, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x4c, 0x5a, 0x34, 0x5f,
	0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x52, 0x45, 0x53, 0x53, 0x10, 0x07,
	0x2a, 0xc7, 0x0e, 0x0a, 0x0b, 0x54, 0x72, 0x70, 0x63, 0x52, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x17, 0x0a, 0x13, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x49, 0x4e, 0x56, 0x4f, 0x4b, 0x45, 0x5f,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x52, 0x50,
	0x43, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x45, 0x52, 0x52, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x45,
	0x52, 0x56, 0x45, 0x52, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x10,
	0x02, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52,
	0x5f, 0x4e, 0x4f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x0b,
	0x12, 0x1a, 0x0a, 0x16, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f,
	0x4e, 0x4f, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x0c, 0x12, 0x1b, 0x0a, 0x17,
	0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x54, 0x49, 0x4d, 0x45,
	0x4f, 0x55, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x15, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x52, 0x50,
	0x43, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x4c, 0x4f, 0x41,
	0x44, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x16, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x52, 0x50, 0x43, 0x5f,
	0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x45, 0x44, 0x5f, 0x45,
	0x52, 0x52, 0x10, 0x17, 0x12, 0x25, 0x0a, 0x21, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x45, 0x52,
	0x56, 0x45, 0x52, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x54, 0x49,
	0x4d, 0x45, 0x4f, 0x55, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x18, 0x12, 0x1a, 0x0a, 0x16, 0x54,
	0x52, 0x50, 0x43, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45,
	0x4d, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x1f, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x52, 0x50, 0x43, 0x5f,
	0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x45, 0x52, 0x52, 0x10,
	0x29, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52,
	0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x33, 0x12,
	0x22, 0x0a, 0x1e, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x49,
	0x4e, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x4f, 0x55, 0x54, 0x5f, 0x45, 0x52,
	0x52, 0x10, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x43, 0x4c, 0x49, 0x45,
	0x4e, 0x54, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x54, 0x49, 0x4d,
	0x45, 0x4f, 0x55, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x66, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x52,
	0x50, 0x43, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43,
	0x54, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x6f, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x52, 0x50, 0x43, 0x5f,
	0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x52,
	0x52, 0x10, 0x79, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x43, 0x4c, 0x49, 0x45,
	0x4e, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x7a, 0x12,
	0x1b, 0x0a, 0x17, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x4c,
	0x49, 0x4d, 0x49, 0x54, 0x45, 0x44, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x7b, 0x12, 0x1c, 0x0a, 0x18,
	0x54, 0x52, 0x50, 0x43, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x56, 0x45, 0x52,
	0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x7c, 0x12, 0x1b, 0x0a, 0x16, 0x54, 0x52,
	0x50, 0x43, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x4f, 0x55, 0x54, 0x45, 0x52,
	0x5f, 0x45, 0x52, 0x52, 0x10, 0x83, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x54, 0x52, 0x50, 0x43, 0x5f,
	0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x45,
	0x52, 0x52, 0x10, 0x8d, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x43, 0x4c,
	0x49, 0x45, 0x4e, 0x54, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x52,
	0x52, 0x10, 0x97, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x43, 0x4c, 0x49,
	0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x5f, 0x45, 0x52, 0x52,
	0x10, 0xa1, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x43, 0x4c, 0x49, 0x45,
	0x4e, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x5f, 0x46, 0x52, 0x41, 0x4d, 0x45, 0x5f, 0x45, 0x52,
	0x52, 0x10, 0xab, 0x01, 0x12, 0x23, 0x0a, 0x1e, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52,
	0x45, 0x41, 0x4d, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f,
	0x52, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xc9, 0x01, 0x12, 0x2c, 0x0a, 0x27, 0x54, 0x52, 0x50,
	0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f,
	0x4d, 0x53, 0x47, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54,
	0x5f, 0x45, 0x52, 0x52, 0x10, 0xd3, 0x01, 0x12, 0x22, 0x0a, 0x1d, 0x54, 0x52, 0x50, 0x43, 0x5f,
	0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x45, 0x4e,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xdd, 0x01, 0x12, 0x22, 0x0a, 0x1d, 0x54,
	0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45,
	0x52, 0x5f, 0x44, 0x45, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xde, 0x01, 0x12,
	0x21, 0x0a, 0x1c, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x53,
	0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x5f, 0x45, 0x4e, 0x44, 0x10,
	0xe7, 0x01, 0x12, 0x2a, 0x0a, 0x25, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41,
	0x4d, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x5f, 0x4f,
	0x56, 0x45, 0x52, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xe8, 0x01, 0x12, 0x27,
	0x0a, 0x22, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x53, 0x45,
	0x52, 0x56, 0x45, 0x52, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45,
	0x5f, 0x45, 0x52, 0x52, 0x10, 0xe9, 0x01, 0x12, 0x29, 0x0a, 0x24, 0x54, 0x52, 0x50, 0x43, 0x5f,
	0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x57, 0x52,
	0x49, 0x54, 0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x4f, 0x55, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x10,
	0xea, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41,
	0x4d, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x5f, 0x45, 0x4e,
	0x44, 0x10, 0xfb, 0x01, 0x12, 0x26, 0x0a, 0x21, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52,
	0x45, 0x41, 0x4d, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x5f,
	0x43, 0x4c, 0x4f, 0x53, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xfc, 0x01, 0x12, 0x26, 0x0a, 0x21,
	0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x53, 0x45, 0x52, 0x56,
	0x45, 0x52, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x5f, 0x45, 0x52,
	0x52, 0x10, 0xfd, 0x01, 0x12, 0x28, 0x0a, 0x23, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52,
	0x45, 0x41, 0x4d, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x5f,
	0x54, 0x49, 0x4d, 0x45, 0x4f, 0x55, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xfe, 0x01, 0x12, 0x28,
	0x0a, 0x23, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x53, 0x45,
	0x52, 0x56, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x4c, 0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x4f, 0x55,
	0x54, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xff, 0x01, 0x12, 0x23, 0x0a, 0x1e, 0x54, 0x52, 0x50, 0x43,
	0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x4e,
	0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xad, 0x02, 0x12, 0x2c, 0x0a,
	0x27, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x43, 0x4c, 0x49,
	0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x5f, 0x4c,
	0x49, 0x4d, 0x49, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xb7, 0x02, 0x12, 0x22, 0x0a, 0x1d, 0x54,
	0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e,
	0x54, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xc1, 0x02, 0x12,
	0x22, 0x0a, 0x1d, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x43,
	0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x52, 0x52,
	0x10, 0xc2, 0x02, 0x12, 0x21, 0x0a, 0x1c, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45,
	0x41, 0x4d, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x5f,
	0x45, 0x4e, 0x44, 0x10, 0xcb, 0x02, 0x12, 0x2a, 0x0a, 0x25, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53,
	0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x57, 0x52, 0x49,
	0x54, 0x45, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x45, 0x52, 0x52, 0x10,
	0xcc, 0x02, 0x12, 0x27, 0x0a, 0x22, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41,
	0x4d, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x5f, 0x43,
	0x4c, 0x4f, 0x53, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xcd, 0x02, 0x12, 0x29, 0x0a, 0x24, 0x54,
	0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e,
	0x54, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x4f, 0x55, 0x54, 0x5f,
	0x45, 0x52, 0x52, 0x10, 0xce, 0x02, 0x12, 0x20, 0x0a, 0x1b, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53,
	0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x41,
	0x44, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0xdf, 0x02, 0x12, 0x26, 0x0a, 0x21, 0x54, 0x52, 0x50, 0x43,
	0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x52,
	0x45, 0x41, 0x44, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xe0, 0x02,
	0x12, 0x26, 0x0a, 0x21, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f,
	0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x5f, 0x45, 0x4d, 0x50, 0x54,
	0x59, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xe1, 0x02, 0x12, 0x28, 0x0a, 0x23, 0x54, 0x52, 0x50, 0x43,
	0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x52,
	0x45, 0x41, 0x44, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x4f, 0x55, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x10,
	0xe2, 0x02, 0x12, 0x28, 0x0a, 0x23, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41,
	0x4d, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x44, 0x4c, 0x45, 0x5f, 0x54, 0x49,
	0x4d, 0x45, 0x4f, 0x55, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xe3, 0x02, 0x12, 0x20, 0x0a, 0x1b,
	0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x43, 0x4c, 0x49, 0x45,
	0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xe9, 0x02, 0x12, 0x1c,
	0x0a, 0x17, 0x54, 0x52, 0x50, 0x43, 0x5f, 0x49, 0x4e, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xe7, 0x07, 0x12, 0x1c, 0x0a, 0x17,
	0x54, 0x52, 0x50, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xe8, 0x07, 0x42, 0x61, 0x0a, 0x26, 0x63, 0x6f,
	0x6d, 0x2e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x42, 0x0c, 0x54, 0x52, 0x50, 0x43, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x74, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72, 0x70, 0x63, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x6f, 0x2f, 0x74, 0x72, 0x70, 0x63, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_trpc_proto_rawDescOnce sync.Once
	file_trpc_proto_rawDescData = file_trpc_proto_rawDesc
)

func file_trpc_proto_rawDescGZIP() []byte {
	file_trpc_proto_rawDescOnce.Do(func() {
		file_trpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_trpc_proto_rawDescData)
	})
	return file_trpc_proto_rawDescData
}

var file_trpc_proto_enumTypes = make([]protoimpl.EnumInfo, 10)
var file_trpc_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_trpc_proto_goTypes = []interface{}{
	(TrpcMagic)(0),                     // 0: trpc.TrpcMagic
	(TrpcDataFrameType)(0),             // 1: trpc.TrpcDataFrameType
	(TrpcStreamFrameType)(0),           // 2: trpc.TrpcStreamFrameType
	(TrpcStreamCloseType)(0),           // 3: trpc.TrpcStreamCloseType
	(TrpcProtoVersion)(0),              // 4: trpc.TrpcProtoVersion
	(TrpcCallType)(0),                  // 5: trpc.TrpcCallType
	(TrpcMessageType)(0),               // 6: trpc.TrpcMessageType
	(TrpcContentEncodeType)(0),         // 7: trpc.TrpcContentEncodeType
	(TrpcCompressType)(0),              // 8: trpc.TrpcCompressType
	(TrpcRetCode)(0),                   // 9: trpc.TrpcRetCode
	(*TrpcStreamInitMeta)(nil),         // 10: trpc.TrpcStreamInitMeta
	(*TrpcStreamInitRequestMeta)(nil),  // 11: trpc.TrpcStreamInitRequestMeta
	(*TrpcStreamInitResponseMeta)(nil), // 12: trpc.TrpcStreamInitResponseMeta
	(*TrpcStreamFeedBackMeta)(nil),     // 13: trpc.TrpcStreamFeedBackMeta
	(*TrpcStreamCloseMeta)(nil),        // 14: trpc.TrpcStreamCloseMeta
	(*RequestProtocol)(nil),            // 15: trpc.RequestProtocol
	(*ResponseProtocol)(nil),           // 16: trpc.ResponseProtocol
	nil,                                // 17: trpc.TrpcStreamInitRequestMeta.TransInfoEntry
	nil,                                // 18: trpc.TrpcStreamCloseMeta.TransInfoEntry
	nil,                                // 19: trpc.RequestProtocol.TransInfoEntry
	nil,                                // 20: trpc.ResponseProtocol.TransInfoEntry
}
var file_trpc_proto_depIdxs = []int32{
	11, // 0: trpc.TrpcStreamInitMeta.request_meta:type_name -> trpc.TrpcStreamInitRequestMeta
	12, // 1: trpc.TrpcStreamInitMeta.response_meta:type_name -> trpc.TrpcStreamInitResponseMeta
	17, // 2: trpc.TrpcStreamInitRequestMeta.trans_info:type_name -> trpc.TrpcStreamInitRequestMeta.TransInfoEntry
	18, // 3: trpc.TrpcStreamCloseMeta.trans_info:type_name -> trpc.TrpcStreamCloseMeta.TransInfoEntry
	19, // 4: trpc.RequestProtocol.trans_info:type_name -> trpc.RequestProtocol.TransInfoEntry
	20, // 5: trpc.ResponseProtocol.trans_info:type_name -> trpc.ResponseProtocol.TransInfoEntry
	6,  // [6:6] is the sub-list for method output_type
	6,  // [6:6] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_trpc_proto_init() }
func file_trpc_proto_init() {
	if File_trpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_trpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrpcStreamInitMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrpcStreamInitRequestMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trpc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrpcStreamInitResponseMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trpc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrpcStreamFeedBackMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trpc_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrpcStreamCloseMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trpc_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestProtocol); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trpc_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResponseProtocol); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_trpc_proto_rawDesc,
			NumEnums:      10,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_trpc_proto_goTypes,
		DependencyIndexes: file_trpc_proto_depIdxs,
		EnumInfos:         file_trpc_proto_enumTypes,
		MessageInfos:      file_trpc_proto_msgTypes,
	}.Build()
	File_trpc_proto = out.File
	file_trpc_proto_rawDesc = nil
	file_trpc_proto_goTypes = nil
	file_trpc_proto_depIdxs = nil
}
