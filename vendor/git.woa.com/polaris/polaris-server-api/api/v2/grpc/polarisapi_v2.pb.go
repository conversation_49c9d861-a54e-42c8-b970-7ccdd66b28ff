// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v2/grpc/polarisapi_v2.proto

package grpc

import (
	context "context"
	model "git.woa.com/polaris/polaris-server-api/api/v2/model"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_v2_grpc_polarisapi_v2_proto protoreflect.FileDescriptor

var file_v2_grpc_polarisapi_v2_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x1a, 0x16, 0x76, 0x32, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x4c, 0x0a, 0x0b, 0x50, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x47, 0x52, 0x50,
	0x43, 0x12, 0x3d, 0x0a, 0x05, 0x57, 0x61, 0x74, 0x63, 0x68, 0x12, 0x16, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x1a, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01,
	0x42, 0x34, 0x5a, 0x32, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_v2_grpc_polarisapi_v2_proto_goTypes = []interface{}{
	(*model.WatchReq)(nil), // 0: api.v2.model.WatchReq
	(*model.WatchRsp)(nil), // 1: api.v2.model.WatchRsp
}
var file_v2_grpc_polarisapi_v2_proto_depIdxs = []int32{
	0, // 0: api.v2.grpc.PolarisGRPC.Watch:input_type -> api.v2.model.WatchReq
	1, // 1: api.v2.grpc.PolarisGRPC.Watch:output_type -> api.v2.model.WatchRsp
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_v2_grpc_polarisapi_v2_proto_init() }
func file_v2_grpc_polarisapi_v2_proto_init() {
	if File_v2_grpc_polarisapi_v2_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v2_grpc_polarisapi_v2_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_v2_grpc_polarisapi_v2_proto_goTypes,
		DependencyIndexes: file_v2_grpc_polarisapi_v2_proto_depIdxs,
	}.Build()
	File_v2_grpc_polarisapi_v2_proto = out.File
	file_v2_grpc_polarisapi_v2_proto_rawDesc = nil
	file_v2_grpc_polarisapi_v2_proto_goTypes = nil
	file_v2_grpc_polarisapi_v2_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// PolarisGRPCClient is the client API for PolarisGRPC service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PolarisGRPCClient interface {
	// 监听接口
	Watch(ctx context.Context, opts ...grpc.CallOption) (PolarisGRPC_WatchClient, error)
}

type polarisGRPCClient struct {
	cc grpc.ClientConnInterface
}

func NewPolarisGRPCClient(cc grpc.ClientConnInterface) PolarisGRPCClient {
	return &polarisGRPCClient{cc}
}

func (c *polarisGRPCClient) Watch(ctx context.Context, opts ...grpc.CallOption) (PolarisGRPC_WatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PolarisGRPC_serviceDesc.Streams[0], "/api.v2.grpc.PolarisGRPC/Watch", opts...)
	if err != nil {
		return nil, err
	}
	x := &polarisGRPCWatchClient{stream}
	return x, nil
}

type PolarisGRPC_WatchClient interface {
	Send(*model.WatchReq) error
	Recv() (*model.WatchRsp, error)
	grpc.ClientStream
}

type polarisGRPCWatchClient struct {
	grpc.ClientStream
}

func (x *polarisGRPCWatchClient) Send(m *model.WatchReq) error {
	return x.ClientStream.SendMsg(m)
}

func (x *polarisGRPCWatchClient) Recv() (*model.WatchRsp, error) {
	m := new(model.WatchRsp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PolarisGRPCServer is the server API for PolarisGRPC service.
type PolarisGRPCServer interface {
	// 监听接口
	Watch(PolarisGRPC_WatchServer) error
}

// UnimplementedPolarisGRPCServer can be embedded to have forward compatible implementations.
type UnimplementedPolarisGRPCServer struct {
}

func (*UnimplementedPolarisGRPCServer) Watch(PolarisGRPC_WatchServer) error {
	return status.Errorf(codes.Unimplemented, "method Watch not implemented")
}

func RegisterPolarisGRPCServer(s *grpc.Server, srv PolarisGRPCServer) {
	s.RegisterService(&_PolarisGRPC_serviceDesc, srv)
}

func _PolarisGRPC_Watch_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(PolarisGRPCServer).Watch(&polarisGRPCWatchServer{stream})
}

type PolarisGRPC_WatchServer interface {
	Send(*model.WatchRsp) error
	Recv() (*model.WatchReq, error)
	grpc.ServerStream
}

type polarisGRPCWatchServer struct {
	grpc.ServerStream
}

func (x *polarisGRPCWatchServer) Send(m *model.WatchRsp) error {
	return x.ServerStream.SendMsg(m)
}

func (x *polarisGRPCWatchServer) Recv() (*model.WatchReq, error) {
	m := new(model.WatchReq)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _PolarisGRPC_serviceDesc = grpc.ServiceDesc{
	ServiceName: "api.v2.grpc.PolarisGRPC",
	HandlerType: (*PolarisGRPCServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Watch",
			Handler:       _PolarisGRPC_Watch_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "v2/grpc/polarisapi_v2.proto",
}
