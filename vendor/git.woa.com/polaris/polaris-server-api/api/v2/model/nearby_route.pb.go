// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v2/model/nearby_route.proto

package model

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 就近路由配置
type NearyRoute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Service *NearyRoute_Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	Config  *NearyRoute_Config  `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
	Rules   []*NearyRoute_Rule  `protobuf:"bytes,3,rep,name=rules,proto3" json:"rules,omitempty"`
	// 是否停用该规则，默认启用
	Disable bool `protobuf:"varint,4,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *NearyRoute) Reset() {
	*x = NearyRoute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v2_model_nearby_route_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NearyRoute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NearyRoute) ProtoMessage() {}

func (x *NearyRoute) ProtoReflect() protoreflect.Message {
	mi := &file_v2_model_nearby_route_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NearyRoute.ProtoReflect.Descriptor instead.
func (*NearyRoute) Descriptor() ([]byte, []int) {
	return file_v2_model_nearby_route_proto_rawDescGZIP(), []int{0}
}

func (x *NearyRoute) GetService() *NearyRoute_Service {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *NearyRoute) GetConfig() *NearyRoute_Config {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *NearyRoute) GetRules() []*NearyRoute_Rule {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *NearyRoute) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

// 用于匹配服务
type NearyRoute_Service struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 命名空间级服务：如果是配置入规则，则表示主调；如果是出规则，则表示被调
	Namespace *MatchString `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Service   *MatchString `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *NearyRoute_Service) Reset() {
	*x = NearyRoute_Service{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v2_model_nearby_route_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NearyRoute_Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NearyRoute_Service) ProtoMessage() {}

func (x *NearyRoute_Service) ProtoReflect() protoreflect.Message {
	mi := &file_v2_model_nearby_route_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NearyRoute_Service.ProtoReflect.Descriptor instead.
func (*NearyRoute_Service) Descriptor() ([]byte, []int) {
	return file_v2_model_nearby_route_proto_rawDescGZIP(), []int{0, 0}
}

func (x *NearyRoute_Service) GetNamespace() *MatchString {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *NearyRoute_Service) GetService() *MatchString {
	if x != nil {
		return x.Service
	}
	return nil
}

// 路由配置
type NearyRoute_Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 默认就近级别，为空时默认为zone
	MatchLevel string `protobuf:"bytes,1,opt,name=match_level,json=matchLevel,proto3" json:"match_level,omitempty"`
	// 最大就近级别，为空值默认为none
	MaxMatchLevel string `protobuf:"bytes,2,opt,name=max_match_level,json=maxMatchLevel,proto3" json:"max_match_level,omitempty"`
	// 降级健康比例，取值[0, 100]，默认0，全部不健康或熔断则降级
	HealthThreshold uint32 `protobuf:"varint,3,opt,name=health_threshold,json=healthThreshold,proto3" json:"health_threshold,omitempty"`
	// 是否开启按权重分流，默认不开启
	EnableWeight bool `protobuf:"varint,4,opt,name=enable_weight,json=enableWeight,proto3" json:"enable_weight,omitempty"`
}

func (x *NearyRoute_Config) Reset() {
	*x = NearyRoute_Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v2_model_nearby_route_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NearyRoute_Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NearyRoute_Config) ProtoMessage() {}

func (x *NearyRoute_Config) ProtoReflect() protoreflect.Message {
	mi := &file_v2_model_nearby_route_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NearyRoute_Config.ProtoReflect.Descriptor instead.
func (*NearyRoute_Config) Descriptor() ([]byte, []int) {
	return file_v2_model_nearby_route_proto_rawDescGZIP(), []int{0, 1}
}

func (x *NearyRoute_Config) GetMatchLevel() string {
	if x != nil {
		return x.MatchLevel
	}
	return ""
}

func (x *NearyRoute_Config) GetMaxMatchLevel() string {
	if x != nil {
		return x.MaxMatchLevel
	}
	return ""
}

func (x *NearyRoute_Config) GetHealthThreshold() uint32 {
	if x != nil {
		return x.HealthThreshold
	}
	return 0
}

func (x *NearyRoute_Config) GetEnableWeight() bool {
	if x != nil {
		return x.EnableWeight
	}
	return false
}

// 主调位置信息匹配规则
type NearyRoute_Source struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Campus *MatchString `protobuf:"bytes,1,opt,name=campus,proto3" json:"campus,omitempty"`
	Zone   *MatchString `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	Region *MatchString `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
}

func (x *NearyRoute_Source) Reset() {
	*x = NearyRoute_Source{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v2_model_nearby_route_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NearyRoute_Source) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NearyRoute_Source) ProtoMessage() {}

func (x *NearyRoute_Source) ProtoReflect() protoreflect.Message {
	mi := &file_v2_model_nearby_route_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NearyRoute_Source.ProtoReflect.Descriptor instead.
func (*NearyRoute_Source) Descriptor() ([]byte, []int) {
	return file_v2_model_nearby_route_proto_rawDescGZIP(), []int{0, 2}
}

func (x *NearyRoute_Source) GetCampus() *MatchString {
	if x != nil {
		return x.Campus
	}
	return nil
}

func (x *NearyRoute_Source) GetZone() *MatchString {
	if x != nil {
		return x.Zone
	}
	return nil
}

func (x *NearyRoute_Source) GetRegion() *MatchString {
	if x != nil {
		return x.Region
	}
	return nil
}

// 被调服务实例匹配规则
type NearyRoute_Destination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Campus *MatchString `protobuf:"bytes,1,opt,name=campus,proto3" json:"campus,omitempty"`
	Zone   *MatchString `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	Region *MatchString `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	// 优先级：整型，范围[0, 9]，默认为0，表示最高优先级
	Priority int32 `protobuf:"varint,4,opt,name=priority,proto3" json:"priority,omitempty"`
	// 权重：整型；同优先级地域按按权重分流，只有开启按权重分流时才生效
	Weight int32 `protobuf:"varint,5,opt,name=weight,proto3" json:"weight,omitempty"`
}

func (x *NearyRoute_Destination) Reset() {
	*x = NearyRoute_Destination{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v2_model_nearby_route_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NearyRoute_Destination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NearyRoute_Destination) ProtoMessage() {}

func (x *NearyRoute_Destination) ProtoReflect() protoreflect.Message {
	mi := &file_v2_model_nearby_route_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NearyRoute_Destination.ProtoReflect.Descriptor instead.
func (*NearyRoute_Destination) Descriptor() ([]byte, []int) {
	return file_v2_model_nearby_route_proto_rawDescGZIP(), []int{0, 3}
}

func (x *NearyRoute_Destination) GetCampus() *MatchString {
	if x != nil {
		return x.Campus
	}
	return nil
}

func (x *NearyRoute_Destination) GetZone() *MatchString {
	if x != nil {
		return x.Zone
	}
	return nil
}

func (x *NearyRoute_Destination) GetRegion() *MatchString {
	if x != nil {
		return x.Region
	}
	return nil
}

func (x *NearyRoute_Destination) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *NearyRoute_Destination) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

// 路由规则
type NearyRoute_Rule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 主调位置匹配规则
	Source *NearyRoute_Source `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	// 被调实例位置匹配规则
	Destinations []*NearyRoute_Destination `protobuf:"bytes,2,rep,name=destinations,proto3" json:"destinations,omitempty"`
}

func (x *NearyRoute_Rule) Reset() {
	*x = NearyRoute_Rule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v2_model_nearby_route_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NearyRoute_Rule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NearyRoute_Rule) ProtoMessage() {}

func (x *NearyRoute_Rule) ProtoReflect() protoreflect.Message {
	mi := &file_v2_model_nearby_route_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NearyRoute_Rule.ProtoReflect.Descriptor instead.
func (*NearyRoute_Rule) Descriptor() ([]byte, []int) {
	return file_v2_model_nearby_route_proto_rawDescGZIP(), []int{0, 4}
}

func (x *NearyRoute_Rule) GetSource() *NearyRoute_Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *NearyRoute_Rule) GetDestinations() []*NearyRoute_Destination {
	if x != nil {
		return x.Destinations
	}
	return nil
}

var File_v2_model_nearby_route_proto protoreflect.FileDescriptor

var file_v2_model_nearby_route_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6e, 0x65, 0x61, 0x72, 0x62,
	0x79, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x14, 0x76, 0x32, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xf2, 0x07, 0x0a, 0x0a, 0x4e, 0x65, 0x61, 0x72, 0x79, 0x52, 0x6f, 0x75, 0x74, 0x65,
	0x12, 0x3a, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x4e, 0x65, 0x61, 0x72, 0x79, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x37, 0x0a, 0x06,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x65, 0x61, 0x72,
	0x79, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x33, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x65, 0x61, 0x72, 0x79, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x2e, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x1a, 0x77, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x37, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x33, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x1a, 0xa1, 0x01,
	0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x61, 0x78,
	0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x29, 0x0a, 0x10, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x74, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x68, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x1a, 0x9d, 0x01, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x31, 0x0a, 0x06,
	0x63, 0x61, 0x6d, 0x70, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x06, 0x63, 0x61, 0x6d, 0x70, 0x75, 0x73, 0x12,
	0x2d, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x31,
	0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x1a, 0xd6, 0x01, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x31, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x70, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x06, 0x63, 0x61,
	0x6d, 0x70, 0x75, 0x73, 0x12, 0x2d, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x12, 0x31, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0x89, 0x01, 0x0a, 0x04, 0x52,
	0x75, 0x6c, 0x65, 0x12, 0x37, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x4e, 0x65, 0x61, 0x72, 0x79, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x2e, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x48, 0x0a, 0x0c,
	0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x4e, 0x65, 0x61, 0x72, 0x79, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x2e, 0x44, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x35, 0x5a, 0x33, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v2_model_nearby_route_proto_rawDescOnce sync.Once
	file_v2_model_nearby_route_proto_rawDescData = file_v2_model_nearby_route_proto_rawDesc
)

func file_v2_model_nearby_route_proto_rawDescGZIP() []byte {
	file_v2_model_nearby_route_proto_rawDescOnce.Do(func() {
		file_v2_model_nearby_route_proto_rawDescData = protoimpl.X.CompressGZIP(file_v2_model_nearby_route_proto_rawDescData)
	})
	return file_v2_model_nearby_route_proto_rawDescData
}

var file_v2_model_nearby_route_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_v2_model_nearby_route_proto_goTypes = []interface{}{
	(*NearyRoute)(nil),             // 0: api.v2.model.NearyRoute
	(*NearyRoute_Service)(nil),     // 1: api.v2.model.NearyRoute.Service
	(*NearyRoute_Config)(nil),      // 2: api.v2.model.NearyRoute.Config
	(*NearyRoute_Source)(nil),      // 3: api.v2.model.NearyRoute.Source
	(*NearyRoute_Destination)(nil), // 4: api.v2.model.NearyRoute.Destination
	(*NearyRoute_Rule)(nil),        // 5: api.v2.model.NearyRoute.Rule
	(*MatchString)(nil),            // 6: api.v2.model.MatchString
}
var file_v2_model_nearby_route_proto_depIdxs = []int32{
	1,  // 0: api.v2.model.NearyRoute.service:type_name -> api.v2.model.NearyRoute.Service
	2,  // 1: api.v2.model.NearyRoute.config:type_name -> api.v2.model.NearyRoute.Config
	5,  // 2: api.v2.model.NearyRoute.rules:type_name -> api.v2.model.NearyRoute.Rule
	6,  // 3: api.v2.model.NearyRoute.Service.namespace:type_name -> api.v2.model.MatchString
	6,  // 4: api.v2.model.NearyRoute.Service.service:type_name -> api.v2.model.MatchString
	6,  // 5: api.v2.model.NearyRoute.Source.campus:type_name -> api.v2.model.MatchString
	6,  // 6: api.v2.model.NearyRoute.Source.zone:type_name -> api.v2.model.MatchString
	6,  // 7: api.v2.model.NearyRoute.Source.region:type_name -> api.v2.model.MatchString
	6,  // 8: api.v2.model.NearyRoute.Destination.campus:type_name -> api.v2.model.MatchString
	6,  // 9: api.v2.model.NearyRoute.Destination.zone:type_name -> api.v2.model.MatchString
	6,  // 10: api.v2.model.NearyRoute.Destination.region:type_name -> api.v2.model.MatchString
	3,  // 11: api.v2.model.NearyRoute.Rule.source:type_name -> api.v2.model.NearyRoute.Source
	4,  // 12: api.v2.model.NearyRoute.Rule.destinations:type_name -> api.v2.model.NearyRoute.Destination
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_v2_model_nearby_route_proto_init() }
func file_v2_model_nearby_route_proto_init() {
	if File_v2_model_nearby_route_proto != nil {
		return
	}
	file_v2_model_model_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_v2_model_nearby_route_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NearyRoute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v2_model_nearby_route_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NearyRoute_Service); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v2_model_nearby_route_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NearyRoute_Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v2_model_nearby_route_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NearyRoute_Source); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v2_model_nearby_route_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NearyRoute_Destination); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v2_model_nearby_route_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NearyRoute_Rule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v2_model_nearby_route_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v2_model_nearby_route_proto_goTypes,
		DependencyIndexes: file_v2_model_nearby_route_proto_depIdxs,
		MessageInfos:      file_v2_model_nearby_route_proto_msgTypes,
	}.Build()
	File_v2_model_nearby_route_proto = out.File
	file_v2_model_nearby_route_proto_rawDesc = nil
	file_v2_model_nearby_route_proto_goTypes = nil
	file_v2_model_nearby_route_proto_depIdxs = nil
}
