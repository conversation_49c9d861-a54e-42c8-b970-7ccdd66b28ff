// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v1/model/mesh.proto

package polarismodel_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MeshService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 所属网格ID
	MeshId *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=mesh_id,proto3" json:"mesh_id,omitempty"`
	// 所属网格名字
	MeshName *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=mesh_name,proto3" json:"mesh_name,omitempty"`
	// 所属网格token
	MeshToken *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=mesh_token,proto3" json:"mesh_token,omitempty"`
	// 在北极星上面的服务的ID
	ServiceId *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=service_id,proto3" json:"service_id,omitempty"`
	// 在北极星上面的服务名
	Service *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=service,proto3" json:"service,omitempty"`
	// 在北极星上面的命名空间
	Namespace *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 服务的部门
	Department *wrapperspb.StringValue `protobuf:"bytes,8,opt,name=department,proto3" json:"department,omitempty"`
	// 服务的业务
	Business *wrapperspb.StringValue `protobuf:"bytes,9,opt,name=business,proto3" json:"business,omitempty"`
	// 服务负责人
	Owners *wrapperspb.StringValue `protobuf:"bytes,10,opt,name=owners,proto3" json:"owners,omitempty"`
	// 在服务网格里面的命名空间
	MeshNamespace *wrapperspb.StringValue `protobuf:"bytes,11,opt,name=mesh_namespace,proto3" json:"mesh_namespace,omitempty"`
	// 在服务网格里面的服务名
	MeshService *wrapperspb.StringValue `protobuf:"bytes,12,opt,name=mesh_service,proto3" json:"mesh_service,omitempty"`
	// 是网格内部还是外部服务
	Location *wrapperspb.StringValue `protobuf:"bytes,13,opt,name=location,proto3" json:"location,omitempty"`
	// 可以被网格的哪些命名空间看到
	ExportTo *wrapperspb.StringValue `protobuf:"bytes,14,opt,name=export_to,proto3" json:"export_to,omitempty"`
	// 网格服务的revision
	Revision *wrapperspb.StringValue `protobuf:"bytes,15,opt,name=revision,proto3" json:"revision,omitempty"`
	// 服务订阅时间
	Ctime *wrapperspb.StringValue `protobuf:"bytes,16,opt,name=ctime,proto3" json:"ctime,omitempty"`
	// 服务订阅信息修改时间
	Mtime *wrapperspb.StringValue `protobuf:"bytes,17,opt,name=mtime,proto3" json:"mtime,omitempty"`
	// 一些额外的属性，如协议，端口号等
	Attributes map[string]string `protobuf:"bytes,18,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 对应destinationRule的subset
	Subsets []*MeshServiceSubset `protobuf:"bytes,19,rep,name=subsets,proto3" json:"subsets,omitempty"`
}

func (x *MeshService) Reset() {
	*x = MeshService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_mesh_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MeshService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MeshService) ProtoMessage() {}

func (x *MeshService) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_mesh_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MeshService.ProtoReflect.Descriptor instead.
func (*MeshService) Descriptor() ([]byte, []int) {
	return file_v1_model_mesh_proto_rawDescGZIP(), []int{0}
}

func (x *MeshService) GetId() *wrapperspb.StringValue {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *MeshService) GetMeshId() *wrapperspb.StringValue {
	if x != nil {
		return x.MeshId
	}
	return nil
}

func (x *MeshService) GetMeshName() *wrapperspb.StringValue {
	if x != nil {
		return x.MeshName
	}
	return nil
}

func (x *MeshService) GetMeshToken() *wrapperspb.StringValue {
	if x != nil {
		return x.MeshToken
	}
	return nil
}

func (x *MeshService) GetServiceId() *wrapperspb.StringValue {
	if x != nil {
		return x.ServiceId
	}
	return nil
}

func (x *MeshService) GetService() *wrapperspb.StringValue {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *MeshService) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *MeshService) GetDepartment() *wrapperspb.StringValue {
	if x != nil {
		return x.Department
	}
	return nil
}

func (x *MeshService) GetBusiness() *wrapperspb.StringValue {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *MeshService) GetOwners() *wrapperspb.StringValue {
	if x != nil {
		return x.Owners
	}
	return nil
}

func (x *MeshService) GetMeshNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.MeshNamespace
	}
	return nil
}

func (x *MeshService) GetMeshService() *wrapperspb.StringValue {
	if x != nil {
		return x.MeshService
	}
	return nil
}

func (x *MeshService) GetLocation() *wrapperspb.StringValue {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *MeshService) GetExportTo() *wrapperspb.StringValue {
	if x != nil {
		return x.ExportTo
	}
	return nil
}

func (x *MeshService) GetRevision() *wrapperspb.StringValue {
	if x != nil {
		return x.Revision
	}
	return nil
}

func (x *MeshService) GetCtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Ctime
	}
	return nil
}

func (x *MeshService) GetMtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Mtime
	}
	return nil
}

func (x *MeshService) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *MeshService) GetSubsets() []*MeshServiceSubset {
	if x != nil {
		return x.Subsets
	}
	return nil
}

type MeshServiceSubset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Labels map[string]string       `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *MeshServiceSubset) Reset() {
	*x = MeshServiceSubset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_mesh_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MeshServiceSubset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MeshServiceSubset) ProtoMessage() {}

func (x *MeshServiceSubset) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_mesh_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MeshServiceSubset.ProtoReflect.Descriptor instead.
func (*MeshServiceSubset) Descriptor() ([]byte, []int) {
	return file_v1_model_mesh_proto_rawDescGZIP(), []int{1}
}

func (x *MeshServiceSubset) GetName() *wrapperspb.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *MeshServiceSubset) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type Mesh struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 网格名字
	Name *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 网格所属业务
	Business *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=business,proto3" json:"business,omitempty"`
	// 网格所属部门
	Department *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=department,proto3" json:"department,omitempty"`
	// 网格版本号
	Revision *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=revision,proto3" json:"revision,omitempty"`
	// 网格token
	Token *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=token,proto3" json:"token,omitempty"`
	// 网格属主
	Owners *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=owners,proto3" json:"owners,omitempty"`
	// 是否为托管网格
	Managed *wrapperspb.BoolValue `protobuf:"bytes,8,opt,name=managed,proto3" json:"managed,omitempty"`
	// istio的版本
	IstioVersion *wrapperspb.StringValue `protobuf:"bytes,9,opt,name=istio_version,proto3" json:"istio_version,omitempty"`
	// 该网格的数据面集群
	DataCluster *wrapperspb.StringValue `protobuf:"bytes,10,opt,name=data_cluster,proto3" json:"data_cluster,omitempty"`
	// 网格订阅的服务
	Services []*MeshService `protobuf:"bytes,11,rep,name=services,proto3" json:"services,omitempty"`
	// 网格描述
	Comment *wrapperspb.StringValue `protobuf:"bytes,12,opt,name=comment,proto3" json:"comment,omitempty"`
	// 网格创建时间
	Ctime *wrapperspb.StringValue `protobuf:"bytes,13,opt,name=ctime,proto3" json:"ctime,omitempty"`
	// 网格修改时间
	Mtime *wrapperspb.StringValue `protobuf:"bytes,14,opt,name=mtime,proto3" json:"mtime,omitempty"`
}

func (x *Mesh) Reset() {
	*x = Mesh{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_mesh_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Mesh) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mesh) ProtoMessage() {}

func (x *Mesh) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_mesh_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mesh.ProtoReflect.Descriptor instead.
func (*Mesh) Descriptor() ([]byte, []int) {
	return file_v1_model_mesh_proto_rawDescGZIP(), []int{2}
}

func (x *Mesh) GetId() *wrapperspb.StringValue {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *Mesh) GetName() *wrapperspb.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *Mesh) GetBusiness() *wrapperspb.StringValue {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *Mesh) GetDepartment() *wrapperspb.StringValue {
	if x != nil {
		return x.Department
	}
	return nil
}

func (x *Mesh) GetRevision() *wrapperspb.StringValue {
	if x != nil {
		return x.Revision
	}
	return nil
}

func (x *Mesh) GetToken() *wrapperspb.StringValue {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *Mesh) GetOwners() *wrapperspb.StringValue {
	if x != nil {
		return x.Owners
	}
	return nil
}

func (x *Mesh) GetManaged() *wrapperspb.BoolValue {
	if x != nil {
		return x.Managed
	}
	return nil
}

func (x *Mesh) GetIstioVersion() *wrapperspb.StringValue {
	if x != nil {
		return x.IstioVersion
	}
	return nil
}

func (x *Mesh) GetDataCluster() *wrapperspb.StringValue {
	if x != nil {
		return x.DataCluster
	}
	return nil
}

func (x *Mesh) GetServices() []*MeshService {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *Mesh) GetComment() *wrapperspb.StringValue {
	if x != nil {
		return x.Comment
	}
	return nil
}

func (x *Mesh) GetCtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Ctime
	}
	return nil
}

func (x *Mesh) GetMtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Mtime
	}
	return nil
}

type MeshResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 所属网格ID
	MeshId *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=mesh_id,proto3" json:"mesh_id,omitempty"`
	// 所属网格名字
	MeshName *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=mesh_name,proto3" json:"mesh_name,omitempty"`
	// 所属网格命名空间
	MeshNamespace *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=mesh_namespace,proto3" json:"mesh_namespace,omitempty"`
	// 网格规则的类型
	TypeUrl *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=type_url,proto3" json:"type_url,omitempty"`
	// 网格规则名字
	Name *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	// 网格规则修订号
	Revision *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=revision,proto3" json:"revision,omitempty"`
	// 网格规则的内容
	Body *wrapperspb.StringValue `protobuf:"bytes,8,opt,name=body,proto3" json:"body,omitempty"`
	// 所属网格的token
	MeshToken *wrapperspb.StringValue `protobuf:"bytes,9,opt,name=mesh_token,proto3" json:"mesh_token,omitempty"`
	// 规则创建时间
	Ctime *wrapperspb.StringValue `protobuf:"bytes,10,opt,name=ctime,proto3" json:"ctime,omitempty"`
	// 规则修改时间
	Mtime *wrapperspb.StringValue `protobuf:"bytes,11,opt,name=mtime,proto3" json:"mtime,omitempty"`
}

func (x *MeshResource) Reset() {
	*x = MeshResource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_mesh_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MeshResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MeshResource) ProtoMessage() {}

func (x *MeshResource) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_mesh_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MeshResource.ProtoReflect.Descriptor instead.
func (*MeshResource) Descriptor() ([]byte, []int) {
	return file_v1_model_mesh_proto_rawDescGZIP(), []int{3}
}

func (x *MeshResource) GetId() *wrapperspb.StringValue {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *MeshResource) GetMeshId() *wrapperspb.StringValue {
	if x != nil {
		return x.MeshId
	}
	return nil
}

func (x *MeshResource) GetMeshName() *wrapperspb.StringValue {
	if x != nil {
		return x.MeshName
	}
	return nil
}

func (x *MeshResource) GetMeshNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.MeshNamespace
	}
	return nil
}

func (x *MeshResource) GetTypeUrl() *wrapperspb.StringValue {
	if x != nil {
		return x.TypeUrl
	}
	return nil
}

func (x *MeshResource) GetName() *wrapperspb.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *MeshResource) GetRevision() *wrapperspb.StringValue {
	if x != nil {
		return x.Revision
	}
	return nil
}

func (x *MeshResource) GetBody() *wrapperspb.StringValue {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *MeshResource) GetMeshToken() *wrapperspb.StringValue {
	if x != nil {
		return x.MeshToken
	}
	return nil
}

func (x *MeshResource) GetCtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Ctime
	}
	return nil
}

func (x *MeshResource) GetMtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Mtime
	}
	return nil
}

type MeshConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 所属网格ID
	MeshId *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=mesh_id,proto3" json:"mesh_id,omitempty"`
	// 所属网格名字
	MeshName *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=mesh_name,proto3" json:"mesh_name,omitempty"`
	// 请求的配置类型
	TypeUrl *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=type_url,proto3" json:"type_url,omitempty"`
	// 具体的各个网格规则
	Resources []*MeshResource `protobuf:"bytes,4,rep,name=resources,proto3" json:"resources,omitempty"`
	// 总体的修订版本号
	Revision *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=revision,proto3" json:"revision,omitempty"`
}

func (x *MeshConfig) Reset() {
	*x = MeshConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_mesh_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MeshConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MeshConfig) ProtoMessage() {}

func (x *MeshConfig) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_mesh_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MeshConfig.ProtoReflect.Descriptor instead.
func (*MeshConfig) Descriptor() ([]byte, []int) {
	return file_v1_model_mesh_proto_rawDescGZIP(), []int{4}
}

func (x *MeshConfig) GetMeshId() *wrapperspb.StringValue {
	if x != nil {
		return x.MeshId
	}
	return nil
}

func (x *MeshConfig) GetMeshName() *wrapperspb.StringValue {
	if x != nil {
		return x.MeshName
	}
	return nil
}

func (x *MeshConfig) GetTypeUrl() *wrapperspb.StringValue {
	if x != nil {
		return x.TypeUrl
	}
	return nil
}

func (x *MeshConfig) GetResources() []*MeshResource {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *MeshConfig) GetRevision() *wrapperspb.StringValue {
	if x != nil {
		return x.Revision
	}
	return nil
}

var File_v1_model_mesh_proto protoreflect.FileDescriptor

var file_v1_model_mesh_proto_rawDesc = []byte{
	0x0a, 0x13, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6d, 0x65, 0x73, 0x68, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xb2, 0x09, 0x0a, 0x0b, 0x4d, 0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x36, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x69, 0x64, 0x12, 0x3a, 0x0a, 0x09, 0x6d, 0x65, 0x73,
	0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6d, 0x65, 0x73, 0x68,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0a, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x3c, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x12, 0x36, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x3c, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x34, 0x0a,
	0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x73, 0x12, 0x44, 0x0a, 0x0e, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x6d, 0x65, 0x73, 0x68, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x40, 0x0a, 0x0c, 0x6d, 0x65, 0x73,
	0x68, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x6d,
	0x65, 0x73, 0x68, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x74, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74,
	0x6f, 0x12, 0x38, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x05, 0x63,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x32, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x6d, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x49, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x39,
	0x0a, 0x07, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d,
	0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74,
	0x52, 0x07, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x73, 0x1a, 0x3d, 0x0a, 0x0f, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc5, 0x01, 0x0a, 0x11, 0x4d, 0x65, 0x73,
	0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x12, 0x30,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x43, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x4d, 0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x62, 0x73, 0x65,
	0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x95, 0x06, 0x0a, 0x04, 0x4d, 0x65, 0x73, 0x68, 0x12, 0x2c, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x12, 0x3c, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x38, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x34, 0x0a, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x34, 0x0a, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x64, 0x12, 0x42, 0x0a, 0x0d, 0x69,
	0x73, 0x74, 0x69, 0x6f, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x0d, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x40, 0x0a, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x12, 0x35, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x08,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x36, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x32, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x63,
	0x74, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xf4, 0x04, 0x0a, 0x0c, 0x4d, 0x65, 0x73,
	0x68, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x02, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x68, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x69, 0x64, 0x12,
	0x3a, 0x0a, 0x09, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x09, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x0e, 0x6d,
	0x65, 0x73, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0e, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x12, 0x38, 0x0a, 0x08, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x08, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x30, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a,
	0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x72,
	0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x3c, 0x0a, 0x0a, 0x6d, 0x65, 0x73,
	0x68, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x6d, 0x65, 0x73,
	0x68, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x32, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x6d,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x22,
	0xae, 0x02, 0x0a, 0x0a, 0x4d, 0x65, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x36,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x68, 0x5f, 0x69, 0x64, 0x12, 0x3a, 0x0a, 0x09, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x08, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x38, 0x0a, 0x09,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d,
	0x65, 0x73, 0x68, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x09, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x3b, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v1_model_mesh_proto_rawDescOnce sync.Once
	file_v1_model_mesh_proto_rawDescData = file_v1_model_mesh_proto_rawDesc
)

func file_v1_model_mesh_proto_rawDescGZIP() []byte {
	file_v1_model_mesh_proto_rawDescOnce.Do(func() {
		file_v1_model_mesh_proto_rawDescData = protoimpl.X.CompressGZIP(file_v1_model_mesh_proto_rawDescData)
	})
	return file_v1_model_mesh_proto_rawDescData
}

var file_v1_model_mesh_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_v1_model_mesh_proto_goTypes = []interface{}{
	(*MeshService)(nil),            // 0: api.v1.model.MeshService
	(*MeshServiceSubset)(nil),      // 1: api.v1.model.MeshServiceSubset
	(*Mesh)(nil),                   // 2: api.v1.model.Mesh
	(*MeshResource)(nil),           // 3: api.v1.model.MeshResource
	(*MeshConfig)(nil),             // 4: api.v1.model.MeshConfig
	nil,                            // 5: api.v1.model.MeshService.AttributesEntry
	nil,                            // 6: api.v1.model.MeshServiceSubset.LabelsEntry
	(*wrapperspb.StringValue)(nil), // 7: google.protobuf.StringValue
	(*wrapperspb.BoolValue)(nil),   // 8: google.protobuf.BoolValue
}
var file_v1_model_mesh_proto_depIdxs = []int32{
	7,  // 0: api.v1.model.MeshService.id:type_name -> google.protobuf.StringValue
	7,  // 1: api.v1.model.MeshService.mesh_id:type_name -> google.protobuf.StringValue
	7,  // 2: api.v1.model.MeshService.mesh_name:type_name -> google.protobuf.StringValue
	7,  // 3: api.v1.model.MeshService.mesh_token:type_name -> google.protobuf.StringValue
	7,  // 4: api.v1.model.MeshService.service_id:type_name -> google.protobuf.StringValue
	7,  // 5: api.v1.model.MeshService.service:type_name -> google.protobuf.StringValue
	7,  // 6: api.v1.model.MeshService.namespace:type_name -> google.protobuf.StringValue
	7,  // 7: api.v1.model.MeshService.department:type_name -> google.protobuf.StringValue
	7,  // 8: api.v1.model.MeshService.business:type_name -> google.protobuf.StringValue
	7,  // 9: api.v1.model.MeshService.owners:type_name -> google.protobuf.StringValue
	7,  // 10: api.v1.model.MeshService.mesh_namespace:type_name -> google.protobuf.StringValue
	7,  // 11: api.v1.model.MeshService.mesh_service:type_name -> google.protobuf.StringValue
	7,  // 12: api.v1.model.MeshService.location:type_name -> google.protobuf.StringValue
	7,  // 13: api.v1.model.MeshService.export_to:type_name -> google.protobuf.StringValue
	7,  // 14: api.v1.model.MeshService.revision:type_name -> google.protobuf.StringValue
	7,  // 15: api.v1.model.MeshService.ctime:type_name -> google.protobuf.StringValue
	7,  // 16: api.v1.model.MeshService.mtime:type_name -> google.protobuf.StringValue
	5,  // 17: api.v1.model.MeshService.attributes:type_name -> api.v1.model.MeshService.AttributesEntry
	1,  // 18: api.v1.model.MeshService.subsets:type_name -> api.v1.model.MeshServiceSubset
	7,  // 19: api.v1.model.MeshServiceSubset.name:type_name -> google.protobuf.StringValue
	6,  // 20: api.v1.model.MeshServiceSubset.labels:type_name -> api.v1.model.MeshServiceSubset.LabelsEntry
	7,  // 21: api.v1.model.Mesh.id:type_name -> google.protobuf.StringValue
	7,  // 22: api.v1.model.Mesh.name:type_name -> google.protobuf.StringValue
	7,  // 23: api.v1.model.Mesh.business:type_name -> google.protobuf.StringValue
	7,  // 24: api.v1.model.Mesh.department:type_name -> google.protobuf.StringValue
	7,  // 25: api.v1.model.Mesh.revision:type_name -> google.protobuf.StringValue
	7,  // 26: api.v1.model.Mesh.token:type_name -> google.protobuf.StringValue
	7,  // 27: api.v1.model.Mesh.owners:type_name -> google.protobuf.StringValue
	8,  // 28: api.v1.model.Mesh.managed:type_name -> google.protobuf.BoolValue
	7,  // 29: api.v1.model.Mesh.istio_version:type_name -> google.protobuf.StringValue
	7,  // 30: api.v1.model.Mesh.data_cluster:type_name -> google.protobuf.StringValue
	0,  // 31: api.v1.model.Mesh.services:type_name -> api.v1.model.MeshService
	7,  // 32: api.v1.model.Mesh.comment:type_name -> google.protobuf.StringValue
	7,  // 33: api.v1.model.Mesh.ctime:type_name -> google.protobuf.StringValue
	7,  // 34: api.v1.model.Mesh.mtime:type_name -> google.protobuf.StringValue
	7,  // 35: api.v1.model.MeshResource.id:type_name -> google.protobuf.StringValue
	7,  // 36: api.v1.model.MeshResource.mesh_id:type_name -> google.protobuf.StringValue
	7,  // 37: api.v1.model.MeshResource.mesh_name:type_name -> google.protobuf.StringValue
	7,  // 38: api.v1.model.MeshResource.mesh_namespace:type_name -> google.protobuf.StringValue
	7,  // 39: api.v1.model.MeshResource.type_url:type_name -> google.protobuf.StringValue
	7,  // 40: api.v1.model.MeshResource.name:type_name -> google.protobuf.StringValue
	7,  // 41: api.v1.model.MeshResource.revision:type_name -> google.protobuf.StringValue
	7,  // 42: api.v1.model.MeshResource.body:type_name -> google.protobuf.StringValue
	7,  // 43: api.v1.model.MeshResource.mesh_token:type_name -> google.protobuf.StringValue
	7,  // 44: api.v1.model.MeshResource.ctime:type_name -> google.protobuf.StringValue
	7,  // 45: api.v1.model.MeshResource.mtime:type_name -> google.protobuf.StringValue
	7,  // 46: api.v1.model.MeshConfig.mesh_id:type_name -> google.protobuf.StringValue
	7,  // 47: api.v1.model.MeshConfig.mesh_name:type_name -> google.protobuf.StringValue
	7,  // 48: api.v1.model.MeshConfig.type_url:type_name -> google.protobuf.StringValue
	3,  // 49: api.v1.model.MeshConfig.resources:type_name -> api.v1.model.MeshResource
	7,  // 50: api.v1.model.MeshConfig.revision:type_name -> google.protobuf.StringValue
	51, // [51:51] is the sub-list for method output_type
	51, // [51:51] is the sub-list for method input_type
	51, // [51:51] is the sub-list for extension type_name
	51, // [51:51] is the sub-list for extension extendee
	0,  // [0:51] is the sub-list for field type_name
}

func init() { file_v1_model_mesh_proto_init() }
func file_v1_model_mesh_proto_init() {
	if File_v1_model_mesh_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_v1_model_mesh_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MeshService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_mesh_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MeshServiceSubset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_mesh_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Mesh); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_mesh_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MeshResource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_mesh_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MeshConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_model_mesh_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v1_model_mesh_proto_goTypes,
		DependencyIndexes: file_v1_model_mesh_proto_depIdxs,
		MessageInfos:      file_v1_model_mesh_proto_msgTypes,
	}.Build()
	File_v1_model_mesh_proto = out.File
	file_v1_model_mesh_proto_rawDesc = nil
	file_v1_model_mesh_proto_goTypes = nil
	file_v1_model_mesh_proto_depIdxs = nil
}
