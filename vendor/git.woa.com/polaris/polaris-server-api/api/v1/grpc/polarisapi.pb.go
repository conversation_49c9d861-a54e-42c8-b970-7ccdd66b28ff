// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v1/grpc/polarisapi.proto

package grpc

import (
	context "context"
	model "git.woa.com/polaris/polaris-server-api/api/v1/model"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_v1_grpc_polarisapi_proto protoreflect.FileDescriptor

var file_v1_grpc_polarisapi_proto_rawDesc = []byte{
	0x0a, 0x18, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x76, 0x31, 0x1a, 0x15,
	0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x76,
	0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xb6,
	0x03, 0x0a, 0x0b, 0x50, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x47, 0x52, 0x50, 0x43, 0x12, 0x3e,
	0x0a, 0x0c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x14,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x1a, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x44,
	0x0a, 0x10, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x16, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x46, 0x0a, 0x12, 0x44, 0x65, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x16, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x1a, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4f, 0x0a, 0x08,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x49, 0x0a,
	0x05, 0x57, 0x61, 0x74, 0x63, 0x68, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x3d, 0x0a, 0x09, 0x48, 0x65, 0x61, 0x72,
	0x74, 0x62, 0x65, 0x61, 0x74, 0x12, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x16, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x34, 0x5a, 0x32, 0x67, 0x69, 0x74, 0x2e, 0x77,
	0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70,
	0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_v1_grpc_polarisapi_proto_goTypes = []interface{}{
	(*model.Client)(nil),           // 0: api.v1.model.Client
	(*model.Instance)(nil),         // 1: api.v1.model.Instance
	(*model.DiscoverRequest)(nil),  // 2: api.v1.model.DiscoverRequest
	(*model.WatchRequest)(nil),     // 3: api.v1.model.WatchRequest
	(*model.Response)(nil),         // 4: api.v1.model.Response
	(*model.DiscoverResponse)(nil), // 5: api.v1.model.DiscoverResponse
}
var file_v1_grpc_polarisapi_proto_depIdxs = []int32{
	0, // 0: v1.PolarisGRPC.ReportClient:input_type -> api.v1.model.Client
	1, // 1: v1.PolarisGRPC.RegisterInstance:input_type -> api.v1.model.Instance
	1, // 2: v1.PolarisGRPC.DeregisterInstance:input_type -> api.v1.model.Instance
	2, // 3: v1.PolarisGRPC.Discover:input_type -> api.v1.model.DiscoverRequest
	3, // 4: v1.PolarisGRPC.Watch:input_type -> api.v1.model.WatchRequest
	1, // 5: v1.PolarisGRPC.Heartbeat:input_type -> api.v1.model.Instance
	4, // 6: v1.PolarisGRPC.ReportClient:output_type -> api.v1.model.Response
	4, // 7: v1.PolarisGRPC.RegisterInstance:output_type -> api.v1.model.Response
	4, // 8: v1.PolarisGRPC.DeregisterInstance:output_type -> api.v1.model.Response
	5, // 9: v1.PolarisGRPC.Discover:output_type -> api.v1.model.DiscoverResponse
	5, // 10: v1.PolarisGRPC.Watch:output_type -> api.v1.model.DiscoverResponse
	4, // 11: v1.PolarisGRPC.Heartbeat:output_type -> api.v1.model.Response
	6, // [6:12] is the sub-list for method output_type
	0, // [0:6] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_v1_grpc_polarisapi_proto_init() }
func file_v1_grpc_polarisapi_proto_init() {
	if File_v1_grpc_polarisapi_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_grpc_polarisapi_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_v1_grpc_polarisapi_proto_goTypes,
		DependencyIndexes: file_v1_grpc_polarisapi_proto_depIdxs,
	}.Build()
	File_v1_grpc_polarisapi_proto = out.File
	file_v1_grpc_polarisapi_proto_rawDesc = nil
	file_v1_grpc_polarisapi_proto_goTypes = nil
	file_v1_grpc_polarisapi_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// PolarisGRPCClient is the client API for PolarisGRPC service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PolarisGRPCClient interface {
	// 客户端上报
	ReportClient(ctx context.Context, in *model.Client, opts ...grpc.CallOption) (*model.Response, error)
	// 被调方注册服务实例
	RegisterInstance(ctx context.Context, in *model.Instance, opts ...grpc.CallOption) (*model.Response, error)
	// 被调方反注册服务实例
	DeregisterInstance(ctx context.Context, in *model.Instance, opts ...grpc.CallOption) (*model.Response, error)
	// 统一发现接口
	Discover(ctx context.Context, opts ...grpc.CallOption) (PolarisGRPC_DiscoverClient, error)
	// 统一Watch接口
	Watch(ctx context.Context, opts ...grpc.CallOption) (PolarisGRPC_WatchClient, error)
	// 被调方上报心跳
	Heartbeat(ctx context.Context, in *model.Instance, opts ...grpc.CallOption) (*model.Response, error)
}

type polarisGRPCClient struct {
	cc grpc.ClientConnInterface
}

func NewPolarisGRPCClient(cc grpc.ClientConnInterface) PolarisGRPCClient {
	return &polarisGRPCClient{cc}
}

func (c *polarisGRPCClient) ReportClient(ctx context.Context, in *model.Client, opts ...grpc.CallOption) (*model.Response, error) {
	out := new(model.Response)
	err := c.cc.Invoke(ctx, "/v1.PolarisGRPC/ReportClient", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *polarisGRPCClient) RegisterInstance(ctx context.Context, in *model.Instance, opts ...grpc.CallOption) (*model.Response, error) {
	out := new(model.Response)
	err := c.cc.Invoke(ctx, "/v1.PolarisGRPC/RegisterInstance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *polarisGRPCClient) DeregisterInstance(ctx context.Context, in *model.Instance, opts ...grpc.CallOption) (*model.Response, error) {
	out := new(model.Response)
	err := c.cc.Invoke(ctx, "/v1.PolarisGRPC/DeregisterInstance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *polarisGRPCClient) Discover(ctx context.Context, opts ...grpc.CallOption) (PolarisGRPC_DiscoverClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PolarisGRPC_serviceDesc.Streams[0], "/v1.PolarisGRPC/Discover", opts...)
	if err != nil {
		return nil, err
	}
	x := &polarisGRPCDiscoverClient{stream}
	return x, nil
}

type PolarisGRPC_DiscoverClient interface {
	Send(*model.DiscoverRequest) error
	Recv() (*model.DiscoverResponse, error)
	grpc.ClientStream
}

type polarisGRPCDiscoverClient struct {
	grpc.ClientStream
}

func (x *polarisGRPCDiscoverClient) Send(m *model.DiscoverRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *polarisGRPCDiscoverClient) Recv() (*model.DiscoverResponse, error) {
	m := new(model.DiscoverResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *polarisGRPCClient) Watch(ctx context.Context, opts ...grpc.CallOption) (PolarisGRPC_WatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PolarisGRPC_serviceDesc.Streams[1], "/v1.PolarisGRPC/Watch", opts...)
	if err != nil {
		return nil, err
	}
	x := &polarisGRPCWatchClient{stream}
	return x, nil
}

type PolarisGRPC_WatchClient interface {
	Send(*model.WatchRequest) error
	Recv() (*model.DiscoverResponse, error)
	grpc.ClientStream
}

type polarisGRPCWatchClient struct {
	grpc.ClientStream
}

func (x *polarisGRPCWatchClient) Send(m *model.WatchRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *polarisGRPCWatchClient) Recv() (*model.DiscoverResponse, error) {
	m := new(model.DiscoverResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *polarisGRPCClient) Heartbeat(ctx context.Context, in *model.Instance, opts ...grpc.CallOption) (*model.Response, error) {
	out := new(model.Response)
	err := c.cc.Invoke(ctx, "/v1.PolarisGRPC/Heartbeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PolarisGRPCServer is the server API for PolarisGRPC service.
type PolarisGRPCServer interface {
	// 客户端上报
	ReportClient(context.Context, *model.Client) (*model.Response, error)
	// 被调方注册服务实例
	RegisterInstance(context.Context, *model.Instance) (*model.Response, error)
	// 被调方反注册服务实例
	DeregisterInstance(context.Context, *model.Instance) (*model.Response, error)
	// 统一发现接口
	Discover(PolarisGRPC_DiscoverServer) error
	// 统一Watch接口
	Watch(PolarisGRPC_WatchServer) error
	// 被调方上报心跳
	Heartbeat(context.Context, *model.Instance) (*model.Response, error)
}

// UnimplementedPolarisGRPCServer can be embedded to have forward compatible implementations.
type UnimplementedPolarisGRPCServer struct {
}

func (*UnimplementedPolarisGRPCServer) ReportClient(context.Context, *model.Client) (*model.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportClient not implemented")
}
func (*UnimplementedPolarisGRPCServer) RegisterInstance(context.Context, *model.Instance) (*model.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterInstance not implemented")
}
func (*UnimplementedPolarisGRPCServer) DeregisterInstance(context.Context, *model.Instance) (*model.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeregisterInstance not implemented")
}
func (*UnimplementedPolarisGRPCServer) Discover(PolarisGRPC_DiscoverServer) error {
	return status.Errorf(codes.Unimplemented, "method Discover not implemented")
}
func (*UnimplementedPolarisGRPCServer) Watch(PolarisGRPC_WatchServer) error {
	return status.Errorf(codes.Unimplemented, "method Watch not implemented")
}
func (*UnimplementedPolarisGRPCServer) Heartbeat(context.Context, *model.Instance) (*model.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Heartbeat not implemented")
}

func RegisterPolarisGRPCServer(s *grpc.Server, srv PolarisGRPCServer) {
	s.RegisterService(&_PolarisGRPC_serviceDesc, srv)
}

func _PolarisGRPC_ReportClient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(model.Client)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PolarisGRPCServer).ReportClient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/v1.PolarisGRPC/ReportClient",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PolarisGRPCServer).ReportClient(ctx, req.(*model.Client))
	}
	return interceptor(ctx, in, info, handler)
}

func _PolarisGRPC_RegisterInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(model.Instance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PolarisGRPCServer).RegisterInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/v1.PolarisGRPC/RegisterInstance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PolarisGRPCServer).RegisterInstance(ctx, req.(*model.Instance))
	}
	return interceptor(ctx, in, info, handler)
}

func _PolarisGRPC_DeregisterInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(model.Instance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PolarisGRPCServer).DeregisterInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/v1.PolarisGRPC/DeregisterInstance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PolarisGRPCServer).DeregisterInstance(ctx, req.(*model.Instance))
	}
	return interceptor(ctx, in, info, handler)
}

func _PolarisGRPC_Discover_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(PolarisGRPCServer).Discover(&polarisGRPCDiscoverServer{stream})
}

type PolarisGRPC_DiscoverServer interface {
	Send(*model.DiscoverResponse) error
	Recv() (*model.DiscoverRequest, error)
	grpc.ServerStream
}

type polarisGRPCDiscoverServer struct {
	grpc.ServerStream
}

func (x *polarisGRPCDiscoverServer) Send(m *model.DiscoverResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *polarisGRPCDiscoverServer) Recv() (*model.DiscoverRequest, error) {
	m := new(model.DiscoverRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _PolarisGRPC_Watch_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(PolarisGRPCServer).Watch(&polarisGRPCWatchServer{stream})
}

type PolarisGRPC_WatchServer interface {
	Send(*model.DiscoverResponse) error
	Recv() (*model.WatchRequest, error)
	grpc.ServerStream
}

type polarisGRPCWatchServer struct {
	grpc.ServerStream
}

func (x *polarisGRPCWatchServer) Send(m *model.DiscoverResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *polarisGRPCWatchServer) Recv() (*model.WatchRequest, error) {
	m := new(model.WatchRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _PolarisGRPC_Heartbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(model.Instance)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PolarisGRPCServer).Heartbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/v1.PolarisGRPC/Heartbeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PolarisGRPCServer).Heartbeat(ctx, req.(*model.Instance))
	}
	return interceptor(ctx, in, info, handler)
}

var _PolarisGRPC_serviceDesc = grpc.ServiceDesc{
	ServiceName: "v1.PolarisGRPC",
	HandlerType: (*PolarisGRPCServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ReportClient",
			Handler:    _PolarisGRPC_ReportClient_Handler,
		},
		{
			MethodName: "RegisterInstance",
			Handler:    _PolarisGRPC_RegisterInstance_Handler,
		},
		{
			MethodName: "DeregisterInstance",
			Handler:    _PolarisGRPC_DeregisterInstance_Handler,
		},
		{
			MethodName: "Heartbeat",
			Handler:    _PolarisGRPC_Heartbeat_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Discover",
			Handler:       _PolarisGRPC_Discover_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "Watch",
			Handler:       _PolarisGRPC_Watch_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "v1/grpc/polarisapi.proto",
}
