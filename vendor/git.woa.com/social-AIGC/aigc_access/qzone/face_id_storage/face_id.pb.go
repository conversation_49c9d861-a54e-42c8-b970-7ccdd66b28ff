// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v3.6.1
// source: face_id.proto

package faceIdStorage

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Faces struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// qq号
	Uin int64 `protobuf:"varint,1,opt,name=uin,proto3" json:"uin,omitempty"`
	// key:原图链接, value: faceInfos
	FaceMap map[string]*FaceList `protobuf:"bytes,2,rep,name=face_map,json=faceMap,proto3" json:"face_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 最新一张图的unix, 在该unix时间之后的图片都未检测过
	LastUnix int64 `protobuf:"varint,3,opt,name=last_unix,json=lastUnix,proto3" json:"last_unix,omitempty"`
	// 标签
	Tags map[string]*TagList `protobuf:"bytes,4,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 人物信息， 按faceID排序
	Characters   []*CharacterInfo `protobuf:"bytes,5,rep,name=characters,proto3" json:"characters,omitempty"`
	DetetedCount int32            `protobuf:"varint,6,opt,name=deteted_count,json=detetedCount,proto3" json:"deteted_count,omitempty"`
}

func (x *Faces) Reset() {
	*x = Faces{}
	mi := &file_face_id_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Faces) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Faces) ProtoMessage() {}

func (x *Faces) ProtoReflect() protoreflect.Message {
	mi := &file_face_id_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Faces.ProtoReflect.Descriptor instead.
func (*Faces) Descriptor() ([]byte, []int) {
	return file_face_id_proto_rawDescGZIP(), []int{0}
}

func (x *Faces) GetUin() int64 {
	if x != nil {
		return x.Uin
	}
	return 0
}

func (x *Faces) GetFaceMap() map[string]*FaceList {
	if x != nil {
		return x.FaceMap
	}
	return nil
}

func (x *Faces) GetLastUnix() int64 {
	if x != nil {
		return x.LastUnix
	}
	return 0
}

func (x *Faces) GetTags() map[string]*TagList {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Faces) GetCharacters() []*CharacterInfo {
	if x != nil {
		return x.Characters
	}
	return nil
}

func (x *Faces) GetDetetedCount() int32 {
	if x != nil {
		return x.DetetedCount
	}
	return 0
}

type CharacterInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// faceID平均年龄
	Age float32 `protobuf:"fixed32,1,opt,name=age,proto3" json:"age,omitempty"`
	// faceID性别
	Gender int32 `protobuf:"varint,2,opt,name=gender,proto3" json:"gender,omitempty"`
}

func (x *CharacterInfo) Reset() {
	*x = CharacterInfo{}
	mi := &file_face_id_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CharacterInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CharacterInfo) ProtoMessage() {}

func (x *CharacterInfo) ProtoReflect() protoreflect.Message {
	mi := &file_face_id_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CharacterInfo.ProtoReflect.Descriptor instead.
func (*CharacterInfo) Descriptor() ([]byte, []int) {
	return file_face_id_proto_rawDescGZIP(), []int{1}
}

func (x *CharacterInfo) GetAge() float32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *CharacterInfo) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

type FaceList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FaceInfos []*FaceInfo `protobuf:"bytes,1,rep,name=face_infos,json=faceInfos,proto3" json:"face_infos,omitempty"`
	Width     int32       `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`
	Height    int32       `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	Undetect  bool        `protobuf:"varint,4,opt,name=undetect,proto3" json:"undetect,omitempty"`
}

func (x *FaceList) Reset() {
	*x = FaceList{}
	mi := &file_face_id_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceList) ProtoMessage() {}

func (x *FaceList) ProtoReflect() protoreflect.Message {
	mi := &file_face_id_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceList.ProtoReflect.Descriptor instead.
func (*FaceList) Descriptor() ([]byte, []int) {
	return file_face_id_proto_rawDescGZIP(), []int{2}
}

func (x *FaceList) GetFaceInfos() []*FaceInfo {
	if x != nil {
		return x.FaceInfos
	}
	return nil
}

func (x *FaceList) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *FaceList) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *FaceList) GetUndetect() bool {
	if x != nil {
		return x.Undetect
	}
	return false
}

type TagList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tags   map[string]float32 `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	Tagged bool               `protobuf:"varint,2,opt,name=tagged,proto3" json:"tagged,omitempty"`
}

func (x *TagList) Reset() {
	*x = TagList{}
	mi := &file_face_id_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TagList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagList) ProtoMessage() {}

func (x *TagList) ProtoReflect() protoreflect.Message {
	mi := &file_face_id_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagList.ProtoReflect.Descriptor instead.
func (*TagList) Descriptor() ([]byte, []int) {
	return file_face_id_proto_rawDescGZIP(), []int{3}
}

func (x *TagList) GetTags() map[string]float32 {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *TagList) GetTagged() bool {
	if x != nil {
		return x.Tagged
	}
	return false
}

type FaceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 第几张人脸
	Index int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	// 512维度 features
	Embeddings []float32 `protobuf:"fixed32,2,rep,packed,name=embeddings,proto3" json:"embeddings,omitempty"`
	// 人脸坐标，左上点(x1, y1), 右下点(x2, y2)
	Boxes []float32 `protobuf:"fixed32,3,rep,packed,name=boxes,proto3" json:"boxes,omitempty"`
	// extra info 如果有的话
	Extra string `protobuf:"bytes,4,opt,name=extra,proto3" json:"extra,omitempty"`
	// 主人脸标识
	IsMaster bool `protobuf:"varint,5,opt,name=is_master,json=isMaster,proto3" json:"is_master,omitempty"`
	// 聚类ID
	ClusterId int32 `protobuf:"varint,6,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// 年龄
	Age int32 `protobuf:"varint,7,opt,name=age,proto3" json:"age,omitempty"`
	// 性别 1 - 女 100 - 男
	Gender int32 `protobuf:"varint,8,opt,name=gender,proto3" json:"gender,omitempty"`
}

func (x *FaceInfo) Reset() {
	*x = FaceInfo{}
	mi := &file_face_id_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceInfo) ProtoMessage() {}

func (x *FaceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_face_id_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceInfo.ProtoReflect.Descriptor instead.
func (*FaceInfo) Descriptor() ([]byte, []int) {
	return file_face_id_proto_rawDescGZIP(), []int{4}
}

func (x *FaceInfo) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *FaceInfo) GetEmbeddings() []float32 {
	if x != nil {
		return x.Embeddings
	}
	return nil
}

func (x *FaceInfo) GetBoxes() []float32 {
	if x != nil {
		return x.Boxes
	}
	return nil
}

func (x *FaceInfo) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *FaceInfo) GetIsMaster() bool {
	if x != nil {
		return x.IsMaster
	}
	return false
}

func (x *FaceInfo) GetClusterId() int32 {
	if x != nil {
		return x.ClusterId
	}
	return 0
}

func (x *FaceInfo) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *FaceInfo) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

var File_face_id_proto protoreflect.FileDescriptor

var file_face_id_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xeb, 0x02, 0x0a, 0x05, 0x46, 0x61, 0x63, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x75, 0x69, 0x6e, 0x12, 0x2e, 0x0a, 0x08, 0x66,
	0x61, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x46, 0x61, 0x63, 0x65, 0x73, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x07, 0x66, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x6c, 0x61, 0x73, 0x74, 0x55, 0x6e, 0x69, 0x78, 0x12, 0x24, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x73, 0x2e, 0x54,
	0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2e,
	0x0a, 0x0a, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73, 0x12, 0x23,
	0x0a, 0x0d, 0x64, 0x65, 0x74, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x64, 0x65, 0x74, 0x65, 0x74, 0x65, 0x64, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x1a, 0x45, 0x0a, 0x0c, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x09, 0x54, 0x61,
	0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x1e, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x54, 0x61, 0x67, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x39, 0x0a,
	0x0d, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10,
	0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x61, 0x67, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x22, 0x7e, 0x0a, 0x08, 0x46, 0x61, 0x63, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0a, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x09, 0x66, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x6e, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x75, 0x6e, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x22, 0x82, 0x01, 0x0a, 0x07, 0x54, 0x61, 0x67,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x54, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x2e, 0x54, 0x61, 0x67,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x74, 0x61, 0x67, 0x67, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x74, 0x61,
	0x67, 0x67, 0x65, 0x64, 0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xd2, 0x01,
	0x0a, 0x08, 0x46, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x02, 0x52, 0x0a, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x62, 0x6f, 0x78, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x02, 0x52,
	0x05, 0x62, 0x6f, 0x78, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x73, 0x5f, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x69, 0x73, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x42, 0x11, 0x5a, 0x0f, 0x2e, 0x2f, 0x66, 0x61, 0x63, 0x65, 0x49, 0x64, 0x53, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_face_id_proto_rawDescOnce sync.Once
	file_face_id_proto_rawDescData = file_face_id_proto_rawDesc
)

func file_face_id_proto_rawDescGZIP() []byte {
	file_face_id_proto_rawDescOnce.Do(func() {
		file_face_id_proto_rawDescData = protoimpl.X.CompressGZIP(file_face_id_proto_rawDescData)
	})
	return file_face_id_proto_rawDescData
}

var file_face_id_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_face_id_proto_goTypes = []any{
	(*Faces)(nil),         // 0: Faces
	(*CharacterInfo)(nil), // 1: CharacterInfo
	(*FaceList)(nil),      // 2: FaceList
	(*TagList)(nil),       // 3: TagList
	(*FaceInfo)(nil),      // 4: FaceInfo
	nil,                   // 5: Faces.FaceMapEntry
	nil,                   // 6: Faces.TagsEntry
	nil,                   // 7: TagList.TagsEntry
}
var file_face_id_proto_depIdxs = []int32{
	5, // 0: Faces.face_map:type_name -> Faces.FaceMapEntry
	6, // 1: Faces.tags:type_name -> Faces.TagsEntry
	1, // 2: Faces.characters:type_name -> CharacterInfo
	4, // 3: FaceList.face_infos:type_name -> FaceInfo
	7, // 4: TagList.tags:type_name -> TagList.TagsEntry
	2, // 5: Faces.FaceMapEntry.value:type_name -> FaceList
	3, // 6: Faces.TagsEntry.value:type_name -> TagList
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_face_id_proto_init() }
func file_face_id_proto_init() {
	if File_face_id_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_face_id_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_face_id_proto_goTypes,
		DependencyIndexes: file_face_id_proto_depIdxs,
		MessageInfos:      file_face_id_proto_msgTypes,
	}.Build()
	File_face_id_proto = out.File
	file_face_id_proto_rawDesc = nil
	file_face_id_proto_goTypes = nil
	file_face_id_proto_depIdxs = nil
}
