// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: monitor/polaris/v1/signature.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MonitorSignature struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string `protobuf:"bytes,1,opt,name=app_id,proto3" json:"app_id,omitempty"`
	Id        string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`                // 请求唯一标识
	Signature string `protobuf:"bytes,3,opt,name=signature,proto3" json:"signature,omitempty"`  // 签名
	Timestamp int64  `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 当前时间戳
}

func (x *MonitorSignature) Reset() {
	*x = MonitorSignature{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_signature_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonitorSignature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorSignature) ProtoMessage() {}

func (x *MonitorSignature) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_signature_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorSignature.ProtoReflect.Descriptor instead.
func (*MonitorSignature) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_signature_proto_rawDescGZIP(), []int{0}
}

func (x *MonitorSignature) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *MonitorSignature) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MonitorSignature) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *MonitorSignature) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

var File_monitor_polaris_v1_signature_proto protoreflect.FileDescriptor

var file_monitor_polaris_v1_signature_proto_rawDesc = []byte{
	0x0a, 0x22, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x76, 0x31, 0x22, 0x76, 0x0a, 0x10, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70,
	0x70, 0x5f, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x42, 0x3f, 0x5a, 0x3d, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_monitor_polaris_v1_signature_proto_rawDescOnce sync.Once
	file_monitor_polaris_v1_signature_proto_rawDescData = file_monitor_polaris_v1_signature_proto_rawDesc
)

func file_monitor_polaris_v1_signature_proto_rawDescGZIP() []byte {
	file_monitor_polaris_v1_signature_proto_rawDescOnce.Do(func() {
		file_monitor_polaris_v1_signature_proto_rawDescData = protoimpl.X.CompressGZIP(file_monitor_polaris_v1_signature_proto_rawDescData)
	})
	return file_monitor_polaris_v1_signature_proto_rawDescData
}

var file_monitor_polaris_v1_signature_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_monitor_polaris_v1_signature_proto_goTypes = []interface{}{
	(*MonitorSignature)(nil), // 0: v1.MonitorSignature
}
var file_monitor_polaris_v1_signature_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_monitor_polaris_v1_signature_proto_init() }
func file_monitor_polaris_v1_signature_proto_init() {
	if File_monitor_polaris_v1_signature_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_monitor_polaris_v1_signature_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonitorSignature); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_monitor_polaris_v1_signature_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_monitor_polaris_v1_signature_proto_goTypes,
		DependencyIndexes: file_monitor_polaris_v1_signature_proto_depIdxs,
		MessageInfos:      file_monitor_polaris_v1_signature_proto_msgTypes,
	}.Build()
	File_monitor_polaris_v1_signature_proto = out.File
	file_monitor_polaris_v1_signature_proto_rawDesc = nil
	file_monitor_polaris_v1_signature_proto_goTypes = nil
	file_monitor_polaris_v1_signature_proto_depIdxs = nil
}
