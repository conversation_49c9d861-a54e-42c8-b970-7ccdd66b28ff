module git.woa.com/fanniliu/output_error_qq_photos

go 1.23.0

toolchain go1.24.0

replace git.woa.com/trpcprotocol/zy_socail/qq_photos => ./stub/git.woa.com/trpcprotocol/zy_socail/qq_photos

require (
	git.code.oa.com/trpc-go/trpc-filter/debuglog v0.1.13
	git.code.oa.com/trpc-go/trpc-filter/recovery v0.1.5
	git.code.oa.com/trpc-go/trpc-go v0.20.0
	git.woa.com/QzonePlatform/qzone-backend v0.0.0-20241017113531-166fdca5b959
	git.woa.com/trpcprotocol/qzone/storage_photo v1.1.6
	git.woa.com/trpcprotocol/zy_socail/qq_photos v0.0.0-00010101000000-000000000000
	github.com/nfnt/resize v0.0.0-20180221191011-83c6a9932646
	gocv.io/x/gocv v0.41.0
)

require (
	git.code.oa.com/PCG-MQQ-QQService-DevGroup2/SSO-Proj/sso_protos v0.1.13 // indirect
	git.code.oa.com/QzonePlatform/QzoneProtocol v0.0.0-20250416023433-22bbdb0c1134 // indirect
	git.code.oa.com/polaris/polaris-go v0.12.12 // indirect
	git.code.oa.com/trpc-go/trpc-codec/oidb v0.3.3 // indirect
	git.code.oa.com/trpc-go/trpc-codec/qzh v0.1.6 // indirect
	git.code.oa.com/trpc-go/trpc-metrics-runtime v0.4.0 // indirect
	git.code.oa.com/trpcprotocol/oicq/oidb_transporter v1.1.3 // indirect
	git.woa.com/polaris/polaris-server-api/api/metric v1.0.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/monitor v1.0.7 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/grpc v1.0.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/model v1.1.4 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/grpc v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/model v1.0.4 // indirect
	git.woa.com/qqlogin/authentication/auth-sdk-go v1.0.5 // indirect
	github.com/creasty/defaults v1.7.0 // indirect
	github.com/forgoer/openssl v0.0.0-20210828150411-6c5378b5b719 // indirect
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/ghodss/yaml v1.0.1-0.20190212211648-25d852aebe32 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.16.0 // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/leodido/go-urn v1.2.4 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/natefinch/lumberjack v2.0.0+incompatible // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	golang.org/x/crypto v0.27.0 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240415180920-8c6c420018be // indirect
	google.golang.org/grpc v1.63.2 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
)

require (
	git.code.oa.com/trpc-go/trpc-naming-polaris v0.5.24
	git.woa.com/QzonePlatform/intimate-space/backend v0.0.0-20250507031006-4693db22329e
	git.woa.com/jce/jce v1.2.0 // indirect
	git.woa.com/social-AIGC/aigc_access v0.0.0-20250509102612-4eb87e17e2c5
	git.woa.com/trpc-go/go_reuseport v1.7.0 // indirect
	git.woa.com/trpc-go/tnet v0.1.0
	github.com/BurntSushi/toml v1.4.0 // indirect
	github.com/andybalholm/brotli v1.1.0 // indirect
	github.com/corona10/goimagehash v1.1.0
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/go-playground/form/v4 v4.2.1 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/flatbuffers v24.3.25+incompatible // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/jinzhu/copier v0.4.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/compress v1.17.10 // indirect
	github.com/lestrrat-go/strftime v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/panjf2000/ants/v2 v2.10.0 // indirect
	github.com/pierrec/lz4/v4 v4.1.21 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/r3labs/sse/v2 v2.10.0 // indirect
	github.com/spf13/cast v1.7.0 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.56.0 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/automaxprocs v1.6.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.27.0 // indirect
	golang.org/x/net v0.29.0 // indirect
	golang.org/x/sync v0.14.0 // indirect
	golang.org/x/sys v0.25.0 // indirect
	golang.org/x/text v0.25.0
	google.golang.org/protobuf v1.36.6 // indirect
	gopkg.in/cenkalti/backoff.v1 v1.1.0 // indirect
	gopkg.in/yaml.v3 v3.0.1
)
