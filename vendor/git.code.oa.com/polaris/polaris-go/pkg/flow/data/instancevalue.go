/**
 * <PERSON><PERSON> is pleased to support the open source community by making CL5 available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */

package data

import (
	"git.code.oa.com/polaris/polaris-go/pkg/metric"
	"git.code.oa.com/polaris/polaris-go/pkg/model"
	"git.code.oa.com/polaris/polaris-go/pkg/model/local"
	"git.code.oa.com/polaris/polaris-go/pkg/model/pb"
	"git.code.oa.com/polaris/polaris-go/pkg/plugin/localregistry"
	"github.com/modern-go/reflect2"
)

//获取一个服务实例的local value
func getInstanceLocalValue(registry localregistry.LocalRegistry, instance model.Instance) local.InstanceLocalValue {
	if reflect2.IsNil(instance) {
		return nil
	}
	pbInst, ok := instance.(*pb.InstanceInProto)
	if ok {
		return pbInst.GetInstanceLocalValue()
	}
	if reflect2.IsNil(registry) {
		return nil
	}
	return registry.GetInstanceLocalValue(instance)
}

// 获取一个服务实例的熔断slicewindow
func GetInstanceSliceWindow(id int32, registry localregistry.LocalRegistry,
	instance model.Instance) []*metric.SliceWindow {
	localValue := getInstanceLocalValue(registry, instance)
	if localValue == nil {
		return nil
	}
	return localValue.GetSliceWindows(id)
}

// 获取一个服务实例的熔断状态
func GetInstanceCircuitBreakerStatus(registry localregistry.LocalRegistry,
	instance model.Instance) model.CircuitBreakerStatus {
	localValue := getInstanceLocalValue(registry, instance)
	if localValue == nil {
		return nil
	}
	return localValue.GetCircuitBreakerStatus()
}

//获取一个服务实例的探活状态
func GetInstanceOutlierDetectorStatus(registry localregistry.LocalRegistry,
	instance model.Instance) model.OutlierDetectorStatus {
	localValue := getInstanceLocalValue(registry, instance)
	if localValue == nil {
		return nil
	}
	return localValue.GetOutlierDetectorStatus()
}

//将服务实例的探活状态改成detecting
func InstanceToDetecting(registry localregistry.LocalRegistry, instance model.Instance) bool {
	localValue := getInstanceLocalValue(registry, instance)
	if localValue == nil {
		return false
	}
	return localValue.ToDetecting()
}

//将服务实例的探活状态改成undetecting
func InstanceUnDetecting(registry localregistry.LocalRegistry, instance model.Instance) bool {
	localValue := getInstanceLocalValue(registry, instance)
	if localValue == nil {
		return false
	}
	return localValue.UnDetecting()
}

//获取服务实例的ExtendedData
func GetInstanceExtendedData(pluginIndex int32, registry localregistry.LocalRegistry,
	instance model.Instance) interface{} {
	localValue := getInstanceLocalValue(registry, instance)
	if localValue == nil {
		return nil
	}
	return localValue.GetExtendedData(pluginIndex)
}

//设置服务实例的ExtendedData
func SetInstanceExtendedData(pluginIndex int32, data interface{}, registry localregistry.LocalRegistry,
	instance model.Instance) {
	localValue := getInstanceLocalValue(registry, instance)
	if localValue == nil {
		return
	}
	localValue.SetExtendedData(pluginIndex, data)
}

//获取服务实例的动态权重
func GetInstanceDynamicWeight(registry localregistry.LocalRegistry, instance model.Instance) int {
	localValue := getInstanceLocalValue(registry, instance)
	if localValue == nil {
		return 0
	}
	return localValue.GetDynamicWeight()
}
