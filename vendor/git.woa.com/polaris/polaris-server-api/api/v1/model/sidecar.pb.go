// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v1/model/sidecar.proto

package polarismodel_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// polaris 获取单个实例请求
type GetOneInstanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Namespace   *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	ServiceName *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
}

func (x *GetOneInstanceRequest) Reset() {
	*x = GetOneInstanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_sidecar_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOneInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOneInstanceRequest) ProtoMessage() {}

func (x *GetOneInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_sidecar_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOneInstanceRequest.ProtoReflect.Descriptor instead.
func (*GetOneInstanceRequest) Descriptor() ([]byte, []int) {
	return file_v1_model_sidecar_proto_rawDescGZIP(), []int{0}
}

func (x *GetOneInstanceRequest) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *GetOneInstanceRequest) GetServiceName() *wrapperspb.StringValue {
	if x != nil {
		return x.ServiceName
	}
	return nil
}

// polaris 获取单个实例返回
type GetOneInstanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Instance *Instance `protobuf:"bytes,1,opt,name=instance,proto3" json:"instance,omitempty"`
}

func (x *GetOneInstanceResponse) Reset() {
	*x = GetOneInstanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_sidecar_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOneInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOneInstanceResponse) ProtoMessage() {}

func (x *GetOneInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_sidecar_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOneInstanceResponse.ProtoReflect.Descriptor instead.
func (*GetOneInstanceResponse) Descriptor() ([]byte, []int) {
	return file_v1_model_sidecar_proto_rawDescGZIP(), []int{1}
}

func (x *GetOneInstanceResponse) GetInstance() *Instance {
	if x != nil {
		return x.Instance
	}
	return nil
}

// 用于用户调用结果上报
type ServiceCallResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Instance *Instance `protobuf:"bytes,1,opt,name=instance,proto3" json:"instance,omitempty"`
	//必选，本地服务调用的状态，正常or异常
	RetStatus int32 `protobuf:"varint,2,opt,name=ret_status,json=retStatus,proto3" json:"ret_status,omitempty"`
	//必选，本地服务调用的返回码
	RetCode int32 `protobuf:"varint,3,opt,name=ret_code,json=retCode,proto3" json:"ret_code,omitempty"`
	//必选，被调服务实例获取接口的最大时延
	TimeDelay int64 `protobuf:"varint,4,opt,name=time_delay,json=timeDelay,proto3" json:"time_delay,omitempty"`
}

func (x *ServiceCallResult) Reset() {
	*x = ServiceCallResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_sidecar_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceCallResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCallResult) ProtoMessage() {}

func (x *ServiceCallResult) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_sidecar_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCallResult.ProtoReflect.Descriptor instead.
func (*ServiceCallResult) Descriptor() ([]byte, []int) {
	return file_v1_model_sidecar_proto_rawDescGZIP(), []int{2}
}

func (x *ServiceCallResult) GetInstance() *Instance {
	if x != nil {
		return x.Instance
	}
	return nil
}

func (x *ServiceCallResult) GetRetStatus() int32 {
	if x != nil {
		return x.RetStatus
	}
	return 0
}

func (x *ServiceCallResult) GetRetCode() int32 {
	if x != nil {
		return x.RetCode
	}
	return 0
}

func (x *ServiceCallResult) GetTimeDelay() int64 {
	if x != nil {
		return x.TimeDelay
	}
	return 0
}

// 批量发送用户调用结果
type BatchServiceCallResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CallResults []*ServiceCallResult `protobuf:"bytes,1,rep,name=call_results,json=callResults,proto3" json:"call_results,omitempty"`
}

func (x *BatchServiceCallResult) Reset() {
	*x = BatchServiceCallResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_sidecar_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchServiceCallResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchServiceCallResult) ProtoMessage() {}

func (x *BatchServiceCallResult) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_sidecar_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchServiceCallResult.ProtoReflect.Descriptor instead.
func (*BatchServiceCallResult) Descriptor() ([]byte, []int) {
	return file_v1_model_sidecar_proto_rawDescGZIP(), []int{3}
}

func (x *BatchServiceCallResult) GetCallResults() []*ServiceCallResult {
	if x != nil {
		return x.CallResults
	}
	return nil
}

// 注册服务
type RegisterInstanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Instance *Instance `protobuf:"bytes,1,opt,name=instance,proto3" json:"instance,omitempty"`
}

func (x *RegisterInstanceRequest) Reset() {
	*x = RegisterInstanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_sidecar_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterInstanceRequest) ProtoMessage() {}

func (x *RegisterInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_sidecar_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterInstanceRequest.ProtoReflect.Descriptor instead.
func (*RegisterInstanceRequest) Descriptor() ([]byte, []int) {
	return file_v1_model_sidecar_proto_rawDescGZIP(), []int{4}
}

func (x *RegisterInstanceRequest) GetInstance() *Instance {
	if x != nil {
		return x.Instance
	}
	return nil
}

// 反注册服务
type DeregisterInstanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Instance *Instance `protobuf:"bytes,1,opt,name=instance,proto3" json:"instance,omitempty"`
}

func (x *DeregisterInstanceRequest) Reset() {
	*x = DeregisterInstanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_sidecar_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeregisterInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeregisterInstanceRequest) ProtoMessage() {}

func (x *DeregisterInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_sidecar_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeregisterInstanceRequest.ProtoReflect.Descriptor instead.
func (*DeregisterInstanceRequest) Descriptor() ([]byte, []int) {
	return file_v1_model_sidecar_proto_rawDescGZIP(), []int{5}
}

func (x *DeregisterInstanceRequest) GetInstance() *Instance {
	if x != nil {
		return x.Instance
	}
	return nil
}

// 心跳
type InstanceHeartbeatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Instance *Instance `protobuf:"bytes,1,opt,name=instance,proto3" json:"instance,omitempty"`
}

func (x *InstanceHeartbeatRequest) Reset() {
	*x = InstanceHeartbeatRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_sidecar_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstanceHeartbeatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstanceHeartbeatRequest) ProtoMessage() {}

func (x *InstanceHeartbeatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_sidecar_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstanceHeartbeatRequest.ProtoReflect.Descriptor instead.
func (*InstanceHeartbeatRequest) Descriptor() ([]byte, []int) {
	return file_v1_model_sidecar_proto_rawDescGZIP(), []int{6}
}

func (x *InstanceHeartbeatRequest) GetInstance() *Instance {
	if x != nil {
		return x.Instance
	}
	return nil
}

// 详细的错误信息
type DetailErrInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrCode uint32 `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg  string `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
}

func (x *DetailErrInfo) Reset() {
	*x = DetailErrInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_sidecar_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailErrInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailErrInfo) ProtoMessage() {}

func (x *DetailErrInfo) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_sidecar_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailErrInfo.ProtoReflect.Descriptor instead.
func (*DetailErrInfo) Descriptor() ([]byte, []int) {
	return file_v1_model_sidecar_proto_rawDescGZIP(), []int{7}
}

func (x *DetailErrInfo) GetErrCode() uint32 {
	if x != nil {
		return x.ErrCode
	}
	return 0
}

func (x *DetailErrInfo) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

var File_v1_model_sidecar_proto protoreflect.FileDescriptor

var file_v1_model_sidecar_proto_rawDesc = []byte{
	0x0a, 0x16, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x73, 0x69, 0x64, 0x65, 0x63,
	0x61, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x94,
	0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x4c, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x32, 0x0a, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x22, 0xa0, 0x01, 0x0a, 0x11, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x32, 0x0a, 0x08, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x72, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08,
	0x72, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x72, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x22, 0x5c, 0x0a, 0x16, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x42, 0x0a, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x6c,
	0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x22, 0x4d, 0x0a, 0x17, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x32, 0x0a, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x22, 0x4f, 0x0a, 0x19, 0x44, 0x65, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x32, 0x0a, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x22, 0x4e, 0x0a, 0x18, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x32, 0x0a, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x22, 0x43, 0x0a, 0x0d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x45, 0x72,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x72, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74,
	0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73,
	0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x3b, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v1_model_sidecar_proto_rawDescOnce sync.Once
	file_v1_model_sidecar_proto_rawDescData = file_v1_model_sidecar_proto_rawDesc
)

func file_v1_model_sidecar_proto_rawDescGZIP() []byte {
	file_v1_model_sidecar_proto_rawDescOnce.Do(func() {
		file_v1_model_sidecar_proto_rawDescData = protoimpl.X.CompressGZIP(file_v1_model_sidecar_proto_rawDescData)
	})
	return file_v1_model_sidecar_proto_rawDescData
}

var file_v1_model_sidecar_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_v1_model_sidecar_proto_goTypes = []interface{}{
	(*GetOneInstanceRequest)(nil),     // 0: api.v1.model.GetOneInstanceRequest
	(*GetOneInstanceResponse)(nil),    // 1: api.v1.model.GetOneInstanceResponse
	(*ServiceCallResult)(nil),         // 2: api.v1.model.ServiceCallResult
	(*BatchServiceCallResult)(nil),    // 3: api.v1.model.BatchServiceCallResult
	(*RegisterInstanceRequest)(nil),   // 4: api.v1.model.RegisterInstanceRequest
	(*DeregisterInstanceRequest)(nil), // 5: api.v1.model.DeregisterInstanceRequest
	(*InstanceHeartbeatRequest)(nil),  // 6: api.v1.model.InstanceHeartbeatRequest
	(*DetailErrInfo)(nil),             // 7: api.v1.model.DetailErrInfo
	(*wrapperspb.StringValue)(nil),    // 8: google.protobuf.StringValue
	(*Instance)(nil),                  // 9: api.v1.model.Instance
}
var file_v1_model_sidecar_proto_depIdxs = []int32{
	8, // 0: api.v1.model.GetOneInstanceRequest.namespace:type_name -> google.protobuf.StringValue
	8, // 1: api.v1.model.GetOneInstanceRequest.service_name:type_name -> google.protobuf.StringValue
	9, // 2: api.v1.model.GetOneInstanceResponse.instance:type_name -> api.v1.model.Instance
	9, // 3: api.v1.model.ServiceCallResult.instance:type_name -> api.v1.model.Instance
	2, // 4: api.v1.model.BatchServiceCallResult.call_results:type_name -> api.v1.model.ServiceCallResult
	9, // 5: api.v1.model.RegisterInstanceRequest.instance:type_name -> api.v1.model.Instance
	9, // 6: api.v1.model.DeregisterInstanceRequest.instance:type_name -> api.v1.model.Instance
	9, // 7: api.v1.model.InstanceHeartbeatRequest.instance:type_name -> api.v1.model.Instance
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_v1_model_sidecar_proto_init() }
func file_v1_model_sidecar_proto_init() {
	if File_v1_model_sidecar_proto != nil {
		return
	}
	file_v1_model_service_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_v1_model_sidecar_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOneInstanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_sidecar_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOneInstanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_sidecar_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceCallResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_sidecar_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchServiceCallResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_sidecar_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterInstanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_sidecar_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeregisterInstanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_sidecar_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstanceHeartbeatRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_sidecar_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailErrInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_model_sidecar_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v1_model_sidecar_proto_goTypes,
		DependencyIndexes: file_v1_model_sidecar_proto_depIdxs,
		MessageInfos:      file_v1_model_sidecar_proto_msgTypes,
	}.Build()
	File_v1_model_sidecar_proto = out.File
	file_v1_model_sidecar_proto_rawDesc = nil
	file_v1_model_sidecar_proto_goTypes = nil
	file_v1_model_sidecar_proto_depIdxs = nil
}
