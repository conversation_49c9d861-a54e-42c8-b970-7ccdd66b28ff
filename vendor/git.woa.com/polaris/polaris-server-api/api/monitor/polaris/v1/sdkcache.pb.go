// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: monitor/polaris/v1/sdkcache.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//一次版本号变更
type RevisionHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//变更时间
	Time *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=time,proto3" json:"time,omitempty"`
	//变更的次序
	ChangeSeq uint32 `protobuf:"varint,2,opt,name=change_seq,json=changeSeq,proto3" json:"change_seq,omitempty"`
	//变更后版本号
	Revision string `protobuf:"bytes,3,opt,name=revision,proto3" json:"revision,omitempty"`
	//实例变化情况
	InstanceChange *InstancesChange `protobuf:"bytes,4,opt,name=instance_change,json=instanceChange,proto3" json:"instance_change,omitempty"`
	//网格服务变化情况
	MeshServiceChange *MeshServicesChange `protobuf:"bytes,5,opt,name=mesh_service_change,json=meshServiceChange,proto3" json:"mesh_service_change,omitempty"`
}

func (x *RevisionHistory) Reset() {
	*x = RevisionHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RevisionHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevisionHistory) ProtoMessage() {}

func (x *RevisionHistory) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevisionHistory.ProtoReflect.Descriptor instead.
func (*RevisionHistory) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP(), []int{0}
}

func (x *RevisionHistory) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *RevisionHistory) GetChangeSeq() uint32 {
	if x != nil {
		return x.ChangeSeq
	}
	return 0
}

func (x *RevisionHistory) GetRevision() string {
	if x != nil {
		return x.Revision
	}
	return ""
}

func (x *RevisionHistory) GetInstanceChange() *InstancesChange {
	if x != nil {
		return x.InstanceChange
	}
	return nil
}

func (x *RevisionHistory) GetMeshServiceChange() *MeshServicesChange {
	if x != nil {
		return x.MeshServiceChange
	}
	return nil
}

// 单个网格服务的变化情况
type ChangeMeshService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MeshNamespace string `protobuf:"bytes,1,opt,name=mesh_namespace,json=meshNamespace,proto3" json:"mesh_namespace,omitempty"`
	MeshService   string `protobuf:"bytes,2,opt,name=mesh_service,json=meshService,proto3" json:"mesh_service,omitempty"`
	Info          string `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *ChangeMeshService) Reset() {
	*x = ChangeMeshService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeMeshService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeMeshService) ProtoMessage() {}

func (x *ChangeMeshService) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeMeshService.ProtoReflect.Descriptor instead.
func (*ChangeMeshService) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP(), []int{1}
}

func (x *ChangeMeshService) GetMeshNamespace() string {
	if x != nil {
		return x.MeshNamespace
	}
	return ""
}

func (x *ChangeMeshService) GetMeshService() string {
	if x != nil {
		return x.MeshService
	}
	return ""
}

func (x *ChangeMeshService) GetInfo() string {
	if x != nil {
		return x.Info
	}
	return ""
}

// 一个网格下面所有服务的变化情况
type MeshServicesChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OldCount         uint32               `protobuf:"varint,1,opt,name=old_count,json=oldCount,proto3" json:"old_count,omitempty"`
	NewCount         uint32               `protobuf:"varint,2,opt,name=new_count,json=newCount,proto3" json:"new_count,omitempty"`
	ModifiedServices []*ChangeMeshService `protobuf:"bytes,3,rep,name=modified_services,json=modifiedServices,proto3" json:"modified_services,omitempty"`
	AddedServices    []*ChangeMeshService `protobuf:"bytes,4,rep,name=added_services,json=addedServices,proto3" json:"added_services,omitempty"`
	DeletedServices  []*ChangeMeshService `protobuf:"bytes,5,rep,name=deleted_services,json=deletedServices,proto3" json:"deleted_services,omitempty"`
}

func (x *MeshServicesChange) Reset() {
	*x = MeshServicesChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MeshServicesChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MeshServicesChange) ProtoMessage() {}

func (x *MeshServicesChange) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MeshServicesChange.ProtoReflect.Descriptor instead.
func (*MeshServicesChange) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP(), []int{2}
}

func (x *MeshServicesChange) GetOldCount() uint32 {
	if x != nil {
		return x.OldCount
	}
	return 0
}

func (x *MeshServicesChange) GetNewCount() uint32 {
	if x != nil {
		return x.NewCount
	}
	return 0
}

func (x *MeshServicesChange) GetModifiedServices() []*ChangeMeshService {
	if x != nil {
		return x.ModifiedServices
	}
	return nil
}

func (x *MeshServicesChange) GetAddedServices() []*ChangeMeshService {
	if x != nil {
		return x.AddedServices
	}
	return nil
}

func (x *MeshServicesChange) GetDeletedServices() []*ChangeMeshService {
	if x != nil {
		return x.DeletedServices
	}
	return nil
}

// 一个网格下面所有服务的变化情况
type MeshServicesHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 版本号变更
	Revision []*RevisionHistory `protobuf:"bytes,1,rep,name=revision,proto3" json:"revision,omitempty"`
}

func (x *MeshServicesHistory) Reset() {
	*x = MeshServicesHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MeshServicesHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MeshServicesHistory) ProtoMessage() {}

func (x *MeshServicesHistory) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MeshServicesHistory.ProtoReflect.Descriptor instead.
func (*MeshServicesHistory) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP(), []int{3}
}

func (x *MeshServicesHistory) GetRevision() []*RevisionHistory {
	if x != nil {
		return x.Revision
	}
	return nil
}

// 一个网格服务的版本变化情况
type SingleMeshServiceHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MeshNamespace      string             `protobuf:"bytes,1,opt,name=mesh_namespace,json=meshNamespace,proto3" json:"mesh_namespace,omitempty"`
	MeshService        string             `protobuf:"bytes,2,opt,name=mesh_service,json=meshService,proto3" json:"mesh_service,omitempty"`
	Revision           []*RevisionHistory `protobuf:"bytes,3,rep,name=revision,proto3" json:"revision,omitempty"`
	MeshServiceDeleted bool               `protobuf:"varint,4,opt,name=mesh_service_deleted,json=meshServiceDeleted,proto3" json:"mesh_service_deleted,omitempty"`
}

func (x *SingleMeshServiceHistory) Reset() {
	*x = SingleMeshServiceHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SingleMeshServiceHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SingleMeshServiceHistory) ProtoMessage() {}

func (x *SingleMeshServiceHistory) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SingleMeshServiceHistory.ProtoReflect.Descriptor instead.
func (*SingleMeshServiceHistory) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP(), []int{4}
}

func (x *SingleMeshServiceHistory) GetMeshNamespace() string {
	if x != nil {
		return x.MeshNamespace
	}
	return ""
}

func (x *SingleMeshServiceHistory) GetMeshService() string {
	if x != nil {
		return x.MeshService
	}
	return ""
}

func (x *SingleMeshServiceHistory) GetRevision() []*RevisionHistory {
	if x != nil {
		return x.Revision
	}
	return nil
}

func (x *SingleMeshServiceHistory) GetMeshServiceDeleted() bool {
	if x != nil {
		return x.MeshServiceDeleted
	}
	return false
}

//路由变更历史
type RoutingHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//版本号变更
	Revision []*RevisionHistory `protobuf:"bytes,1,rep,name=revision,proto3" json:"revision,omitempty"`
}

func (x *RoutingHistory) Reset() {
	*x = RoutingHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoutingHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoutingHistory) ProtoMessage() {}

func (x *RoutingHistory) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoutingHistory.ProtoReflect.Descriptor instead.
func (*RoutingHistory) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP(), []int{5}
}

func (x *RoutingHistory) GetRevision() []*RevisionHistory {
	if x != nil {
		return x.Revision
	}
	return nil
}

//一个实例信息
type ChangeInstance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VpcId string `protobuf:"bytes,1,opt,name=vpc_id,json=vpcId,proto3" json:"vpc_id,omitempty"`
	Host  string `protobuf:"bytes,2,opt,name=host,proto3" json:"host,omitempty"`
	Port  uint32 `protobuf:"varint,3,opt,name=port,proto3" json:"port,omitempty"`
	Info  string `protobuf:"bytes,4,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *ChangeInstance) Reset() {
	*x = ChangeInstance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeInstance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeInstance) ProtoMessage() {}

func (x *ChangeInstance) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeInstance.ProtoReflect.Descriptor instead.
func (*ChangeInstance) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP(), []int{6}
}

func (x *ChangeInstance) GetVpcId() string {
	if x != nil {
		return x.VpcId
	}
	return ""
}

func (x *ChangeInstance) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *ChangeInstance) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ChangeInstance) GetInfo() string {
	if x != nil {
		return x.Info
	}
	return ""
}

//实例信息的变化情况
type InstancesChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OldCount          uint32            `protobuf:"varint,1,opt,name=old_count,json=oldCount,proto3" json:"old_count,omitempty"`
	NewCount          uint32            `protobuf:"varint,2,opt,name=new_count,json=newCount,proto3" json:"new_count,omitempty"`
	ModifiedInstances []*ChangeInstance `protobuf:"bytes,3,rep,name=modified_instances,json=modifiedInstances,proto3" json:"modified_instances,omitempty"`
	AddedInstances    []*ChangeInstance `protobuf:"bytes,4,rep,name=added_instances,json=addedInstances,proto3" json:"added_instances,omitempty"`
	DeletedInstances  []*ChangeInstance `protobuf:"bytes,5,rep,name=deleted_instances,json=deletedInstances,proto3" json:"deleted_instances,omitempty"`
}

func (x *InstancesChange) Reset() {
	*x = InstancesChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstancesChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstancesChange) ProtoMessage() {}

func (x *InstancesChange) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstancesChange.ProtoReflect.Descriptor instead.
func (*InstancesChange) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP(), []int{7}
}

func (x *InstancesChange) GetOldCount() uint32 {
	if x != nil {
		return x.OldCount
	}
	return 0
}

func (x *InstancesChange) GetNewCount() uint32 {
	if x != nil {
		return x.NewCount
	}
	return 0
}

func (x *InstancesChange) GetModifiedInstances() []*ChangeInstance {
	if x != nil {
		return x.ModifiedInstances
	}
	return nil
}

func (x *InstancesChange) GetAddedInstances() []*ChangeInstance {
	if x != nil {
		return x.AddedInstances
	}
	return nil
}

func (x *InstancesChange) GetDeletedInstances() []*ChangeInstance {
	if x != nil {
		return x.DeletedInstances
	}
	return nil
}

//实例变更历史
type InstancesHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//版本号变更
	Revision []*RevisionHistory `protobuf:"bytes,1,rep,name=revision,proto3" json:"revision,omitempty"`
}

func (x *InstancesHistory) Reset() {
	*x = InstancesHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstancesHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstancesHistory) ProtoMessage() {}

func (x *InstancesHistory) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstancesHistory.ProtoReflect.Descriptor instead.
func (*InstancesHistory) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP(), []int{8}
}

func (x *InstancesHistory) GetRevision() []*RevisionHistory {
	if x != nil {
		return x.Revision
	}
	return nil
}

//一个单独限流规则（具备一个ruleId）的变更历史
type SingleRateLimitRuleHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RuleId   string             `protobuf:"bytes,1,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`
	Revision []*RevisionHistory `protobuf:"bytes,2,rep,name=revision,proto3" json:"revision,omitempty"`
}

func (x *SingleRateLimitRuleHistory) Reset() {
	*x = SingleRateLimitRuleHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SingleRateLimitRuleHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SingleRateLimitRuleHistory) ProtoMessage() {}

func (x *SingleRateLimitRuleHistory) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SingleRateLimitRuleHistory.ProtoReflect.Descriptor instead.
func (*SingleRateLimitRuleHistory) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP(), []int{9}
}

func (x *SingleRateLimitRuleHistory) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

func (x *SingleRateLimitRuleHistory) GetRevision() []*RevisionHistory {
	if x != nil {
		return x.Revision
	}
	return nil
}

//限流规则（各个单独限流规则的汇总）变更历史
type RateLimitHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//版本号变更
	Revision []*RevisionHistory `protobuf:"bytes,1,rep,name=revision,proto3" json:"revision,omitempty"`
}

func (x *RateLimitHistory) Reset() {
	*x = RateLimitHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimitHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimitHistory) ProtoMessage() {}

func (x *RateLimitHistory) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimitHistory.ProtoReflect.Descriptor instead.
func (*RateLimitHistory) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP(), []int{10}
}

func (x *RateLimitHistory) GetRevision() []*RevisionHistory {
	if x != nil {
		return x.Revision
	}
	return nil
}

//上报的服务信息
type ServiceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//该条记录的唯一id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//sdk标识
	SdkToken  *SDKToken `protobuf:"bytes,2,opt,name=sdk_token,json=sdkToken,proto3" json:"sdk_token,omitempty"`
	Namespace string    `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Service   string    `protobuf:"bytes,4,opt,name=service,proto3" json:"service,omitempty"`
	//服务实例的版本变化
	InstancesHistory *InstancesHistory `protobuf:"bytes,5,opt,name=instances_history,json=instancesHistory,proto3" json:"instances_history,omitempty"`
	//服务的实例数据已从sdk中删除（过期淘汰或者server删除了）
	InstanceEliminated bool `protobuf:"varint,6,opt,name=instance_eliminated,json=instanceEliminated,proto3" json:"instance_eliminated,omitempty"`
	//该服务对应的路由变化情况
	RoutingHistory *RoutingHistory `protobuf:"bytes,7,opt,name=routing_history,json=routingHistory,proto3" json:"routing_history,omitempty"`
	//服务的路由数据已从sdk中删除（过期淘汰或者server删除了）
	RoutingEliminated bool `protobuf:"varint,8,opt,name=routing_eliminated,json=routingEliminated,proto3" json:"routing_eliminated,omitempty"`
	//服务对应的限流规则版本
	RateLimitHistory *RateLimitHistory `protobuf:"bytes,9,opt,name=rate_limit_history,json=rateLimitHistory,proto3" json:"rate_limit_history,omitempty"`
	//各个单独限流规则的版本变更历史
	SingleRateLimitHistories []*SingleRateLimitRuleHistory `protobuf:"bytes,10,rep,name=single_rate_limit_histories,json=singleRateLimitHistories,proto3" json:"single_rate_limit_histories,omitempty"`
	//服务的限流规则数据已从sdk中删除（过期淘汰或者server删除了）
	RateLimitEliminated bool              `protobuf:"varint,11,opt,name=rate_limit_eliminated,json=rateLimitEliminated,proto3" json:"rate_limit_eliminated,omitempty"`
	Signature           *MonitorSignature `protobuf:"bytes,12,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *ServiceInfo) Reset() {
	*x = ServiceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInfo) ProtoMessage() {}

func (x *ServiceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInfo.ProtoReflect.Descriptor instead.
func (*ServiceInfo) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP(), []int{11}
}

func (x *ServiceInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ServiceInfo) GetSdkToken() *SDKToken {
	if x != nil {
		return x.SdkToken
	}
	return nil
}

func (x *ServiceInfo) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ServiceInfo) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *ServiceInfo) GetInstancesHistory() *InstancesHistory {
	if x != nil {
		return x.InstancesHistory
	}
	return nil
}

func (x *ServiceInfo) GetInstanceEliminated() bool {
	if x != nil {
		return x.InstanceEliminated
	}
	return false
}

func (x *ServiceInfo) GetRoutingHistory() *RoutingHistory {
	if x != nil {
		return x.RoutingHistory
	}
	return nil
}

func (x *ServiceInfo) GetRoutingEliminated() bool {
	if x != nil {
		return x.RoutingEliminated
	}
	return false
}

func (x *ServiceInfo) GetRateLimitHistory() *RateLimitHistory {
	if x != nil {
		return x.RateLimitHistory
	}
	return nil
}

func (x *ServiceInfo) GetSingleRateLimitHistories() []*SingleRateLimitRuleHistory {
	if x != nil {
		return x.SingleRateLimitHistories
	}
	return nil
}

func (x *ServiceInfo) GetRateLimitEliminated() bool {
	if x != nil {
		return x.RateLimitEliminated
	}
	return false
}

func (x *ServiceInfo) GetSignature() *MonitorSignature {
	if x != nil {
		return x.Signature
	}
	return nil
}

//单个网格规则的版本变更记录
type SingleMeshResourceRuleHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                string             `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Revision            []*RevisionHistory `protobuf:"bytes,2,rep,name=revision,proto3" json:"revision,omitempty"`
	MeshResourceDeleted bool               `protobuf:"varint,3,opt,name=mesh_resource_deleted,json=meshResourceDeleted,proto3" json:"mesh_resource_deleted,omitempty"`
}

func (x *SingleMeshResourceRuleHistory) Reset() {
	*x = SingleMeshResourceRuleHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SingleMeshResourceRuleHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SingleMeshResourceRuleHistory) ProtoMessage() {}

func (x *SingleMeshResourceRuleHistory) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SingleMeshResourceRuleHistory.ProtoReflect.Descriptor instead.
func (*SingleMeshResourceRuleHistory) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP(), []int{12}
}

func (x *SingleMeshResourceRuleHistory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SingleMeshResourceRuleHistory) GetRevision() []*RevisionHistory {
	if x != nil {
		return x.Revision
	}
	return nil
}

func (x *SingleMeshResourceRuleHistory) GetMeshResourceDeleted() bool {
	if x != nil {
		return x.MeshResourceDeleted
	}
	return false
}

//网格规则的版本变更记录
type MeshResourceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//sdk标识
	SdkToken *SDKToken `protobuf:"bytes,2,opt,name=sdk_token,json=sdkToken,proto3" json:"sdk_token,omitempty"`
	MeshId   string    `protobuf:"bytes,3,opt,name=mesh_id,json=meshId,proto3" json:"mesh_id,omitempty"`
	TypeUrl  string    `protobuf:"bytes,4,opt,name=type_url,json=typeUrl,proto3" json:"type_url,omitempty"`
	//整个规则集体的版本号变更
	Revision []*RevisionHistory `protobuf:"bytes,5,rep,name=revision,proto3" json:"revision,omitempty"`
	//整个规则是否被删除
	MeshConfigDeleted bool `protobuf:"varint,6,opt,name=mesh_config_deleted,json=meshConfigDeleted,proto3" json:"mesh_config_deleted,omitempty"`
	//各个单个规则的版本变更历史
	SingleMeshResourceHistory []*SingleMeshResourceRuleHistory `protobuf:"bytes,7,rep,name=single_mesh_resource_history,json=singleMeshResourceHistory,proto3" json:"single_mesh_resource_history,omitempty"`
	Signature                 *MonitorSignature                `protobuf:"bytes,8,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *MeshResourceInfo) Reset() {
	*x = MeshResourceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MeshResourceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MeshResourceInfo) ProtoMessage() {}

func (x *MeshResourceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MeshResourceInfo.ProtoReflect.Descriptor instead.
func (*MeshResourceInfo) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP(), []int{13}
}

func (x *MeshResourceInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MeshResourceInfo) GetSdkToken() *SDKToken {
	if x != nil {
		return x.SdkToken
	}
	return nil
}

func (x *MeshResourceInfo) GetMeshId() string {
	if x != nil {
		return x.MeshId
	}
	return ""
}

func (x *MeshResourceInfo) GetTypeUrl() string {
	if x != nil {
		return x.TypeUrl
	}
	return ""
}

func (x *MeshResourceInfo) GetRevision() []*RevisionHistory {
	if x != nil {
		return x.Revision
	}
	return nil
}

func (x *MeshResourceInfo) GetMeshConfigDeleted() bool {
	if x != nil {
		return x.MeshConfigDeleted
	}
	return false
}

func (x *MeshResourceInfo) GetSingleMeshResourceHistory() []*SingleMeshResourceRuleHistory {
	if x != nil {
		return x.SingleMeshResourceHistory
	}
	return nil
}

func (x *MeshResourceInfo) GetSignature() *MonitorSignature {
	if x != nil {
		return x.Signature
	}
	return nil
}

//网格服务的版本变更记录
type MeshInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//sdk标识
	SdkToken *SDKToken `protobuf:"bytes,2,opt,name=sdk_token,json=sdkToken,proto3" json:"sdk_token,omitempty"`
	MeshId   string    `protobuf:"bytes,3,opt,name=mesh_id,json=meshId,proto3" json:"mesh_id,omitempty"`
	//整个订阅服务集体的版本号变更
	Revision []*RevisionHistory `protobuf:"bytes,4,rep,name=revision,proto3" json:"revision,omitempty"`
	//整个网格是否被删除
	MeshDeleted bool `protobuf:"varint,5,opt,name=mesh_deleted,json=meshDeleted,proto3" json:"mesh_deleted,omitempty"`
	//网格服务的版本变化
	MeshServicesHistory *MeshServicesHistory `protobuf:"bytes,6,opt,name=mesh_services_history,json=meshServicesHistory,proto3" json:"mesh_services_history,omitempty"`
	//各个单个规则的版本变更历史
	SingleMeshServiceHistory []*SingleMeshServiceHistory `protobuf:"bytes,7,rep,name=single_mesh_service_history,json=singleMeshServiceHistory,proto3" json:"single_mesh_service_history,omitempty"`
	Signature                *MonitorSignature           `protobuf:"bytes,8,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *MeshInfo) Reset() {
	*x = MeshInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MeshInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MeshInfo) ProtoMessage() {}

func (x *MeshInfo) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkcache_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MeshInfo.ProtoReflect.Descriptor instead.
func (*MeshInfo) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP(), []int{14}
}

func (x *MeshInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MeshInfo) GetSdkToken() *SDKToken {
	if x != nil {
		return x.SdkToken
	}
	return nil
}

func (x *MeshInfo) GetMeshId() string {
	if x != nil {
		return x.MeshId
	}
	return ""
}

func (x *MeshInfo) GetRevision() []*RevisionHistory {
	if x != nil {
		return x.Revision
	}
	return nil
}

func (x *MeshInfo) GetMeshDeleted() bool {
	if x != nil {
		return x.MeshDeleted
	}
	return false
}

func (x *MeshInfo) GetMeshServicesHistory() *MeshServicesHistory {
	if x != nil {
		return x.MeshServicesHistory
	}
	return nil
}

func (x *MeshInfo) GetSingleMeshServiceHistory() []*SingleMeshServiceHistory {
	if x != nil {
		return x.SingleMeshServiceHistory
	}
	return nil
}

func (x *MeshInfo) GetSignature() *MonitorSignature {
	if x != nil {
		return x.Signature
	}
	return nil
}

var File_monitor_polaris_v1_sdkcache_proto protoreflect.FileDescriptor

var file_monitor_polaris_v1_sdkcache_proto_rawDesc = []byte{
	0x0a, 0x21, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x64, 0x6b, 0x63, 0x61, 0x63, 0x68, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x02, 0x76, 0x31, 0x1a, 0x21, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x64, 0x6b, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x82, 0x02, 0x0a, 0x0f, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x73, 0x65,
	0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53,
	0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3c,
	0x0a, 0x0f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0e, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x46, 0x0a, 0x13,
	0x6d, 0x65, 0x73, 0x68, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x31, 0x2e, 0x4d,
	0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x52, 0x11, 0x6d, 0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x22, 0x71, 0x0a, 0x11, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x65,
	0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x65, 0x73,
	0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x6d, 0x65, 0x73, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x92, 0x02, 0x0a, 0x12, 0x4d, 0x65, 0x73, 0x68,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x6f, 0x6c, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x6f, 0x6c, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6e,
	0x65, 0x77, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x6e, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x11, 0x6d, 0x6f, 0x64, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4d,
	0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x10, 0x6d, 0x6f, 0x64, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x0e,
	0x61, 0x64, 0x64, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x4d, 0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x0d, 0x61, 0x64, 0x64,
	0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x10, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x4d, 0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x0f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0x46, 0x0a, 0x13,
	0x4d, 0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x12, 0x2f, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0xc7, 0x01, 0x0a, 0x18, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x4d,
	0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x65, 0x73, 0x68, 0x4e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x68,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6d, 0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2f, 0x0a, 0x08, 0x72,
	0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x14,
	0x6d, 0x65, 0x73, 0x68, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x6d, 0x65, 0x73, 0x68,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x22, 0x41,
	0x0a, 0x0e, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x12, 0x2f, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x22, 0x63, 0x0a, 0x0e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x76, 0x70, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x70, 0x63, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x8c, 0x02, 0x0a, 0x0f, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x6c,
	0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6f,
	0x6c, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x65, 0x77, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6e, 0x65, 0x77, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x41, 0x0a, 0x12, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x11, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x65, 0x64,
	0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x11, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x10, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x22, 0x43, 0x0a, 0x10, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x2f, 0x0a, 0x08, 0x72, 0x65, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x66, 0x0a, 0x1a, 0x53, 0x69,
	0x6e, 0x67, 0x6c, 0x65, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c,
	0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x2f, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0x43, 0x0a, 0x10, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x2f, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x72,
	0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xeb, 0x04, 0x0a, 0x0b, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x73, 0x64, 0x6b, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x44, 0x4b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x08, 0x73, 0x64, 0x6b, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x41, 0x0a, 0x11, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x10, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x2f, 0x0a,
	0x13, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x6e,
	0x61, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x45, 0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x12, 0x3b,
	0x0a, 0x0f, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x75,
	0x74, 0x69, 0x6e, 0x67, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x0e, 0x72, 0x6f, 0x75,
	0x74, 0x69, 0x6e, 0x67, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x2d, 0x0a, 0x12, 0x72,
	0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67,
	0x45, 0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x12, 0x42, 0x0a, 0x12, 0x72, 0x61,
	0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x10, 0x72, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x5d,
	0x0a, 0x1b, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x0a, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x52,
	0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x52, 0x18, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x52, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x32, 0x0a,
	0x15, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x65, 0x6c, 0x69, 0x6d,
	0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x72, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x45, 0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65,
	0x64, 0x12, 0x32, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x22, 0x98, 0x01, 0x0a, 0x1d, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65,
	0x4d, 0x65, 0x73, 0x68, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x08, 0x72,
	0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x15,
	0x6d, 0x65, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x6d, 0x65, 0x73,
	0x68, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x22, 0xfa, 0x02, 0x0a, 0x10, 0x4d, 0x65, 0x73, 0x68, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x73, 0x64, 0x6b, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x44,
	0x4b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x08, 0x73, 0x64, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x17, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x65, 0x73, 0x68, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x79, 0x70,
	0x65, 0x55, 0x72, 0x6c, 0x12, 0x2f, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x72, 0x65, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x11, 0x6d, 0x65, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x62, 0x0a, 0x1c, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x5f,
	0x6d, 0x65, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x68, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x68, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x19,
	0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x68, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x32, 0x0a, 0x09, 0x73, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22, 0x90, 0x03,
	0x0a, 0x08, 0x4d, 0x65, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x73, 0x64,
	0x6b, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x44, 0x4b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x08, 0x73, 0x64, 0x6b,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x73, 0x68, 0x49, 0x64, 0x12, 0x2f,
	0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x12, 0x4b, 0x0a, 0x15, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x13, 0x6d, 0x65, 0x73, 0x68,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12,
	0x5b, 0x0a, 0x1b, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x68, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65,
	0x4d, 0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x18, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x68, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x32, 0x0a, 0x09,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x42, 0x3f, 0x5a, 0x3d, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_monitor_polaris_v1_sdkcache_proto_rawDescOnce sync.Once
	file_monitor_polaris_v1_sdkcache_proto_rawDescData = file_monitor_polaris_v1_sdkcache_proto_rawDesc
)

func file_monitor_polaris_v1_sdkcache_proto_rawDescGZIP() []byte {
	file_monitor_polaris_v1_sdkcache_proto_rawDescOnce.Do(func() {
		file_monitor_polaris_v1_sdkcache_proto_rawDescData = protoimpl.X.CompressGZIP(file_monitor_polaris_v1_sdkcache_proto_rawDescData)
	})
	return file_monitor_polaris_v1_sdkcache_proto_rawDescData
}

var file_monitor_polaris_v1_sdkcache_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_monitor_polaris_v1_sdkcache_proto_goTypes = []interface{}{
	(*RevisionHistory)(nil),               // 0: v1.RevisionHistory
	(*ChangeMeshService)(nil),             // 1: v1.ChangeMeshService
	(*MeshServicesChange)(nil),            // 2: v1.MeshServicesChange
	(*MeshServicesHistory)(nil),           // 3: v1.MeshServicesHistory
	(*SingleMeshServiceHistory)(nil),      // 4: v1.SingleMeshServiceHistory
	(*RoutingHistory)(nil),                // 5: v1.RoutingHistory
	(*ChangeInstance)(nil),                // 6: v1.ChangeInstance
	(*InstancesChange)(nil),               // 7: v1.InstancesChange
	(*InstancesHistory)(nil),              // 8: v1.InstancesHistory
	(*SingleRateLimitRuleHistory)(nil),    // 9: v1.SingleRateLimitRuleHistory
	(*RateLimitHistory)(nil),              // 10: v1.RateLimitHistory
	(*ServiceInfo)(nil),                   // 11: v1.ServiceInfo
	(*SingleMeshResourceRuleHistory)(nil), // 12: v1.SingleMeshResourceRuleHistory
	(*MeshResourceInfo)(nil),              // 13: v1.MeshResourceInfo
	(*MeshInfo)(nil),                      // 14: v1.MeshInfo
	(*timestamppb.Timestamp)(nil),         // 15: google.protobuf.Timestamp
	(*SDKToken)(nil),                      // 16: v1.SDKToken
	(*MonitorSignature)(nil),              // 17: v1.MonitorSignature
}
var file_monitor_polaris_v1_sdkcache_proto_depIdxs = []int32{
	15, // 0: v1.RevisionHistory.time:type_name -> google.protobuf.Timestamp
	7,  // 1: v1.RevisionHistory.instance_change:type_name -> v1.InstancesChange
	2,  // 2: v1.RevisionHistory.mesh_service_change:type_name -> v1.MeshServicesChange
	1,  // 3: v1.MeshServicesChange.modified_services:type_name -> v1.ChangeMeshService
	1,  // 4: v1.MeshServicesChange.added_services:type_name -> v1.ChangeMeshService
	1,  // 5: v1.MeshServicesChange.deleted_services:type_name -> v1.ChangeMeshService
	0,  // 6: v1.MeshServicesHistory.revision:type_name -> v1.RevisionHistory
	0,  // 7: v1.SingleMeshServiceHistory.revision:type_name -> v1.RevisionHistory
	0,  // 8: v1.RoutingHistory.revision:type_name -> v1.RevisionHistory
	6,  // 9: v1.InstancesChange.modified_instances:type_name -> v1.ChangeInstance
	6,  // 10: v1.InstancesChange.added_instances:type_name -> v1.ChangeInstance
	6,  // 11: v1.InstancesChange.deleted_instances:type_name -> v1.ChangeInstance
	0,  // 12: v1.InstancesHistory.revision:type_name -> v1.RevisionHistory
	0,  // 13: v1.SingleRateLimitRuleHistory.revision:type_name -> v1.RevisionHistory
	0,  // 14: v1.RateLimitHistory.revision:type_name -> v1.RevisionHistory
	16, // 15: v1.ServiceInfo.sdk_token:type_name -> v1.SDKToken
	8,  // 16: v1.ServiceInfo.instances_history:type_name -> v1.InstancesHistory
	5,  // 17: v1.ServiceInfo.routing_history:type_name -> v1.RoutingHistory
	10, // 18: v1.ServiceInfo.rate_limit_history:type_name -> v1.RateLimitHistory
	9,  // 19: v1.ServiceInfo.single_rate_limit_histories:type_name -> v1.SingleRateLimitRuleHistory
	17, // 20: v1.ServiceInfo.signature:type_name -> v1.MonitorSignature
	0,  // 21: v1.SingleMeshResourceRuleHistory.revision:type_name -> v1.RevisionHistory
	16, // 22: v1.MeshResourceInfo.sdk_token:type_name -> v1.SDKToken
	0,  // 23: v1.MeshResourceInfo.revision:type_name -> v1.RevisionHistory
	12, // 24: v1.MeshResourceInfo.single_mesh_resource_history:type_name -> v1.SingleMeshResourceRuleHistory
	17, // 25: v1.MeshResourceInfo.signature:type_name -> v1.MonitorSignature
	16, // 26: v1.MeshInfo.sdk_token:type_name -> v1.SDKToken
	0,  // 27: v1.MeshInfo.revision:type_name -> v1.RevisionHistory
	3,  // 28: v1.MeshInfo.mesh_services_history:type_name -> v1.MeshServicesHistory
	4,  // 29: v1.MeshInfo.single_mesh_service_history:type_name -> v1.SingleMeshServiceHistory
	17, // 30: v1.MeshInfo.signature:type_name -> v1.MonitorSignature
	31, // [31:31] is the sub-list for method output_type
	31, // [31:31] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_monitor_polaris_v1_sdkcache_proto_init() }
func file_monitor_polaris_v1_sdkcache_proto_init() {
	if File_monitor_polaris_v1_sdkcache_proto != nil {
		return
	}
	file_monitor_polaris_v1_sdktoken_proto_init()
	file_monitor_polaris_v1_signature_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_monitor_polaris_v1_sdkcache_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RevisionHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_sdkcache_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeMeshService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_sdkcache_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MeshServicesChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_sdkcache_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MeshServicesHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_sdkcache_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SingleMeshServiceHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_sdkcache_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoutingHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_sdkcache_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeInstance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_sdkcache_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstancesChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_sdkcache_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstancesHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_sdkcache_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SingleRateLimitRuleHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_sdkcache_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimitHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_sdkcache_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_sdkcache_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SingleMeshResourceRuleHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_sdkcache_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MeshResourceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_sdkcache_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MeshInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_monitor_polaris_v1_sdkcache_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_monitor_polaris_v1_sdkcache_proto_goTypes,
		DependencyIndexes: file_monitor_polaris_v1_sdkcache_proto_depIdxs,
		MessageInfos:      file_monitor_polaris_v1_sdkcache_proto_msgTypes,
	}.Build()
	File_monitor_polaris_v1_sdkcache_proto = out.File
	file_monitor_polaris_v1_sdkcache_proto_rawDesc = nil
	file_monitor_polaris_v1_sdkcache_proto_goTypes = nil
	file_monitor_polaris_v1_sdkcache_proto_depIdxs = nil
}
