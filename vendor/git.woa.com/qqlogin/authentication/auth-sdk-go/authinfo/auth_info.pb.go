// Copyright (c) 2022, Tencent Inc.
// All rights reserved.
// Author: <EMAIL>

// 使用proto2降低使用门槛

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.22.3
// source: auth_info.proto

package authinfo

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CallerName *string `protobuf:"bytes,1,opt,name=caller_name,json=callerName" json:"caller_name,omitempty"`  // 主调模块名
	Timestamp  *int64  `protobuf:"varint,2,opt,name=timestamp" json:"timestamp,omitempty"`                     // 请求发起时间戳
	SignMethod *int32  `protobuf:"varint,3,opt,name=sign_method,json=signMethod" json:"sign_method,omitempty"` // 签名算法
	Signature  []byte  `protobuf:"bytes,4,opt,name=signature" json:"signature,omitempty"`                      // caller_name+caller_ip+sign_method+timestamp基于hmac算法生成的签名
}

func (x *AuthInfo) Reset() {
	*x = AuthInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_info_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthInfo) ProtoMessage() {}

func (x *AuthInfo) ProtoReflect() protoreflect.Message {
	mi := &file_auth_info_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthInfo.ProtoReflect.Descriptor instead.
func (*AuthInfo) Descriptor() ([]byte, []int) {
	return file_auth_info_proto_rawDescGZIP(), []int{0}
}

func (x *AuthInfo) GetCallerName() string {
	if x != nil && x.CallerName != nil {
		return *x.CallerName
	}
	return ""
}

func (x *AuthInfo) GetTimestamp() int64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

func (x *AuthInfo) GetSignMethod() int32 {
	if x != nil && x.SignMethod != nil {
		return *x.SignMethod
	}
	return 0
}

func (x *AuthInfo) GetSignature() []byte {
	if x != nil {
		return x.Signature
	}
	return nil
}

var File_auth_info_proto protoreflect.FileDescriptor

var file_auth_info_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x11, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2e, 0x71, 0x71, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0x88, 0x01, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x42,
	0x0d, 0x5a, 0x0b, 0x2e, 0x2e, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x69, 0x6e, 0x66, 0x6f,
}

var (
	file_auth_info_proto_rawDescOnce sync.Once
	file_auth_info_proto_rawDescData = file_auth_info_proto_rawDesc
)

func file_auth_info_proto_rawDescGZIP() []byte {
	file_auth_info_proto_rawDescOnce.Do(func() {
		file_auth_info_proto_rawDescData = protoimpl.X.CompressGZIP(file_auth_info_proto_rawDescData)
	})
	return file_auth_info_proto_rawDescData
}

var file_auth_info_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_auth_info_proto_goTypes = []interface{}{
	(*AuthInfo)(nil), // 0: tencent.qq.common.AuthInfo
}
var file_auth_info_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_auth_info_proto_init() }
func file_auth_info_proto_init() {
	if File_auth_info_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_auth_info_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_auth_info_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_auth_info_proto_goTypes,
		DependencyIndexes: file_auth_info_proto_depIdxs,
		MessageInfos:      file_auth_info_proto_msgTypes,
	}.Build()
	File_auth_info_proto = out.File
	file_auth_info_proto_rawDesc = nil
	file_auth_info_proto_goTypes = nil
	file_auth_info_proto_depIdxs = nil
}
