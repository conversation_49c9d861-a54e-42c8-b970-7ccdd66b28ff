/**
 * <PERSON><PERSON> is pleased to support the open source community by making CL5 available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */

package detect

import (
	"context"
	"git.code.oa.com/polaris/polaris-go/pkg/clock"
	"git.code.oa.com/polaris/polaris-go/pkg/config"
	"git.code.oa.com/polaris/polaris-go/pkg/flow/data"
	"git.code.oa.com/polaris/polaris-go/pkg/log"
	"git.code.oa.com/polaris/polaris-go/pkg/model"
	"git.code.oa.com/polaris/polaris-go/pkg/model/local"
	"git.code.oa.com/polaris/polaris-go/pkg/plugin"
	"git.code.oa.com/polaris/polaris-go/pkg/plugin/common"
	"git.code.oa.com/polaris/polaris-go/pkg/plugin/localregistry"
	"git.code.oa.com/polaris/polaris-go/pkg/plugin/outlierdetection"
	"sync/atomic"
	"time"
)

//创建健康检查的回调
func NewOutlierDetectCallBack(cfg config.Configuration, supplier plugin.Supplier) (*OutlierDetectCallBack, error) {
	var err error
	callback := &OutlierDetectCallBack{}
	//获取所有公共的健康检查插件
	if callback.outlierDetectionChain, err = data.GetOutlierDetectionChain(cfg, supplier); nil != err {
		return nil, err
	}
	//获取服务指定的探活器
	if callback.serviceSpecificDetectionChain, err = data.GetServiceSpecificOutlierDetectionChain(cfg,
		supplier); nil != err {
		return nil, err
	}
	if callback.registry, err = data.GetRegistry(cfg, supplier); nil != err {
		return nil, err
	}
	callback.interval = cfg.GetConsumer().GetOutlierDetectionConfig().GetCheckPeriod()
	callback.outlierDetectionConfig = cfg.GetConsumer().GetOutlierDetectionConfig()
	callback.wholeConfig = cfg
	return callback, nil
}

//健康探测回调任务
type OutlierDetectCallBack struct {
	//公共探活器
	outlierDetectionChain []outlierdetection.OutlierDetector
	//服务指定探活器
	serviceSpecificDetectionChain map[model.ServiceKey][]outlierdetection.OutlierDetector
	//本地缓存
	registry localregistry.LocalRegistry
	//轮询间隔
	interval time.Duration
	//健康检查的配置
	outlierDetectionConfig config.OutlierDetectionConfig
	//全部配置
	wholeConfig config.Configuration
	//任务队列
	taskChannels []chan model.Instance
	//任务下标
	taskIndex uint64
	//任务取消函数
	taskWorkerCancel context.CancelFunc
}

const channelBuffer = 100

//执行任务
func (c *OutlierDetectCallBack) Process(
	taskKey interface{}, taskValue interface{}, lastProcessTime time.Time) model.TaskResult {
	svc := taskKey.(model.ServiceKey)
	cfg := c.getServiceOutlierDetectionConfig(svc.Namespace, svc.Service)
	//如果对于这个服务没有开启探活，直接结束
	if !cfg.IsEnable() {
		log.GetBaseLogger().Infof("service %s detection dose not enable", svc)
		return model.TERMINATE
	}
	svcInstances := c.registry.GetInstances(&svc, false, true)
	if !svcInstances.IsInitialized() || len(svcInstances.GetInstances()) == 0 {
		return model.CONTINUE
	}
	err := c.doOutlierDetectionService(svcInstances, cfg)
	if nil != err {
		log.GetDetectLogger().Errorf("fail to update instances for %v, error: %v", svc, err)
		return model.CONTINUE
	}
	return model.CONTINUE
}

//OnTaskEvent 任务事件回调
func (c *OutlierDetectCallBack) OnTaskEvent(event model.TaskEvent) {
	switch event {
	case model.EventStart:
		var taskWorkerCtx context.Context
		concurrency := c.getConcurrency()
		c.taskChannels = make([]chan model.Instance, 0, concurrency)
		taskWorkerCtx, c.taskWorkerCancel = context.WithCancel(context.Background())
		for i := 0; i < concurrency; i++ {
			taskChan := make(chan model.Instance, channelBuffer)
			c.taskChannels = append(c.taskChannels, taskChan)
			go c.outlierDetectionLoop(taskChan, taskWorkerCtx)
		}
	case model.EventStop:
		c.taskWorkerCancel()
	}
}

func (c *OutlierDetectCallBack) getConcurrency() int {
	var res int
	if c.wholeConfig.GetConsumer().GetOutlierDetectionConfig().IsEnable() {
		res = c.wholeConfig.GetConsumer().GetOutlierDetectionConfig().GetConcurrency()
	}
	for _, sConf := range c.wholeConfig.GetConsumer().GetAllServiceSpecific() {
		if sConf.GetHealthCheck().IsEnable() {
			if sConf.GetHealthCheck().GetConcurrency() > res {
				res = sConf.GetHealthCheck().GetConcurrency()
			}
		}
	}
	return res
}

//根据任务名字获取特定的健康检测配置
func (c *OutlierDetectCallBack) getServiceOutlierDetectionConfig(namespace,
	service string) config.OutlierDetectionConfig {
	cfg := c.outlierDetectionConfig
	specific := c.wholeConfig.GetConsumer().GetServiceSpecific(namespace, service)
	if specific != nil {
		cfg = specific.GetHealthCheck()
	}
	return cfg
}

// 接受探活任务，进行探活
func (c *OutlierDetectCallBack) outlierDetectionLoop(taskChannel chan model.Instance, taskWorkerCtx context.Context) {
	log.GetDetectLogger().Infof("[outlierDetectionLoop] detect task starts")
	for {
		select {
		case <-taskWorkerCtx.Done():
			log.GetDetectLogger().Infof("[OutlierDetection] detect task stops")
			return
		case instance := <-taskChannel:
			err := c.processOutlierDetection(&model.ServiceKey{
				Namespace: instance.GetNamespace(),
				Service:   instance.GetService(),
			}, instance)
			// 经历过一次探测，将探测状态改为非正在探测，避免影响下次探测
			if localValue, ok := instance.(local.InstanceLocalValue); ok {
				localValue.UnDetecting()
			}
			if nil != err {
				log.GetDetectLogger().Errorf("[outlierDetectionLoop] fail to do outlier detection, err is %v", err)
			}
		}
	}
}

// 对于一个实例进行探活
func (c *OutlierDetectCallBack) processOutlierDetection(svc *model.ServiceKey, instance model.Instance) error {
	if !instance.IsHealthy() || instance.IsIsolated() || instance.GetWeight() == 0 {
		// 不健康或者隔离的实例，不进行探活
		return nil
	}
	policy := c.outlierDetectionConfig.GetWhen()
	if sConf := c.wholeConfig.GetConsumer().GetServiceSpecific(svc.Namespace, svc.Service); sConf != nil {
		policy = sConf.GetHealthCheck().GetWhen()
	}
	cbStatus := instance.GetCircuitBreakerStatus()
	// 如果实例不是半开的，并且探测策略不是 HealthCheckAlways，那么不进行探活
	if (cbStatus == nil || cbStatus.GetStatus() != model.Open) && policy != config.HealthCheckAlways {
		return nil
	}
	detectors, ok := c.serviceSpecificDetectionChain[*svc]
	if !ok {
		detectors = c.outlierDetectionChain
	}
	res := c.doOutlierDetectionInstance(detectors, instance)
	var status model.DetectorStatus
	var detectTime time.Time
	if res != nil {
		detectTime = res.GetDetectTime()
	} else {
		detectTime = clock.GetClock().Now()
	}
	if res != nil && res.GetRetStatus() == model.RetSuccess {
		status = model.Healthy
	} else {
		status = model.Dead
	}
	// 构造请求，更新探测结果
	updateRequest := &localregistry.ServiceUpdateRequest{
		ServiceKey: *svc,
		Properties: []localregistry.InstanceProperties{
			{
				ID:      instance.GetId(),
				Service: svc,
				Properties: map[string]interface{}{
					localregistry.PropertyOutlierDetectorStatus: &outlierDetectorStatus{
						status:    status,
						startTime: detectTime,
					},
				},
			},
		},
	}
	log.GetDetectLogger().Infof("[OutlierDetection] detect UpdateRequest, request is %s", updateRequest)
	return c.registry.UpdateInstances(updateRequest)
}

// doOutlierDetectionService 对一组服务进行探活逻辑
func (c *OutlierDetectCallBack) doOutlierDetectionService(svcInstances model.ServiceInstances,
	cfg config.OutlierDetectionConfig) error {
	if len(svcInstances.GetInstances()) == 0 {
		return nil
	}
	policy := cfg.GetWhen()
	for _, oneInstance := range svcInstances.GetInstances() {
		// 不健康，隔离和权重0的实例，不进行探活
		if !oneInstance.IsHealthy() || oneInstance.IsIsolated() || oneInstance.GetWeight() == 0 {
			continue
		}
		cbStatus := oneInstance.GetCircuitBreakerStatus()
		// 如果策略是出现问题的实例才进行探活，并且实例正常，不进行探活
		if policy == config.HealthCheckOnRecover && (cbStatus == nil || cbStatus.GetStatus() != model.Open) {
			continue
		}
		var instanceLocalValue local.InstanceLocalValue
		instanceLocalValue, ok := oneInstance.(local.InstanceLocalValue)
		if !ok {
			continue
		}
		activeDetectStatus := instanceLocalValue.GetOutlierDetectorStatus()
		//这个实例距离上次探活的时间还没有超出配置的探活周期，不再继续探活
		if nil != activeDetectStatus && time.Since(activeDetectStatus.GetStartTime()) < cfg.GetCheckPeriod() {
			continue
		}
		//如果转化为正在探活失败，说明这个实例正在探活，不再继续探活
		if !instanceLocalValue.ToDetecting() {
			continue
		}
		var success bool
		// 往每个探测协程尝试进行发送
		for i := 0; i < len(c.taskChannels); i++ {
			nextIdx := atomic.AddUint64(&c.taskIndex, 1)
			select {
			case c.taskChannels[int(nextIdx%uint64(len(c.taskChannels)))] <- oneInstance:
				success = true
			default:
				break
			}
			if success {
				break
			}
		}
		// 如果没有成功加入探活队列中，转化为非正在探活状态
		if !success {
			instanceLocalValue.UnDetecting()
		}
	}
	return nil
}

// doOutlierDetectionService 对一组服务中的一个服务实例进行探活逻辑
// 探测成功返回result，否则返回nil
func (c *OutlierDetectCallBack) doOutlierDetectionInstance(detectors []outlierdetection.OutlierDetector,
	oneInstance model.Instance) common.DetectResult {
	var result common.DetectResult
	var err error
	for _, outlierDetection := range detectors {
		result, err = outlierDetection.DetectInstance(oneInstance)
		if err != nil {
			log.GetDetectLogger().Errorf("timing_flow OutlierDetection Err:%s", err.Error())
			continue
		}
		if result == nil {
			continue
		}
		if result.GetRetStatus() == model.RetSuccess {
			return result
		}
	}
	return result
}
