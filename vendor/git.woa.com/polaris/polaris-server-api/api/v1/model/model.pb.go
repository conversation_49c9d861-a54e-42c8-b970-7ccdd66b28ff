// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v1/model/model.proto

package polarismodel_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MatchString_MatchStringType int32

const (
	MatchString_EXACT         MatchString_MatchStringType = 0
	MatchString_REGEX         MatchString_MatchStringType = 1
	MatchString_INVERT_EXACT  MatchString_MatchStringType = 2 // 精确取反
	MatchString_INVERT_REGEX  MatchString_MatchStringType = 3 // 正则取反
	MatchString_KEY_NOT_EXIST MatchString_MatchStringType = 4 // Key不存在
)

// Enum value maps for MatchString_MatchStringType.
var (
	MatchString_MatchStringType_name = map[int32]string{
		0: "EXACT",
		1: "REGEX",
		2: "INVERT_EXACT",
		3: "INVERT_REGEX",
		4: "KEY_NOT_EXIST",
	}
	MatchString_MatchStringType_value = map[string]int32{
		"EXACT":         0,
		"REGEX":         1,
		"INVERT_EXACT":  2,
		"INVERT_REGEX":  3,
		"KEY_NOT_EXIST": 4,
	}
)

func (x MatchString_MatchStringType) Enum() *MatchString_MatchStringType {
	p := new(MatchString_MatchStringType)
	*p = x
	return p
}

func (x MatchString_MatchStringType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MatchString_MatchStringType) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_model_proto_enumTypes[0].Descriptor()
}

func (MatchString_MatchStringType) Type() protoreflect.EnumType {
	return &file_v1_model_model_proto_enumTypes[0]
}

func (x MatchString_MatchStringType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MatchString_MatchStringType.Descriptor instead.
func (MatchString_MatchStringType) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_model_proto_rawDescGZIP(), []int{1, 0}
}

type MatchString_ValueType int32

const (
	MatchString_TEXT      MatchString_ValueType = 0
	MatchString_PARAMETER MatchString_ValueType = 1
	MatchString_VARIABLE  MatchString_ValueType = 2
)

// Enum value maps for MatchString_ValueType.
var (
	MatchString_ValueType_name = map[int32]string{
		0: "TEXT",
		1: "PARAMETER",
		2: "VARIABLE",
	}
	MatchString_ValueType_value = map[string]int32{
		"TEXT":      0,
		"PARAMETER": 1,
		"VARIABLE":  2,
	}
)

func (x MatchString_ValueType) Enum() *MatchString_ValueType {
	p := new(MatchString_ValueType)
	*p = x
	return p
}

func (x MatchString_ValueType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MatchString_ValueType) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_model_proto_enumTypes[1].Descriptor()
}

func (MatchString_ValueType) Type() protoreflect.EnumType {
	return &file_v1_model_model_proto_enumTypes[1]
}

func (x MatchString_ValueType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MatchString_ValueType.Descriptor instead.
func (MatchString_ValueType) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_model_proto_rawDescGZIP(), []int{1, 1}
}

type Location struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	Zone   *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	Campus *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=campus,proto3" json:"campus,omitempty"`
}

func (x *Location) Reset() {
	*x = Location{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Location) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Location) ProtoMessage() {}

func (x *Location) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Location.ProtoReflect.Descriptor instead.
func (*Location) Descriptor() ([]byte, []int) {
	return file_v1_model_model_proto_rawDescGZIP(), []int{0}
}

func (x *Location) GetRegion() *wrapperspb.StringValue {
	if x != nil {
		return x.Region
	}
	return nil
}

func (x *Location) GetZone() *wrapperspb.StringValue {
	if x != nil {
		return x.Zone
	}
	return nil
}

func (x *Location) GetCampus() *wrapperspb.StringValue {
	if x != nil {
		return x.Campus
	}
	return nil
}

type MatchString struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type      MatchString_MatchStringType `protobuf:"varint,1,opt,name=type,proto3,enum=api.v1.model.MatchString_MatchStringType" json:"type,omitempty"`
	Value     *wrapperspb.StringValue     `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	ValueType MatchString_ValueType       `protobuf:"varint,3,opt,name=value_type,json=valueType,proto3,enum=api.v1.model.MatchString_ValueType" json:"value_type,omitempty"`
}

func (x *MatchString) Reset() {
	*x = MatchString{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_model_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchString) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchString) ProtoMessage() {}

func (x *MatchString) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_model_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchString.ProtoReflect.Descriptor instead.
func (*MatchString) Descriptor() ([]byte, []int) {
	return file_v1_model_model_proto_rawDescGZIP(), []int{1}
}

func (x *MatchString) GetType() MatchString_MatchStringType {
	if x != nil {
		return x.Type
	}
	return MatchString_EXACT
}

func (x *MatchString) GetValue() *wrapperspb.StringValue {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *MatchString) GetValueType() MatchString_ValueType {
	if x != nil {
		return x.ValueType
	}
	return MatchString_TEXT
}

var File_v1_model_model_proto protoreflect.FileDescriptor

var file_v1_model_model_proto_rawDesc = []byte{
	0x0a, 0x14, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa8, 0x01, 0x0a, 0x08, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x34, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x63, 0x61, 0x6d,
	0x70, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x63, 0x61, 0x6d, 0x70, 0x75, 0x73, 0x22,
	0xd8, 0x02, 0x0a, 0x0b, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x3d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x32,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x42, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x5e, 0x0a, 0x0f, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x58, 0x41,
	0x43, 0x54, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45, 0x47, 0x45, 0x58, 0x10, 0x01, 0x12,
	0x10, 0x0a, 0x0c, 0x49, 0x4e, 0x56, 0x45, 0x52, 0x54, 0x5f, 0x45, 0x58, 0x41, 0x43, 0x54, 0x10,
	0x02, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x4e, 0x56, 0x45, 0x52, 0x54, 0x5f, 0x52, 0x45, 0x47, 0x45,
	0x58, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x4b, 0x45, 0x59, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45,
	0x58, 0x49, 0x53, 0x54, 0x10, 0x04, 0x22, 0x32, 0x0a, 0x09, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x45, 0x58, 0x54, 0x10, 0x00, 0x12, 0x0d, 0x0a,
	0x09, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08,
	0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69,
	0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x3b, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v1_model_model_proto_rawDescOnce sync.Once
	file_v1_model_model_proto_rawDescData = file_v1_model_model_proto_rawDesc
)

func file_v1_model_model_proto_rawDescGZIP() []byte {
	file_v1_model_model_proto_rawDescOnce.Do(func() {
		file_v1_model_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_v1_model_model_proto_rawDescData)
	})
	return file_v1_model_model_proto_rawDescData
}

var file_v1_model_model_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_v1_model_model_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_v1_model_model_proto_goTypes = []interface{}{
	(MatchString_MatchStringType)(0), // 0: api.v1.model.MatchString.MatchStringType
	(MatchString_ValueType)(0),       // 1: api.v1.model.MatchString.ValueType
	(*Location)(nil),                 // 2: api.v1.model.Location
	(*MatchString)(nil),              // 3: api.v1.model.MatchString
	(*wrapperspb.StringValue)(nil),   // 4: google.protobuf.StringValue
}
var file_v1_model_model_proto_depIdxs = []int32{
	4, // 0: api.v1.model.Location.region:type_name -> google.protobuf.StringValue
	4, // 1: api.v1.model.Location.zone:type_name -> google.protobuf.StringValue
	4, // 2: api.v1.model.Location.campus:type_name -> google.protobuf.StringValue
	0, // 3: api.v1.model.MatchString.type:type_name -> api.v1.model.MatchString.MatchStringType
	4, // 4: api.v1.model.MatchString.value:type_name -> google.protobuf.StringValue
	1, // 5: api.v1.model.MatchString.value_type:type_name -> api.v1.model.MatchString.ValueType
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_v1_model_model_proto_init() }
func file_v1_model_model_proto_init() {
	if File_v1_model_model_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_v1_model_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Location); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_model_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchString); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_model_model_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v1_model_model_proto_goTypes,
		DependencyIndexes: file_v1_model_model_proto_depIdxs,
		EnumInfos:         file_v1_model_model_proto_enumTypes,
		MessageInfos:      file_v1_model_model_proto_msgTypes,
	}.Build()
	File_v1_model_model_proto = out.File
	file_v1_model_model_proto_rawDesc = nil
	file_v1_model_model_proto_goTypes = nil
	file_v1_model_model_proto_depIdxs = nil
}
