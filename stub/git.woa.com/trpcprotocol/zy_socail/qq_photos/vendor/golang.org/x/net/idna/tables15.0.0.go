// Code generated by running "go generate" in golang.org/x/text. DO NOT EDIT.

//go:build go1.21

package idna

// UnicodeVersion is the Unicode version from which the tables in this package are derived.
const UnicodeVersion = "15.0.0"

var mappings string = "" + // Size: 6704 bytes
	"  ̈a ̄23 ́ ̧1o1⁄41⁄23⁄4i̇l·ʼnsdžⱥⱦhjrwy ̆ ̇ ̊ ̨ ̃ ̋lẍ́ ι; ̈́եւاٴوٴۇٴيٴक" +
	"़ख़ग़ज़ड़ढ़फ़य़ড়ঢ়য়ਲ਼ਸ਼ਖ਼ਗ਼ਜ਼ਫ਼ଡ଼ଢ଼ําໍາຫນຫມགྷཌྷདྷབྷཛྷཀྵཱཱིུྲྀྲཱྀླྀླཱ" +
	"ཱྀྀྒྷྜྷྡྷྦྷྫྷྐྵвдостъѣæbdeǝgikmnȣptuɐɑəɛɜŋɔɯvβγδφχρнɒcɕðfɟɡɥɨɩɪʝɭʟɱɰɲɳ" +
	"ɴɵɸʂʃƫʉʊʋʌzʐʑʒθssάέήίόύώἀιἁιἂιἃιἄιἅιἆιἇιἠιἡιἢιἣιἤιἥιἦιἧιὠιὡιὢιὣιὤιὥιὦιὧ" +
	"ιὰιαιάιᾶιι ̈͂ὴιηιήιῆι ̓̀ ̓́ ̓͂ΐ ̔̀ ̔́ ̔͂ΰ ̈̀`ὼιωιώιῶι′′′′′‵‵‵‵‵!!???!!?" +
	"′′′′0456789+=()rsħnoqsmtmωåאבגדπ1⁄71⁄91⁄101⁄32⁄31⁄52⁄53⁄54⁄51⁄65⁄61⁄83" +
	"⁄85⁄87⁄81⁄iiivviviiiixxi0⁄3∫∫∫∫∫∮∮∮∮∮1011121314151617181920(10)(11)(12" +
	")(13)(14)(15)(16)(17)(18)(19)(20)∫∫∫∫==⫝̸ɫɽȿɀ. ゙ ゚よりコト(ᄀ)(ᄂ)(ᄃ)(ᄅ)(ᄆ)(ᄇ)" +
	"(ᄉ)(ᄋ)(ᄌ)(ᄎ)(ᄏ)(ᄐ)(ᄑ)(ᄒ)(가)(나)(다)(라)(마)(바)(사)(아)(자)(차)(카)(타)(파)(하)(주)(오전" +
	")(오후)(一)(二)(三)(四)(五)(六)(七)(八)(九)(十)(月)(火)(水)(木)(金)(土)(日)(株)(有)(社)(名)(特)(" +
	"財)(祝)(労)(代)(呼)(学)(監)(企)(資)(協)(祭)(休)(自)(至)21222324252627282930313233343" +
	"5참고주의3637383940414243444546474849501月2月3月4月5月6月7月8月9月10月11月12月hgev令和アパート" +
	"アルファアンペアアールイニングインチウォンエスクードエーカーオンスオームカイリカラットカロリーガロンガンマギガギニーキュリーギルダーキロキロ" +
	"グラムキロメートルキロワットグラムグラムトンクルゼイロクローネケースコルナコーポサイクルサンチームシリングセンチセントダースデシドルトンナノ" +
	"ノットハイツパーセントパーツバーレルピアストルピクルピコビルファラッドフィートブッシェルフランヘクタールペソペニヒヘルツペンスページベータポ" +
	"イントボルトホンポンドホールホーンマイクロマイルマッハマルクマンションミクロンミリミリバールメガメガトンメートルヤードヤールユアンリットルリ" +
	"ラルピールーブルレムレントゲンワット0点1点2点3点4点5点6点7点8点9点10点11点12点13点14点15点16点17点18点19点20" +
	"点21点22点23点24点daauovpcdmiu平成昭和大正明治株式会社panamakakbmbgbkcalpfnfmgkghzmldlk" +
	"lfmnmmmcmkmm2m3m∕sm∕s2rad∕srad∕s2psnsmspvnvmvkvpwnwmwkwbqcccdc∕kgdbgyhah" +
	"pinkkktlmlnlxphprsrsvwbv∕ma∕m1日2日3日4日5日6日7日8日9日10日11日12日13日14日15日16日17日1" +
	"8日19日20日21日22日23日24日25日26日27日28日29日30日31日ьɦɬʞʇœʍ𤋮𢡊𢡄𣏕𥉉𥳐𧻓fffiflstմնմեմիվնմ" +
	"խיִײַעהכלםרתשׁשׂשּׁשּׂאַאָאּבּגּדּהּוּזּטּיּךּכּלּמּנּסּףּפּצּקּרּשּתּו" +
	"ֹבֿכֿפֿאלٱٻپڀٺٿٹڤڦڄڃچڇڍڌڎڈژڑکگڳڱںڻۀہھےۓڭۇۆۈۋۅۉېىئائەئوئۇئۆئۈئېئىیئجئحئم" +
	"ئيبجبحبخبمبىبيتجتحتختمتىتيثجثمثىثيجحجمحجحمخجخحخمسجسحسخسمصحصمضجضحضخضمطحط" +
	"مظمعجعمغجغمفجفحفخفمفىفيقحقمقىقيكاكجكحكخكلكمكىكيلجلحلخلملىليمجمحمخمممىمي" +
	"نجنحنخنمنىنيهجهمهىهييجيحيخيميىييذٰرٰىٰ ٌّ ٍّ َّ ُّ ِّ ّٰئرئزئنبربزبنترت" +
	"زتنثرثزثنمانرنزننيريزينئخئهبهتهصخلهنههٰيهثهسهشمشهـَّـُّـِّطىطيعىعيغىغيس" +
	"ىسيشىشيحىحيجىجيخىخيصىصيضىضيشجشحشخشرسرصرضراًتجمتحجتحمتخمتمجتمحتمخجمححميح" +
	"مىسحجسجحسجىسمحسمجسممصححصممشحمشجيشمخشممضحىضخمطمحطممطميعجمعممعمىغممغميغمى" +
	"فخمقمحقمملحملحيلحىلججلخملمحمحجمحممحيمجحمجممخجمخممجخهمجهممنحمنحىنجمنجىنم" +
	"ينمىيممبخيتجيتجىتخيتخىتميتمىجميجحىجمىسخىصحيشحيضحيلجيلمييحييجييميمميقمين" +
	"حيعميكمينجحمخيلجمكممجحيحجيمجيفميبحيسخينجيصلےقلےاللهاكبرمحمدصلعمرسولعليه" +
	"وسلمصلىصلى الله عليه وسلمجل جلالهریال,:!?_{}[]#&*-<>\\$%@ـًـَـُـِـّـْءآ" +
	"أؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهويلآلألإلا\x22'/^|~¢£¬¦¥ːˑʙɓʣꭦʥʤɖɗᶑɘɞʩɤɢ" +
	"ɠʛʜɧʄʪʫꞎɮʎøɶɷɺɾʀʨʦꭧʧʈⱱʏʡʢʘǀǁǂ𝅗𝅥𝅘𝅥𝅘𝅥𝅮𝅘𝅥𝅯𝅘𝅥𝅰𝅘𝅥𝅱𝅘𝅥𝅲𝆹𝅥𝆺𝅥𝆹𝅥𝅮𝆺𝅥𝅮𝆹𝅥𝅯𝆺𝅥𝅯ıȷαεζηκ" +
	"λμνξοστυψ∇∂ϝабгежзиклмпруфхцчшыэюꚉәіјөүӏґѕџҫꙑұٮڡٯ0,1,2,3,4,5,6,7,8,9,(a" +
	")(b)(c)(d)(e)(f)(g)(h)(i)(j)(k)(l)(m)(n)(o)(p)(q)(r)(s)(t)(u)(v)(w)(x)(y" +
	")(z)〔s〕wzhvsdppvwcmcmdmrdjほかココサ手字双デ二多解天交映無料前後再新初終生販声吹演投捕一三遊左中右指走打禁空合満有月申" +
	"割営配〔本〕〔三〕〔二〕〔安〕〔点〕〔打〕〔盗〕〔勝〕〔敗〕得可丽丸乁你侮侻倂偺備僧像㒞免兔兤具㒹內冗冤仌冬况凵刃㓟刻剆剷㔕勇勉勤勺包匆北卉" +
	"卑博即卽卿灰及叟叫叱吆咞吸呈周咢哶唐啓啣善喙喫喳嗂圖嘆圗噑噴切壮城埴堍型堲報墬売壷夆夢奢姬娛娧姘婦㛮嬈嬾寃寘寧寳寿将尢㞁屠屮峀岍嵃嵮嵫嵼巡巢" +
	"㠯巽帨帽幩㡢㡼庰庳庶廊廾舁弢㣇形彫㣣徚忍志忹悁㤺㤜悔惇慈慌慎慺憎憲憤憯懞懲懶成戛扝抱拔捐挽拼捨掃揤搢揅掩㨮摩摾撝摷㩬敏敬旣書晉㬙暑㬈㫤冒冕最" +
	"暜肭䏙朗望朡杞杓㭉柺枅桒梅梎栟椔㮝楂榣槪檨櫛㰘次歔㱎歲殟殺殻汎沿泍汧洖派海流浩浸涅洴港湮㴳滋滇淹潮濆瀹瀞瀛㶖灊災灷炭煅熜爨爵牐犀犕獺王㺬玥㺸" +
	"瑇瑜瑱璅瓊㼛甤甾異瘐㿼䀈直眞真睊䀹瞋䁆䂖硎碌磌䃣祖福秫䄯穀穊穏䈂篆築䈧糒䊠糨糣紀絣䌁緇縂繅䌴䍙罺羕翺者聠聰䏕育脃䐋脾媵舄辞䑫芑芋芝劳花芳芽苦" +
	"若茝荣莭茣莽菧著荓菊菌菜䔫蓱蓳蔖蕤䕝䕡䕫虐虜虧虩蚩蚈蜎蛢蝹蜨蝫螆蟡蠁䗹衠衣裗裞䘵裺㒻䚾䛇誠諭變豕貫賁贛起跋趼跰軔輸邔郱鄑鄛鈸鋗鋘鉼鏹鐕開䦕閷" +
	"䧦雃嶲霣䩮䩶韠䪲頋頩飢䬳餩馧駂駾䯎鬒鱀鳽䳎䳭鵧䳸麻䵖黹黾鼅鼏鼖鼻"

var mappingIndex = []uint16{ // 1729 elements
	// Entry 0 - 3F
	0x0000, 0x0000, 0x0001, 0x0004, 0x0005, 0x0008, 0x0009, 0x000a,
	0x000d, 0x0010, 0x0011, 0x0012, 0x0017, 0x001c, 0x0021, 0x0024,
	0x0027, 0x002a, 0x002b, 0x002e, 0x0031, 0x0034, 0x0035, 0x0036,
	0x0037, 0x0038, 0x0039, 0x003c, 0x003f, 0x0042, 0x0045, 0x0048,
	0x004b, 0x004c, 0x004d, 0x0051, 0x0054, 0x0055, 0x005a, 0x005e,
	0x0062, 0x0066, 0x006a, 0x006e, 0x0074, 0x007a, 0x0080, 0x0086,
	0x008c, 0x0092, 0x0098, 0x009e, 0x00a4, 0x00aa, 0x00b0, 0x00b6,
	0x00bc, 0x00c2, 0x00c8, 0x00ce, 0x00d4, 0x00da, 0x00e0, 0x00e6,
	// Entry 40 - 7F
	0x00ec, 0x00f2, 0x00f8, 0x00fe, 0x0104, 0x010a, 0x0110, 0x0116,
	0x011c, 0x0122, 0x0128, 0x012e, 0x0137, 0x013d, 0x0146, 0x014c,
	0x0152, 0x0158, 0x015e, 0x0164, 0x016a, 0x0170, 0x0172, 0x0174,
	0x0176, 0x0178, 0x017a, 0x017c, 0x017e, 0x0180, 0x0181, 0x0182,
	0x0183, 0x0185, 0x0186, 0x0187, 0x0188, 0x0189, 0x018a, 0x018c,
	0x018d, 0x018e, 0x018f, 0x0191, 0x0193, 0x0195, 0x0197, 0x0199,
	0x019b, 0x019d, 0x019f, 0x01a0, 0x01a2, 0x01a4, 0x01a6, 0x01a8,
	0x01aa, 0x01ac, 0x01ae, 0x01b0, 0x01b1, 0x01b3, 0x01b5, 0x01b6,
	// Entry 80 - BF
	0x01b8, 0x01ba, 0x01bc, 0x01be, 0x01c0, 0x01c2, 0x01c4, 0x01c6,
	0x01c8, 0x01ca, 0x01cc, 0x01ce, 0x01d0, 0x01d2, 0x01d4, 0x01d6,
	0x01d8, 0x01da, 0x01dc, 0x01de, 0x01e0, 0x01e2, 0x01e4, 0x01e5,
	0x01e7, 0x01e9, 0x01eb, 0x01ed, 0x01ef, 0x01f1, 0x01f3, 0x01f5,
	0x01f7, 0x01f9, 0x01fb, 0x01fd, 0x0202, 0x0207, 0x020c, 0x0211,
	0x0216, 0x021b, 0x0220, 0x0225, 0x022a, 0x022f, 0x0234, 0x0239,
	0x023e, 0x0243, 0x0248, 0x024d, 0x0252, 0x0257, 0x025c, 0x0261,
	0x0266, 0x026b, 0x0270, 0x0275, 0x027a, 0x027e, 0x0282, 0x0287,
	// Entry C0 - FF
	0x0289, 0x028e, 0x0293, 0x0297, 0x029b, 0x02a0, 0x02a5, 0x02aa,
	0x02af, 0x02b1, 0x02b6, 0x02bb, 0x02c0, 0x02c2, 0x02c7, 0x02c8,
	0x02cd, 0x02d1, 0x02d5, 0x02da, 0x02e0, 0x02e9, 0x02ef, 0x02f8,
	0x02fa, 0x02fc, 0x02fe, 0x0300, 0x030c, 0x030d, 0x030e, 0x030f,
	0x0310, 0x0311, 0x0312, 0x0313, 0x0314, 0x0315, 0x0316, 0x0317,
	0x0319, 0x031b, 0x031d, 0x031e, 0x0320, 0x0322, 0x0324, 0x0326,
	0x0328, 0x032a, 0x032c, 0x032e, 0x0330, 0x0335, 0x033a, 0x0340,
	0x0345, 0x034a, 0x034f, 0x0354, 0x0359, 0x035e, 0x0363, 0x0368,
	// Entry 100 - 13F
	0x036d, 0x0372, 0x0377, 0x037c, 0x0380, 0x0382, 0x0384, 0x0386,
	0x038a, 0x038c, 0x038e, 0x0393, 0x0399, 0x03a2, 0x03a8, 0x03b1,
	0x03b3, 0x03b5, 0x03b7, 0x03b9, 0x03bb, 0x03bd, 0x03bf, 0x03c1,
	0x03c3, 0x03c5, 0x03c7, 0x03cb, 0x03cf, 0x03d3, 0x03d7, 0x03db,
	0x03df, 0x03e3, 0x03e7, 0x03eb, 0x03ef, 0x03f3, 0x03ff, 0x0401,
	0x0406, 0x0408, 0x040a, 0x040c, 0x040e, 0x040f, 0x0413, 0x0417,
	0x041d, 0x0423, 0x0428, 0x042d, 0x0432, 0x0437, 0x043c, 0x0441,
	0x0446, 0x044b, 0x0450, 0x0455, 0x045a, 0x045f, 0x0464, 0x0469,
	// Entry 140 - 17F
	0x046e, 0x0473, 0x0478, 0x047d, 0x0482, 0x0487, 0x048c, 0x0491,
	0x0496, 0x049b, 0x04a0, 0x04a5, 0x04aa, 0x04af, 0x04b4, 0x04bc,
	0x04c4, 0x04c9, 0x04ce, 0x04d3, 0x04d8, 0x04dd, 0x04e2, 0x04e7,
	0x04ec, 0x04f1, 0x04f6, 0x04fb, 0x0500, 0x0505, 0x050a, 0x050f,
	0x0514, 0x0519, 0x051e, 0x0523, 0x0528, 0x052d, 0x0532, 0x0537,
	0x053c, 0x0541, 0x0546, 0x054b, 0x0550, 0x0555, 0x055a, 0x055f,
	0x0564, 0x0569, 0x056e, 0x0573, 0x0578, 0x057a, 0x057c, 0x057e,
	0x0580, 0x0582, 0x0584, 0x0586, 0x0588, 0x058a, 0x058c, 0x058e,
	// Entry 180 - 1BF
	0x0590, 0x0592, 0x0594, 0x0596, 0x059c, 0x05a2, 0x05a4, 0x05a6,
	0x05a8, 0x05aa, 0x05ac, 0x05ae, 0x05b0, 0x05b2, 0x05b4, 0x05b6,
	0x05b8, 0x05ba, 0x05bc, 0x05be, 0x05c0, 0x05c4, 0x05c8, 0x05cc,
	0x05d0, 0x05d4, 0x05d8, 0x05dc, 0x05e0, 0x05e4, 0x05e9, 0x05ee,
	0x05f3, 0x05f5, 0x05f7, 0x05fd, 0x0609, 0x0615, 0x0621, 0x062a,
	0x0636, 0x063f, 0x0648, 0x0657, 0x0663, 0x066c, 0x0675, 0x067e,
	0x068a, 0x0696, 0x069f, 0x06a8, 0x06ae, 0x06b7, 0x06c3, 0x06cf,
	0x06d5, 0x06e4, 0x06f6, 0x0705, 0x070e, 0x071d, 0x072c, 0x0738,
	// Entry 1C0 - 1FF
	0x0741, 0x074a, 0x0753, 0x075f, 0x076e, 0x077a, 0x0783, 0x078c,
	0x0795, 0x079b, 0x07a1, 0x07a7, 0x07ad, 0x07b6, 0x07bf, 0x07ce,
	0x07d7, 0x07e3, 0x07f2, 0x07fb, 0x0801, 0x0807, 0x0816, 0x0822,
	0x0831, 0x083a, 0x0849, 0x084f, 0x0858, 0x0861, 0x086a, 0x0873,
	0x087c, 0x0888, 0x0891, 0x0897, 0x08a0, 0x08a9, 0x08b2, 0x08be,
	0x08c7, 0x08d0, 0x08d9, 0x08e8, 0x08f4, 0x08fa, 0x0909, 0x090f,
	0x091b, 0x0927, 0x0930, 0x0939, 0x0942, 0x094e, 0x0954, 0x095d,
	0x0969, 0x096f, 0x097e, 0x0987, 0x098b, 0x098f, 0x0993, 0x0997,
	// Entry 200 - 23F
	0x099b, 0x099f, 0x09a3, 0x09a7, 0x09ab, 0x09af, 0x09b4, 0x09b9,
	0x09be, 0x09c3, 0x09c8, 0x09cd, 0x09d2, 0x09d7, 0x09dc, 0x09e1,
	0x09e6, 0x09eb, 0x09f0, 0x09f5, 0x09fa, 0x09fc, 0x09fe, 0x0a00,
	0x0a02, 0x0a04, 0x0a06, 0x0a0c, 0x0a12, 0x0a18, 0x0a1e, 0x0a2a,
	0x0a2c, 0x0a2e, 0x0a30, 0x0a32, 0x0a34, 0x0a36, 0x0a38, 0x0a3c,
	0x0a3e, 0x0a40, 0x0a42, 0x0a44, 0x0a46, 0x0a48, 0x0a4a, 0x0a4c,
	0x0a4e, 0x0a50, 0x0a52, 0x0a54, 0x0a56, 0x0a58, 0x0a5a, 0x0a5f,
	0x0a65, 0x0a6c, 0x0a74, 0x0a76, 0x0a78, 0x0a7a, 0x0a7c, 0x0a7e,
	// Entry 240 - 27F
	0x0a80, 0x0a82, 0x0a84, 0x0a86, 0x0a88, 0x0a8a, 0x0a8c, 0x0a8e,
	0x0a90, 0x0a96, 0x0a98, 0x0a9a, 0x0a9c, 0x0a9e, 0x0aa0, 0x0aa2,
	0x0aa4, 0x0aa6, 0x0aa8, 0x0aaa, 0x0aac, 0x0aae, 0x0ab0, 0x0ab2,
	0x0ab4, 0x0ab9, 0x0abe, 0x0ac2, 0x0ac6, 0x0aca, 0x0ace, 0x0ad2,
	0x0ad6, 0x0ada, 0x0ade, 0x0ae2, 0x0ae7, 0x0aec, 0x0af1, 0x0af6,
	0x0afb, 0x0b00, 0x0b05, 0x0b0a, 0x0b0f, 0x0b14, 0x0b19, 0x0b1e,
	0x0b23, 0x0b28, 0x0b2d, 0x0b32, 0x0b37, 0x0b3c, 0x0b41, 0x0b46,
	0x0b4b, 0x0b50, 0x0b52, 0x0b54, 0x0b56, 0x0b58, 0x0b5a, 0x0b5c,
	// Entry 280 - 2BF
	0x0b5e, 0x0b62, 0x0b66, 0x0b6a, 0x0b6e, 0x0b72, 0x0b76, 0x0b7a,
	0x0b7c, 0x0b7e, 0x0b80, 0x0b82, 0x0b86, 0x0b8a, 0x0b8e, 0x0b92,
	0x0b96, 0x0b9a, 0x0b9e, 0x0ba0, 0x0ba2, 0x0ba4, 0x0ba6, 0x0ba8,
	0x0baa, 0x0bac, 0x0bb0, 0x0bb4, 0x0bba, 0x0bc0, 0x0bc4, 0x0bc8,
	0x0bcc, 0x0bd0, 0x0bd4, 0x0bd8, 0x0bdc, 0x0be0, 0x0be4, 0x0be8,
	0x0bec, 0x0bf0, 0x0bf4, 0x0bf8, 0x0bfc, 0x0c00, 0x0c04, 0x0c08,
	0x0c0c, 0x0c10, 0x0c14, 0x0c18, 0x0c1c, 0x0c20, 0x0c24, 0x0c28,
	0x0c2c, 0x0c30, 0x0c34, 0x0c36, 0x0c38, 0x0c3a, 0x0c3c, 0x0c3e,
	// Entry 2C0 - 2FF
	0x0c40, 0x0c42, 0x0c44, 0x0c46, 0x0c48, 0x0c4a, 0x0c4c, 0x0c4e,
	0x0c50, 0x0c52, 0x0c54, 0x0c56, 0x0c58, 0x0c5a, 0x0c5c, 0x0c5e,
	0x0c60, 0x0c62, 0x0c64, 0x0c66, 0x0c68, 0x0c6a, 0x0c6c, 0x0c6e,
	0x0c70, 0x0c72, 0x0c74, 0x0c76, 0x0c78, 0x0c7a, 0x0c7c, 0x0c7e,
	0x0c80, 0x0c82, 0x0c86, 0x0c8a, 0x0c8e, 0x0c92, 0x0c96, 0x0c9a,
	0x0c9e, 0x0ca2, 0x0ca4, 0x0ca8, 0x0cac, 0x0cb0, 0x0cb4, 0x0cb8,
	0x0cbc, 0x0cc0, 0x0cc4, 0x0cc8, 0x0ccc, 0x0cd0, 0x0cd4, 0x0cd8,
	0x0cdc, 0x0ce0, 0x0ce4, 0x0ce8, 0x0cec, 0x0cf0, 0x0cf4, 0x0cf8,
	// Entry 300 - 33F
	0x0cfc, 0x0d00, 0x0d04, 0x0d08, 0x0d0c, 0x0d10, 0x0d14, 0x0d18,
	0x0d1c, 0x0d20, 0x0d24, 0x0d28, 0x0d2c, 0x0d30, 0x0d34, 0x0d38,
	0x0d3c, 0x0d40, 0x0d44, 0x0d48, 0x0d4c, 0x0d50, 0x0d54, 0x0d58,
	0x0d5c, 0x0d60, 0x0d64, 0x0d68, 0x0d6c, 0x0d70, 0x0d74, 0x0d78,
	0x0d7c, 0x0d80, 0x0d84, 0x0d88, 0x0d8c, 0x0d90, 0x0d94, 0x0d98,
	0x0d9c, 0x0da0, 0x0da4, 0x0da8, 0x0dac, 0x0db0, 0x0db4, 0x0db8,
	0x0dbc, 0x0dc0, 0x0dc4, 0x0dc8, 0x0dcc, 0x0dd0, 0x0dd4, 0x0dd8,
	0x0ddc, 0x0de0, 0x0de4, 0x0de8, 0x0dec, 0x0df0, 0x0df4, 0x0df8,
	// Entry 340 - 37F
	0x0dfc, 0x0e00, 0x0e04, 0x0e08, 0x0e0c, 0x0e10, 0x0e14, 0x0e18,
	0x0e1d, 0x0e22, 0x0e27, 0x0e2c, 0x0e31, 0x0e36, 0x0e3a, 0x0e3e,
	0x0e42, 0x0e46, 0x0e4a, 0x0e4e, 0x0e52, 0x0e56, 0x0e5a, 0x0e5e,
	0x0e62, 0x0e66, 0x0e6a, 0x0e6e, 0x0e72, 0x0e76, 0x0e7a, 0x0e7e,
	0x0e82, 0x0e86, 0x0e8a, 0x0e8e, 0x0e92, 0x0e96, 0x0e9a, 0x0e9e,
	0x0ea2, 0x0ea6, 0x0eaa, 0x0eae, 0x0eb2, 0x0eb6, 0x0ebc, 0x0ec2,
	0x0ec8, 0x0ecc, 0x0ed0, 0x0ed4, 0x0ed8, 0x0edc, 0x0ee0, 0x0ee4,
	0x0ee8, 0x0eec, 0x0ef0, 0x0ef4, 0x0ef8, 0x0efc, 0x0f00, 0x0f04,
	// Entry 380 - 3BF
	0x0f08, 0x0f0c, 0x0f10, 0x0f14, 0x0f18, 0x0f1c, 0x0f20, 0x0f24,
	0x0f28, 0x0f2c, 0x0f30, 0x0f34, 0x0f38, 0x0f3e, 0x0f44, 0x0f4a,
	0x0f50, 0x0f56, 0x0f5c, 0x0f62, 0x0f68, 0x0f6e, 0x0f74, 0x0f7a,
	0x0f80, 0x0f86, 0x0f8c, 0x0f92, 0x0f98, 0x0f9e, 0x0fa4, 0x0faa,
	0x0fb0, 0x0fb6, 0x0fbc, 0x0fc2, 0x0fc8, 0x0fce, 0x0fd4, 0x0fda,
	0x0fe0, 0x0fe6, 0x0fec, 0x0ff2, 0x0ff8, 0x0ffe, 0x1004, 0x100a,
	0x1010, 0x1016, 0x101c, 0x1022, 0x1028, 0x102e, 0x1034, 0x103a,
	0x1040, 0x1046, 0x104c, 0x1052, 0x1058, 0x105e, 0x1064, 0x106a,
	// Entry 3C0 - 3FF
	0x1070, 0x1076, 0x107c, 0x1082, 0x1088, 0x108e, 0x1094, 0x109a,
	0x10a0, 0x10a6, 0x10ac, 0x10b2, 0x10b8, 0x10be, 0x10c4, 0x10ca,
	0x10d0, 0x10d6, 0x10dc, 0x10e2, 0x10e8, 0x10ee, 0x10f4, 0x10fa,
	0x1100, 0x1106, 0x110c, 0x1112, 0x1118, 0x111e, 0x1124, 0x112a,
	0x1130, 0x1136, 0x113c, 0x1142, 0x1148, 0x114e, 0x1154, 0x115a,
	0x1160, 0x1166, 0x116c, 0x1172, 0x1178, 0x1180, 0x1188, 0x1190,
	0x1198, 0x11a0, 0x11a8, 0x11b0, 0x11b6, 0x11d7, 0x11e6, 0x11ee,
	0x11ef, 0x11f0, 0x11f1, 0x11f2, 0x11f3, 0x11f4, 0x11f5, 0x11f6,
	// Entry 400 - 43F
	0x11f7, 0x11f8, 0x11f9, 0x11fa, 0x11fb, 0x11fc, 0x11fd, 0x11fe,
	0x11ff, 0x1200, 0x1201, 0x1205, 0x1209, 0x120d, 0x1211, 0x1215,
	0x1219, 0x121b, 0x121d, 0x121f, 0x1221, 0x1223, 0x1225, 0x1227,
	0x1229, 0x122b, 0x122d, 0x122f, 0x1231, 0x1233, 0x1235, 0x1237,
	0x1239, 0x123b, 0x123d, 0x123f, 0x1241, 0x1243, 0x1245, 0x1247,
	0x1249, 0x124b, 0x124d, 0x124f, 0x1251, 0x1253, 0x1255, 0x1257,
	0x1259, 0x125b, 0x125d, 0x125f, 0x1263, 0x1267, 0x126b, 0x126f,
	0x1270, 0x1271, 0x1272, 0x1273, 0x1274, 0x1275, 0x1277, 0x1279,
	// Entry 440 - 47F
	0x127b, 0x127d, 0x127f, 0x1281, 0x1283, 0x1285, 0x1287, 0x1289,
	0x128c, 0x128e, 0x1290, 0x1292, 0x1294, 0x1297, 0x1299, 0x129b,
	0x129d, 0x129f, 0x12a1, 0x12a3, 0x12a5, 0x12a7, 0x12a9, 0x12ab,
	0x12ad, 0x12af, 0x12b2, 0x12b4, 0x12b6, 0x12b8, 0x12ba, 0x12bc,
	0x12be, 0x12c0, 0x12c2, 0x12c4, 0x12c6, 0x12c9, 0x12cb, 0x12cd,
	0x12d0, 0x12d2, 0x12d4, 0x12d6, 0x12d8, 0x12da, 0x12dc, 0x12de,
	0x12e6, 0x12ee, 0x12fa, 0x1306, 0x1312, 0x131e, 0x132a, 0x1332,
	0x133a, 0x1346, 0x1352, 0x135e, 0x136a, 0x136c, 0x136e, 0x1370,
	// Entry 480 - 4BF
	0x1372, 0x1374, 0x1376, 0x1378, 0x137a, 0x137c, 0x137e, 0x1380,
	0x1382, 0x1384, 0x1386, 0x1388, 0x138a, 0x138d, 0x1390, 0x1392,
	0x1394, 0x1396, 0x1398, 0x139a, 0x139c, 0x139e, 0x13a0, 0x13a2,
	0x13a4, 0x13a6, 0x13a8, 0x13aa, 0x13ac, 0x13ae, 0x13b0, 0x13b2,
	0x13b4, 0x13b6, 0x13b8, 0x13ba, 0x13bc, 0x13bf, 0x13c1, 0x13c3,
	0x13c5, 0x13c7, 0x13c9, 0x13cb, 0x13cd, 0x13cf, 0x13d1, 0x13d3,
	0x13d6, 0x13d8, 0x13da, 0x13dc, 0x13de, 0x13e0, 0x13e2, 0x13e4,
	0x13e6, 0x13e8, 0x13ea, 0x13ec, 0x13ee, 0x13f0, 0x13f2, 0x13f5,
	// Entry 4C0 - 4FF
	0x13f8, 0x13fb, 0x13fe, 0x1401, 0x1404, 0x1407, 0x140a, 0x140d,
	0x1410, 0x1413, 0x1416, 0x1419, 0x141c, 0x141f, 0x1422, 0x1425,
	0x1428, 0x142b, 0x142e, 0x1431, 0x1434, 0x1437, 0x143a, 0x143d,
	0x1440, 0x1447, 0x1449, 0x144b, 0x144d, 0x1450, 0x1452, 0x1454,
	0x1456, 0x1458, 0x145a, 0x1460, 0x1466, 0x1469, 0x146c, 0x146f,
	0x1472, 0x1475, 0x1478, 0x147b, 0x147e, 0x1481, 0x1484, 0x1487,
	0x148a, 0x148d, 0x1490, 0x1493, 0x1496, 0x1499, 0x149c, 0x149f,
	0x14a2, 0x14a5, 0x14a8, 0x14ab, 0x14ae, 0x14b1, 0x14b4, 0x14b7,
	// Entry 500 - 53F
	0x14ba, 0x14bd, 0x14c0, 0x14c3, 0x14c6, 0x14c9, 0x14cc, 0x14cf,
	0x14d2, 0x14d5, 0x14d8, 0x14db, 0x14de, 0x14e1, 0x14e4, 0x14e7,
	0x14ea, 0x14ed, 0x14f6, 0x14ff, 0x1508, 0x1511, 0x151a, 0x1523,
	0x152c, 0x1535, 0x153e, 0x1541, 0x1544, 0x1547, 0x154a, 0x154d,
	0x1550, 0x1553, 0x1556, 0x1559, 0x155c, 0x155f, 0x1562, 0x1565,
	0x1568, 0x156b, 0x156e, 0x1571, 0x1574, 0x1577, 0x157a, 0x157d,
	0x1580, 0x1583, 0x1586, 0x1589, 0x158c, 0x158f, 0x1592, 0x1595,
	0x1598, 0x159b, 0x159e, 0x15a1, 0x15a4, 0x15a7, 0x15aa, 0x15ad,
	// Entry 540 - 57F
	0x15b0, 0x15b3, 0x15b6, 0x15b9, 0x15bc, 0x15bf, 0x15c2, 0x15c5,
	0x15c8, 0x15cb, 0x15ce, 0x15d1, 0x15d4, 0x15d7, 0x15da, 0x15dd,
	0x15e0, 0x15e3, 0x15e6, 0x15e9, 0x15ec, 0x15ef, 0x15f2, 0x15f5,
	0x15f8, 0x15fb, 0x15fe, 0x1601, 0x1604, 0x1607, 0x160a, 0x160d,
	0x1610, 0x1613, 0x1616, 0x1619, 0x161c, 0x161f, 0x1622, 0x1625,
	0x1628, 0x162b, 0x162e, 0x1631, 0x1634, 0x1637, 0x163a, 0x163d,
	0x1640, 0x1643, 0x1646, 0x1649, 0x164c, 0x164f, 0x1652, 0x1655,
	0x1658, 0x165b, 0x165e, 0x1661, 0x1664, 0x1667, 0x166a, 0x166d,
	// Entry 580 - 5BF
	0x1670, 0x1673, 0x1676, 0x1679, 0x167c, 0x167f, 0x1682, 0x1685,
	0x1688, 0x168b, 0x168e, 0x1691, 0x1694, 0x1697, 0x169a, 0x169d,
	0x16a0, 0x16a3, 0x16a6, 0x16a9, 0x16ac, 0x16af, 0x16b2, 0x16b5,
	0x16b8, 0x16bb, 0x16be, 0x16c1, 0x16c4, 0x16c7, 0x16ca, 0x16cd,
	0x16d0, 0x16d3, 0x16d6, 0x16d9, 0x16dc, 0x16df, 0x16e2, 0x16e5,
	0x16e8, 0x16eb, 0x16ee, 0x16f1, 0x16f4, 0x16f7, 0x16fa, 0x16fd,
	0x1700, 0x1703, 0x1706, 0x1709, 0x170c, 0x170f, 0x1712, 0x1715,
	0x1718, 0x171b, 0x171e, 0x1721, 0x1724, 0x1727, 0x172a, 0x172d,
	// Entry 5C0 - 5FF
	0x1730, 0x1733, 0x1736, 0x1739, 0x173c, 0x173f, 0x1742, 0x1745,
	0x1748, 0x174b, 0x174e, 0x1751, 0x1754, 0x1757, 0x175a, 0x175d,
	0x1760, 0x1763, 0x1766, 0x1769, 0x176c, 0x176f, 0x1772, 0x1775,
	0x1778, 0x177b, 0x177e, 0x1781, 0x1784, 0x1787, 0x178a, 0x178d,
	0x1790, 0x1793, 0x1796, 0x1799, 0x179c, 0x179f, 0x17a2, 0x17a5,
	0x17a8, 0x17ab, 0x17ae, 0x17b1, 0x17b4, 0x17b7, 0x17ba, 0x17bd,
	0x17c0, 0x17c3, 0x17c6, 0x17c9, 0x17cc, 0x17cf, 0x17d2, 0x17d5,
	0x17d8, 0x17db, 0x17de, 0x17e1, 0x17e4, 0x17e7, 0x17ea, 0x17ed,
	// Entry 600 - 63F
	0x17f0, 0x17f3, 0x17f6, 0x17f9, 0x17fc, 0x17ff, 0x1802, 0x1805,
	0x1808, 0x180b, 0x180e, 0x1811, 0x1814, 0x1817, 0x181a, 0x181d,
	0x1820, 0x1823, 0x1826, 0x1829, 0x182c, 0x182f, 0x1832, 0x1835,
	0x1838, 0x183b, 0x183e, 0x1841, 0x1844, 0x1847, 0x184a, 0x184d,
	0x1850, 0x1853, 0x1856, 0x1859, 0x185c, 0x185f, 0x1862, 0x1865,
	0x1868, 0x186b, 0x186e, 0x1871, 0x1874, 0x1877, 0x187a, 0x187d,
	0x1880, 0x1883, 0x1886, 0x1889, 0x188c, 0x188f, 0x1892, 0x1895,
	0x1898, 0x189b, 0x189e, 0x18a1, 0x18a4, 0x18a7, 0x18aa, 0x18ad,
	// Entry 640 - 67F
	0x18b0, 0x18b3, 0x18b6, 0x18b9, 0x18bc, 0x18bf, 0x18c2, 0x18c5,
	0x18c8, 0x18cb, 0x18ce, 0x18d1, 0x18d4, 0x18d7, 0x18da, 0x18dd,
	0x18e0, 0x18e3, 0x18e6, 0x18e9, 0x18ec, 0x18ef, 0x18f2, 0x18f5,
	0x18f8, 0x18fb, 0x18fe, 0x1901, 0x1904, 0x1907, 0x190a, 0x190d,
	0x1910, 0x1913, 0x1916, 0x1919, 0x191c, 0x191f, 0x1922, 0x1925,
	0x1928, 0x192b, 0x192e, 0x1931, 0x1934, 0x1937, 0x193a, 0x193d,
	0x1940, 0x1943, 0x1946, 0x1949, 0x194c, 0x194f, 0x1952, 0x1955,
	0x1958, 0x195b, 0x195e, 0x1961, 0x1964, 0x1967, 0x196a, 0x196d,
	// Entry 680 - 6BF
	0x1970, 0x1973, 0x1976, 0x1979, 0x197c, 0x197f, 0x1982, 0x1985,
	0x1988, 0x198b, 0x198e, 0x1991, 0x1994, 0x1997, 0x199a, 0x199d,
	0x19a0, 0x19a3, 0x19a6, 0x19a9, 0x19ac, 0x19af, 0x19b2, 0x19b5,
	0x19b8, 0x19bb, 0x19be, 0x19c1, 0x19c4, 0x19c7, 0x19ca, 0x19cd,
	0x19d0, 0x19d3, 0x19d6, 0x19d9, 0x19dc, 0x19df, 0x19e2, 0x19e5,
	0x19e8, 0x19eb, 0x19ee, 0x19f1, 0x19f4, 0x19f7, 0x19fa, 0x19fd,
	0x1a00, 0x1a03, 0x1a06, 0x1a09, 0x1a0c, 0x1a0f, 0x1a12, 0x1a15,
	0x1a18, 0x1a1b, 0x1a1e, 0x1a21, 0x1a24, 0x1a27, 0x1a2a, 0x1a2d,
	// Entry 6C0 - 6FF
	0x1a30,
} // Size: 3482 bytes

var xorData string = "" + // Size: 4907 bytes
	"\x02\x0c\x09\x02\xb0\xec\x02\xad\xd8\x02\xad\xd9\x02\x06\x07\x02\x0f\x12" +
	"\x02\x0f\x1f\x02\x0f\x1d\x02\x01\x13\x02\x0f\x16\x02\x0f\x0b\x02\x0f3" +
	"\x02\x0f7\x02\x0f?\x02\x0f/\x02\x0f*\x02\x0c&\x02\x0c*\x02\x0c;\x02\x0c9" +
	"\x02\x0c%\x02\xab\xed\x02\xab\xe2\x02\xab\xe3\x02\xa9\xe0\x02\xa9\xe1" +
	"\x02\xa9\xe6\x02\xa3\xcb\x02\xa3\xc8\x02\xa3\xc9\x02\x01#\x02\x01\x08" +
	"\x02\x0e>\x02\x0e'\x02\x0f\x03\x02\x03\x0d\x02\x03\x09\x02\x03\x17\x02" +
	"\x03\x0e\x02\x02\x03\x02\x011\x02\x01\x00\x02\x01\x10\x02\x03<\x02\x07" +
	"\x0d\x02\x02\x0c\x02\x0c0\x02\x01\x03\x02\x01\x01\x02\x01 \x02\x01\x22" +
	"\x02\x01)\x02\x01\x0a\x02\x01\x0c\x02\x02\x06\x02\x02\x02\x02\x03\x10" +
	"\x03\x037 \x03\x0b+\x03\x021\x00\x02\x01\x04\x02\x01\x02\x02\x019\x02" +
	"\x03\x1c\x02\x02$\x03\x80p$\x02\x03:\x02\x03\x0a\x03\xc1r.\x03\xc1r,\x03" +
	"\xc1r\x02\x02\x02:\x02\x02>\x02\x02,\x02\x02\x10\x02\x02\x00\x03\xc1s<" +
	"\x03\xc1s*\x03\xc2L$\x03\xc2L;\x02\x09)\x02\x0a\x19\x03\x83\xab\xe3\x03" +
	"\x83\xab\xf2\x03 4\xe0\x03\x81\xab\xea\x03\x81\xab\xf3\x03 4\xef\x03\x96" +
	"\xe1\xcd\x03\x84\xe5\xc3\x02\x0d\x11\x03\x8b\xec\xcb\x03\x94\xec\xcf\x03" +
	"\x9a\xec\xc2\x03\x8b\xec\xdb\x03\x94\xec\xdf\x03\x9a\xec\xd2\x03\x01\x0c" +
	"!\x03\x01\x0c#\x03ʠ\x9d\x03ʣ\x9c\x03ʢ\x9f\x03ʥ\x9e\x03ʤ\x91\x03ʧ\x90\x03" +
	"ʦ\x93\x03ʩ\x92\x03ʨ\x95\x03\xca\xf3\xb5\x03\xca\xf0\xb4\x03\xca\xf1\xb7" +
	"\x03\xca\xf6\xb6\x03\xca\xf7\x89\x03\xca\xf4\x88\x03\xca\xf5\x8b\x03\xca" +
	"\xfa\x8a\x03\xca\xfb\x8d\x03\xca\xf8\x8c\x03\xca\xf9\x8f\x03\xca\xfe\x8e" +
	"\x03\xca\xff\x81\x03\xca\xfc\x80\x03\xca\xfd\x83\x03\xca\xe2\x82\x03\xca" +
	"\xe3\x85\x03\xca\xe0\x84\x03\xca\xe1\x87\x03\xca\xe6\x86\x03\xca\xe7\x99" +
	"\x03\xca\xe4\x98\x03\xca\xe5\x9b\x03\xca\xea\x9a\x03\xca\xeb\x9d\x03\xca" +
	"\xe8\x9c\x03ؓ\x89\x03ߔ\x8b\x02\x010\x03\x03\x04\x1e\x03\x04\x15\x12\x03" +
	"\x0b\x05,\x03\x06\x04\x00\x03\x06\x04)\x03\x06\x044\x03\x06\x04<\x03\x06" +
	"\x05\x1d\x03\x06\x06\x00\x03\x06\x06\x0a\x03\x06\x06'\x03\x06\x062\x03" +
	"\x0786\x03\x079/\x03\x079 \x03\x07:\x0e\x03\x07:\x1b\x03\x07:%\x03\x07;/" +
	"\x03\x07;%\x03\x074\x11\x03\x076\x09\x03\x077*\x03\x070\x01\x03\x070\x0f" +
	"\x03\x070.\x03\x071\x16\x03\x071\x04\x03\x0710\x03\x072\x18\x03\x072-" +
	"\x03\x073\x14\x03\x073>\x03\x07'\x09\x03\x07 \x00\x03\x07\x1f\x0b\x03" +
	"\x07\x18#\x03\x07\x18(\x03\x07\x186\x03\x07\x18\x03\x03\x07\x19\x16\x03" +
	"\x07\x116\x03\x07\x12'\x03\x07\x13\x10\x03\x07\x0c&\x03\x07\x0c\x08\x03" +
	"\x07\x0c\x13\x03\x07\x0d\x02\x03\x07\x0d\x1c\x03\x07\x0b5\x03\x07\x0b" +
	"\x0a\x03\x07\x0b\x01\x03\x07\x0b\x0f\x03\x07\x05\x00\x03\x07\x05\x09\x03" +
	"\x07\x05\x0b\x03\x07\x07\x01\x03\x07\x07\x08\x03\x07\x00<\x03\x07\x00+" +
	"\x03\x07\x01)\x03\x07\x01\x1b\x03\x07\x01\x08\x03\x07\x03?\x03\x0445\x03" +
	"\x044\x08\x03\x0454\x03\x04)/\x03\x04)5\x03\x04+\x05\x03\x04+\x14\x03" +
	"\x04+ \x03\x04+<\x03\x04*&\x03\x04*\x22\x03\x04&8\x03\x04!\x01\x03\x04!" +
	"\x22\x03\x04\x11+\x03\x04\x10.\x03\x04\x104\x03\x04\x13=\x03\x04\x12\x04" +
	"\x03\x04\x12\x0a\x03\x04\x0d\x1d\x03\x04\x0d\x07\x03\x04\x0d \x03\x05<>" +
	"\x03\x055<\x03\x055!\x03\x055#\x03\x055&\x03\x054\x1d\x03\x054\x02\x03" +
	"\x054\x07\x03\x0571\x03\x053\x1a\x03\x053\x16\x03\x05.<\x03\x05.\x07\x03" +
	"\x05):\x03\x05)<\x03\x05)\x0c\x03\x05)\x15\x03\x05+-\x03\x05+5\x03\x05$" +
	"\x1e\x03\x05$\x14\x03\x05'\x04\x03\x05'\x14\x03\x05&\x02\x03\x05\x226" +
	"\x03\x05\x22\x0c\x03\x05\x22\x1c\x03\x05\x19\x0a\x03\x05\x1b\x09\x03\x05" +
	"\x1b\x0c\x03\x05\x14\x07\x03\x05\x16?\x03\x05\x16\x0c\x03\x05\x0c\x05" +
	"\x03\x05\x0e\x0f\x03\x05\x01\x0e\x03\x05\x00(\x03\x05\x030\x03\x05\x03" +
	"\x06\x03\x0a==\x03\x0a=1\x03\x0a=,\x03\x0a=\x0c\x03\x0a??\x03\x0a<\x08" +
	"\x03\x0a9!\x03\x0a9)\x03\x0a97\x03\x0a99\x03\x0a6\x0a\x03\x0a6\x1c\x03" +
	"\x0a6\x17\x03\x0a7'\x03\x0a78\x03\x0a73\x03\x0a'\x01\x03\x0a'&\x03\x0a" +
	"\x1f\x0e\x03\x0a\x1f\x03\x03\x0a\x1f3\x03\x0a\x1b/\x03\x0a\x18\x19\x03" +
	"\x0a\x19\x01\x03\x0a\x16\x14\x03\x0a\x0e\x22\x03\x0a\x0f\x10\x03\x0a\x0f" +
	"\x02\x03\x0a\x0f \x03\x0a\x0c\x04\x03\x0a\x0b>\x03\x0a\x0b+\x03\x0a\x08/" +
	"\x03\x0a\x046\x03\x0a\x05\x14\x03\x0a\x00\x04\x03\x0a\x00\x10\x03\x0a" +
	"\x00\x14\x03\x0b<3\x03\x0b;*\x03\x0b9\x22\x03\x0b9)\x03\x0b97\x03\x0b+" +
	"\x10\x03\x0b((\x03\x0b&5\x03\x0b$\x1c\x03\x0b$\x12\x03\x0b%\x04\x03\x0b#" +
	"<\x03\x0b#0\x03\x0b#\x0d\x03\x0b#\x19\x03\x0b!:\x03\x0b!\x1f\x03\x0b!" +
	"\x00\x03\x0b\x1e5\x03\x0b\x1c\x1d\x03\x0b\x1d-\x03\x0b\x1d(\x03\x0b\x18." +
	"\x03\x0b\x18 \x03\x0b\x18\x16\x03\x0b\x14\x13\x03\x0b\x15$\x03\x0b\x15" +
	"\x22\x03\x0b\x12\x1b\x03\x0b\x12\x10\x03\x0b\x132\x03\x0b\x13=\x03\x0b" +
	"\x12\x18\x03\x0b\x0c&\x03\x0b\x061\x03\x0b\x06:\x03\x0b\x05#\x03\x0b\x05" +
	"<\x03\x0b\x04\x0b\x03\x0b\x04\x04\x03\x0b\x04\x1b\x03\x0b\x042\x03\x0b" +
	"\x041\x03\x0b\x03\x03\x03\x0b\x03\x1d\x03\x0b\x03/\x03\x0b\x03+\x03\x0b" +
	"\x02\x1b\x03\x0b\x02\x00\x03\x0b\x01\x1e\x03\x0b\x01\x08\x03\x0b\x015" +
	"\x03\x06\x0d9\x03\x06\x0d=\x03\x06\x0d?\x03\x02\x001\x03\x02\x003\x03" +
	"\x02\x02\x19\x03\x02\x006\x03\x02\x02\x1b\x03\x02\x004\x03\x02\x00<\x03" +
	"\x02\x02\x0a\x03\x02\x02\x0e\x03\x02\x01\x1a\x03\x02\x01\x07\x03\x02\x01" +
	"\x05\x03\x02\x01\x0b\x03\x02\x01%\x03\x02\x01\x0c\x03\x02\x01\x04\x03" +
	"\x02\x01\x1c\x03\x02\x00.\x03\x02\x002\x03\x02\x00>\x03\x02\x00\x12\x03" +
	"\x02\x00\x16\x03\x02\x011\x03\x02\x013\x03\x02\x02 \x03\x02\x02%\x03\x02" +
	"\x02$\x03\x02\x028\x03\x02\x02;\x03\x02\x024\x03\x02\x012\x03\x02\x022" +
	"\x03\x02\x02/\x03\x02\x01,\x03\x02\x01\x13\x03\x02\x01\x16\x03\x02\x01" +
	"\x11\x03\x02\x01\x1e\x03\x02\x01\x15\x03\x02\x01\x17\x03\x02\x01\x0f\x03" +
	"\x02\x01\x08\x03\x02\x00?\x03\x02\x03\x07\x03\x02\x03\x0d\x03\x02\x03" +
	"\x13\x03\x02\x03\x1d\x03\x02\x03\x1f\x03\x02\x00\x03\x03\x02\x00\x0d\x03" +
	"\x02\x00\x01\x03\x02\x00\x1b\x03\x02\x00\x19\x03\x02\x00\x18\x03\x02\x00" +
	"\x13\x03\x02\x00/\x03\x07>\x12\x03\x07<\x1f\x03\x07>\x1d\x03\x06\x1d\x0e" +
	"\x03\x07>\x1c\x03\x07>:\x03\x07>\x13\x03\x04\x12+\x03\x07?\x03\x03\x07>" +
	"\x02\x03\x06\x224\x03\x06\x1a.\x03\x07<%\x03\x06\x1c\x0b\x03\x0609\x03" +
	"\x05\x1f\x01\x03\x04'\x08\x03\x93\xfd\xf5\x03\x02\x0d \x03\x02\x0d#\x03" +
	"\x02\x0d!\x03\x02\x0d&\x03\x02\x0d\x22\x03\x02\x0d/\x03\x02\x0d,\x03\x02" +
	"\x0d$\x03\x02\x0d'\x03\x02\x0d%\x03\x02\x0d;\x03\x02\x0d=\x03\x02\x0d?" +
	"\x03\x099.\x03\x08\x0b7\x03\x08\x02\x14\x03\x08\x14\x0d\x03\x08.:\x03" +
	"\x089'\x03\x0f\x0b\x18\x03\x0f\x1c1\x03\x0f\x17&\x03\x0f9\x1f\x03\x0f0" +
	"\x0c\x03\x0e\x0a9\x03\x0e\x056\x03\x0e\x1c#\x03\x0f\x13\x0e\x03\x072\x00" +
	"\x03\x070\x0d\x03\x072\x0b\x03\x06\x11\x18\x03\x070\x10\x03\x06\x0f(\x03" +
	"\x072\x05\x03\x06\x0f,\x03\x073\x15\x03\x06\x07\x08\x03\x05\x16\x02\x03" +
	"\x04\x0b \x03\x05:8\x03\x05\x16%\x03\x0a\x0d\x1f\x03\x06\x16\x10\x03\x05" +
	"\x1d5\x03\x05*;\x03\x05\x16\x1b\x03\x04.-\x03\x06\x1a\x19\x03\x04\x03," +
	"\x03\x0b87\x03\x04/\x0a\x03\x06\x00,\x03\x04-\x01\x03\x04\x1e-\x03\x06/(" +
	"\x03\x0a\x0b5\x03\x06\x0e7\x03\x06\x07.\x03\x0597\x03\x0a*%\x03\x0760" +
	"\x03\x06\x0c;\x03\x05'\x00\x03\x072.\x03\x072\x08\x03\x06=\x01\x03\x06" +
	"\x05\x1b\x03\x06\x06\x12\x03\x06$=\x03\x06'\x0d\x03\x04\x11\x0f\x03\x076" +
	",\x03\x06\x07;\x03\x06.,\x03\x86\xf9\xea\x03\x8f\xff\xeb\x02\x092\x02" +
	"\x095\x02\x094\x02\x09;\x02\x09>\x02\x098\x02\x09*\x02\x09/\x02\x09,\x02" +
	"\x09%\x02\x09&\x02\x09#\x02\x09 \x02\x08!\x02\x08%\x02\x08$\x02\x08+\x02" +
	"\x08.\x02\x08*\x02\x08&\x02\x088\x02\x08>\x02\x084\x02\x086\x02\x080\x02" +
	"\x08\x10\x02\x08\x17\x02\x08\x12\x02\x08\x1d\x02\x08\x1f\x02\x08\x13\x02" +
	"\x08\x15\x02\x08\x14\x02\x08\x0c\x03\x8b\xfd\xd0\x03\x81\xec\xc6\x03\x87" +
	"\xe0\x8a\x03-2\xe3\x03\x80\xef\xe4\x03-2\xea\x03\x88\xe6\xeb\x03\x8e\xe6" +
	"\xe8\x03\x84\xe6\xe9\x03\x97\xe6\xee\x03-2\xf9\x03-2\xf6\x03\x8e\xe3\xad" +
	"\x03\x80\xe3\x92\x03\x88\xe3\x90\x03\x8e\xe3\x90\x03\x80\xe3\x97\x03\x88" +
	"\xe3\x95\x03\x88\xfe\xcb\x03\x8e\xfe\xca\x03\x84\xfe\xcd\x03\x91\xef\xc9" +
	"\x03-2\xc1\x03-2\xc0\x03-2\xcb\x03\x88@\x09\x03\x8e@\x08\x03\x8f\xe0\xf5" +
	"\x03\x8e\xe6\xf9\x03\x8e\xe0\xfa\x03\x93\xff\xf4\x03\x84\xee\xd3\x03\x0b" +
	"(\x04\x023 \x03\x0b)\x08\x021;\x02\x01*\x03\x0b#\x10\x03\x0b 0\x03\x0b!" +
	"\x10\x03\x0b!0\x03\x07\x15\x08\x03\x09?5\x03\x07\x1f\x08\x03\x07\x17\x0b" +
	"\x03\x09\x1f\x15\x03\x0b\x1c7\x03\x0a+#\x03\x06\x1a\x1b\x03\x06\x1a\x14" +
	"\x03\x0a\x01\x18\x03\x06#\x1b\x03\x0a2\x0c\x03\x0a\x01\x04\x03\x09#;\x03" +
	"\x08='\x03\x08\x1a\x0a\x03\x07</\x03\x07:+\x03\x07\x07*\x03\x06&\x1c\x03" +
	"\x09\x0c\x16\x03\x09\x10\x0e\x03\x08'\x0f\x03\x08+\x09\x03\x074%\x03\x06" +
	"!3\x03\x06\x03+\x03\x0b\x1e\x19\x03\x0a))\x03\x09\x08\x19\x03\x08,\x05" +
	"\x03\x07<2\x03\x06\x1c>\x03\x0a\x111\x03\x09\x1b\x09\x03\x073.\x03\x07" +
	"\x01\x00\x03\x09/,\x03\x07#>\x03\x07\x048\x03\x0a\x1f\x22\x03\x098>\x03" +
	"\x09\x11\x00\x03\x08/\x17\x03\x06'\x22\x03\x0b\x1a+\x03\x0a\x22\x19\x03" +
	"\x0a/1\x03\x0974\x03\x09\x0f\x22\x03\x08,\x22\x03\x08?\x14\x03\x07$5\x03" +
	"\x07<3\x03\x07=*\x03\x07\x13\x18\x03\x068\x0a\x03\x06\x09\x16\x03\x06" +
	"\x13\x00\x03\x08\x067\x03\x08\x01\x03\x03\x08\x12\x1d\x03\x07+7\x03\x06(" +
	";\x03\x06\x1c?\x03\x07\x0e\x17\x03\x0a\x06\x1d\x03\x0a\x19\x07\x03\x08" +
	"\x14$\x03\x07$;\x03\x08,$\x03\x08\x06\x0d\x03\x07\x16\x0a\x03\x06>>\x03" +
	"\x0a\x06\x12\x03\x0a\x14)\x03\x09\x0d\x1f\x03\x09\x12\x17\x03\x09\x19" +
	"\x01\x03\x08\x11 \x03\x08\x1d'\x03\x06<\x1a\x03\x0a.\x00\x03\x07'\x18" +
	"\x03\x0a\x22\x08\x03\x08\x0d\x0a\x03\x08\x13)\x03\x07*)\x03\x06<,\x03" +
	"\x07\x0b\x1a\x03\x09.\x14\x03\x09\x0d\x1e\x03\x07\x0e#\x03\x0b\x1d'\x03" +
	"\x0a\x0a8\x03\x09%2\x03\x08+&\x03\x080\x12\x03\x0a)4\x03\x08\x06\x1f\x03" +
	"\x0b\x1b\x1a\x03\x0a\x1b\x0f\x03\x0b\x1d*\x03\x09\x16$\x03\x090\x11\x03" +
	"\x08\x11\x08\x03\x0a*(\x03\x0a\x042\x03\x089,\x03\x074'\x03\x07\x0f\x05" +
	"\x03\x09\x0b\x0a\x03\x07\x1b\x01\x03\x09\x17:\x03\x09.\x0d\x03\x07.\x11" +
	"\x03\x09+\x15\x03\x080\x13\x03\x0b\x1f\x19\x03\x0a \x11\x03\x0a\x220\x03" +
	"\x09\x07;\x03\x08\x16\x1c\x03\x07,\x13\x03\x07\x0e/\x03\x06\x221\x03\x0a" +
	".\x0a\x03\x0a7\x02\x03\x0a\x032\x03\x0a\x1d.\x03\x091\x06\x03\x09\x19:" +
	"\x03\x08\x02/\x03\x060+\x03\x06\x0f-\x03\x06\x1c\x1f\x03\x06\x1d\x07\x03" +
	"\x0a,\x11\x03\x09=\x0d\x03\x09\x0b;\x03\x07\x1b/\x03\x0a\x1f:\x03\x09 " +
	"\x1f\x03\x09.\x10\x03\x094\x0b\x03\x09\x1a1\x03\x08#\x1a\x03\x084\x1d" +
	"\x03\x08\x01\x1f\x03\x08\x11\x22\x03\x07'8\x03\x07\x1a>\x03\x0757\x03" +
	"\x06&9\x03\x06+\x11\x03\x0a.\x0b\x03\x0a,>\x03\x0a4#\x03\x08%\x17\x03" +
	"\x07\x05\x22\x03\x07\x0c\x0b\x03\x0a\x1d+\x03\x0a\x19\x16\x03\x09+\x1f" +
	"\x03\x09\x08\x0b\x03\x08\x16\x18\x03\x08+\x12\x03\x0b\x1d\x0c\x03\x0a=" +
	"\x10\x03\x0a\x09\x0d\x03\x0a\x10\x11\x03\x09&0\x03\x08(\x1f\x03\x087\x07" +
	"\x03\x08\x185\x03\x07'6\x03\x06.\x05\x03\x06=\x04\x03\x06;;\x03\x06\x06," +
	"\x03\x0b\x18>\x03\x08\x00\x18\x03\x06 \x03\x03\x06<\x00\x03\x09%\x18\x03" +
	"\x0b\x1c<\x03\x0a%!\x03\x0a\x09\x12\x03\x0a\x16\x02\x03\x090'\x03\x09" +
	"\x0e=\x03\x08 \x0e\x03\x08>\x03\x03\x074>\x03\x06&?\x03\x06\x19\x09\x03" +
	"\x06?(\x03\x0a-\x0e\x03\x09:3\x03\x098:\x03\x09\x12\x0b\x03\x09\x1d\x17" +
	"\x03\x087\x05\x03\x082\x14\x03\x08\x06%\x03\x08\x13\x1f\x03\x06\x06\x0e" +
	"\x03\x0a\x22<\x03\x09/<\x03\x06>+\x03\x0a'?\x03\x0a\x13\x0c\x03\x09\x10<" +
	"\x03\x07\x1b=\x03\x0a\x19\x13\x03\x09\x22\x1d\x03\x09\x07\x0d\x03\x08)" +
	"\x1c\x03\x06=\x1a\x03\x0a/4\x03\x0a7\x11\x03\x0a\x16:\x03\x09?3\x03\x09:" +
	"/\x03\x09\x05\x0a\x03\x09\x14\x06\x03\x087\x22\x03\x080\x07\x03\x08\x1a" +
	"\x1f\x03\x07\x04(\x03\x07\x04\x09\x03\x06 %\x03\x06<\x08\x03\x0a+\x14" +
	"\x03\x09\x1d\x16\x03\x0a70\x03\x08 >\x03\x0857\x03\x070\x0a\x03\x06=\x12" +
	"\x03\x06\x16%\x03\x06\x1d,\x03\x099#\x03\x09\x10>\x03\x07 \x1e\x03\x08" +
	"\x0c<\x03\x08\x0b\x18\x03\x08\x15+\x03\x08,:\x03\x08%\x22\x03\x07\x0a$" +
	"\x03\x0b\x1c=\x03\x07+\x08\x03\x0a/\x05\x03\x0a \x07\x03\x0a\x12'\x03" +
	"\x09#\x11\x03\x08\x1b\x15\x03\x0a\x06\x01\x03\x09\x1c\x1b\x03\x0922\x03" +
	"\x07\x14<\x03\x07\x09\x04\x03\x061\x04\x03\x07\x0e\x01\x03\x0a\x13\x18" +
	"\x03\x0a-\x0c\x03\x0a?\x0d\x03\x0a\x09\x0a\x03\x091&\x03\x0a/\x0b\x03" +
	"\x08$<\x03\x083\x1d\x03\x08\x0c$\x03\x08\x0d\x07\x03\x08\x0d?\x03\x08" +
	"\x0e\x14\x03\x065\x0a\x03\x08\x1a#\x03\x08\x16#\x03\x0702\x03\x07\x03" +
	"\x1a\x03\x06(\x1d\x03\x06+\x1b\x03\x06\x0b\x05\x03\x06\x0b\x17\x03\x06" +
	"\x0c\x04\x03\x06\x1e\x19\x03\x06+0\x03\x062\x18\x03\x0b\x16\x1e\x03\x0a+" +
	"\x16\x03\x0a-?\x03\x0a#:\x03\x0a#\x10\x03\x0a%$\x03\x0a>+\x03\x0a01\x03" +
	"\x0a1\x10\x03\x0a\x099\x03\x0a\x0a\x12\x03\x0a\x19\x1f\x03\x0a\x19\x12" +
	"\x03\x09*)\x03\x09-\x16\x03\x09.1\x03\x09.2\x03\x09<\x0e\x03\x09> \x03" +
	"\x093\x12\x03\x09\x0b\x01\x03\x09\x1c2\x03\x09\x11\x1c\x03\x09\x15%\x03" +
	"\x08,&\x03\x08!\x22\x03\x089(\x03\x08\x0b\x1a\x03\x08\x0d2\x03\x08\x0c" +
	"\x04\x03\x08\x0c\x06\x03\x08\x0c\x1f\x03\x08\x0c\x0c\x03\x08\x0f\x1f\x03" +
	"\x08\x0f\x1d\x03\x08\x00\x14\x03\x08\x03\x14\x03\x08\x06\x16\x03\x08\x1e" +
	"#\x03\x08\x11\x11\x03\x08\x10\x18\x03\x08\x14(\x03\x07)\x1e\x03\x07.1" +
	"\x03\x07 $\x03\x07 '\x03\x078\x08\x03\x07\x0d0\x03\x07\x0f7\x03\x07\x05#" +
	"\x03\x07\x05\x1a\x03\x07\x1a7\x03\x07\x1d-\x03\x07\x17\x10\x03\x06)\x1f" +
	"\x03\x062\x0b\x03\x066\x16\x03\x06\x09\x11\x03\x09(\x1e\x03\x07!5\x03" +
	"\x0b\x11\x16\x03\x0a/\x04\x03\x0a,\x1a\x03\x0b\x173\x03\x0a,1\x03\x0a/5" +
	"\x03\x0a\x221\x03\x0a\x22\x0d\x03\x0a?%\x03\x0a<,\x03\x0a?#\x03\x0a>\x19" +
	"\x03\x0a\x08&\x03\x0a\x0b\x0e\x03\x0a\x0c:\x03\x0a\x0c+\x03\x0a\x03\x22" +
	"\x03\x0a\x06)\x03\x0a\x11\x10\x03\x0a\x11\x1a\x03\x0a\x17-\x03\x0a\x14(" +
	"\x03\x09)\x1e\x03\x09/\x09\x03\x09.\x00\x03\x09,\x07\x03\x09/*\x03\x09-9" +
	"\x03\x09\x228\x03\x09%\x09\x03\x09:\x12\x03\x09;\x1d\x03\x09?\x06\x03" +
	"\x093%\x03\x096\x05\x03\x096\x08\x03\x097\x02\x03\x09\x07,\x03\x09\x04," +
	"\x03\x09\x1f\x16\x03\x09\x11\x03\x03\x09\x11\x12\x03\x09\x168\x03\x08*" +
	"\x05\x03\x08/2\x03\x084:\x03\x08\x22+\x03\x08 0\x03\x08&\x0a\x03\x08;" +
	"\x10\x03\x08>$\x03\x08>\x18\x03\x0829\x03\x082:\x03\x081,\x03\x081<\x03" +
	"\x081\x1c\x03\x087#\x03\x087*\x03\x08\x09'\x03\x08\x00\x1d\x03\x08\x05-" +
	"\x03\x08\x1f4\x03\x08\x1d\x04\x03\x08\x16\x0f\x03\x07*7\x03\x07'!\x03" +
	"\x07%\x1b\x03\x077\x0c\x03\x07\x0c1\x03\x07\x0c.\x03\x07\x00\x06\x03\x07" +
	"\x01\x02\x03\x07\x010\x03\x07\x06=\x03\x07\x01\x03\x03\x07\x01\x13\x03" +
	"\x07\x06\x06\x03\x07\x05\x0a\x03\x07\x1f\x09\x03\x07\x17:\x03\x06*1\x03" +
	"\x06-\x1d\x03\x06\x223\x03\x062:\x03\x060$\x03\x066\x1e\x03\x064\x12\x03" +
	"\x0645\x03\x06\x0b\x00\x03\x06\x0b7\x03\x06\x07\x1f\x03\x06\x15\x12\x03" +
	"\x0c\x05\x0f\x03\x0b+\x0b\x03\x0b+-\x03\x06\x16\x1b\x03\x06\x15\x17\x03" +
	"\x89\xca\xea\x03\x89\xca\xe8\x03\x0c8\x10\x03\x0c8\x01\x03\x0c8\x0f\x03" +
	"\x0d8%\x03\x0d8!\x03\x0c8-\x03\x0c8/\x03\x0c8+\x03\x0c87\x03\x0c85\x03" +
	"\x0c9\x09\x03\x0c9\x0d\x03\x0c9\x0f\x03\x0c9\x0b\x03\xcfu\x0c\x03\xcfu" +
	"\x0f\x03\xcfu\x0e\x03\xcfu\x09\x03\x0c9\x10\x03\x0d9\x0c\x03\xcf`;\x03" +
	"\xcf`>\x03\xcf`9\x03\xcf`8\x03\xcf`7\x03\xcf`*\x03\xcf`-\x03\xcf`,\x03" +
	"\x0d\x1b\x1a\x03\x0d\x1b&\x03\x0c=.\x03\x0c=%\x03\x0c>\x1e\x03\x0c>\x14" +
	"\x03\x0c?\x06\x03\x0c?\x0b\x03\x0c?\x0c\x03\x0c?\x0d\x03\x0c?\x02\x03" +
	"\x0c>\x0f\x03\x0c>\x08\x03\x0c>\x09\x03\x0c>,\x03\x0c>\x0c\x03\x0c?\x13" +
	"\x03\x0c?\x16\x03\x0c?\x15\x03\x0c?\x1c\x03\x0c?\x1f\x03\x0c?\x1d\x03" +
	"\x0c?\x1a\x03\x0c?\x17\x03\x0c?\x08\x03\x0c?\x09\x03\x0c?\x0e\x03\x0c?" +
	"\x04\x03\x0c?\x05\x03\x0c<?\x03\x0c=\x00\x03\x0c=\x06\x03\x0c=\x05\x03" +
	"\x0c=\x0c\x03\x0c=\x0f\x03\x0c=\x0d\x03\x0c=\x0b\x03\x0c=\x07\x03\x0c=" +
	"\x19\x03\x0c=\x15\x03\x0c=\x11\x03\x0c=1\x03\x0c=3\x03\x0c=0\x03\x0c=>" +
	"\x03\x0c=2\x03\x0c=6\x03\x0c<\x07\x03\x0c<\x05\x03\x0e:!\x03\x0e:#\x03" +
	"\x0e8\x09\x03\x0e:&\x03\x0e8\x0b\x03\x0e:$\x03\x0e:,\x03\x0e8\x1a\x03" +
	"\x0e8\x1e\x03\x0e:*\x03\x0e:7\x03\x0e:5\x03\x0e:;\x03\x0e:\x15\x03\x0e:<" +
	"\x03\x0e:4\x03\x0e:'\x03\x0e:-\x03\x0e:%\x03\x0e:?\x03\x0e:=\x03\x0e:)" +
	"\x03\x0e:/\x03\xcfs'\x03\x0d=\x0f\x03\x0d+*\x03\x0d99\x03\x0d9;\x03\x0d9" +
	"?\x03\x0d)\x0d\x03\x0d(%\x02\x01\x18\x02\x01(\x02\x03'\x02\x03)\x02\x03+" +
	"\x02\x03/\x02\x03\x19\x02\x03\x1b\x02\x03\x1f\x03\x0d\x22\x18\x03\x0d" +
	"\x22\x1a\x03\x0d\x22'\x03\x0d\x22/\x03\x0d\x223\x03\x0d\x22$\x02\x01\x1e" +
	"\x03\x0f$!\x03\x0f87\x03\x0f4\x0e\x03\x0f5\x1d\x03\x06'\x03\x03\x0f\x08" +
	"\x18\x03\x0f\x0d\x1b\x03\x0e2=\x03\x0e;\x08\x03\x0e:\x0b\x03\x0e\x06$" +
	"\x03\x0e\x0d)\x03\x0e\x16\x1f\x03\x0e\x16\x1b\x03\x0d$\x0a\x03\x05,\x1d" +
	"\x03\x0d. \x03\x0d.#\x03\x0c(/\x03\x09%\x02\x03\x0d90\x03\x0d\x0e4\x03" +
	"\x0d\x0d\x0f\x03\x0c#\x00\x03\x0c,\x1e\x03\x0c2\x0e\x03\x0c\x01\x17\x03" +
	"\x0c\x09:\x03\x0e\x173\x03\x0c\x08\x03\x03\x0c\x11\x07\x03\x0c\x10\x18" +
	"\x03\x0c\x1f\x1c\x03\x0c\x19\x0e\x03\x0c\x1a\x1f\x03\x0f0>\x03\x0b->\x03" +
	"\x0b<+\x03\x0b8\x13\x03\x0b\x043\x03\x0b\x14\x03\x03\x0b\x16%\x03\x0d" +
	"\x22&\x03\x0b\x1a\x1a\x03\x0b\x1a\x04\x03\x0a%9\x03\x0a&2\x03\x0a&0\x03" +
	"\x0a!\x1a\x03\x0a!7\x03\x0a5\x10\x03\x0a=4\x03\x0a?\x0e\x03\x0a>\x10\x03" +
	"\x0a\x00 \x03\x0a\x0f:\x03\x0a\x0f9\x03\x0a\x0b\x0a\x03\x0a\x17%\x03\x0a" +
	"\x1b-\x03\x09-\x1a\x03\x09,4\x03\x09.,\x03\x09)\x09\x03\x096!\x03\x091" +
	"\x1f\x03\x093\x16\x03\x0c+\x1f\x03\x098 \x03\x098=\x03\x0c(\x1a\x03\x0c(" +
	"\x16\x03\x09\x0a+\x03\x09\x16\x12\x03\x09\x13\x0e\x03\x09\x153\x03\x08)!" +
	"\x03\x09\x1a\x01\x03\x09\x18\x01\x03\x08%#\x03\x08>\x22\x03\x08\x05%\x03" +
	"\x08\x02*\x03\x08\x15;\x03\x08\x1b7\x03\x0f\x07\x1d\x03\x0f\x04\x03\x03" +
	"\x070\x0c\x03\x07;\x0b\x03\x07\x08\x17\x03\x07\x12\x06\x03\x06/-\x03\x06" +
	"71\x03\x065+\x03\x06>7\x03\x06\x049\x03\x05+\x1e\x03\x05,\x17\x03\x05 " +
	"\x1d\x03\x05\x22\x05\x03\x050\x1d"

// lookup returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *idnaTrie) lookup(s []byte) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return idnaValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := idnaIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := idnaIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = idnaIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := idnaIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = idnaIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = idnaIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *idnaTrie) lookupUnsafe(s []byte) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return idnaValues[c0]
	}
	i := idnaIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = idnaIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = idnaIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// lookupString returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *idnaTrie) lookupString(s string) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return idnaValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := idnaIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := idnaIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = idnaIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := idnaIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = idnaIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = idnaIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupStringUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *idnaTrie) lookupStringUnsafe(s string) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return idnaValues[c0]
	}
	i := idnaIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = idnaIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = idnaIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// idnaTrie. Total size: 31598 bytes (30.86 KiB). Checksum: d3118eda0d6b5360.
type idnaTrie struct{}

func newIdnaTrie(i int) *idnaTrie {
	return &idnaTrie{}
}

// lookupValue determines the type of block n and looks up the value for b.
func (t *idnaTrie) lookupValue(n uint32, b byte) uint16 {
	switch {
	case n < 133:
		return uint16(idnaValues[n<<6+uint32(b)])
	default:
		n -= 133
		return uint16(idnaSparse.lookup(n, b))
	}
}

// idnaValues: 135 blocks, 8640 entries, 17280 bytes
// The third block is the zero block.
var idnaValues = [8640]uint16{
	// Block 0x0, offset 0x0
	0x00: 0x0080, 0x01: 0x0080, 0x02: 0x0080, 0x03: 0x0080, 0x04: 0x0080, 0x05: 0x0080,
	0x06: 0x0080, 0x07: 0x0080, 0x08: 0x0080, 0x09: 0x0080, 0x0a: 0x0080, 0x0b: 0x0080,
	0x0c: 0x0080, 0x0d: 0x0080, 0x0e: 0x0080, 0x0f: 0x0080, 0x10: 0x0080, 0x11: 0x0080,
	0x12: 0x0080, 0x13: 0x0080, 0x14: 0x0080, 0x15: 0x0080, 0x16: 0x0080, 0x17: 0x0080,
	0x18: 0x0080, 0x19: 0x0080, 0x1a: 0x0080, 0x1b: 0x0080, 0x1c: 0x0080, 0x1d: 0x0080,
	0x1e: 0x0080, 0x1f: 0x0080, 0x20: 0x0080, 0x21: 0x0080, 0x22: 0x0080, 0x23: 0x0080,
	0x24: 0x0080, 0x25: 0x0080, 0x26: 0x0080, 0x27: 0x0080, 0x28: 0x0080, 0x29: 0x0080,
	0x2a: 0x0080, 0x2b: 0x0080, 0x2c: 0x0080, 0x2d: 0x0008, 0x2e: 0x0008, 0x2f: 0x0080,
	0x30: 0x0008, 0x31: 0x0008, 0x32: 0x0008, 0x33: 0x0008, 0x34: 0x0008, 0x35: 0x0008,
	0x36: 0x0008, 0x37: 0x0008, 0x38: 0x0008, 0x39: 0x0008, 0x3a: 0x0080, 0x3b: 0x0080,
	0x3c: 0x0080, 0x3d: 0x0080, 0x3e: 0x0080, 0x3f: 0x0080,
	// Block 0x1, offset 0x40
	0x40: 0x0080, 0x41: 0xe105, 0x42: 0xe105, 0x43: 0xe105, 0x44: 0xe105, 0x45: 0xe105,
	0x46: 0xe105, 0x47: 0xe105, 0x48: 0xe105, 0x49: 0xe105, 0x4a: 0xe105, 0x4b: 0xe105,
	0x4c: 0xe105, 0x4d: 0xe105, 0x4e: 0xe105, 0x4f: 0xe105, 0x50: 0xe105, 0x51: 0xe105,
	0x52: 0xe105, 0x53: 0xe105, 0x54: 0xe105, 0x55: 0xe105, 0x56: 0xe105, 0x57: 0xe105,
	0x58: 0xe105, 0x59: 0xe105, 0x5a: 0xe105, 0x5b: 0x0080, 0x5c: 0x0080, 0x5d: 0x0080,
	0x5e: 0x0080, 0x5f: 0x0080, 0x60: 0x0080, 0x61: 0x0008, 0x62: 0x0008, 0x63: 0x0008,
	0x64: 0x0008, 0x65: 0x0008, 0x66: 0x0008, 0x67: 0x0008, 0x68: 0x0008, 0x69: 0x0008,
	0x6a: 0x0008, 0x6b: 0x0008, 0x6c: 0x0008, 0x6d: 0x0008, 0x6e: 0x0008, 0x6f: 0x0008,
	0x70: 0x0008, 0x71: 0x0008, 0x72: 0x0008, 0x73: 0x0008, 0x74: 0x0008, 0x75: 0x0008,
	0x76: 0x0008, 0x77: 0x0008, 0x78: 0x0008, 0x79: 0x0008, 0x7a: 0x0008, 0x7b: 0x0080,
	0x7c: 0x0080, 0x7d: 0x0080, 0x7e: 0x0080, 0x7f: 0x0080,
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc0: 0x0040, 0xc1: 0x0040, 0xc2: 0x0040, 0xc3: 0x0040, 0xc4: 0x0040, 0xc5: 0x0040,
	0xc6: 0x0040, 0xc7: 0x0040, 0xc8: 0x0040, 0xc9: 0x0040, 0xca: 0x0040, 0xcb: 0x0040,
	0xcc: 0x0040, 0xcd: 0x0040, 0xce: 0x0040, 0xcf: 0x0040, 0xd0: 0x0040, 0xd1: 0x0040,
	0xd2: 0x0040, 0xd3: 0x0040, 0xd4: 0x0040, 0xd5: 0x0040, 0xd6: 0x0040, 0xd7: 0x0040,
	0xd8: 0x0040, 0xd9: 0x0040, 0xda: 0x0040, 0xdb: 0x0040, 0xdc: 0x0040, 0xdd: 0x0040,
	0xde: 0x0040, 0xdf: 0x0040, 0xe0: 0x000a, 0xe1: 0x0018, 0xe2: 0x0018, 0xe3: 0x0018,
	0xe4: 0x0018, 0xe5: 0x0018, 0xe6: 0x0018, 0xe7: 0x0018, 0xe8: 0x0012, 0xe9: 0x0018,
	0xea: 0x0019, 0xeb: 0x0018, 0xec: 0x0018, 0xed: 0x03c0, 0xee: 0x0018, 0xef: 0x0022,
	0xf0: 0x0018, 0xf1: 0x0018, 0xf2: 0x0029, 0xf3: 0x0031, 0xf4: 0x003a, 0xf5: 0x0005,
	0xf6: 0x0018, 0xf7: 0x0008, 0xf8: 0x0042, 0xf9: 0x0049, 0xfa: 0x0051, 0xfb: 0x0018,
	0xfc: 0x0059, 0xfd: 0x0061, 0xfe: 0x0069, 0xff: 0x0018,
	// Block 0x4, offset 0x100
	0x100: 0xe00d, 0x101: 0x0008, 0x102: 0xe00d, 0x103: 0x0008, 0x104: 0xe00d, 0x105: 0x0008,
	0x106: 0xe00d, 0x107: 0x0008, 0x108: 0xe00d, 0x109: 0x0008, 0x10a: 0xe00d, 0x10b: 0x0008,
	0x10c: 0xe00d, 0x10d: 0x0008, 0x10e: 0xe00d, 0x10f: 0x0008, 0x110: 0xe00d, 0x111: 0x0008,
	0x112: 0xe00d, 0x113: 0x0008, 0x114: 0xe00d, 0x115: 0x0008, 0x116: 0xe00d, 0x117: 0x0008,
	0x118: 0xe00d, 0x119: 0x0008, 0x11a: 0xe00d, 0x11b: 0x0008, 0x11c: 0xe00d, 0x11d: 0x0008,
	0x11e: 0xe00d, 0x11f: 0x0008, 0x120: 0xe00d, 0x121: 0x0008, 0x122: 0xe00d, 0x123: 0x0008,
	0x124: 0xe00d, 0x125: 0x0008, 0x126: 0xe00d, 0x127: 0x0008, 0x128: 0xe00d, 0x129: 0x0008,
	0x12a: 0xe00d, 0x12b: 0x0008, 0x12c: 0xe00d, 0x12d: 0x0008, 0x12e: 0xe00d, 0x12f: 0x0008,
	0x130: 0x0071, 0x131: 0x0008, 0x132: 0x0035, 0x133: 0x004d, 0x134: 0xe00d, 0x135: 0x0008,
	0x136: 0xe00d, 0x137: 0x0008, 0x138: 0x0008, 0x139: 0xe01d, 0x13a: 0x0008, 0x13b: 0xe03d,
	0x13c: 0x0008, 0x13d: 0xe01d, 0x13e: 0x0008, 0x13f: 0x0079,
	// Block 0x5, offset 0x140
	0x140: 0x0079, 0x141: 0xe01d, 0x142: 0x0008, 0x143: 0xe03d, 0x144: 0x0008, 0x145: 0xe01d,
	0x146: 0x0008, 0x147: 0xe07d, 0x148: 0x0008, 0x149: 0x0081, 0x14a: 0xe00d, 0x14b: 0x0008,
	0x14c: 0xe00d, 0x14d: 0x0008, 0x14e: 0xe00d, 0x14f: 0x0008, 0x150: 0xe00d, 0x151: 0x0008,
	0x152: 0xe00d, 0x153: 0x0008, 0x154: 0xe00d, 0x155: 0x0008, 0x156: 0xe00d, 0x157: 0x0008,
	0x158: 0xe00d, 0x159: 0x0008, 0x15a: 0xe00d, 0x15b: 0x0008, 0x15c: 0xe00d, 0x15d: 0x0008,
	0x15e: 0xe00d, 0x15f: 0x0008, 0x160: 0xe00d, 0x161: 0x0008, 0x162: 0xe00d, 0x163: 0x0008,
	0x164: 0xe00d, 0x165: 0x0008, 0x166: 0xe00d, 0x167: 0x0008, 0x168: 0xe00d, 0x169: 0x0008,
	0x16a: 0xe00d, 0x16b: 0x0008, 0x16c: 0xe00d, 0x16d: 0x0008, 0x16e: 0xe00d, 0x16f: 0x0008,
	0x170: 0xe00d, 0x171: 0x0008, 0x172: 0xe00d, 0x173: 0x0008, 0x174: 0xe00d, 0x175: 0x0008,
	0x176: 0xe00d, 0x177: 0x0008, 0x178: 0x0065, 0x179: 0xe01d, 0x17a: 0x0008, 0x17b: 0xe03d,
	0x17c: 0x0008, 0x17d: 0xe01d, 0x17e: 0x0008, 0x17f: 0x0089,
	// Block 0x6, offset 0x180
	0x180: 0x0008, 0x181: 0x007d, 0x182: 0xe00d, 0x183: 0x0008, 0x184: 0xe00d, 0x185: 0x0008,
	0x186: 0x007d, 0x187: 0xe07d, 0x188: 0x0008, 0x189: 0x0095, 0x18a: 0x00ad, 0x18b: 0xe03d,
	0x18c: 0x0008, 0x18d: 0x0008, 0x18e: 0x00c5, 0x18f: 0x00dd, 0x190: 0x00f5, 0x191: 0xe01d,
	0x192: 0x0008, 0x193: 0x010d, 0x194: 0x0125, 0x195: 0x0008, 0x196: 0x013d, 0x197: 0x013d,
	0x198: 0xe00d, 0x199: 0x0008, 0x19a: 0x0008, 0x19b: 0x0008, 0x19c: 0x010d, 0x19d: 0x0155,
	0x19e: 0x0008, 0x19f: 0x016d, 0x1a0: 0xe00d, 0x1a1: 0x0008, 0x1a2: 0xe00d, 0x1a3: 0x0008,
	0x1a4: 0xe00d, 0x1a5: 0x0008, 0x1a6: 0x0185, 0x1a7: 0xe07d, 0x1a8: 0x0008, 0x1a9: 0x019d,
	0x1aa: 0x0008, 0x1ab: 0x0008, 0x1ac: 0xe00d, 0x1ad: 0x0008, 0x1ae: 0x0185, 0x1af: 0xe0fd,
	0x1b0: 0x0008, 0x1b1: 0x01b5, 0x1b2: 0x01cd, 0x1b3: 0xe03d, 0x1b4: 0x0008, 0x1b5: 0xe01d,
	0x1b6: 0x0008, 0x1b7: 0x01e5, 0x1b8: 0xe00d, 0x1b9: 0x0008, 0x1ba: 0x0008, 0x1bb: 0x0008,
	0x1bc: 0xe00d, 0x1bd: 0x0008, 0x1be: 0x0008, 0x1bf: 0x0008,
	// Block 0x7, offset 0x1c0
	0x1c0: 0x0008, 0x1c1: 0x0008, 0x1c2: 0x0008, 0x1c3: 0x0008, 0x1c4: 0x0091, 0x1c5: 0x0091,
	0x1c6: 0x0091, 0x1c7: 0x01fd, 0x1c8: 0x0215, 0x1c9: 0x022d, 0x1ca: 0x0245, 0x1cb: 0x025d,
	0x1cc: 0x0275, 0x1cd: 0xe01d, 0x1ce: 0x0008, 0x1cf: 0xe0fd, 0x1d0: 0x0008, 0x1d1: 0xe01d,
	0x1d2: 0x0008, 0x1d3: 0xe03d, 0x1d4: 0x0008, 0x1d5: 0xe01d, 0x1d6: 0x0008, 0x1d7: 0xe07d,
	0x1d8: 0x0008, 0x1d9: 0xe01d, 0x1da: 0x0008, 0x1db: 0xe03d, 0x1dc: 0x0008, 0x1dd: 0x0008,
	0x1de: 0xe00d, 0x1df: 0x0008, 0x1e0: 0xe00d, 0x1e1: 0x0008, 0x1e2: 0xe00d, 0x1e3: 0x0008,
	0x1e4: 0xe00d, 0x1e5: 0x0008, 0x1e6: 0xe00d, 0x1e7: 0x0008, 0x1e8: 0xe00d, 0x1e9: 0x0008,
	0x1ea: 0xe00d, 0x1eb: 0x0008, 0x1ec: 0xe00d, 0x1ed: 0x0008, 0x1ee: 0xe00d, 0x1ef: 0x0008,
	0x1f0: 0x0008, 0x1f1: 0x028d, 0x1f2: 0x02a5, 0x1f3: 0x02bd, 0x1f4: 0xe00d, 0x1f5: 0x0008,
	0x1f6: 0x02d5, 0x1f7: 0x02ed, 0x1f8: 0xe00d, 0x1f9: 0x0008, 0x1fa: 0xe00d, 0x1fb: 0x0008,
	0x1fc: 0xe00d, 0x1fd: 0x0008, 0x1fe: 0xe00d, 0x1ff: 0x0008,
	// Block 0x8, offset 0x200
	0x200: 0xe00d, 0x201: 0x0008, 0x202: 0xe00d, 0x203: 0x0008, 0x204: 0xe00d, 0x205: 0x0008,
	0x206: 0xe00d, 0x207: 0x0008, 0x208: 0xe00d, 0x209: 0x0008, 0x20a: 0xe00d, 0x20b: 0x0008,
	0x20c: 0xe00d, 0x20d: 0x0008, 0x20e: 0xe00d, 0x20f: 0x0008, 0x210: 0xe00d, 0x211: 0x0008,
	0x212: 0xe00d, 0x213: 0x0008, 0x214: 0xe00d, 0x215: 0x0008, 0x216: 0xe00d, 0x217: 0x0008,
	0x218: 0xe00d, 0x219: 0x0008, 0x21a: 0xe00d, 0x21b: 0x0008, 0x21c: 0xe00d, 0x21d: 0x0008,
	0x21e: 0xe00d, 0x21f: 0x0008, 0x220: 0x0305, 0x221: 0x0008, 0x222: 0xe00d, 0x223: 0x0008,
	0x224: 0xe00d, 0x225: 0x0008, 0x226: 0xe00d, 0x227: 0x0008, 0x228: 0xe00d, 0x229: 0x0008,
	0x22a: 0xe00d, 0x22b: 0x0008, 0x22c: 0xe00d, 0x22d: 0x0008, 0x22e: 0xe00d, 0x22f: 0x0008,
	0x230: 0xe00d, 0x231: 0x0008, 0x232: 0xe00d, 0x233: 0x0008, 0x234: 0x0008, 0x235: 0x0008,
	0x236: 0x0008, 0x237: 0x0008, 0x238: 0x0008, 0x239: 0x0008, 0x23a: 0x0099, 0x23b: 0xe03d,
	0x23c: 0x0008, 0x23d: 0x031d, 0x23e: 0x00a1, 0x23f: 0x0008,
	// Block 0x9, offset 0x240
	0x240: 0x0008, 0x241: 0x0008, 0x242: 0x0018, 0x243: 0x0018, 0x244: 0x0018, 0x245: 0x0018,
	0x246: 0x0008, 0x247: 0x0008, 0x248: 0x0008, 0x249: 0x0008, 0x24a: 0x0008, 0x24b: 0x0008,
	0x24c: 0x0008, 0x24d: 0x0008, 0x24e: 0x0008, 0x24f: 0x0008, 0x250: 0x0008, 0x251: 0x0008,
	0x252: 0x0018, 0x253: 0x0018, 0x254: 0x0018, 0x255: 0x0018, 0x256: 0x0018, 0x257: 0x0018,
	0x258: 0x00d2, 0x259: 0x00da, 0x25a: 0x00e2, 0x25b: 0x00ea, 0x25c: 0x00f2, 0x25d: 0x00fa,
	0x25e: 0x0018, 0x25f: 0x0018, 0x260: 0x03ad, 0x261: 0x0101, 0x262: 0x0089, 0x263: 0x0109,
	0x264: 0x03c5, 0x265: 0x0018, 0x266: 0x0018, 0x267: 0x0018, 0x268: 0x0018, 0x269: 0x0018,
	0x26a: 0x0018, 0x26b: 0x0018, 0x26c: 0x0008, 0x26d: 0x0018, 0x26e: 0x0008, 0x26f: 0x0018,
	0x270: 0x0018, 0x271: 0x0018, 0x272: 0x0018, 0x273: 0x0018, 0x274: 0x0018, 0x275: 0x0018,
	0x276: 0x0018, 0x277: 0x0018, 0x278: 0x0018, 0x279: 0x0018, 0x27a: 0x0018, 0x27b: 0x0018,
	0x27c: 0x0018, 0x27d: 0x0018, 0x27e: 0x0018, 0x27f: 0x0018,
	// Block 0xa, offset 0x280
	0x280: 0x03dd, 0x281: 0x03dd, 0x282: 0x3308, 0x283: 0x03f5, 0x284: 0x0111, 0x285: 0x040d,
	0x286: 0x3308, 0x287: 0x3308, 0x288: 0x3308, 0x289: 0x3308, 0x28a: 0x3308, 0x28b: 0x3308,
	0x28c: 0x3308, 0x28d: 0x3308, 0x28e: 0x3308, 0x28f: 0x33c0, 0x290: 0x3308, 0x291: 0x3308,
	0x292: 0x3308, 0x293: 0x3308, 0x294: 0x3308, 0x295: 0x3308, 0x296: 0x3308, 0x297: 0x3308,
	0x298: 0x3308, 0x299: 0x3308, 0x29a: 0x3308, 0x29b: 0x3308, 0x29c: 0x3308, 0x29d: 0x3308,
	0x29e: 0x3308, 0x29f: 0x3308, 0x2a0: 0x3308, 0x2a1: 0x3308, 0x2a2: 0x3308, 0x2a3: 0x3308,
	0x2a4: 0x3308, 0x2a5: 0x3308, 0x2a6: 0x3308, 0x2a7: 0x3308, 0x2a8: 0x3308, 0x2a9: 0x3308,
	0x2aa: 0x3308, 0x2ab: 0x3308, 0x2ac: 0x3308, 0x2ad: 0x3308, 0x2ae: 0x3308, 0x2af: 0x3308,
	0x2b0: 0xe00d, 0x2b1: 0x0008, 0x2b2: 0xe00d, 0x2b3: 0x0008, 0x2b4: 0x0425, 0x2b5: 0x0008,
	0x2b6: 0xe00d, 0x2b7: 0x0008, 0x2b8: 0x0040, 0x2b9: 0x0040, 0x2ba: 0x011a, 0x2bb: 0x0008,
	0x2bc: 0x0008, 0x2bd: 0x0008, 0x2be: 0x0122, 0x2bf: 0x043d,
	// Block 0xb, offset 0x2c0
	0x2c0: 0x0040, 0x2c1: 0x0040, 0x2c2: 0x0040, 0x2c3: 0x0040, 0x2c4: 0x003a, 0x2c5: 0x012a,
	0x2c6: 0xe155, 0x2c7: 0x0455, 0x2c8: 0xe12d, 0x2c9: 0xe13d, 0x2ca: 0xe12d, 0x2cb: 0x0040,
	0x2cc: 0x03dd, 0x2cd: 0x0040, 0x2ce: 0x046d, 0x2cf: 0x0485, 0x2d0: 0x0008, 0x2d1: 0xe105,
	0x2d2: 0xe105, 0x2d3: 0xe105, 0x2d4: 0xe105, 0x2d5: 0xe105, 0x2d6: 0xe105, 0x2d7: 0xe105,
	0x2d8: 0xe105, 0x2d9: 0xe105, 0x2da: 0xe105, 0x2db: 0xe105, 0x2dc: 0xe105, 0x2dd: 0xe105,
	0x2de: 0xe105, 0x2df: 0xe105, 0x2e0: 0x049d, 0x2e1: 0x049d, 0x2e2: 0x0040, 0x2e3: 0x049d,
	0x2e4: 0x049d, 0x2e5: 0x049d, 0x2e6: 0x049d, 0x2e7: 0x049d, 0x2e8: 0x049d, 0x2e9: 0x049d,
	0x2ea: 0x049d, 0x2eb: 0x049d, 0x2ec: 0x0008, 0x2ed: 0x0008, 0x2ee: 0x0008, 0x2ef: 0x0008,
	0x2f0: 0x0008, 0x2f1: 0x0008, 0x2f2: 0x0008, 0x2f3: 0x0008, 0x2f4: 0x0008, 0x2f5: 0x0008,
	0x2f6: 0x0008, 0x2f7: 0x0008, 0x2f8: 0x0008, 0x2f9: 0x0008, 0x2fa: 0x0008, 0x2fb: 0x0008,
	0x2fc: 0x0008, 0x2fd: 0x0008, 0x2fe: 0x0008, 0x2ff: 0x0008,
	// Block 0xc, offset 0x300
	0x300: 0x0008, 0x301: 0x0008, 0x302: 0xe00f, 0x303: 0x0008, 0x304: 0x0008, 0x305: 0x0008,
	0x306: 0x0008, 0x307: 0x0008, 0x308: 0x0008, 0x309: 0x0008, 0x30a: 0x0008, 0x30b: 0x0008,
	0x30c: 0x0008, 0x30d: 0x0008, 0x30e: 0x0008, 0x30f: 0xe0c5, 0x310: 0x04b5, 0x311: 0x04cd,
	0x312: 0xe0bd, 0x313: 0xe0f5, 0x314: 0xe0fd, 0x315: 0xe09d, 0x316: 0xe0b5, 0x317: 0x0008,
	0x318: 0xe00d, 0x319: 0x0008, 0x31a: 0xe00d, 0x31b: 0x0008, 0x31c: 0xe00d, 0x31d: 0x0008,
	0x31e: 0xe00d, 0x31f: 0x0008, 0x320: 0xe00d, 0x321: 0x0008, 0x322: 0xe00d, 0x323: 0x0008,
	0x324: 0xe00d, 0x325: 0x0008, 0x326: 0xe00d, 0x327: 0x0008, 0x328: 0xe00d, 0x329: 0x0008,
	0x32a: 0xe00d, 0x32b: 0x0008, 0x32c: 0xe00d, 0x32d: 0x0008, 0x32e: 0xe00d, 0x32f: 0x0008,
	0x330: 0x04e5, 0x331: 0xe185, 0x332: 0xe18d, 0x333: 0x0008, 0x334: 0x04fd, 0x335: 0x03dd,
	0x336: 0x0018, 0x337: 0xe07d, 0x338: 0x0008, 0x339: 0xe1d5, 0x33a: 0xe00d, 0x33b: 0x0008,
	0x33c: 0x0008, 0x33d: 0x0515, 0x33e: 0x052d, 0x33f: 0x052d,
	// Block 0xd, offset 0x340
	0x340: 0x0008, 0x341: 0x0008, 0x342: 0x0008, 0x343: 0x0008, 0x344: 0x0008, 0x345: 0x0008,
	0x346: 0x0008, 0x347: 0x0008, 0x348: 0x0008, 0x349: 0x0008, 0x34a: 0x0008, 0x34b: 0x0008,
	0x34c: 0x0008, 0x34d: 0x0008, 0x34e: 0x0008, 0x34f: 0x0008, 0x350: 0x0008, 0x351: 0x0008,
	0x352: 0x0008, 0x353: 0x0008, 0x354: 0x0008, 0x355: 0x0008, 0x356: 0x0008, 0x357: 0x0008,
	0x358: 0x0008, 0x359: 0x0008, 0x35a: 0x0008, 0x35b: 0x0008, 0x35c: 0x0008, 0x35d: 0x0008,
	0x35e: 0x0008, 0x35f: 0x0008, 0x360: 0xe00d, 0x361: 0x0008, 0x362: 0xe00d, 0x363: 0x0008,
	0x364: 0xe00d, 0x365: 0x0008, 0x366: 0xe00d, 0x367: 0x0008, 0x368: 0xe00d, 0x369: 0x0008,
	0x36a: 0xe00d, 0x36b: 0x0008, 0x36c: 0xe00d, 0x36d: 0x0008, 0x36e: 0xe00d, 0x36f: 0x0008,
	0x370: 0xe00d, 0x371: 0x0008, 0x372: 0xe00d, 0x373: 0x0008, 0x374: 0xe00d, 0x375: 0x0008,
	0x376: 0xe00d, 0x377: 0x0008, 0x378: 0xe00d, 0x379: 0x0008, 0x37a: 0xe00d, 0x37b: 0x0008,
	0x37c: 0xe00d, 0x37d: 0x0008, 0x37e: 0xe00d, 0x37f: 0x0008,
	// Block 0xe, offset 0x380
	0x380: 0xe00d, 0x381: 0x0008, 0x382: 0x0018, 0x383: 0x3308, 0x384: 0x3308, 0x385: 0x3308,
	0x386: 0x3308, 0x387: 0x3308, 0x388: 0x3318, 0x389: 0x3318, 0x38a: 0xe00d, 0x38b: 0x0008,
	0x38c: 0xe00d, 0x38d: 0x0008, 0x38e: 0xe00d, 0x38f: 0x0008, 0x390: 0xe00d, 0x391: 0x0008,
	0x392: 0xe00d, 0x393: 0x0008, 0x394: 0xe00d, 0x395: 0x0008, 0x396: 0xe00d, 0x397: 0x0008,
	0x398: 0xe00d, 0x399: 0x0008, 0x39a: 0xe00d, 0x39b: 0x0008, 0x39c: 0xe00d, 0x39d: 0x0008,
	0x39e: 0xe00d, 0x39f: 0x0008, 0x3a0: 0xe00d, 0x3a1: 0x0008, 0x3a2: 0xe00d, 0x3a3: 0x0008,
	0x3a4: 0xe00d, 0x3a5: 0x0008, 0x3a6: 0xe00d, 0x3a7: 0x0008, 0x3a8: 0xe00d, 0x3a9: 0x0008,
	0x3aa: 0xe00d, 0x3ab: 0x0008, 0x3ac: 0xe00d, 0x3ad: 0x0008, 0x3ae: 0xe00d, 0x3af: 0x0008,
	0x3b0: 0xe00d, 0x3b1: 0x0008, 0x3b2: 0xe00d, 0x3b3: 0x0008, 0x3b4: 0xe00d, 0x3b5: 0x0008,
	0x3b6: 0xe00d, 0x3b7: 0x0008, 0x3b8: 0xe00d, 0x3b9: 0x0008, 0x3ba: 0xe00d, 0x3bb: 0x0008,
	0x3bc: 0xe00d, 0x3bd: 0x0008, 0x3be: 0xe00d, 0x3bf: 0x0008,
	// Block 0xf, offset 0x3c0
	0x3c0: 0x0040, 0x3c1: 0xe01d, 0x3c2: 0x0008, 0x3c3: 0xe03d, 0x3c4: 0x0008, 0x3c5: 0xe01d,
	0x3c6: 0x0008, 0x3c7: 0xe07d, 0x3c8: 0x0008, 0x3c9: 0xe01d, 0x3ca: 0x0008, 0x3cb: 0xe03d,
	0x3cc: 0x0008, 0x3cd: 0xe01d, 0x3ce: 0x0008, 0x3cf: 0x0008, 0x3d0: 0xe00d, 0x3d1: 0x0008,
	0x3d2: 0xe00d, 0x3d3: 0x0008, 0x3d4: 0xe00d, 0x3d5: 0x0008, 0x3d6: 0xe00d, 0x3d7: 0x0008,
	0x3d8: 0xe00d, 0x3d9: 0x0008, 0x3da: 0xe00d, 0x3db: 0x0008, 0x3dc: 0xe00d, 0x3dd: 0x0008,
	0x3de: 0xe00d, 0x3df: 0x0008, 0x3e0: 0xe00d, 0x3e1: 0x0008, 0x3e2: 0xe00d, 0x3e3: 0x0008,
	0x3e4: 0xe00d, 0x3e5: 0x0008, 0x3e6: 0xe00d, 0x3e7: 0x0008, 0x3e8: 0xe00d, 0x3e9: 0x0008,
	0x3ea: 0xe00d, 0x3eb: 0x0008, 0x3ec: 0xe00d, 0x3ed: 0x0008, 0x3ee: 0xe00d, 0x3ef: 0x0008,
	0x3f0: 0xe00d, 0x3f1: 0x0008, 0x3f2: 0xe00d, 0x3f3: 0x0008, 0x3f4: 0xe00d, 0x3f5: 0x0008,
	0x3f6: 0xe00d, 0x3f7: 0x0008, 0x3f8: 0xe00d, 0x3f9: 0x0008, 0x3fa: 0xe00d, 0x3fb: 0x0008,
	0x3fc: 0xe00d, 0x3fd: 0x0008, 0x3fe: 0xe00d, 0x3ff: 0x0008,
	// Block 0x10, offset 0x400
	0x400: 0xe00d, 0x401: 0x0008, 0x402: 0xe00d, 0x403: 0x0008, 0x404: 0xe00d, 0x405: 0x0008,
	0x406: 0xe00d, 0x407: 0x0008, 0x408: 0xe00d, 0x409: 0x0008, 0x40a: 0xe00d, 0x40b: 0x0008,
	0x40c: 0xe00d, 0x40d: 0x0008, 0x40e: 0xe00d, 0x40f: 0x0008, 0x410: 0xe00d, 0x411: 0x0008,
	0x412: 0xe00d, 0x413: 0x0008, 0x414: 0xe00d, 0x415: 0x0008, 0x416: 0xe00d, 0x417: 0x0008,
	0x418: 0xe00d, 0x419: 0x0008, 0x41a: 0xe00d, 0x41b: 0x0008, 0x41c: 0xe00d, 0x41d: 0x0008,
	0x41e: 0xe00d, 0x41f: 0x0008, 0x420: 0xe00d, 0x421: 0x0008, 0x422: 0xe00d, 0x423: 0x0008,
	0x424: 0xe00d, 0x425: 0x0008, 0x426: 0xe00d, 0x427: 0x0008, 0x428: 0xe00d, 0x429: 0x0008,
	0x42a: 0xe00d, 0x42b: 0x0008, 0x42c: 0xe00d, 0x42d: 0x0008, 0x42e: 0xe00d, 0x42f: 0x0008,
	0x430: 0x0040, 0x431: 0x03f5, 0x432: 0x03f5, 0x433: 0x03f5, 0x434: 0x03f5, 0x435: 0x03f5,
	0x436: 0x03f5, 0x437: 0x03f5, 0x438: 0x03f5, 0x439: 0x03f5, 0x43a: 0x03f5, 0x43b: 0x03f5,
	0x43c: 0x03f5, 0x43d: 0x03f5, 0x43e: 0x03f5, 0x43f: 0x03f5,
	// Block 0x11, offset 0x440
	0x440: 0x0840, 0x441: 0x0840, 0x442: 0x0840, 0x443: 0x0840, 0x444: 0x0840, 0x445: 0x0840,
	0x446: 0x0018, 0x447: 0x0018, 0x448: 0x0818, 0x449: 0x0018, 0x44a: 0x0018, 0x44b: 0x0818,
	0x44c: 0x0018, 0x44d: 0x0818, 0x44e: 0x0018, 0x44f: 0x0018, 0x450: 0x3308, 0x451: 0x3308,
	0x452: 0x3308, 0x453: 0x3308, 0x454: 0x3308, 0x455: 0x3308, 0x456: 0x3308, 0x457: 0x3308,
	0x458: 0x3308, 0x459: 0x3308, 0x45a: 0x3308, 0x45b: 0x0818, 0x45c: 0x0b40, 0x45d: 0x0818,
	0x45e: 0x0818, 0x45f: 0x0818, 0x460: 0x0a08, 0x461: 0x0808, 0x462: 0x0c08, 0x463: 0x0c08,
	0x464: 0x0c08, 0x465: 0x0c08, 0x466: 0x0a08, 0x467: 0x0c08, 0x468: 0x0a08, 0x469: 0x0c08,
	0x46a: 0x0a08, 0x46b: 0x0a08, 0x46c: 0x0a08, 0x46d: 0x0a08, 0x46e: 0x0a08, 0x46f: 0x0c08,
	0x470: 0x0c08, 0x471: 0x0c08, 0x472: 0x0c08, 0x473: 0x0a08, 0x474: 0x0a08, 0x475: 0x0a08,
	0x476: 0x0a08, 0x477: 0x0a08, 0x478: 0x0a08, 0x479: 0x0a08, 0x47a: 0x0a08, 0x47b: 0x0a08,
	0x47c: 0x0a08, 0x47d: 0x0a08, 0x47e: 0x0a08, 0x47f: 0x0a08,
	// Block 0x12, offset 0x480
	0x480: 0x0818, 0x481: 0x0a08, 0x482: 0x0a08, 0x483: 0x0a08, 0x484: 0x0a08, 0x485: 0x0a08,
	0x486: 0x0a08, 0x487: 0x0a08, 0x488: 0x0c08, 0x489: 0x0a08, 0x48a: 0x0a08, 0x48b: 0x3308,
	0x48c: 0x3308, 0x48d: 0x3308, 0x48e: 0x3308, 0x48f: 0x3308, 0x490: 0x3308, 0x491: 0x3308,
	0x492: 0x3308, 0x493: 0x3308, 0x494: 0x3308, 0x495: 0x3308, 0x496: 0x3308, 0x497: 0x3308,
	0x498: 0x3308, 0x499: 0x3308, 0x49a: 0x3308, 0x49b: 0x3308, 0x49c: 0x3308, 0x49d: 0x3308,
	0x49e: 0x3308, 0x49f: 0x3308, 0x4a0: 0x0808, 0x4a1: 0x0808, 0x4a2: 0x0808, 0x4a3: 0x0808,
	0x4a4: 0x0808, 0x4a5: 0x0808, 0x4a6: 0x0808, 0x4a7: 0x0808, 0x4a8: 0x0808, 0x4a9: 0x0808,
	0x4aa: 0x0018, 0x4ab: 0x0818, 0x4ac: 0x0818, 0x4ad: 0x0818, 0x4ae: 0x0a08, 0x4af: 0x0a08,
	0x4b0: 0x3308, 0x4b1: 0x0c08, 0x4b2: 0x0c08, 0x4b3: 0x0c08, 0x4b4: 0x0808, 0x4b5: 0x0139,
	0x4b6: 0x0141, 0x4b7: 0x0149, 0x4b8: 0x0151, 0x4b9: 0x0a08, 0x4ba: 0x0a08, 0x4bb: 0x0a08,
	0x4bc: 0x0a08, 0x4bd: 0x0a08, 0x4be: 0x0a08, 0x4bf: 0x0a08,
	// Block 0x13, offset 0x4c0
	0x4c0: 0x0c08, 0x4c1: 0x0a08, 0x4c2: 0x0a08, 0x4c3: 0x0c08, 0x4c4: 0x0c08, 0x4c5: 0x0c08,
	0x4c6: 0x0c08, 0x4c7: 0x0c08, 0x4c8: 0x0c08, 0x4c9: 0x0c08, 0x4ca: 0x0c08, 0x4cb: 0x0c08,
	0x4cc: 0x0a08, 0x4cd: 0x0c08, 0x4ce: 0x0a08, 0x4cf: 0x0c08, 0x4d0: 0x0a08, 0x4d1: 0x0a08,
	0x4d2: 0x0c08, 0x4d3: 0x0c08, 0x4d4: 0x0818, 0x4d5: 0x0c08, 0x4d6: 0x3308, 0x4d7: 0x3308,
	0x4d8: 0x3308, 0x4d9: 0x3308, 0x4da: 0x3308, 0x4db: 0x3308, 0x4dc: 0x3308, 0x4dd: 0x0840,
	0x4de: 0x0018, 0x4df: 0x3308, 0x4e0: 0x3308, 0x4e1: 0x3308, 0x4e2: 0x3308, 0x4e3: 0x3308,
	0x4e4: 0x3308, 0x4e5: 0x0808, 0x4e6: 0x0808, 0x4e7: 0x3308, 0x4e8: 0x3308, 0x4e9: 0x0018,
	0x4ea: 0x3308, 0x4eb: 0x3308, 0x4ec: 0x3308, 0x4ed: 0x3308, 0x4ee: 0x0c08, 0x4ef: 0x0c08,
	0x4f0: 0x0008, 0x4f1: 0x0008, 0x4f2: 0x0008, 0x4f3: 0x0008, 0x4f4: 0x0008, 0x4f5: 0x0008,
	0x4f6: 0x0008, 0x4f7: 0x0008, 0x4f8: 0x0008, 0x4f9: 0x0008, 0x4fa: 0x0a08, 0x4fb: 0x0a08,
	0x4fc: 0x0a08, 0x4fd: 0x0808, 0x4fe: 0x0808, 0x4ff: 0x0a08,
	// Block 0x14, offset 0x500
	0x500: 0x0818, 0x501: 0x0818, 0x502: 0x0818, 0x503: 0x0818, 0x504: 0x0818, 0x505: 0x0818,
	0x506: 0x0818, 0x507: 0x0818, 0x508: 0x0818, 0x509: 0x0818, 0x50a: 0x0818, 0x50b: 0x0818,
	0x50c: 0x0818, 0x50d: 0x0818, 0x50e: 0x0040, 0x50f: 0x0b40, 0x510: 0x0c08, 0x511: 0x3308,
	0x512: 0x0a08, 0x513: 0x0a08, 0x514: 0x0a08, 0x515: 0x0c08, 0x516: 0x0c08, 0x517: 0x0c08,
	0x518: 0x0c08, 0x519: 0x0c08, 0x51a: 0x0a08, 0x51b: 0x0a08, 0x51c: 0x0a08, 0x51d: 0x0a08,
	0x51e: 0x0c08, 0x51f: 0x0a08, 0x520: 0x0a08, 0x521: 0x0a08, 0x522: 0x0a08, 0x523: 0x0a08,
	0x524: 0x0a08, 0x525: 0x0a08, 0x526: 0x0a08, 0x527: 0x0a08, 0x528: 0x0c08, 0x529: 0x0a08,
	0x52a: 0x0c08, 0x52b: 0x0a08, 0x52c: 0x0c08, 0x52d: 0x0a08, 0x52e: 0x0a08, 0x52f: 0x0c08,
	0x530: 0x3308, 0x531: 0x3308, 0x532: 0x3308, 0x533: 0x3308, 0x534: 0x3308, 0x535: 0x3308,
	0x536: 0x3308, 0x537: 0x3308, 0x538: 0x3308, 0x539: 0x3308, 0x53a: 0x3308, 0x53b: 0x3308,
	0x53c: 0x3308, 0x53d: 0x3308, 0x53e: 0x3308, 0x53f: 0x3308,
	// Block 0x15, offset 0x540
	0x540: 0x0c08, 0x541: 0x0a08, 0x542: 0x0a08, 0x543: 0x0a08, 0x544: 0x0a08, 0x545: 0x0a08,
	0x546: 0x0c08, 0x547: 0x0c08, 0x548: 0x0a08, 0x549: 0x0c08, 0x54a: 0x0a08, 0x54b: 0x0a08,
	0x54c: 0x0a08, 0x54d: 0x0a08, 0x54e: 0x0a08, 0x54f: 0x0a08, 0x550: 0x0a08, 0x551: 0x0a08,
	0x552: 0x0a08, 0x553: 0x0a08, 0x554: 0x0c08, 0x555: 0x0a08, 0x556: 0x0c08, 0x557: 0x0c08,
	0x558: 0x0c08, 0x559: 0x3308, 0x55a: 0x3308, 0x55b: 0x3308, 0x55c: 0x0040, 0x55d: 0x0040,
	0x55e: 0x0818, 0x55f: 0x0040, 0x560: 0x0a08, 0x561: 0x0808, 0x562: 0x0a08, 0x563: 0x0a08,
	0x564: 0x0a08, 0x565: 0x0a08, 0x566: 0x0808, 0x567: 0x0c08, 0x568: 0x0a08, 0x569: 0x0c08,
	0x56a: 0x0c08, 0x56b: 0x0040, 0x56c: 0x0040, 0x56d: 0x0040, 0x56e: 0x0040, 0x56f: 0x0040,
	0x570: 0x0c08, 0x571: 0x0c08, 0x572: 0x0c08, 0x573: 0x0c08, 0x574: 0x0c08, 0x575: 0x0c08,
	0x576: 0x0c08, 0x577: 0x0c08, 0x578: 0x0c08, 0x579: 0x0c08, 0x57a: 0x0c08, 0x57b: 0x0c08,
	0x57c: 0x0c08, 0x57d: 0x0c08, 0x57e: 0x0c08, 0x57f: 0x0c08,
	// Block 0x16, offset 0x580
	0x580: 0x0c08, 0x581: 0x0c08, 0x582: 0x0c08, 0x583: 0x0808, 0x584: 0x0808, 0x585: 0x0808,
	0x586: 0x0a08, 0x587: 0x0808, 0x588: 0x0818, 0x589: 0x0a08, 0x58a: 0x0a08, 0x58b: 0x0a08,
	0x58c: 0x0a08, 0x58d: 0x0a08, 0x58e: 0x0c08, 0x58f: 0x0040, 0x590: 0x0840, 0x591: 0x0840,
	0x592: 0x0040, 0x593: 0x0040, 0x594: 0x0040, 0x595: 0x0040, 0x596: 0x0040, 0x597: 0x0040,
	0x598: 0x3308, 0x599: 0x3308, 0x59a: 0x3308, 0x59b: 0x3308, 0x59c: 0x3308, 0x59d: 0x3308,
	0x59e: 0x3308, 0x59f: 0x3308, 0x5a0: 0x0a08, 0x5a1: 0x0a08, 0x5a2: 0x0a08, 0x5a3: 0x0a08,
	0x5a4: 0x0a08, 0x5a5: 0x0a08, 0x5a6: 0x0a08, 0x5a7: 0x0a08, 0x5a8: 0x0a08, 0x5a9: 0x0a08,
	0x5aa: 0x0c08, 0x5ab: 0x0c08, 0x5ac: 0x0c08, 0x5ad: 0x0808, 0x5ae: 0x0c08, 0x5af: 0x0a08,
	0x5b0: 0x0a08, 0x5b1: 0x0c08, 0x5b2: 0x0c08, 0x5b3: 0x0a08, 0x5b4: 0x0a08, 0x5b5: 0x0a08,
	0x5b6: 0x0a08, 0x5b7: 0x0a08, 0x5b8: 0x0a08, 0x5b9: 0x0c08, 0x5ba: 0x0a08, 0x5bb: 0x0a08,
	0x5bc: 0x0a08, 0x5bd: 0x0a08, 0x5be: 0x0a08, 0x5bf: 0x0a08,
	// Block 0x17, offset 0x5c0
	0x5c0: 0x3008, 0x5c1: 0x3308, 0x5c2: 0x3308, 0x5c3: 0x3308, 0x5c4: 0x3308, 0x5c5: 0x3308,
	0x5c6: 0x3308, 0x5c7: 0x3308, 0x5c8: 0x3308, 0x5c9: 0x3008, 0x5ca: 0x3008, 0x5cb: 0x3008,
	0x5cc: 0x3008, 0x5cd: 0x3b08, 0x5ce: 0x3008, 0x5cf: 0x3008, 0x5d0: 0x0008, 0x5d1: 0x3308,
	0x5d2: 0x3308, 0x5d3: 0x3308, 0x5d4: 0x3308, 0x5d5: 0x3308, 0x5d6: 0x3308, 0x5d7: 0x3308,
	0x5d8: 0x0159, 0x5d9: 0x0161, 0x5da: 0x0169, 0x5db: 0x0171, 0x5dc: 0x0179, 0x5dd: 0x0181,
	0x5de: 0x0189, 0x5df: 0x0191, 0x5e0: 0x0008, 0x5e1: 0x0008, 0x5e2: 0x3308, 0x5e3: 0x3308,
	0x5e4: 0x0018, 0x5e5: 0x0018, 0x5e6: 0x0008, 0x5e7: 0x0008, 0x5e8: 0x0008, 0x5e9: 0x0008,
	0x5ea: 0x0008, 0x5eb: 0x0008, 0x5ec: 0x0008, 0x5ed: 0x0008, 0x5ee: 0x0008, 0x5ef: 0x0008,
	0x5f0: 0x0018, 0x5f1: 0x0008, 0x5f2: 0x0008, 0x5f3: 0x0008, 0x5f4: 0x0008, 0x5f5: 0x0008,
	0x5f6: 0x0008, 0x5f7: 0x0008, 0x5f8: 0x0008, 0x5f9: 0x0008, 0x5fa: 0x0008, 0x5fb: 0x0008,
	0x5fc: 0x0008, 0x5fd: 0x0008, 0x5fe: 0x0008, 0x5ff: 0x0008,
	// Block 0x18, offset 0x600
	0x600: 0x0008, 0x601: 0x3308, 0x602: 0x3008, 0x603: 0x3008, 0x604: 0x0040, 0x605: 0x0008,
	0x606: 0x0008, 0x607: 0x0008, 0x608: 0x0008, 0x609: 0x0008, 0x60a: 0x0008, 0x60b: 0x0008,
	0x60c: 0x0008, 0x60d: 0x0040, 0x60e: 0x0040, 0x60f: 0x0008, 0x610: 0x0008, 0x611: 0x0040,
	0x612: 0x0040, 0x613: 0x0008, 0x614: 0x0008, 0x615: 0x0008, 0x616: 0x0008, 0x617: 0x0008,
	0x618: 0x0008, 0x619: 0x0008, 0x61a: 0x0008, 0x61b: 0x0008, 0x61c: 0x0008, 0x61d: 0x0008,
	0x61e: 0x0008, 0x61f: 0x0008, 0x620: 0x0008, 0x621: 0x0008, 0x622: 0x0008, 0x623: 0x0008,
	0x624: 0x0008, 0x625: 0x0008, 0x626: 0x0008, 0x627: 0x0008, 0x628: 0x0008, 0x629: 0x0040,
	0x62a: 0x0008, 0x62b: 0x0008, 0x62c: 0x0008, 0x62d: 0x0008, 0x62e: 0x0008, 0x62f: 0x0008,
	0x630: 0x0008, 0x631: 0x0040, 0x632: 0x0008, 0x633: 0x0040, 0x634: 0x0040, 0x635: 0x0040,
	0x636: 0x0008, 0x637: 0x0008, 0x638: 0x0008, 0x639: 0x0008, 0x63a: 0x0040, 0x63b: 0x0040,
	0x63c: 0x3308, 0x63d: 0x0008, 0x63e: 0x3008, 0x63f: 0x3008,
	// Block 0x19, offset 0x640
	0x640: 0x3008, 0x641: 0x3308, 0x642: 0x3308, 0x643: 0x3308, 0x644: 0x3308, 0x645: 0x0040,
	0x646: 0x0040, 0x647: 0x3008, 0x648: 0x3008, 0x649: 0x0040, 0x64a: 0x0040, 0x64b: 0x3008,
	0x64c: 0x3008, 0x64d: 0x3b08, 0x64e: 0x0008, 0x64f: 0x0040, 0x650: 0x0040, 0x651: 0x0040,
	0x652: 0x0040, 0x653: 0x0040, 0x654: 0x0040, 0x655: 0x0040, 0x656: 0x0040, 0x657: 0x3008,
	0x658: 0x0040, 0x659: 0x0040, 0x65a: 0x0040, 0x65b: 0x0040, 0x65c: 0x0199, 0x65d: 0x01a1,
	0x65e: 0x0040, 0x65f: 0x01a9, 0x660: 0x0008, 0x661: 0x0008, 0x662: 0x3308, 0x663: 0x3308,
	0x664: 0x0040, 0x665: 0x0040, 0x666: 0x0008, 0x667: 0x0008, 0x668: 0x0008, 0x669: 0x0008,
	0x66a: 0x0008, 0x66b: 0x0008, 0x66c: 0x0008, 0x66d: 0x0008, 0x66e: 0x0008, 0x66f: 0x0008,
	0x670: 0x0008, 0x671: 0x0008, 0x672: 0x0018, 0x673: 0x0018, 0x674: 0x0018, 0x675: 0x0018,
	0x676: 0x0018, 0x677: 0x0018, 0x678: 0x0018, 0x679: 0x0018, 0x67a: 0x0018, 0x67b: 0x0018,
	0x67c: 0x0008, 0x67d: 0x0018, 0x67e: 0x3308, 0x67f: 0x0040,
	// Block 0x1a, offset 0x680
	0x680: 0x0040, 0x681: 0x3308, 0x682: 0x3308, 0x683: 0x3008, 0x684: 0x0040, 0x685: 0x0008,
	0x686: 0x0008, 0x687: 0x0008, 0x688: 0x0008, 0x689: 0x0008, 0x68a: 0x0008, 0x68b: 0x0040,
	0x68c: 0x0040, 0x68d: 0x0040, 0x68e: 0x0040, 0x68f: 0x0008, 0x690: 0x0008, 0x691: 0x0040,
	0x692: 0x0040, 0x693: 0x0008, 0x694: 0x0008, 0x695: 0x0008, 0x696: 0x0008, 0x697: 0x0008,
	0x698: 0x0008, 0x699: 0x0008, 0x69a: 0x0008, 0x69b: 0x0008, 0x69c: 0x0008, 0x69d: 0x0008,
	0x69e: 0x0008, 0x69f: 0x0008, 0x6a0: 0x0008, 0x6a1: 0x0008, 0x6a2: 0x0008, 0x6a3: 0x0008,
	0x6a4: 0x0008, 0x6a5: 0x0008, 0x6a6: 0x0008, 0x6a7: 0x0008, 0x6a8: 0x0008, 0x6a9: 0x0040,
	0x6aa: 0x0008, 0x6ab: 0x0008, 0x6ac: 0x0008, 0x6ad: 0x0008, 0x6ae: 0x0008, 0x6af: 0x0008,
	0x6b0: 0x0008, 0x6b1: 0x0040, 0x6b2: 0x0008, 0x6b3: 0x01b1, 0x6b4: 0x0040, 0x6b5: 0x0008,
	0x6b6: 0x01b9, 0x6b7: 0x0040, 0x6b8: 0x0008, 0x6b9: 0x0008, 0x6ba: 0x0040, 0x6bb: 0x0040,
	0x6bc: 0x3308, 0x6bd: 0x0040, 0x6be: 0x3008, 0x6bf: 0x3008,
	// Block 0x1b, offset 0x6c0
	0x6c0: 0x3008, 0x6c1: 0x3308, 0x6c2: 0x3308, 0x6c3: 0x0040, 0x6c4: 0x0040, 0x6c5: 0x0040,
	0x6c6: 0x0040, 0x6c7: 0x3308, 0x6c8: 0x3308, 0x6c9: 0x0040, 0x6ca: 0x0040, 0x6cb: 0x3308,
	0x6cc: 0x3308, 0x6cd: 0x3b08, 0x6ce: 0x0040, 0x6cf: 0x0040, 0x6d0: 0x0040, 0x6d1: 0x3308,
	0x6d2: 0x0040, 0x6d3: 0x0040, 0x6d4: 0x0040, 0x6d5: 0x0040, 0x6d6: 0x0040, 0x6d7: 0x0040,
	0x6d8: 0x0040, 0x6d9: 0x01c1, 0x6da: 0x01c9, 0x6db: 0x01d1, 0x6dc: 0x0008, 0x6dd: 0x0040,
	0x6de: 0x01d9, 0x6df: 0x0040, 0x6e0: 0x0040, 0x6e1: 0x0040, 0x6e2: 0x0040, 0x6e3: 0x0040,
	0x6e4: 0x0040, 0x6e5: 0x0040, 0x6e6: 0x0008, 0x6e7: 0x0008, 0x6e8: 0x0008, 0x6e9: 0x0008,
	0x6ea: 0x0008, 0x6eb: 0x0008, 0x6ec: 0x0008, 0x6ed: 0x0008, 0x6ee: 0x0008, 0x6ef: 0x0008,
	0x6f0: 0x3308, 0x6f1: 0x3308, 0x6f2: 0x0008, 0x6f3: 0x0008, 0x6f4: 0x0008, 0x6f5: 0x3308,
	0x6f6: 0x0018, 0x6f7: 0x0040, 0x6f8: 0x0040, 0x6f9: 0x0040, 0x6fa: 0x0040, 0x6fb: 0x0040,
	0x6fc: 0x0040, 0x6fd: 0x0040, 0x6fe: 0x0040, 0x6ff: 0x0040,
	// Block 0x1c, offset 0x700
	0x700: 0x0040, 0x701: 0x3308, 0x702: 0x3308, 0x703: 0x3008, 0x704: 0x0040, 0x705: 0x0008,
	0x706: 0x0008, 0x707: 0x0008, 0x708: 0x0008, 0x709: 0x0008, 0x70a: 0x0008, 0x70b: 0x0008,
	0x70c: 0x0008, 0x70d: 0x0008, 0x70e: 0x0040, 0x70f: 0x0008, 0x710: 0x0008, 0x711: 0x0008,
	0x712: 0x0040, 0x713: 0x0008, 0x714: 0x0008, 0x715: 0x0008, 0x716: 0x0008, 0x717: 0x0008,
	0x718: 0x0008, 0x719: 0x0008, 0x71a: 0x0008, 0x71b: 0x0008, 0x71c: 0x0008, 0x71d: 0x0008,
	0x71e: 0x0008, 0x71f: 0x0008, 0x720: 0x0008, 0x721: 0x0008, 0x722: 0x0008, 0x723: 0x0008,
	0x724: 0x0008, 0x725: 0x0008, 0x726: 0x0008, 0x727: 0x0008, 0x728: 0x0008, 0x729: 0x0040,
	0x72a: 0x0008, 0x72b: 0x0008, 0x72c: 0x0008, 0x72d: 0x0008, 0x72e: 0x0008, 0x72f: 0x0008,
	0x730: 0x0008, 0x731: 0x0040, 0x732: 0x0008, 0x733: 0x0008, 0x734: 0x0040, 0x735: 0x0008,
	0x736: 0x0008, 0x737: 0x0008, 0x738: 0x0008, 0x739: 0x0008, 0x73a: 0x0040, 0x73b: 0x0040,
	0x73c: 0x3308, 0x73d: 0x0008, 0x73e: 0x3008, 0x73f: 0x3008,
	// Block 0x1d, offset 0x740
	0x740: 0x3008, 0x741: 0x3308, 0x742: 0x3308, 0x743: 0x3308, 0x744: 0x3308, 0x745: 0x3308,
	0x746: 0x0040, 0x747: 0x3308, 0x748: 0x3308, 0x749: 0x3008, 0x74a: 0x0040, 0x74b: 0x3008,
	0x74c: 0x3008, 0x74d: 0x3b08, 0x74e: 0x0040, 0x74f: 0x0040, 0x750: 0x0008, 0x751: 0x0040,
	0x752: 0x0040, 0x753: 0x0040, 0x754: 0x0040, 0x755: 0x0040, 0x756: 0x0040, 0x757: 0x0040,
	0x758: 0x0040, 0x759: 0x0040, 0x75a: 0x0040, 0x75b: 0x0040, 0x75c: 0x0040, 0x75d: 0x0040,
	0x75e: 0x0040, 0x75f: 0x0040, 0x760: 0x0008, 0x761: 0x0008, 0x762: 0x3308, 0x763: 0x3308,
	0x764: 0x0040, 0x765: 0x0040, 0x766: 0x0008, 0x767: 0x0008, 0x768: 0x0008, 0x769: 0x0008,
	0x76a: 0x0008, 0x76b: 0x0008, 0x76c: 0x0008, 0x76d: 0x0008, 0x76e: 0x0008, 0x76f: 0x0008,
	0x770: 0x0018, 0x771: 0x0018, 0x772: 0x0040, 0x773: 0x0040, 0x774: 0x0040, 0x775: 0x0040,
	0x776: 0x0040, 0x777: 0x0040, 0x778: 0x0040, 0x779: 0x0008, 0x77a: 0x3308, 0x77b: 0x3308,
	0x77c: 0x3308, 0x77d: 0x3308, 0x77e: 0x3308, 0x77f: 0x3308,
	// Block 0x1e, offset 0x780
	0x780: 0x0040, 0x781: 0x3308, 0x782: 0x3008, 0x783: 0x3008, 0x784: 0x0040, 0x785: 0x0008,
	0x786: 0x0008, 0x787: 0x0008, 0x788: 0x0008, 0x789: 0x0008, 0x78a: 0x0008, 0x78b: 0x0008,
	0x78c: 0x0008, 0x78d: 0x0040, 0x78e: 0x0040, 0x78f: 0x0008, 0x790: 0x0008, 0x791: 0x0040,
	0x792: 0x0040, 0x793: 0x0008, 0x794: 0x0008, 0x795: 0x0008, 0x796: 0x0008, 0x797: 0x0008,
	0x798: 0x0008, 0x799: 0x0008, 0x79a: 0x0008, 0x79b: 0x0008, 0x79c: 0x0008, 0x79d: 0x0008,
	0x79e: 0x0008, 0x79f: 0x0008, 0x7a0: 0x0008, 0x7a1: 0x0008, 0x7a2: 0x0008, 0x7a3: 0x0008,
	0x7a4: 0x0008, 0x7a5: 0x0008, 0x7a6: 0x0008, 0x7a7: 0x0008, 0x7a8: 0x0008, 0x7a9: 0x0040,
	0x7aa: 0x0008, 0x7ab: 0x0008, 0x7ac: 0x0008, 0x7ad: 0x0008, 0x7ae: 0x0008, 0x7af: 0x0008,
	0x7b0: 0x0008, 0x7b1: 0x0040, 0x7b2: 0x0008, 0x7b3: 0x0008, 0x7b4: 0x0040, 0x7b5: 0x0008,
	0x7b6: 0x0008, 0x7b7: 0x0008, 0x7b8: 0x0008, 0x7b9: 0x0008, 0x7ba: 0x0040, 0x7bb: 0x0040,
	0x7bc: 0x3308, 0x7bd: 0x0008, 0x7be: 0x3008, 0x7bf: 0x3308,
	// Block 0x1f, offset 0x7c0
	0x7c0: 0x3008, 0x7c1: 0x3308, 0x7c2: 0x3308, 0x7c3: 0x3308, 0x7c4: 0x3308, 0x7c5: 0x0040,
	0x7c6: 0x0040, 0x7c7: 0x3008, 0x7c8: 0x3008, 0x7c9: 0x0040, 0x7ca: 0x0040, 0x7cb: 0x3008,
	0x7cc: 0x3008, 0x7cd: 0x3b08, 0x7ce: 0x0040, 0x7cf: 0x0040, 0x7d0: 0x0040, 0x7d1: 0x0040,
	0x7d2: 0x0040, 0x7d3: 0x0040, 0x7d4: 0x0040, 0x7d5: 0x3308, 0x7d6: 0x3308, 0x7d7: 0x3008,
	0x7d8: 0x0040, 0x7d9: 0x0040, 0x7da: 0x0040, 0x7db: 0x0040, 0x7dc: 0x01e1, 0x7dd: 0x01e9,
	0x7de: 0x0040, 0x7df: 0x0008, 0x7e0: 0x0008, 0x7e1: 0x0008, 0x7e2: 0x3308, 0x7e3: 0x3308,
	0x7e4: 0x0040, 0x7e5: 0x0040, 0x7e6: 0x0008, 0x7e7: 0x0008, 0x7e8: 0x0008, 0x7e9: 0x0008,
	0x7ea: 0x0008, 0x7eb: 0x0008, 0x7ec: 0x0008, 0x7ed: 0x0008, 0x7ee: 0x0008, 0x7ef: 0x0008,
	0x7f0: 0x0018, 0x7f1: 0x0008, 0x7f2: 0x0018, 0x7f3: 0x0018, 0x7f4: 0x0018, 0x7f5: 0x0018,
	0x7f6: 0x0018, 0x7f7: 0x0018, 0x7f8: 0x0040, 0x7f9: 0x0040, 0x7fa: 0x0040, 0x7fb: 0x0040,
	0x7fc: 0x0040, 0x7fd: 0x0040, 0x7fe: 0x0040, 0x7ff: 0x0040,
	// Block 0x20, offset 0x800
	0x800: 0x0040, 0x801: 0x0040, 0x802: 0x3308, 0x803: 0x0008, 0x804: 0x0040, 0x805: 0x0008,
	0x806: 0x0008, 0x807: 0x0008, 0x808: 0x0008, 0x809: 0x0008, 0x80a: 0x0008, 0x80b: 0x0040,
	0x80c: 0x0040, 0x80d: 0x0040, 0x80e: 0x0008, 0x80f: 0x0008, 0x810: 0x0008, 0x811: 0x0040,
	0x812: 0x0008, 0x813: 0x0008, 0x814: 0x0008, 0x815: 0x0008, 0x816: 0x0040, 0x817: 0x0040,
	0x818: 0x0040, 0x819: 0x0008, 0x81a: 0x0008, 0x81b: 0x0040, 0x81c: 0x0008, 0x81d: 0x0040,
	0x81e: 0x0008, 0x81f: 0x0008, 0x820: 0x0040, 0x821: 0x0040, 0x822: 0x0040, 0x823: 0x0008,
	0x824: 0x0008, 0x825: 0x0040, 0x826: 0x0040, 0x827: 0x0040, 0x828: 0x0008, 0x829: 0x0008,
	0x82a: 0x0008, 0x82b: 0x0040, 0x82c: 0x0040, 0x82d: 0x0040, 0x82e: 0x0008, 0x82f: 0x0008,
	0x830: 0x0008, 0x831: 0x0008, 0x832: 0x0008, 0x833: 0x0008, 0x834: 0x0008, 0x835: 0x0008,
	0x836: 0x0008, 0x837: 0x0008, 0x838: 0x0008, 0x839: 0x0008, 0x83a: 0x0040, 0x83b: 0x0040,
	0x83c: 0x0040, 0x83d: 0x0040, 0x83e: 0x3008, 0x83f: 0x3008,
	// Block 0x21, offset 0x840
	0x840: 0x3308, 0x841: 0x3008, 0x842: 0x3008, 0x843: 0x3008, 0x844: 0x3008, 0x845: 0x0040,
	0x846: 0x3308, 0x847: 0x3308, 0x848: 0x3308, 0x849: 0x0040, 0x84a: 0x3308, 0x84b: 0x3308,
	0x84c: 0x3308, 0x84d: 0x3b08, 0x84e: 0x0040, 0x84f: 0x0040, 0x850: 0x0040, 0x851: 0x0040,
	0x852: 0x0040, 0x853: 0x0040, 0x854: 0x0040, 0x855: 0x3308, 0x856: 0x3308, 0x857: 0x0040,
	0x858: 0x0008, 0x859: 0x0008, 0x85a: 0x0008, 0x85b: 0x0040, 0x85c: 0x0040, 0x85d: 0x0008,
	0x85e: 0x0040, 0x85f: 0x0040, 0x860: 0x0008, 0x861: 0x0008, 0x862: 0x3308, 0x863: 0x3308,
	0x864: 0x0040, 0x865: 0x0040, 0x866: 0x0008, 0x867: 0x0008, 0x868: 0x0008, 0x869: 0x0008,
	0x86a: 0x0008, 0x86b: 0x0008, 0x86c: 0x0008, 0x86d: 0x0008, 0x86e: 0x0008, 0x86f: 0x0008,
	0x870: 0x0040, 0x871: 0x0040, 0x872: 0x0040, 0x873: 0x0040, 0x874: 0x0040, 0x875: 0x0040,
	0x876: 0x0040, 0x877: 0x0018, 0x878: 0x0018, 0x879: 0x0018, 0x87a: 0x0018, 0x87b: 0x0018,
	0x87c: 0x0018, 0x87d: 0x0018, 0x87e: 0x0018, 0x87f: 0x0018,
	// Block 0x22, offset 0x880
	0x880: 0x0008, 0x881: 0x3308, 0x882: 0x3008, 0x883: 0x3008, 0x884: 0x0018, 0x885: 0x0008,
	0x886: 0x0008, 0x887: 0x0008, 0x888: 0x0008, 0x889: 0x0008, 0x88a: 0x0008, 0x88b: 0x0008,
	0x88c: 0x0008, 0x88d: 0x0040, 0x88e: 0x0008, 0x88f: 0x0008, 0x890: 0x0008, 0x891: 0x0040,
	0x892: 0x0008, 0x893: 0x0008, 0x894: 0x0008, 0x895: 0x0008, 0x896: 0x0008, 0x897: 0x0008,
	0x898: 0x0008, 0x899: 0x0008, 0x89a: 0x0008, 0x89b: 0x0008, 0x89c: 0x0008, 0x89d: 0x0008,
	0x89e: 0x0008, 0x89f: 0x0008, 0x8a0: 0x0008, 0x8a1: 0x0008, 0x8a2: 0x0008, 0x8a3: 0x0008,
	0x8a4: 0x0008, 0x8a5: 0x0008, 0x8a6: 0x0008, 0x8a7: 0x0008, 0x8a8: 0x0008, 0x8a9: 0x0040,
	0x8aa: 0x0008, 0x8ab: 0x0008, 0x8ac: 0x0008, 0x8ad: 0x0008, 0x8ae: 0x0008, 0x8af: 0x0008,
	0x8b0: 0x0008, 0x8b1: 0x0008, 0x8b2: 0x0008, 0x8b3: 0x0008, 0x8b4: 0x0040, 0x8b5: 0x0008,
	0x8b6: 0x0008, 0x8b7: 0x0008, 0x8b8: 0x0008, 0x8b9: 0x0008, 0x8ba: 0x0040, 0x8bb: 0x0040,
	0x8bc: 0x3308, 0x8bd: 0x0008, 0x8be: 0x3008, 0x8bf: 0x3308,
	// Block 0x23, offset 0x8c0
	0x8c0: 0x3008, 0x8c1: 0x3008, 0x8c2: 0x3008, 0x8c3: 0x3008, 0x8c4: 0x3008, 0x8c5: 0x0040,
	0x8c6: 0x3308, 0x8c7: 0x3008, 0x8c8: 0x3008, 0x8c9: 0x0040, 0x8ca: 0x3008, 0x8cb: 0x3008,
	0x8cc: 0x3308, 0x8cd: 0x3b08, 0x8ce: 0x0040, 0x8cf: 0x0040, 0x8d0: 0x0040, 0x8d1: 0x0040,
	0x8d2: 0x0040, 0x8d3: 0x0040, 0x8d4: 0x0040, 0x8d5: 0x3008, 0x8d6: 0x3008, 0x8d7: 0x0040,
	0x8d8: 0x0040, 0x8d9: 0x0040, 0x8da: 0x0040, 0x8db: 0x0040, 0x8dc: 0x0040, 0x8dd: 0x0008,
	0x8de: 0x0008, 0x8df: 0x0040, 0x8e0: 0x0008, 0x8e1: 0x0008, 0x8e2: 0x3308, 0x8e3: 0x3308,
	0x8e4: 0x0040, 0x8e5: 0x0040, 0x8e6: 0x0008, 0x8e7: 0x0008, 0x8e8: 0x0008, 0x8e9: 0x0008,
	0x8ea: 0x0008, 0x8eb: 0x0008, 0x8ec: 0x0008, 0x8ed: 0x0008, 0x8ee: 0x0008, 0x8ef: 0x0008,
	0x8f0: 0x0040, 0x8f1: 0x0008, 0x8f2: 0x0008, 0x8f3: 0x3008, 0x8f4: 0x0040, 0x8f5: 0x0040,
	0x8f6: 0x0040, 0x8f7: 0x0040, 0x8f8: 0x0040, 0x8f9: 0x0040, 0x8fa: 0x0040, 0x8fb: 0x0040,
	0x8fc: 0x0040, 0x8fd: 0x0040, 0x8fe: 0x0040, 0x8ff: 0x0040,
	// Block 0x24, offset 0x900
	0x900: 0x3008, 0x901: 0x3308, 0x902: 0x3308, 0x903: 0x3308, 0x904: 0x3308, 0x905: 0x0040,
	0x906: 0x3008, 0x907: 0x3008, 0x908: 0x3008, 0x909: 0x0040, 0x90a: 0x3008, 0x90b: 0x3008,
	0x90c: 0x3008, 0x90d: 0x3b08, 0x90e: 0x0008, 0x90f: 0x0018, 0x910: 0x0040, 0x911: 0x0040,
	0x912: 0x0040, 0x913: 0x0040, 0x914: 0x0008, 0x915: 0x0008, 0x916: 0x0008, 0x917: 0x3008,
	0x918: 0x0018, 0x919: 0x0018, 0x91a: 0x0018, 0x91b: 0x0018, 0x91c: 0x0018, 0x91d: 0x0018,
	0x91e: 0x0018, 0x91f: 0x0008, 0x920: 0x0008, 0x921: 0x0008, 0x922: 0x3308, 0x923: 0x3308,
	0x924: 0x0040, 0x925: 0x0040, 0x926: 0x0008, 0x927: 0x0008, 0x928: 0x0008, 0x929: 0x0008,
	0x92a: 0x0008, 0x92b: 0x0008, 0x92c: 0x0008, 0x92d: 0x0008, 0x92e: 0x0008, 0x92f: 0x0008,
	0x930: 0x0018, 0x931: 0x0018, 0x932: 0x0018, 0x933: 0x0018, 0x934: 0x0018, 0x935: 0x0018,
	0x936: 0x0018, 0x937: 0x0018, 0x938: 0x0018, 0x939: 0x0018, 0x93a: 0x0008, 0x93b: 0x0008,
	0x93c: 0x0008, 0x93d: 0x0008, 0x93e: 0x0008, 0x93f: 0x0008,
	// Block 0x25, offset 0x940
	0x940: 0x0040, 0x941: 0x0008, 0x942: 0x0008, 0x943: 0x0040, 0x944: 0x0008, 0x945: 0x0040,
	0x946: 0x0008, 0x947: 0x0008, 0x948: 0x0008, 0x949: 0x0008, 0x94a: 0x0008, 0x94b: 0x0040,
	0x94c: 0x0008, 0x94d: 0x0008, 0x94e: 0x0008, 0x94f: 0x0008, 0x950: 0x0008, 0x951: 0x0008,
	0x952: 0x0008, 0x953: 0x0008, 0x954: 0x0008, 0x955: 0x0008, 0x956: 0x0008, 0x957: 0x0008,
	0x958: 0x0008, 0x959: 0x0008, 0x95a: 0x0008, 0x95b: 0x0008, 0x95c: 0x0008, 0x95d: 0x0008,
	0x95e: 0x0008, 0x95f: 0x0008, 0x960: 0x0008, 0x961: 0x0008, 0x962: 0x0008, 0x963: 0x0008,
	0x964: 0x0040, 0x965: 0x0008, 0x966: 0x0040, 0x967: 0x0008, 0x968: 0x0008, 0x969: 0x0008,
	0x96a: 0x0008, 0x96b: 0x0008, 0x96c: 0x0008, 0x96d: 0x0008, 0x96e: 0x0008, 0x96f: 0x0008,
	0x970: 0x0008, 0x971: 0x3308, 0x972: 0x0008, 0x973: 0x01f9, 0x974: 0x3308, 0x975: 0x3308,
	0x976: 0x3308, 0x977: 0x3308, 0x978: 0x3308, 0x979: 0x3308, 0x97a: 0x3b08, 0x97b: 0x3308,
	0x97c: 0x3308, 0x97d: 0x0008, 0x97e: 0x0040, 0x97f: 0x0040,
	// Block 0x26, offset 0x980
	0x980: 0x0008, 0x981: 0x0008, 0x982: 0x0008, 0x983: 0x0211, 0x984: 0x0008, 0x985: 0x0008,
	0x986: 0x0008, 0x987: 0x0008, 0x988: 0x0040, 0x989: 0x0008, 0x98a: 0x0008, 0x98b: 0x0008,
	0x98c: 0x0008, 0x98d: 0x0219, 0x98e: 0x0008, 0x98f: 0x0008, 0x990: 0x0008, 0x991: 0x0008,
	0x992: 0x0221, 0x993: 0x0008, 0x994: 0x0008, 0x995: 0x0008, 0x996: 0x0008, 0x997: 0x0229,
	0x998: 0x0008, 0x999: 0x0008, 0x99a: 0x0008, 0x99b: 0x0008, 0x99c: 0x0231, 0x99d: 0x0008,
	0x99e: 0x0008, 0x99f: 0x0008, 0x9a0: 0x0008, 0x9a1: 0x0008, 0x9a2: 0x0008, 0x9a3: 0x0008,
	0x9a4: 0x0008, 0x9a5: 0x0008, 0x9a6: 0x0008, 0x9a7: 0x0008, 0x9a8: 0x0008, 0x9a9: 0x0239,
	0x9aa: 0x0008, 0x9ab: 0x0008, 0x9ac: 0x0008, 0x9ad: 0x0040, 0x9ae: 0x0040, 0x9af: 0x0040,
	0x9b0: 0x0040, 0x9b1: 0x3308, 0x9b2: 0x3308, 0x9b3: 0x0241, 0x9b4: 0x3308, 0x9b5: 0x0249,
	0x9b6: 0x0251, 0x9b7: 0x0259, 0x9b8: 0x0261, 0x9b9: 0x0269, 0x9ba: 0x3308, 0x9bb: 0x3308,
	0x9bc: 0x3308, 0x9bd: 0x3308, 0x9be: 0x3308, 0x9bf: 0x3008,
	// Block 0x27, offset 0x9c0
	0x9c0: 0x3308, 0x9c1: 0x0271, 0x9c2: 0x3308, 0x9c3: 0x3308, 0x9c4: 0x3b08, 0x9c5: 0x0018,
	0x9c6: 0x3308, 0x9c7: 0x3308, 0x9c8: 0x0008, 0x9c9: 0x0008, 0x9ca: 0x0008, 0x9cb: 0x0008,
	0x9cc: 0x0008, 0x9cd: 0x3308, 0x9ce: 0x3308, 0x9cf: 0x3308, 0x9d0: 0x3308, 0x9d1: 0x3308,
	0x9d2: 0x3308, 0x9d3: 0x0279, 0x9d4: 0x3308, 0x9d5: 0x3308, 0x9d6: 0x3308, 0x9d7: 0x3308,
	0x9d8: 0x0040, 0x9d9: 0x3308, 0x9da: 0x3308, 0x9db: 0x3308, 0x9dc: 0x3308, 0x9dd: 0x0281,
	0x9de: 0x3308, 0x9df: 0x3308, 0x9e0: 0x3308, 0x9e1: 0x3308, 0x9e2: 0x0289, 0x9e3: 0x3308,
	0x9e4: 0x3308, 0x9e5: 0x3308, 0x9e6: 0x3308, 0x9e7: 0x0291, 0x9e8: 0x3308, 0x9e9: 0x3308,
	0x9ea: 0x3308, 0x9eb: 0x3308, 0x9ec: 0x0299, 0x9ed: 0x3308, 0x9ee: 0x3308, 0x9ef: 0x3308,
	0x9f0: 0x3308, 0x9f1: 0x3308, 0x9f2: 0x3308, 0x9f3: 0x3308, 0x9f4: 0x3308, 0x9f5: 0x3308,
	0x9f6: 0x3308, 0x9f7: 0x3308, 0x9f8: 0x3308, 0x9f9: 0x02a1, 0x9fa: 0x3308, 0x9fb: 0x3308,
	0x9fc: 0x3308, 0x9fd: 0x0040, 0x9fe: 0x0018, 0x9ff: 0x0018,
	// Block 0x28, offset 0xa00
	0xa00: 0x0008, 0xa01: 0x0008, 0xa02: 0x0008, 0xa03: 0x0008, 0xa04: 0x0008, 0xa05: 0x0008,
	0xa06: 0x0008, 0xa07: 0x0008, 0xa08: 0x0008, 0xa09: 0x0008, 0xa0a: 0x0008, 0xa0b: 0x0008,
	0xa0c: 0x0008, 0xa0d: 0x0008, 0xa0e: 0x0008, 0xa0f: 0x0008, 0xa10: 0x0008, 0xa11: 0x0008,
	0xa12: 0x0008, 0xa13: 0x0008, 0xa14: 0x0008, 0xa15: 0x0008, 0xa16: 0x0008, 0xa17: 0x0008,
	0xa18: 0x0008, 0xa19: 0x0008, 0xa1a: 0x0008, 0xa1b: 0x0008, 0xa1c: 0x0008, 0xa1d: 0x0008,
	0xa1e: 0x0008, 0xa1f: 0x0008, 0xa20: 0x0008, 0xa21: 0x0008, 0xa22: 0x0008, 0xa23: 0x0008,
	0xa24: 0x0008, 0xa25: 0x0008, 0xa26: 0x0008, 0xa27: 0x0008, 0xa28: 0x0008, 0xa29: 0x0008,
	0xa2a: 0x0008, 0xa2b: 0x0008, 0xa2c: 0x0019, 0xa2d: 0x02e1, 0xa2e: 0x02e9, 0xa2f: 0x0008,
	0xa30: 0x02f1, 0xa31: 0x02f9, 0xa32: 0x0301, 0xa33: 0x0309, 0xa34: 0x00a9, 0xa35: 0x0311,
	0xa36: 0x00b1, 0xa37: 0x0319, 0xa38: 0x0101, 0xa39: 0x0321, 0xa3a: 0x0329, 0xa3b: 0x0008,
	0xa3c: 0x0051, 0xa3d: 0x0331, 0xa3e: 0x0339, 0xa3f: 0x00b9,
	// Block 0x29, offset 0xa40
	0xa40: 0x0341, 0xa41: 0x0349, 0xa42: 0x00c1, 0xa43: 0x0019, 0xa44: 0x0351, 0xa45: 0x0359,
	0xa46: 0x05b5, 0xa47: 0x02e9, 0xa48: 0x02f1, 0xa49: 0x02f9, 0xa4a: 0x0361, 0xa4b: 0x0369,
	0xa4c: 0x0371, 0xa4d: 0x0309, 0xa4e: 0x0008, 0xa4f: 0x0319, 0xa50: 0x0321, 0xa51: 0x0379,
	0xa52: 0x0051, 0xa53: 0x0381, 0xa54: 0x05cd, 0xa55: 0x05cd, 0xa56: 0x0339, 0xa57: 0x0341,
	0xa58: 0x0349, 0xa59: 0x05b5, 0xa5a: 0x0389, 0xa5b: 0x0391, 0xa5c: 0x05e5, 0xa5d: 0x0399,
	0xa5e: 0x03a1, 0xa5f: 0x03a9, 0xa60: 0x03b1, 0xa61: 0x03b9, 0xa62: 0x0311, 0xa63: 0x00b9,
	0xa64: 0x0349, 0xa65: 0x0391, 0xa66: 0x0399, 0xa67: 0x03a1, 0xa68: 0x03c1, 0xa69: 0x03b1,
	0xa6a: 0x03b9, 0xa6b: 0x0008, 0xa6c: 0x0008, 0xa6d: 0x0008, 0xa6e: 0x0008, 0xa6f: 0x0008,
	0xa70: 0x0008, 0xa71: 0x0008, 0xa72: 0x0008, 0xa73: 0x0008, 0xa74: 0x0008, 0xa75: 0x0008,
	0xa76: 0x0008, 0xa77: 0x0008, 0xa78: 0x03c9, 0xa79: 0x0008, 0xa7a: 0x0008, 0xa7b: 0x0008,
	0xa7c: 0x0008, 0xa7d: 0x0008, 0xa7e: 0x0008, 0xa7f: 0x0008,
	// Block 0x2a, offset 0xa80
	0xa80: 0x0008, 0xa81: 0x0008, 0xa82: 0x0008, 0xa83: 0x0008, 0xa84: 0x0008, 0xa85: 0x0008,
	0xa86: 0x0008, 0xa87: 0x0008, 0xa88: 0x0008, 0xa89: 0x0008, 0xa8a: 0x0008, 0xa8b: 0x0008,
	0xa8c: 0x0008, 0xa8d: 0x0008, 0xa8e: 0x0008, 0xa8f: 0x0008, 0xa90: 0x0008, 0xa91: 0x0008,
	0xa92: 0x0008, 0xa93: 0x0008, 0xa94: 0x0008, 0xa95: 0x0008, 0xa96: 0x0008, 0xa97: 0x0008,
	0xa98: 0x0008, 0xa99: 0x0008, 0xa9a: 0x0008, 0xa9b: 0x03d1, 0xa9c: 0x03d9, 0xa9d: 0x03e1,
	0xa9e: 0x03e9, 0xa9f: 0x0371, 0xaa0: 0x03f1, 0xaa1: 0x03f9, 0xaa2: 0x0401, 0xaa3: 0x0409,
	0xaa4: 0x0411, 0xaa5: 0x0419, 0xaa6: 0x0421, 0xaa7: 0x05fd, 0xaa8: 0x0429, 0xaa9: 0x0431,
	0xaaa: 0xe17d, 0xaab: 0x0439, 0xaac: 0x0441, 0xaad: 0x0449, 0xaae: 0x0451, 0xaaf: 0x0459,
	0xab0: 0x0461, 0xab1: 0x0469, 0xab2: 0x0471, 0xab3: 0x0479, 0xab4: 0x0481, 0xab5: 0x0489,
	0xab6: 0x0491, 0xab7: 0x0499, 0xab8: 0x0615, 0xab9: 0x04a1, 0xaba: 0x04a9, 0xabb: 0x04b1,
	0xabc: 0x04b9, 0xabd: 0x04c1, 0xabe: 0x04c9, 0xabf: 0x04d1,
	// Block 0x2b, offset 0xac0
	0xac0: 0xe00d, 0xac1: 0x0008, 0xac2: 0xe00d, 0xac3: 0x0008, 0xac4: 0xe00d, 0xac5: 0x0008,
	0xac6: 0xe00d, 0xac7: 0x0008, 0xac8: 0xe00d, 0xac9: 0x0008, 0xaca: 0xe00d, 0xacb: 0x0008,
	0xacc: 0xe00d, 0xacd: 0x0008, 0xace: 0xe00d, 0xacf: 0x0008, 0xad0: 0xe00d, 0xad1: 0x0008,
	0xad2: 0xe00d, 0xad3: 0x0008, 0xad4: 0xe00d, 0xad5: 0x0008, 0xad6: 0xe00d, 0xad7: 0x0008,
	0xad8: 0xe00d, 0xad9: 0x0008, 0xada: 0xe00d, 0xadb: 0x0008, 0xadc: 0xe00d, 0xadd: 0x0008,
	0xade: 0xe00d, 0xadf: 0x0008, 0xae0: 0xe00d, 0xae1: 0x0008, 0xae2: 0xe00d, 0xae3: 0x0008,
	0xae4: 0xe00d, 0xae5: 0x0008, 0xae6: 0xe00d, 0xae7: 0x0008, 0xae8: 0xe00d, 0xae9: 0x0008,
	0xaea: 0xe00d, 0xaeb: 0x0008, 0xaec: 0xe00d, 0xaed: 0x0008, 0xaee: 0xe00d, 0xaef: 0x0008,
	0xaf0: 0xe00d, 0xaf1: 0x0008, 0xaf2: 0xe00d, 0xaf3: 0x0008, 0xaf4: 0xe00d, 0xaf5: 0x0008,
	0xaf6: 0xe00d, 0xaf7: 0x0008, 0xaf8: 0xe00d, 0xaf9: 0x0008, 0xafa: 0xe00d, 0xafb: 0x0008,
	0xafc: 0xe00d, 0xafd: 0x0008, 0xafe: 0xe00d, 0xaff: 0x0008,
	// Block 0x2c, offset 0xb00
	0xb00: 0xe00d, 0xb01: 0x0008, 0xb02: 0xe00d, 0xb03: 0x0008, 0xb04: 0xe00d, 0xb05: 0x0008,
	0xb06: 0xe00d, 0xb07: 0x0008, 0xb08: 0xe00d, 0xb09: 0x0008, 0xb0a: 0xe00d, 0xb0b: 0x0008,
	0xb0c: 0xe00d, 0xb0d: 0x0008, 0xb0e: 0xe00d, 0xb0f: 0x0008, 0xb10: 0xe00d, 0xb11: 0x0008,
	0xb12: 0xe00d, 0xb13: 0x0008, 0xb14: 0xe00d, 0xb15: 0x0008, 0xb16: 0x0008, 0xb17: 0x0008,
	0xb18: 0x0008, 0xb19: 0x0008, 0xb1a: 0x062d, 0xb1b: 0x064d, 0xb1c: 0x0008, 0xb1d: 0x0008,
	0xb1e: 0x04d9, 0xb1f: 0x0008, 0xb20: 0xe00d, 0xb21: 0x0008, 0xb22: 0xe00d, 0xb23: 0x0008,
	0xb24: 0xe00d, 0xb25: 0x0008, 0xb26: 0xe00d, 0xb27: 0x0008, 0xb28: 0xe00d, 0xb29: 0x0008,
	0xb2a: 0xe00d, 0xb2b: 0x0008, 0xb2c: 0xe00d, 0xb2d: 0x0008, 0xb2e: 0xe00d, 0xb2f: 0x0008,
	0xb30: 0xe00d, 0xb31: 0x0008, 0xb32: 0xe00d, 0xb33: 0x0008, 0xb34: 0xe00d, 0xb35: 0x0008,
	0xb36: 0xe00d, 0xb37: 0x0008, 0xb38: 0xe00d, 0xb39: 0x0008, 0xb3a: 0xe00d, 0xb3b: 0x0008,
	0xb3c: 0xe00d, 0xb3d: 0x0008, 0xb3e: 0xe00d, 0xb3f: 0x0008,
	// Block 0x2d, offset 0xb40
	0xb40: 0x0008, 0xb41: 0x0008, 0xb42: 0x0008, 0xb43: 0x0008, 0xb44: 0x0008, 0xb45: 0x0008,
	0xb46: 0x0040, 0xb47: 0x0040, 0xb48: 0xe045, 0xb49: 0xe045, 0xb4a: 0xe045, 0xb4b: 0xe045,
	0xb4c: 0xe045, 0xb4d: 0xe045, 0xb4e: 0x0040, 0xb4f: 0x0040, 0xb50: 0x0008, 0xb51: 0x0008,
	0xb52: 0x0008, 0xb53: 0x0008, 0xb54: 0x0008, 0xb55: 0x0008, 0xb56: 0x0008, 0xb57: 0x0008,
	0xb58: 0x0040, 0xb59: 0xe045, 0xb5a: 0x0040, 0xb5b: 0xe045, 0xb5c: 0x0040, 0xb5d: 0xe045,
	0xb5e: 0x0040, 0xb5f: 0xe045, 0xb60: 0x0008, 0xb61: 0x0008, 0xb62: 0x0008, 0xb63: 0x0008,
	0xb64: 0x0008, 0xb65: 0x0008, 0xb66: 0x0008, 0xb67: 0x0008, 0xb68: 0xe045, 0xb69: 0xe045,
	0xb6a: 0xe045, 0xb6b: 0xe045, 0xb6c: 0xe045, 0xb6d: 0xe045, 0xb6e: 0xe045, 0xb6f: 0xe045,
	0xb70: 0x0008, 0xb71: 0x04e1, 0xb72: 0x0008, 0xb73: 0x04e9, 0xb74: 0x0008, 0xb75: 0x04f1,
	0xb76: 0x0008, 0xb77: 0x04f9, 0xb78: 0x0008, 0xb79: 0x0501, 0xb7a: 0x0008, 0xb7b: 0x0509,
	0xb7c: 0x0008, 0xb7d: 0x0511, 0xb7e: 0x0040, 0xb7f: 0x0040,
	// Block 0x2e, offset 0xb80
	0xb80: 0x0519, 0xb81: 0x0521, 0xb82: 0x0529, 0xb83: 0x0531, 0xb84: 0x0539, 0xb85: 0x0541,
	0xb86: 0x0549, 0xb87: 0x0551, 0xb88: 0x0519, 0xb89: 0x0521, 0xb8a: 0x0529, 0xb8b: 0x0531,
	0xb8c: 0x0539, 0xb8d: 0x0541, 0xb8e: 0x0549, 0xb8f: 0x0551, 0xb90: 0x0559, 0xb91: 0x0561,
	0xb92: 0x0569, 0xb93: 0x0571, 0xb94: 0x0579, 0xb95: 0x0581, 0xb96: 0x0589, 0xb97: 0x0591,
	0xb98: 0x0559, 0xb99: 0x0561, 0xb9a: 0x0569, 0xb9b: 0x0571, 0xb9c: 0x0579, 0xb9d: 0x0581,
	0xb9e: 0x0589, 0xb9f: 0x0591, 0xba0: 0x0599, 0xba1: 0x05a1, 0xba2: 0x05a9, 0xba3: 0x05b1,
	0xba4: 0x05b9, 0xba5: 0x05c1, 0xba6: 0x05c9, 0xba7: 0x05d1, 0xba8: 0x0599, 0xba9: 0x05a1,
	0xbaa: 0x05a9, 0xbab: 0x05b1, 0xbac: 0x05b9, 0xbad: 0x05c1, 0xbae: 0x05c9, 0xbaf: 0x05d1,
	0xbb0: 0x0008, 0xbb1: 0x0008, 0xbb2: 0x05d9, 0xbb3: 0x05e1, 0xbb4: 0x05e9, 0xbb5: 0x0040,
	0xbb6: 0x0008, 0xbb7: 0x05f1, 0xbb8: 0xe045, 0xbb9: 0xe045, 0xbba: 0x0665, 0xbbb: 0x04e1,
	0xbbc: 0x05e1, 0xbbd: 0x067e, 0xbbe: 0x05f9, 0xbbf: 0x069e,
	// Block 0x2f, offset 0xbc0
	0xbc0: 0x06be, 0xbc1: 0x0602, 0xbc2: 0x0609, 0xbc3: 0x0611, 0xbc4: 0x0619, 0xbc5: 0x0040,
	0xbc6: 0x0008, 0xbc7: 0x0621, 0xbc8: 0x06dd, 0xbc9: 0x04e9, 0xbca: 0x06f5, 0xbcb: 0x04f1,
	0xbcc: 0x0611, 0xbcd: 0x062a, 0xbce: 0x0632, 0xbcf: 0x063a, 0xbd0: 0x0008, 0xbd1: 0x0008,
	0xbd2: 0x0008, 0xbd3: 0x0641, 0xbd4: 0x0040, 0xbd5: 0x0040, 0xbd6: 0x0008, 0xbd7: 0x0008,
	0xbd8: 0xe045, 0xbd9: 0xe045, 0xbda: 0x070d, 0xbdb: 0x04f9, 0xbdc: 0x0040, 0xbdd: 0x064a,
	0xbde: 0x0652, 0xbdf: 0x065a, 0xbe0: 0x0008, 0xbe1: 0x0008, 0xbe2: 0x0008, 0xbe3: 0x0661,
	0xbe4: 0x0008, 0xbe5: 0x0008, 0xbe6: 0x0008, 0xbe7: 0x0008, 0xbe8: 0xe045, 0xbe9: 0xe045,
	0xbea: 0x0725, 0xbeb: 0x0509, 0xbec: 0xe04d, 0xbed: 0x066a, 0xbee: 0x012a, 0xbef: 0x0672,
	0xbf0: 0x0040, 0xbf1: 0x0040, 0xbf2: 0x0679, 0xbf3: 0x0681, 0xbf4: 0x0689, 0xbf5: 0x0040,
	0xbf6: 0x0008, 0xbf7: 0x0691, 0xbf8: 0x073d, 0xbf9: 0x0501, 0xbfa: 0x0515, 0xbfb: 0x0511,
	0xbfc: 0x0681, 0xbfd: 0x0756, 0xbfe: 0x0776, 0xbff: 0x0040,
	// Block 0x30, offset 0xc00
	0xc00: 0x000a, 0xc01: 0x000a, 0xc02: 0x000a, 0xc03: 0x000a, 0xc04: 0x000a, 0xc05: 0x000a,
	0xc06: 0x000a, 0xc07: 0x000a, 0xc08: 0x000a, 0xc09: 0x000a, 0xc0a: 0x000a, 0xc0b: 0x03c0,
	0xc0c: 0x0003, 0xc0d: 0x0003, 0xc0e: 0x0340, 0xc0f: 0x0b40, 0xc10: 0x0018, 0xc11: 0xe00d,
	0xc12: 0x0018, 0xc13: 0x0018, 0xc14: 0x0018, 0xc15: 0x0018, 0xc16: 0x0018, 0xc17: 0x0796,
	0xc18: 0x0018, 0xc19: 0x0018, 0xc1a: 0x0018, 0xc1b: 0x0018, 0xc1c: 0x0018, 0xc1d: 0x0018,
	0xc1e: 0x0018, 0xc1f: 0x0018, 0xc20: 0x0018, 0xc21: 0x0018, 0xc22: 0x0018, 0xc23: 0x0018,
	0xc24: 0x0040, 0xc25: 0x0040, 0xc26: 0x0040, 0xc27: 0x0018, 0xc28: 0x0040, 0xc29: 0x0040,
	0xc2a: 0x0340, 0xc2b: 0x0340, 0xc2c: 0x0340, 0xc2d: 0x0340, 0xc2e: 0x0340, 0xc2f: 0x000a,
	0xc30: 0x0018, 0xc31: 0x0018, 0xc32: 0x0018, 0xc33: 0x0699, 0xc34: 0x06a1, 0xc35: 0x0018,
	0xc36: 0x06a9, 0xc37: 0x06b1, 0xc38: 0x0018, 0xc39: 0x0018, 0xc3a: 0x0018, 0xc3b: 0x0018,
	0xc3c: 0x06ba, 0xc3d: 0x0018, 0xc3e: 0x07b6, 0xc3f: 0x0018,
	// Block 0x31, offset 0xc40
	0xc40: 0x0018, 0xc41: 0x0018, 0xc42: 0x0018, 0xc43: 0x0018, 0xc44: 0x0018, 0xc45: 0x0018,
	0xc46: 0x0018, 0xc47: 0x06c2, 0xc48: 0x06ca, 0xc49: 0x06d2, 0xc4a: 0x0018, 0xc4b: 0x0018,
	0xc4c: 0x0018, 0xc4d: 0x0018, 0xc4e: 0x0018, 0xc4f: 0x0018, 0xc50: 0x0018, 0xc51: 0x0018,
	0xc52: 0x0018, 0xc53: 0x0018, 0xc54: 0x0018, 0xc55: 0x0018, 0xc56: 0x0018, 0xc57: 0x06d9,
	0xc58: 0x0018, 0xc59: 0x0018, 0xc5a: 0x0018, 0xc5b: 0x0018, 0xc5c: 0x0018, 0xc5d: 0x0018,
	0xc5e: 0x0018, 0xc5f: 0x000a, 0xc60: 0x03c0, 0xc61: 0x0340, 0xc62: 0x0340, 0xc63: 0x0340,
	0xc64: 0x03c0, 0xc65: 0x0040, 0xc66: 0x0040, 0xc67: 0x0040, 0xc68: 0x0040, 0xc69: 0x0040,
	0xc6a: 0x0340, 0xc6b: 0x0340, 0xc6c: 0x0340, 0xc6d: 0x0340, 0xc6e: 0x0340, 0xc6f: 0x0340,
	0xc70: 0x06e1, 0xc71: 0x0311, 0xc72: 0x0040, 0xc73: 0x0040, 0xc74: 0x06e9, 0xc75: 0x06f1,
	0xc76: 0x06f9, 0xc77: 0x0701, 0xc78: 0x0709, 0xc79: 0x0711, 0xc7a: 0x071a, 0xc7b: 0x07d5,
	0xc7c: 0x0722, 0xc7d: 0x072a, 0xc7e: 0x0732, 0xc7f: 0x0329,
	// Block 0x32, offset 0xc80
	0xc80: 0x06e1, 0xc81: 0x0049, 0xc82: 0x0029, 0xc83: 0x0031, 0xc84: 0x06e9, 0xc85: 0x06f1,
	0xc86: 0x06f9, 0xc87: 0x0701, 0xc88: 0x0709, 0xc89: 0x0711, 0xc8a: 0x071a, 0xc8b: 0x07ed,
	0xc8c: 0x0722, 0xc8d: 0x072a, 0xc8e: 0x0732, 0xc8f: 0x0040, 0xc90: 0x0019, 0xc91: 0x02f9,
	0xc92: 0x0051, 0xc93: 0x0109, 0xc94: 0x0361, 0xc95: 0x00a9, 0xc96: 0x0319, 0xc97: 0x0101,
	0xc98: 0x0321, 0xc99: 0x0329, 0xc9a: 0x0339, 0xc9b: 0x0089, 0xc9c: 0x0341, 0xc9d: 0x0040,
	0xc9e: 0x0040, 0xc9f: 0x0040, 0xca0: 0x0018, 0xca1: 0x0018, 0xca2: 0x0018, 0xca3: 0x0018,
	0xca4: 0x0018, 0xca5: 0x0018, 0xca6: 0x0018, 0xca7: 0x0018, 0xca8: 0x0739, 0xca9: 0x0018,
	0xcaa: 0x0018, 0xcab: 0x0018, 0xcac: 0x0018, 0xcad: 0x0018, 0xcae: 0x0018, 0xcaf: 0x0018,
	0xcb0: 0x0018, 0xcb1: 0x0018, 0xcb2: 0x0018, 0xcb3: 0x0018, 0xcb4: 0x0018, 0xcb5: 0x0018,
	0xcb6: 0x0018, 0xcb7: 0x0018, 0xcb8: 0x0018, 0xcb9: 0x0018, 0xcba: 0x0018, 0xcbb: 0x0018,
	0xcbc: 0x0018, 0xcbd: 0x0018, 0xcbe: 0x0018, 0xcbf: 0x0018,
	// Block 0x33, offset 0xcc0
	0xcc0: 0x0806, 0xcc1: 0x0826, 0xcc2: 0x03d9, 0xcc3: 0x0845, 0xcc4: 0x0018, 0xcc5: 0x0866,
	0xcc6: 0x0886, 0xcc7: 0x0369, 0xcc8: 0x0018, 0xcc9: 0x08a5, 0xcca: 0x0309, 0xccb: 0x00a9,
	0xccc: 0x00a9, 0xccd: 0x00a9, 0xcce: 0x00a9, 0xccf: 0x0741, 0xcd0: 0x0311, 0xcd1: 0x0311,
	0xcd2: 0x0101, 0xcd3: 0x0101, 0xcd4: 0x0018, 0xcd5: 0x0329, 0xcd6: 0x0749, 0xcd7: 0x0018,
	0xcd8: 0x0018, 0xcd9: 0x0339, 0xcda: 0x0751, 0xcdb: 0x00b9, 0xcdc: 0x00b9, 0xcdd: 0x00b9,
	0xcde: 0x0018, 0xcdf: 0x0018, 0xce0: 0x0759, 0xce1: 0x08c5, 0xce2: 0x0761, 0xce3: 0x0018,
	0xce4: 0x04b1, 0xce5: 0x0018, 0xce6: 0x0769, 0xce7: 0x0018, 0xce8: 0x04b1, 0xce9: 0x0018,
	0xcea: 0x0319, 0xceb: 0x0771, 0xcec: 0x02e9, 0xced: 0x03d9, 0xcee: 0x0018, 0xcef: 0x02f9,
	0xcf0: 0x02f9, 0xcf1: 0x03f1, 0xcf2: 0x0040, 0xcf3: 0x0321, 0xcf4: 0x0051, 0xcf5: 0x0779,
	0xcf6: 0x0781, 0xcf7: 0x0789, 0xcf8: 0x0791, 0xcf9: 0x0311, 0xcfa: 0x0018, 0xcfb: 0x08e5,
	0xcfc: 0x0799, 0xcfd: 0x03a1, 0xcfe: 0x03a1, 0xcff: 0x0799,
	// Block 0x34, offset 0xd00
	0xd00: 0x0905, 0xd01: 0x0018, 0xd02: 0x0018, 0xd03: 0x0018, 0xd04: 0x0018, 0xd05: 0x02f1,
	0xd06: 0x02f1, 0xd07: 0x02f9, 0xd08: 0x0311, 0xd09: 0x00b1, 0xd0a: 0x0018, 0xd0b: 0x0018,
	0xd0c: 0x0018, 0xd0d: 0x0018, 0xd0e: 0x0008, 0xd0f: 0x0018, 0xd10: 0x07a1, 0xd11: 0x07a9,
	0xd12: 0x07b1, 0xd13: 0x07b9, 0xd14: 0x07c1, 0xd15: 0x07c9, 0xd16: 0x07d1, 0xd17: 0x07d9,
	0xd18: 0x07e1, 0xd19: 0x07e9, 0xd1a: 0x07f1, 0xd1b: 0x07f9, 0xd1c: 0x0801, 0xd1d: 0x0809,
	0xd1e: 0x0811, 0xd1f: 0x0819, 0xd20: 0x0311, 0xd21: 0x0821, 0xd22: 0x091d, 0xd23: 0x0829,
	0xd24: 0x0391, 0xd25: 0x0831, 0xd26: 0x093d, 0xd27: 0x0839, 0xd28: 0x0841, 0xd29: 0x0109,
	0xd2a: 0x0849, 0xd2b: 0x095d, 0xd2c: 0x0101, 0xd2d: 0x03d9, 0xd2e: 0x02f1, 0xd2f: 0x0321,
	0xd30: 0x0311, 0xd31: 0x0821, 0xd32: 0x097d, 0xd33: 0x0829, 0xd34: 0x0391, 0xd35: 0x0831,
	0xd36: 0x099d, 0xd37: 0x0839, 0xd38: 0x0841, 0xd39: 0x0109, 0xd3a: 0x0849, 0xd3b: 0x09bd,
	0xd3c: 0x0101, 0xd3d: 0x03d9, 0xd3e: 0x02f1, 0xd3f: 0x0321,
	// Block 0x35, offset 0xd40
	0xd40: 0x0018, 0xd41: 0x0018, 0xd42: 0x0018, 0xd43: 0x0018, 0xd44: 0x0018, 0xd45: 0x0018,
	0xd46: 0x0018, 0xd47: 0x0018, 0xd48: 0x0018, 0xd49: 0x0018, 0xd4a: 0x0018, 0xd4b: 0x0040,
	0xd4c: 0x0040, 0xd4d: 0x0040, 0xd4e: 0x0040, 0xd4f: 0x0040, 0xd50: 0x0040, 0xd51: 0x0040,
	0xd52: 0x0040, 0xd53: 0x0040, 0xd54: 0x0040, 0xd55: 0x0040, 0xd56: 0x0040, 0xd57: 0x0040,
	0xd58: 0x0040, 0xd59: 0x0040, 0xd5a: 0x0040, 0xd5b: 0x0040, 0xd5c: 0x0040, 0xd5d: 0x0040,
	0xd5e: 0x0040, 0xd5f: 0x0040, 0xd60: 0x0049, 0xd61: 0x0029, 0xd62: 0x0031, 0xd63: 0x06e9,
	0xd64: 0x06f1, 0xd65: 0x06f9, 0xd66: 0x0701, 0xd67: 0x0709, 0xd68: 0x0711, 0xd69: 0x0879,
	0xd6a: 0x0881, 0xd6b: 0x0889, 0xd6c: 0x0891, 0xd6d: 0x0899, 0xd6e: 0x08a1, 0xd6f: 0x08a9,
	0xd70: 0x08b1, 0xd71: 0x08b9, 0xd72: 0x08c1, 0xd73: 0x08c9, 0xd74: 0x0a1e, 0xd75: 0x0a3e,
	0xd76: 0x0a5e, 0xd77: 0x0a7e, 0xd78: 0x0a9e, 0xd79: 0x0abe, 0xd7a: 0x0ade, 0xd7b: 0x0afe,
	0xd7c: 0x0b1e, 0xd7d: 0x08d2, 0xd7e: 0x08da, 0xd7f: 0x08e2,
	// Block 0x36, offset 0xd80
	0xd80: 0x08ea, 0xd81: 0x08f2, 0xd82: 0x08fa, 0xd83: 0x0902, 0xd84: 0x090a, 0xd85: 0x0912,
	0xd86: 0x091a, 0xd87: 0x0922, 0xd88: 0x0040, 0xd89: 0x0040, 0xd8a: 0x0040, 0xd8b: 0x0040,
	0xd8c: 0x0040, 0xd8d: 0x0040, 0xd8e: 0x0040, 0xd8f: 0x0040, 0xd90: 0x0040, 0xd91: 0x0040,
	0xd92: 0x0040, 0xd93: 0x0040, 0xd94: 0x0040, 0xd95: 0x0040, 0xd96: 0x0040, 0xd97: 0x0040,
	0xd98: 0x0040, 0xd99: 0x0040, 0xd9a: 0x0040, 0xd9b: 0x0040, 0xd9c: 0x0b3e, 0xd9d: 0x0b5e,
	0xd9e: 0x0b7e, 0xd9f: 0x0b9e, 0xda0: 0x0bbe, 0xda1: 0x0bde, 0xda2: 0x0bfe, 0xda3: 0x0c1e,
	0xda4: 0x0c3e, 0xda5: 0x0c5e, 0xda6: 0x0c7e, 0xda7: 0x0c9e, 0xda8: 0x0cbe, 0xda9: 0x0cde,
	0xdaa: 0x0cfe, 0xdab: 0x0d1e, 0xdac: 0x0d3e, 0xdad: 0x0d5e, 0xdae: 0x0d7e, 0xdaf: 0x0d9e,
	0xdb0: 0x0dbe, 0xdb1: 0x0dde, 0xdb2: 0x0dfe, 0xdb3: 0x0e1e, 0xdb4: 0x0e3e, 0xdb5: 0x0e5e,
	0xdb6: 0x0019, 0xdb7: 0x02e9, 0xdb8: 0x03d9, 0xdb9: 0x02f1, 0xdba: 0x02f9, 0xdbb: 0x03f1,
	0xdbc: 0x0309, 0xdbd: 0x00a9, 0xdbe: 0x0311, 0xdbf: 0x00b1,
	// Block 0x37, offset 0xdc0
	0xdc0: 0x0319, 0xdc1: 0x0101, 0xdc2: 0x0321, 0xdc3: 0x0329, 0xdc4: 0x0051, 0xdc5: 0x0339,
	0xdc6: 0x0751, 0xdc7: 0x00b9, 0xdc8: 0x0089, 0xdc9: 0x0341, 0xdca: 0x0349, 0xdcb: 0x0391,
	0xdcc: 0x00c1, 0xdcd: 0x0109, 0xdce: 0x00c9, 0xdcf: 0x04b1, 0xdd0: 0x0019, 0xdd1: 0x02e9,
	0xdd2: 0x03d9, 0xdd3: 0x02f1, 0xdd4: 0x02f9, 0xdd5: 0x03f1, 0xdd6: 0x0309, 0xdd7: 0x00a9,
	0xdd8: 0x0311, 0xdd9: 0x00b1, 0xdda: 0x0319, 0xddb: 0x0101, 0xddc: 0x0321, 0xddd: 0x0329,
	0xdde: 0x0051, 0xddf: 0x0339, 0xde0: 0x0751, 0xde1: 0x00b9, 0xde2: 0x0089, 0xde3: 0x0341,
	0xde4: 0x0349, 0xde5: 0x0391, 0xde6: 0x00c1, 0xde7: 0x0109, 0xde8: 0x00c9, 0xde9: 0x04b1,
	0xdea: 0x06e1, 0xdeb: 0x0018, 0xdec: 0x0018, 0xded: 0x0018, 0xdee: 0x0018, 0xdef: 0x0018,
	0xdf0: 0x0018, 0xdf1: 0x0018, 0xdf2: 0x0018, 0xdf3: 0x0018, 0xdf4: 0x0018, 0xdf5: 0x0018,
	0xdf6: 0x0018, 0xdf7: 0x0018, 0xdf8: 0x0018, 0xdf9: 0x0018, 0xdfa: 0x0018, 0xdfb: 0x0018,
	0xdfc: 0x0018, 0xdfd: 0x0018, 0xdfe: 0x0018, 0xdff: 0x0018,
	// Block 0x38, offset 0xe00
	0xe00: 0x0008, 0xe01: 0x0008, 0xe02: 0x0008, 0xe03: 0x0008, 0xe04: 0x0008, 0xe05: 0x0008,
	0xe06: 0x0008, 0xe07: 0x0008, 0xe08: 0x0008, 0xe09: 0x0008, 0xe0a: 0x0008, 0xe0b: 0x0008,
	0xe0c: 0x0008, 0xe0d: 0x0008, 0xe0e: 0x0008, 0xe0f: 0x0008, 0xe10: 0x0008, 0xe11: 0x0008,
	0xe12: 0x0008, 0xe13: 0x0008, 0xe14: 0x0008, 0xe15: 0x0008, 0xe16: 0x0008, 0xe17: 0x0008,
	0xe18: 0x0008, 0xe19: 0x0008, 0xe1a: 0x0008, 0xe1b: 0x0008, 0xe1c: 0x0008, 0xe1d: 0x0008,
	0xe1e: 0x0008, 0xe1f: 0x0008, 0xe20: 0xe00d, 0xe21: 0x0008, 0xe22: 0x0941, 0xe23: 0x0ed5,
	0xe24: 0x0949, 0xe25: 0x0008, 0xe26: 0x0008, 0xe27: 0xe07d, 0xe28: 0x0008, 0xe29: 0xe01d,
	0xe2a: 0x0008, 0xe2b: 0xe03d, 0xe2c: 0x0008, 0xe2d: 0x0359, 0xe2e: 0x0441, 0xe2f: 0x0351,
	0xe30: 0x03d1, 0xe31: 0x0008, 0xe32: 0xe00d, 0xe33: 0x0008, 0xe34: 0x0008, 0xe35: 0xe01d,
	0xe36: 0x0008, 0xe37: 0x0008, 0xe38: 0x0008, 0xe39: 0x0008, 0xe3a: 0x0008, 0xe3b: 0x0008,
	0xe3c: 0x00b1, 0xe3d: 0x0391, 0xe3e: 0x0951, 0xe3f: 0x0959,
	// Block 0x39, offset 0xe40
	0xe40: 0xe00d, 0xe41: 0x0008, 0xe42: 0xe00d, 0xe43: 0x0008, 0xe44: 0xe00d, 0xe45: 0x0008,
	0xe46: 0xe00d, 0xe47: 0x0008, 0xe48: 0xe00d, 0xe49: 0x0008, 0xe4a: 0xe00d, 0xe4b: 0x0008,
	0xe4c: 0xe00d, 0xe4d: 0x0008, 0xe4e: 0xe00d, 0xe4f: 0x0008, 0xe50: 0xe00d, 0xe51: 0x0008,
	0xe52: 0xe00d, 0xe53: 0x0008, 0xe54: 0xe00d, 0xe55: 0x0008, 0xe56: 0xe00d, 0xe57: 0x0008,
	0xe58: 0xe00d, 0xe59: 0x0008, 0xe5a: 0xe00d, 0xe5b: 0x0008, 0xe5c: 0xe00d, 0xe5d: 0x0008,
	0xe5e: 0xe00d, 0xe5f: 0x0008, 0xe60: 0xe00d, 0xe61: 0x0008, 0xe62: 0xe00d, 0xe63: 0x0008,
	0xe64: 0x0008, 0xe65: 0x0018, 0xe66: 0x0018, 0xe67: 0x0018, 0xe68: 0x0018, 0xe69: 0x0018,
	0xe6a: 0x0018, 0xe6b: 0xe03d, 0xe6c: 0x0008, 0xe6d: 0xe01d, 0xe6e: 0x0008, 0xe6f: 0x3308,
	0xe70: 0x3308, 0xe71: 0x3308, 0xe72: 0xe00d, 0xe73: 0x0008, 0xe74: 0x0040, 0xe75: 0x0040,
	0xe76: 0x0040, 0xe77: 0x0040, 0xe78: 0x0040, 0xe79: 0x0018, 0xe7a: 0x0018, 0xe7b: 0x0018,
	0xe7c: 0x0018, 0xe7d: 0x0018, 0xe7e: 0x0018, 0xe7f: 0x0018,
	// Block 0x3a, offset 0xe80
	0xe80: 0x2715, 0xe81: 0x2735, 0xe82: 0x2755, 0xe83: 0x2775, 0xe84: 0x2795, 0xe85: 0x27b5,
	0xe86: 0x27d5, 0xe87: 0x27f5, 0xe88: 0x2815, 0xe89: 0x2835, 0xe8a: 0x2855, 0xe8b: 0x2875,
	0xe8c: 0x2895, 0xe8d: 0x28b5, 0xe8e: 0x28d5, 0xe8f: 0x28f5, 0xe90: 0x2915, 0xe91: 0x2935,
	0xe92: 0x2955, 0xe93: 0x2975, 0xe94: 0x2995, 0xe95: 0x29b5, 0xe96: 0x0040, 0xe97: 0x0040,
	0xe98: 0x0040, 0xe99: 0x0040, 0xe9a: 0x0040, 0xe9b: 0x0040, 0xe9c: 0x0040, 0xe9d: 0x0040,
	0xe9e: 0x0040, 0xe9f: 0x0040, 0xea0: 0x0040, 0xea1: 0x0040, 0xea2: 0x0040, 0xea3: 0x0040,
	0xea4: 0x0040, 0xea5: 0x0040, 0xea6: 0x0040, 0xea7: 0x0040, 0xea8: 0x0040, 0xea9: 0x0040,
	0xeaa: 0x0040, 0xeab: 0x0040, 0xeac: 0x0040, 0xead: 0x0040, 0xeae: 0x0040, 0xeaf: 0x0040,
	0xeb0: 0x0040, 0xeb1: 0x0040, 0xeb2: 0x0040, 0xeb3: 0x0040, 0xeb4: 0x0040, 0xeb5: 0x0040,
	0xeb6: 0x0040, 0xeb7: 0x0040, 0xeb8: 0x0040, 0xeb9: 0x0040, 0xeba: 0x0040, 0xebb: 0x0040,
	0xebc: 0x0040, 0xebd: 0x0040, 0xebe: 0x0040, 0xebf: 0x0040,
	// Block 0x3b, offset 0xec0
	0xec0: 0x000a, 0xec1: 0x0018, 0xec2: 0x0961, 0xec3: 0x0018, 0xec4: 0x0018, 0xec5: 0x0008,
	0xec6: 0x0008, 0xec7: 0x0008, 0xec8: 0x0018, 0xec9: 0x0018, 0xeca: 0x0018, 0xecb: 0x0018,
	0xecc: 0x0018, 0xecd: 0x0018, 0xece: 0x0018, 0xecf: 0x0018, 0xed0: 0x0018, 0xed1: 0x0018,
	0xed2: 0x0018, 0xed3: 0x0018, 0xed4: 0x0018, 0xed5: 0x0018, 0xed6: 0x0018, 0xed7: 0x0018,
	0xed8: 0x0018, 0xed9: 0x0018, 0xeda: 0x0018, 0xedb: 0x0018, 0xedc: 0x0018, 0xedd: 0x0018,
	0xede: 0x0018, 0xedf: 0x0018, 0xee0: 0x0018, 0xee1: 0x0018, 0xee2: 0x0018, 0xee3: 0x0018,
	0xee4: 0x0018, 0xee5: 0x0018, 0xee6: 0x0018, 0xee7: 0x0018, 0xee8: 0x0018, 0xee9: 0x0018,
	0xeea: 0x3308, 0xeeb: 0x3308, 0xeec: 0x3308, 0xeed: 0x3308, 0xeee: 0x3018, 0xeef: 0x3018,
	0xef0: 0x0018, 0xef1: 0x0018, 0xef2: 0x0018, 0xef3: 0x0018, 0xef4: 0x0018, 0xef5: 0x0018,
	0xef6: 0xe125, 0xef7: 0x0018, 0xef8: 0x29d5, 0xef9: 0x29f5, 0xefa: 0x2a15, 0xefb: 0x0018,
	0xefc: 0x0008, 0xefd: 0x0018, 0xefe: 0x0018, 0xeff: 0x0018,
	// Block 0x3c, offset 0xf00
	0xf00: 0x2b55, 0xf01: 0x2b75, 0xf02: 0x2b95, 0xf03: 0x2bb5, 0xf04: 0x2bd5, 0xf05: 0x2bf5,
	0xf06: 0x2bf5, 0xf07: 0x2bf5, 0xf08: 0x2c15, 0xf09: 0x2c15, 0xf0a: 0x2c15, 0xf0b: 0x2c15,
	0xf0c: 0x2c35, 0xf0d: 0x2c35, 0xf0e: 0x2c35, 0xf0f: 0x2c55, 0xf10: 0x2c75, 0xf11: 0x2c75,
	0xf12: 0x2a95, 0xf13: 0x2a95, 0xf14: 0x2c75, 0xf15: 0x2c75, 0xf16: 0x2c95, 0xf17: 0x2c95,
	0xf18: 0x2c75, 0xf19: 0x2c75, 0xf1a: 0x2a95, 0xf1b: 0x2a95, 0xf1c: 0x2c75, 0xf1d: 0x2c75,
	0xf1e: 0x2c55, 0xf1f: 0x2c55, 0xf20: 0x2cb5, 0xf21: 0x2cb5, 0xf22: 0x2cd5, 0xf23: 0x2cd5,
	0xf24: 0x0040, 0xf25: 0x2cf5, 0xf26: 0x2d15, 0xf27: 0x2d35, 0xf28: 0x2d35, 0xf29: 0x2d55,
	0xf2a: 0x2d75, 0xf2b: 0x2d95, 0xf2c: 0x2db5, 0xf2d: 0x2dd5, 0xf2e: 0x2df5, 0xf2f: 0x2e15,
	0xf30: 0x2e35, 0xf31: 0x2e55, 0xf32: 0x2e55, 0xf33: 0x2e75, 0xf34: 0x2e95, 0xf35: 0x2e95,
	0xf36: 0x2eb5, 0xf37: 0x2ed5, 0xf38: 0x2e75, 0xf39: 0x2ef5, 0xf3a: 0x2f15, 0xf3b: 0x2ef5,
	0xf3c: 0x2e75, 0xf3d: 0x2f35, 0xf3e: 0x2f55, 0xf3f: 0x2f75,
	// Block 0x3d, offset 0xf40
	0xf40: 0x2f95, 0xf41: 0x2fb5, 0xf42: 0x2d15, 0xf43: 0x2cf5, 0xf44: 0x2fd5, 0xf45: 0x2ff5,
	0xf46: 0x3015, 0xf47: 0x3035, 0xf48: 0x3055, 0xf49: 0x3075, 0xf4a: 0x3095, 0xf4b: 0x30b5,
	0xf4c: 0x30d5, 0xf4d: 0x30f5, 0xf4e: 0x3115, 0xf4f: 0x0040, 0xf50: 0x0018, 0xf51: 0x0018,
	0xf52: 0x3135, 0xf53: 0x3155, 0xf54: 0x3175, 0xf55: 0x3195, 0xf56: 0x31b5, 0xf57: 0x31d5,
	0xf58: 0x31f5, 0xf59: 0x3215, 0xf5a: 0x3235, 0xf5b: 0x3255, 0xf5c: 0x3175, 0xf5d: 0x3275,
	0xf5e: 0x3295, 0xf5f: 0x32b5, 0xf60: 0x0008, 0xf61: 0x0008, 0xf62: 0x0008, 0xf63: 0x0008,
	0xf64: 0x0008, 0xf65: 0x0008, 0xf66: 0x0008, 0xf67: 0x0008, 0xf68: 0x0008, 0xf69: 0x0008,
	0xf6a: 0x0008, 0xf6b: 0x0008, 0xf6c: 0x0008, 0xf6d: 0x0008, 0xf6e: 0x0008, 0xf6f: 0x0008,
	0xf70: 0x0008, 0xf71: 0x0008, 0xf72: 0x0008, 0xf73: 0x0008, 0xf74: 0x0008, 0xf75: 0x0008,
	0xf76: 0x0008, 0xf77: 0x0008, 0xf78: 0x0008, 0xf79: 0x0008, 0xf7a: 0x0008, 0xf7b: 0x0008,
	0xf7c: 0x0008, 0xf7d: 0x0008, 0xf7e: 0x0008, 0xf7f: 0x0008,
	// Block 0x3e, offset 0xf80
	0xf80: 0x0b82, 0xf81: 0x0b8a, 0xf82: 0x0b92, 0xf83: 0x0b9a, 0xf84: 0x32d5, 0xf85: 0x32f5,
	0xf86: 0x3315, 0xf87: 0x3335, 0xf88: 0x0018, 0xf89: 0x0018, 0xf8a: 0x0018, 0xf8b: 0x0018,
	0xf8c: 0x0018, 0xf8d: 0x0018, 0xf8e: 0x0018, 0xf8f: 0x0018, 0xf90: 0x3355, 0xf91: 0x0ba1,
	0xf92: 0x0ba9, 0xf93: 0x0bb1, 0xf94: 0x0bb9, 0xf95: 0x0bc1, 0xf96: 0x0bc9, 0xf97: 0x0bd1,
	0xf98: 0x0bd9, 0xf99: 0x0be1, 0xf9a: 0x0be9, 0xf9b: 0x0bf1, 0xf9c: 0x0bf9, 0xf9d: 0x0c01,
	0xf9e: 0x0c09, 0xf9f: 0x0c11, 0xfa0: 0x3375, 0xfa1: 0x3395, 0xfa2: 0x33b5, 0xfa3: 0x33d5,
	0xfa4: 0x33f5, 0xfa5: 0x33f5, 0xfa6: 0x3415, 0xfa7: 0x3435, 0xfa8: 0x3455, 0xfa9: 0x3475,
	0xfaa: 0x3495, 0xfab: 0x34b5, 0xfac: 0x34d5, 0xfad: 0x34f5, 0xfae: 0x3515, 0xfaf: 0x3535,
	0xfb0: 0x3555, 0xfb1: 0x3575, 0xfb2: 0x3595, 0xfb3: 0x35b5, 0xfb4: 0x35d5, 0xfb5: 0x35f5,
	0xfb6: 0x3615, 0xfb7: 0x3635, 0xfb8: 0x3655, 0xfb9: 0x3675, 0xfba: 0x3695, 0xfbb: 0x36b5,
	0xfbc: 0x0c19, 0xfbd: 0x0c21, 0xfbe: 0x36d5, 0xfbf: 0x0018,
	// Block 0x3f, offset 0xfc0
	0xfc0: 0x36f5, 0xfc1: 0x3715, 0xfc2: 0x3735, 0xfc3: 0x3755, 0xfc4: 0x3775, 0xfc5: 0x3795,
	0xfc6: 0x37b5, 0xfc7: 0x37d5, 0xfc8: 0x37f5, 0xfc9: 0x3815, 0xfca: 0x3835, 0xfcb: 0x3855,
	0xfcc: 0x3875, 0xfcd: 0x3895, 0xfce: 0x38b5, 0xfcf: 0x38d5, 0xfd0: 0x38f5, 0xfd1: 0x3915,
	0xfd2: 0x3935, 0xfd3: 0x3955, 0xfd4: 0x3975, 0xfd5: 0x3995, 0xfd6: 0x39b5, 0xfd7: 0x39d5,
	0xfd8: 0x39f5, 0xfd9: 0x3a15, 0xfda: 0x3a35, 0xfdb: 0x3a55, 0xfdc: 0x3a75, 0xfdd: 0x3a95,
	0xfde: 0x3ab5, 0xfdf: 0x3ad5, 0xfe0: 0x3af5, 0xfe1: 0x3b15, 0xfe2: 0x3b35, 0xfe3: 0x3b55,
	0xfe4: 0x3b75, 0xfe5: 0x3b95, 0xfe6: 0x1295, 0xfe7: 0x3bb5, 0xfe8: 0x3bd5, 0xfe9: 0x3bf5,
	0xfea: 0x3c15, 0xfeb: 0x3c35, 0xfec: 0x3c55, 0xfed: 0x3c75, 0xfee: 0x23b5, 0xfef: 0x3c95,
	0xff0: 0x3cb5, 0xff1: 0x0c29, 0xff2: 0x0c31, 0xff3: 0x0c39, 0xff4: 0x0c41, 0xff5: 0x0c49,
	0xff6: 0x0c51, 0xff7: 0x0c59, 0xff8: 0x0c61, 0xff9: 0x0c69, 0xffa: 0x0c71, 0xffb: 0x0c79,
	0xffc: 0x0c81, 0xffd: 0x0c89, 0xffe: 0x0c91, 0xfff: 0x0c99,
	// Block 0x40, offset 0x1000
	0x1000: 0x0ca1, 0x1001: 0x0ca9, 0x1002: 0x0cb1, 0x1003: 0x0cb9, 0x1004: 0x0cc1, 0x1005: 0x0cc9,
	0x1006: 0x0cd1, 0x1007: 0x0cd9, 0x1008: 0x0ce1, 0x1009: 0x0ce9, 0x100a: 0x0cf1, 0x100b: 0x0cf9,
	0x100c: 0x0d01, 0x100d: 0x3cd5, 0x100e: 0x0d09, 0x100f: 0x3cf5, 0x1010: 0x3d15, 0x1011: 0x3d2d,
	0x1012: 0x3d45, 0x1013: 0x3d5d, 0x1014: 0x3d75, 0x1015: 0x3d75, 0x1016: 0x3d5d, 0x1017: 0x3d8d,
	0x1018: 0x07d5, 0x1019: 0x3da5, 0x101a: 0x3dbd, 0x101b: 0x3dd5, 0x101c: 0x3ded, 0x101d: 0x3e05,
	0x101e: 0x3e1d, 0x101f: 0x3e35, 0x1020: 0x3e4d, 0x1021: 0x3e65, 0x1022: 0x3e7d, 0x1023: 0x3e95,
	0x1024: 0x3ead, 0x1025: 0x3ead, 0x1026: 0x3ec5, 0x1027: 0x3ec5, 0x1028: 0x3edd, 0x1029: 0x3edd,
	0x102a: 0x3ef5, 0x102b: 0x3f0d, 0x102c: 0x3f25, 0x102d: 0x3f3d, 0x102e: 0x3f55, 0x102f: 0x3f55,
	0x1030: 0x3f6d, 0x1031: 0x3f6d, 0x1032: 0x3f6d, 0x1033: 0x3f85, 0x1034: 0x3f9d, 0x1035: 0x3fb5,
	0x1036: 0x3fcd, 0x1037: 0x3fb5, 0x1038: 0x3fe5, 0x1039: 0x3ffd, 0x103a: 0x3f85, 0x103b: 0x4015,
	0x103c: 0x402d, 0x103d: 0x402d, 0x103e: 0x402d, 0x103f: 0x0d11,
	// Block 0x41, offset 0x1040
	0x1040: 0x10f9, 0x1041: 0x1101, 0x1042: 0x40a5, 0x1043: 0x1109, 0x1044: 0x1111, 0x1045: 0x1119,
	0x1046: 0x1121, 0x1047: 0x1129, 0x1048: 0x40c5, 0x1049: 0x1131, 0x104a: 0x1139, 0x104b: 0x1141,
	0x104c: 0x40e5, 0x104d: 0x40e5, 0x104e: 0x1149, 0x104f: 0x1151, 0x1050: 0x1159, 0x1051: 0x4105,
	0x1052: 0x4125, 0x1053: 0x4145, 0x1054: 0x4165, 0x1055: 0x4185, 0x1056: 0x1161, 0x1057: 0x1169,
	0x1058: 0x1171, 0x1059: 0x1179, 0x105a: 0x1181, 0x105b: 0x41a5, 0x105c: 0x1189, 0x105d: 0x1191,
	0x105e: 0x1199, 0x105f: 0x41c5, 0x1060: 0x41e5, 0x1061: 0x11a1, 0x1062: 0x4205, 0x1063: 0x4225,
	0x1064: 0x4245, 0x1065: 0x11a9, 0x1066: 0x4265, 0x1067: 0x11b1, 0x1068: 0x11b9, 0x1069: 0x10f9,
	0x106a: 0x4285, 0x106b: 0x42a5, 0x106c: 0x42c5, 0x106d: 0x42e5, 0x106e: 0x11c1, 0x106f: 0x11c9,
	0x1070: 0x11d1, 0x1071: 0x11d9, 0x1072: 0x4305, 0x1073: 0x11e1, 0x1074: 0x11e9, 0x1075: 0x11f1,
	0x1076: 0x4325, 0x1077: 0x11f9, 0x1078: 0x1201, 0x1079: 0x11f9, 0x107a: 0x1209, 0x107b: 0x1211,
	0x107c: 0x4345, 0x107d: 0x1219, 0x107e: 0x1221, 0x107f: 0x1219,
	// Block 0x42, offset 0x1080
	0x1080: 0x4365, 0x1081: 0x4385, 0x1082: 0x0040, 0x1083: 0x1229, 0x1084: 0x1231, 0x1085: 0x1239,
	0x1086: 0x1241, 0x1087: 0x0040, 0x1088: 0x1249, 0x1089: 0x1251, 0x108a: 0x1259, 0x108b: 0x1261,
	0x108c: 0x1269, 0x108d: 0x1271, 0x108e: 0x1199, 0x108f: 0x1279, 0x1090: 0x1281, 0x1091: 0x1289,
	0x1092: 0x43a5, 0x1093: 0x1291, 0x1094: 0x1121, 0x1095: 0x43c5, 0x1096: 0x43e5, 0x1097: 0x1299,
	0x1098: 0x0040, 0x1099: 0x4405, 0x109a: 0x12a1, 0x109b: 0x12a9, 0x109c: 0x12b1, 0x109d: 0x12b9,
	0x109e: 0x12c1, 0x109f: 0x12c9, 0x10a0: 0x12d1, 0x10a1: 0x12d9, 0x10a2: 0x12e1, 0x10a3: 0x12e9,
	0x10a4: 0x12f1, 0x10a5: 0x12f9, 0x10a6: 0x1301, 0x10a7: 0x1309, 0x10a8: 0x1311, 0x10a9: 0x1319,
	0x10aa: 0x1321, 0x10ab: 0x1329, 0x10ac: 0x1331, 0x10ad: 0x1339, 0x10ae: 0x1341, 0x10af: 0x1349,
	0x10b0: 0x1351, 0x10b1: 0x1359, 0x10b2: 0x1361, 0x10b3: 0x1369, 0x10b4: 0x1371, 0x10b5: 0x1379,
	0x10b6: 0x1381, 0x10b7: 0x1389, 0x10b8: 0x1391, 0x10b9: 0x1399, 0x10ba: 0x13a1, 0x10bb: 0x13a9,
	0x10bc: 0x13b1, 0x10bd: 0x13b9, 0x10be: 0x13c1, 0x10bf: 0x4425,
	// Block 0x43, offset 0x10c0
	0x10c0: 0xe00d, 0x10c1: 0x0008, 0x10c2: 0xe00d, 0x10c3: 0x0008, 0x10c4: 0xe00d, 0x10c5: 0x0008,
	0x10c6: 0xe00d, 0x10c7: 0x0008, 0x10c8: 0xe00d, 0x10c9: 0x0008, 0x10ca: 0xe00d, 0x10cb: 0x0008,
	0x10cc: 0xe00d, 0x10cd: 0x0008, 0x10ce: 0xe00d, 0x10cf: 0x0008, 0x10d0: 0xe00d, 0x10d1: 0x0008,
	0x10d2: 0xe00d, 0x10d3: 0x0008, 0x10d4: 0xe00d, 0x10d5: 0x0008, 0x10d6: 0xe00d, 0x10d7: 0x0008,
	0x10d8: 0xe00d, 0x10d9: 0x0008, 0x10da: 0xe00d, 0x10db: 0x0008, 0x10dc: 0xe00d, 0x10dd: 0x0008,
	0x10de: 0xe00d, 0x10df: 0x0008, 0x10e0: 0xe00d, 0x10e1: 0x0008, 0x10e2: 0xe00d, 0x10e3: 0x0008,
	0x10e4: 0xe00d, 0x10e5: 0x0008, 0x10e6: 0xe00d, 0x10e7: 0x0008, 0x10e8: 0xe00d, 0x10e9: 0x0008,
	0x10ea: 0xe00d, 0x10eb: 0x0008, 0x10ec: 0xe00d, 0x10ed: 0x0008, 0x10ee: 0x0008, 0x10ef: 0x3308,
	0x10f0: 0x3318, 0x10f1: 0x3318, 0x10f2: 0x3318, 0x10f3: 0x0018, 0x10f4: 0x3308, 0x10f5: 0x3308,
	0x10f6: 0x3308, 0x10f7: 0x3308, 0x10f8: 0x3308, 0x10f9: 0x3308, 0x10fa: 0x3308, 0x10fb: 0x3308,
	0x10fc: 0x3308, 0x10fd: 0x3308, 0x10fe: 0x0018, 0x10ff: 0x0008,
	// Block 0x44, offset 0x1100
	0x1100: 0xe00d, 0x1101: 0x0008, 0x1102: 0xe00d, 0x1103: 0x0008, 0x1104: 0xe00d, 0x1105: 0x0008,
	0x1106: 0xe00d, 0x1107: 0x0008, 0x1108: 0xe00d, 0x1109: 0x0008, 0x110a: 0xe00d, 0x110b: 0x0008,
	0x110c: 0xe00d, 0x110d: 0x0008, 0x110e: 0xe00d, 0x110f: 0x0008, 0x1110: 0xe00d, 0x1111: 0x0008,
	0x1112: 0xe00d, 0x1113: 0x0008, 0x1114: 0xe00d, 0x1115: 0x0008, 0x1116: 0xe00d, 0x1117: 0x0008,
	0x1118: 0xe00d, 0x1119: 0x0008, 0x111a: 0xe00d, 0x111b: 0x0008, 0x111c: 0x02d1, 0x111d: 0x13c9,
	0x111e: 0x3308, 0x111f: 0x3308, 0x1120: 0x0008, 0x1121: 0x0008, 0x1122: 0x0008, 0x1123: 0x0008,
	0x1124: 0x0008, 0x1125: 0x0008, 0x1126: 0x0008, 0x1127: 0x0008, 0x1128: 0x0008, 0x1129: 0x0008,
	0x112a: 0x0008, 0x112b: 0x0008, 0x112c: 0x0008, 0x112d: 0x0008, 0x112e: 0x0008, 0x112f: 0x0008,
	0x1130: 0x0008, 0x1131: 0x0008, 0x1132: 0x0008, 0x1133: 0x0008, 0x1134: 0x0008, 0x1135: 0x0008,
	0x1136: 0x0008, 0x1137: 0x0008, 0x1138: 0x0008, 0x1139: 0x0008, 0x113a: 0x0008, 0x113b: 0x0008,
	0x113c: 0x0008, 0x113d: 0x0008, 0x113e: 0x0008, 0x113f: 0x0008,
	// Block 0x45, offset 0x1140
	0x1140: 0x0018, 0x1141: 0x0018, 0x1142: 0x0018, 0x1143: 0x0018, 0x1144: 0x0018, 0x1145: 0x0018,
	0x1146: 0x0018, 0x1147: 0x0018, 0x1148: 0x0018, 0x1149: 0x0018, 0x114a: 0x0018, 0x114b: 0x0018,
	0x114c: 0x0018, 0x114d: 0x0018, 0x114e: 0x0018, 0x114f: 0x0018, 0x1150: 0x0018, 0x1151: 0x0018,
	0x1152: 0x0018, 0x1153: 0x0018, 0x1154: 0x0018, 0x1155: 0x0018, 0x1156: 0x0018, 0x1157: 0x0008,
	0x1158: 0x0008, 0x1159: 0x0008, 0x115a: 0x0008, 0x115b: 0x0008, 0x115c: 0x0008, 0x115d: 0x0008,
	0x115e: 0x0008, 0x115f: 0x0008, 0x1160: 0x0018, 0x1161: 0x0018, 0x1162: 0xe00d, 0x1163: 0x0008,
	0x1164: 0xe00d, 0x1165: 0x0008, 0x1166: 0xe00d, 0x1167: 0x0008, 0x1168: 0xe00d, 0x1169: 0x0008,
	0x116a: 0xe00d, 0x116b: 0x0008, 0x116c: 0xe00d, 0x116d: 0x0008, 0x116e: 0xe00d, 0x116f: 0x0008,
	0x1170: 0x0008, 0x1171: 0x0008, 0x1172: 0xe00d, 0x1173: 0x0008, 0x1174: 0xe00d, 0x1175: 0x0008,
	0x1176: 0xe00d, 0x1177: 0x0008, 0x1178: 0xe00d, 0x1179: 0x0008, 0x117a: 0xe00d, 0x117b: 0x0008,
	0x117c: 0xe00d, 0x117d: 0x0008, 0x117e: 0xe00d, 0x117f: 0x0008,
	// Block 0x46, offset 0x1180
	0x1180: 0xe00d, 0x1181: 0x0008, 0x1182: 0xe00d, 0x1183: 0x0008, 0x1184: 0xe00d, 0x1185: 0x0008,
	0x1186: 0xe00d, 0x1187: 0x0008, 0x1188: 0xe00d, 0x1189: 0x0008, 0x118a: 0xe00d, 0x118b: 0x0008,
	0x118c: 0xe00d, 0x118d: 0x0008, 0x118e: 0xe00d, 0x118f: 0x0008, 0x1190: 0xe00d, 0x1191: 0x0008,
	0x1192: 0xe00d, 0x1193: 0x0008, 0x1194: 0xe00d, 0x1195: 0x0008, 0x1196: 0xe00d, 0x1197: 0x0008,
	0x1198: 0xe00d, 0x1199: 0x0008, 0x119a: 0xe00d, 0x119b: 0x0008, 0x119c: 0xe00d, 0x119d: 0x0008,
	0x119e: 0xe00d, 0x119f: 0x0008, 0x11a0: 0xe00d, 0x11a1: 0x0008, 0x11a2: 0xe00d, 0x11a3: 0x0008,
	0x11a4: 0xe00d, 0x11a5: 0x0008, 0x11a6: 0xe00d, 0x11a7: 0x0008, 0x11a8: 0xe00d, 0x11a9: 0x0008,
	0x11aa: 0xe00d, 0x11ab: 0x0008, 0x11ac: 0xe00d, 0x11ad: 0x0008, 0x11ae: 0xe00d, 0x11af: 0x0008,
	0x11b0: 0xe0fd, 0x11b1: 0x0008, 0x11b2: 0x0008, 0x11b3: 0x0008, 0x11b4: 0x0008, 0x11b5: 0x0008,
	0x11b6: 0x0008, 0x11b7: 0x0008, 0x11b8: 0x0008, 0x11b9: 0xe01d, 0x11ba: 0x0008, 0x11bb: 0xe03d,
	0x11bc: 0x0008, 0x11bd: 0x4445, 0x11be: 0xe00d, 0x11bf: 0x0008,
	// Block 0x47, offset 0x11c0
	0x11c0: 0xe00d, 0x11c1: 0x0008, 0x11c2: 0xe00d, 0x11c3: 0x0008, 0x11c4: 0xe00d, 0x11c5: 0x0008,
	0x11c6: 0xe00d, 0x11c7: 0x0008, 0x11c8: 0x0008, 0x11c9: 0x0018, 0x11ca: 0x0018, 0x11cb: 0xe03d,
	0x11cc: 0x0008, 0x11cd: 0x0409, 0x11ce: 0x0008, 0x11cf: 0x0008, 0x11d0: 0xe00d, 0x11d1: 0x0008,
	0x11d2: 0xe00d, 0x11d3: 0x0008, 0x11d4: 0x0008, 0x11d5: 0x0008, 0x11d6: 0xe00d, 0x11d7: 0x0008,
	0x11d8: 0xe00d, 0x11d9: 0x0008, 0x11da: 0xe00d, 0x11db: 0x0008, 0x11dc: 0xe00d, 0x11dd: 0x0008,
	0x11de: 0xe00d, 0x11df: 0x0008, 0x11e0: 0xe00d, 0x11e1: 0x0008, 0x11e2: 0xe00d, 0x11e3: 0x0008,
	0x11e4: 0xe00d, 0x11e5: 0x0008, 0x11e6: 0xe00d, 0x11e7: 0x0008, 0x11e8: 0xe00d, 0x11e9: 0x0008,
	0x11ea: 0x13d1, 0x11eb: 0x0371, 0x11ec: 0x0401, 0x11ed: 0x13d9, 0x11ee: 0x0421, 0x11ef: 0x0008,
	0x11f0: 0x13e1, 0x11f1: 0x13e9, 0x11f2: 0x0429, 0x11f3: 0x4465, 0x11f4: 0xe00d, 0x11f5: 0x0008,
	0x11f6: 0xe00d, 0x11f7: 0x0008, 0x11f8: 0xe00d, 0x11f9: 0x0008, 0x11fa: 0xe00d, 0x11fb: 0x0008,
	0x11fc: 0xe00d, 0x11fd: 0x0008, 0x11fe: 0xe00d, 0x11ff: 0x0008,
	// Block 0x48, offset 0x1200
	0x1200: 0xe00d, 0x1201: 0x0008, 0x1202: 0xe00d, 0x1203: 0x0008, 0x1204: 0x03f5, 0x1205: 0x0479,
	0x1206: 0x447d, 0x1207: 0xe07d, 0x1208: 0x0008, 0x1209: 0xe01d, 0x120a: 0x0008, 0x120b: 0x0040,
	0x120c: 0x0040, 0x120d: 0x0040, 0x120e: 0x0040, 0x120f: 0x0040, 0x1210: 0xe00d, 0x1211: 0x0008,
	0x1212: 0x0040, 0x1213: 0x0008, 0x1214: 0x0040, 0x1215: 0x0008, 0x1216: 0xe00d, 0x1217: 0x0008,
	0x1218: 0xe00d, 0x1219: 0x0008, 0x121a: 0x0040, 0x121b: 0x0040, 0x121c: 0x0040, 0x121d: 0x0040,
	0x121e: 0x0040, 0x121f: 0x0040, 0x1220: 0x0040, 0x1221: 0x0040, 0x1222: 0x0040, 0x1223: 0x0040,
	0x1224: 0x0040, 0x1225: 0x0040, 0x1226: 0x0040, 0x1227: 0x0040, 0x1228: 0x0040, 0x1229: 0x0040,
	0x122a: 0x0040, 0x122b: 0x0040, 0x122c: 0x0040, 0x122d: 0x0040, 0x122e: 0x0040, 0x122f: 0x0040,
	0x1230: 0x0040, 0x1231: 0x0040, 0x1232: 0x03d9, 0x1233: 0x03f1, 0x1234: 0x0751, 0x1235: 0xe01d,
	0x1236: 0x0008, 0x1237: 0x0008, 0x1238: 0x0741, 0x1239: 0x13f1, 0x123a: 0x0008, 0x123b: 0x0008,
	0x123c: 0x0008, 0x123d: 0x0008, 0x123e: 0x0008, 0x123f: 0x0008,
	// Block 0x49, offset 0x1240
	0x1240: 0x650d, 0x1241: 0x652d, 0x1242: 0x654d, 0x1243: 0x656d, 0x1244: 0x658d, 0x1245: 0x65ad,
	0x1246: 0x65cd, 0x1247: 0x65ed, 0x1248: 0x660d, 0x1249: 0x662d, 0x124a: 0x664d, 0x124b: 0x666d,
	0x124c: 0x668d, 0x124d: 0x66ad, 0x124e: 0x0008, 0x124f: 0x0008, 0x1250: 0x66cd, 0x1251: 0x0008,
	0x1252: 0x66ed, 0x1253: 0x0008, 0x1254: 0x0008, 0x1255: 0x670d, 0x1256: 0x672d, 0x1257: 0x674d,
	0x1258: 0x676d, 0x1259: 0x678d, 0x125a: 0x67ad, 0x125b: 0x67cd, 0x125c: 0x67ed, 0x125d: 0x680d,
	0x125e: 0x682d, 0x125f: 0x0008, 0x1260: 0x684d, 0x1261: 0x0008, 0x1262: 0x686d, 0x1263: 0x0008,
	0x1264: 0x0008, 0x1265: 0x688d, 0x1266: 0x68ad, 0x1267: 0x0008, 0x1268: 0x0008, 0x1269: 0x0008,
	0x126a: 0x68cd, 0x126b: 0x68ed, 0x126c: 0x690d, 0x126d: 0x692d, 0x126e: 0x694d, 0x126f: 0x696d,
	0x1270: 0x698d, 0x1271: 0x69ad, 0x1272: 0x69cd, 0x1273: 0x69ed, 0x1274: 0x6a0d, 0x1275: 0x6a2d,
	0x1276: 0x6a4d, 0x1277: 0x6a6d, 0x1278: 0x6a8d, 0x1279: 0x6aad, 0x127a: 0x6acd, 0x127b: 0x6aed,
	0x127c: 0x6b0d, 0x127d: 0x6b2d, 0x127e: 0x6b4d, 0x127f: 0x6b6d,
	// Block 0x4a, offset 0x1280
	0x1280: 0x7acd, 0x1281: 0x7aed, 0x1282: 0x7b0d, 0x1283: 0x7b2d, 0x1284: 0x7b4d, 0x1285: 0x7b6d,
	0x1286: 0x7b8d, 0x1287: 0x7bad, 0x1288: 0x7bcd, 0x1289: 0x7bed, 0x128a: 0x7c0d, 0x128b: 0x7c2d,
	0x128c: 0x7c4d, 0x128d: 0x7c6d, 0x128e: 0x7c8d, 0x128f: 0x1409, 0x1290: 0x1411, 0x1291: 0x1419,
	0x1292: 0x7cad, 0x1293: 0x7ccd, 0x1294: 0x7ced, 0x1295: 0x1421, 0x1296: 0x1429, 0x1297: 0x1431,
	0x1298: 0x7d0d, 0x1299: 0x7d2d, 0x129a: 0x0040, 0x129b: 0x0040, 0x129c: 0x0040, 0x129d: 0x0040,
	0x129e: 0x0040, 0x129f: 0x0040, 0x12a0: 0x0040, 0x12a1: 0x0040, 0x12a2: 0x0040, 0x12a3: 0x0040,
	0x12a4: 0x0040, 0x12a5: 0x0040, 0x12a6: 0x0040, 0x12a7: 0x0040, 0x12a8: 0x0040, 0x12a9: 0x0040,
	0x12aa: 0x0040, 0x12ab: 0x0040, 0x12ac: 0x0040, 0x12ad: 0x0040, 0x12ae: 0x0040, 0x12af: 0x0040,
	0x12b0: 0x0040, 0x12b1: 0x0040, 0x12b2: 0x0040, 0x12b3: 0x0040, 0x12b4: 0x0040, 0x12b5: 0x0040,
	0x12b6: 0x0040, 0x12b7: 0x0040, 0x12b8: 0x0040, 0x12b9: 0x0040, 0x12ba: 0x0040, 0x12bb: 0x0040,
	0x12bc: 0x0040, 0x12bd: 0x0040, 0x12be: 0x0040, 0x12bf: 0x0040,
	// Block 0x4b, offset 0x12c0
	0x12c0: 0x1439, 0x12c1: 0x1441, 0x12c2: 0x1449, 0x12c3: 0x7d4d, 0x12c4: 0x7d6d, 0x12c5: 0x1451,
	0x12c6: 0x1451, 0x12c7: 0x0040, 0x12c8: 0x0040, 0x12c9: 0x0040, 0x12ca: 0x0040, 0x12cb: 0x0040,
	0x12cc: 0x0040, 0x12cd: 0x0040, 0x12ce: 0x0040, 0x12cf: 0x0040, 0x12d0: 0x0040, 0x12d1: 0x0040,
	0x12d2: 0x0040, 0x12d3: 0x1459, 0x12d4: 0x1461, 0x12d5: 0x1469, 0x12d6: 0x1471, 0x12d7: 0x1479,
	0x12d8: 0x0040, 0x12d9: 0x0040, 0x12da: 0x0040, 0x12db: 0x0040, 0x12dc: 0x0040, 0x12dd: 0x1481,
	0x12de: 0x3308, 0x12df: 0x1489, 0x12e0: 0x1491, 0x12e1: 0x0779, 0x12e2: 0x0791, 0x12e3: 0x1499,
	0x12e4: 0x14a1, 0x12e5: 0x14a9, 0x12e6: 0x14b1, 0x12e7: 0x14b9, 0x12e8: 0x14c1, 0x12e9: 0x071a,
	0x12ea: 0x14c9, 0x12eb: 0x14d1, 0x12ec: 0x14d9, 0x12ed: 0x14e1, 0x12ee: 0x14e9, 0x12ef: 0x14f1,
	0x12f0: 0x14f9, 0x12f1: 0x1501, 0x12f2: 0x1509, 0x12f3: 0x1511, 0x12f4: 0x1519, 0x12f5: 0x1521,
	0x12f6: 0x1529, 0x12f7: 0x0040, 0x12f8: 0x1531, 0x12f9: 0x1539, 0x12fa: 0x1541, 0x12fb: 0x1549,
	0x12fc: 0x1551, 0x12fd: 0x0040, 0x12fe: 0x1559, 0x12ff: 0x0040,
	// Block 0x4c, offset 0x1300
	0x1300: 0x1561, 0x1301: 0x1569, 0x1302: 0x0040, 0x1303: 0x1571, 0x1304: 0x1579, 0x1305: 0x0040,
	0x1306: 0x1581, 0x1307: 0x1589, 0x1308: 0x1591, 0x1309: 0x1599, 0x130a: 0x15a1, 0x130b: 0x15a9,
	0x130c: 0x15b1, 0x130d: 0x15b9, 0x130e: 0x15c1, 0x130f: 0x15c9, 0x1310: 0x15d1, 0x1311: 0x15d1,
	0x1312: 0x15d9, 0x1313: 0x15d9, 0x1314: 0x15d9, 0x1315: 0x15d9, 0x1316: 0x15e1, 0x1317: 0x15e1,
	0x1318: 0x15e1, 0x1319: 0x15e1, 0x131a: 0x15e9, 0x131b: 0x15e9, 0x131c: 0x15e9, 0x131d: 0x15e9,
	0x131e: 0x15f1, 0x131f: 0x15f1, 0x1320: 0x15f1, 0x1321: 0x15f1, 0x1322: 0x15f9, 0x1323: 0x15f9,
	0x1324: 0x15f9, 0x1325: 0x15f9, 0x1326: 0x1601, 0x1327: 0x1601, 0x1328: 0x1601, 0x1329: 0x1601,
	0x132a: 0x1609, 0x132b: 0x1609, 0x132c: 0x1609, 0x132d: 0x1609, 0x132e: 0x1611, 0x132f: 0x1611,
	0x1330: 0x1611, 0x1331: 0x1611, 0x1332: 0x1619, 0x1333: 0x1619, 0x1334: 0x1619, 0x1335: 0x1619,
	0x1336: 0x1621, 0x1337: 0x1621, 0x1338: 0x1621, 0x1339: 0x1621, 0x133a: 0x1629, 0x133b: 0x1629,
	0x133c: 0x1629, 0x133d: 0x1629, 0x133e: 0x1631, 0x133f: 0x1631,
	// Block 0x4d, offset 0x1340
	0x1340: 0x1631, 0x1341: 0x1631, 0x1342: 0x1639, 0x1343: 0x1639, 0x1344: 0x1641, 0x1345: 0x1641,
	0x1346: 0x1649, 0x1347: 0x1649, 0x1348: 0x1651, 0x1349: 0x1651, 0x134a: 0x1659, 0x134b: 0x1659,
	0x134c: 0x1661, 0x134d: 0x1661, 0x134e: 0x1669, 0x134f: 0x1669, 0x1350: 0x1669, 0x1351: 0x1669,
	0x1352: 0x1671, 0x1353: 0x1671, 0x1354: 0x1671, 0x1355: 0x1671, 0x1356: 0x1679, 0x1357: 0x1679,
	0x1358: 0x1679, 0x1359: 0x1679, 0x135a: 0x1681, 0x135b: 0x1681, 0x135c: 0x1681, 0x135d: 0x1681,
	0x135e: 0x1689, 0x135f: 0x1689, 0x1360: 0x1691, 0x1361: 0x1691, 0x1362: 0x1691, 0x1363: 0x1691,
	0x1364: 0x1699, 0x1365: 0x1699, 0x1366: 0x16a1, 0x1367: 0x16a1, 0x1368: 0x16a1, 0x1369: 0x16a1,
	0x136a: 0x16a9, 0x136b: 0x16a9, 0x136c: 0x16a9, 0x136d: 0x16a9, 0x136e: 0x16b1, 0x136f: 0x16b1,
	0x1370: 0x16b9, 0x1371: 0x16b9, 0x1372: 0x0818, 0x1373: 0x0818, 0x1374: 0x0818, 0x1375: 0x0818,
	0x1376: 0x0818, 0x1377: 0x0818, 0x1378: 0x0818, 0x1379: 0x0818, 0x137a: 0x0818, 0x137b: 0x0818,
	0x137c: 0x0818, 0x137d: 0x0818, 0x137e: 0x0818, 0x137f: 0x0818,
	// Block 0x4e, offset 0x1380
	0x1380: 0x0818, 0x1381: 0x0818, 0x1382: 0x0818, 0x1383: 0x0040, 0x1384: 0x0040, 0x1385: 0x0040,
	0x1386: 0x0040, 0x1387: 0x0040, 0x1388: 0x0040, 0x1389: 0x0040, 0x138a: 0x0040, 0x138b: 0x0040,
	0x138c: 0x0040, 0x138d: 0x0040, 0x138e: 0x0040, 0x138f: 0x0040, 0x1390: 0x0040, 0x1391: 0x0040,
	0x1392: 0x0040, 0x1393: 0x16c1, 0x1394: 0x16c1, 0x1395: 0x16c1, 0x1396: 0x16c1, 0x1397: 0x16c9,
	0x1398: 0x16c9, 0x1399: 0x16d1, 0x139a: 0x16d1, 0x139b: 0x16d9, 0x139c: 0x16d9, 0x139d: 0x0149,
	0x139e: 0x16e1, 0x139f: 0x16e1, 0x13a0: 0x16e9, 0x13a1: 0x16e9, 0x13a2: 0x16f1, 0x13a3: 0x16f1,
	0x13a4: 0x16f9, 0x13a5: 0x16f9, 0x13a6: 0x16f9, 0x13a7: 0x16f9, 0x13a8: 0x1701, 0x13a9: 0x1701,
	0x13aa: 0x1709, 0x13ab: 0x1709, 0x13ac: 0x1711, 0x13ad: 0x1711, 0x13ae: 0x1719, 0x13af: 0x1719,
	0x13b0: 0x1721, 0x13b1: 0x1721, 0x13b2: 0x1729, 0x13b3: 0x1729, 0x13b4: 0x1731, 0x13b5: 0x1731,
	0x13b6: 0x1739, 0x13b7: 0x1739, 0x13b8: 0x1739, 0x13b9: 0x1741, 0x13ba: 0x1741, 0x13bb: 0x1741,
	0x13bc: 0x1749, 0x13bd: 0x1749, 0x13be: 0x1749, 0x13bf: 0x1749,
	// Block 0x4f, offset 0x13c0
	0x13c0: 0x1949, 0x13c1: 0x1951, 0x13c2: 0x1959, 0x13c3: 0x1961, 0x13c4: 0x1969, 0x13c5: 0x1971,
	0x13c6: 0x1979, 0x13c7: 0x1981, 0x13c8: 0x1989, 0x13c9: 0x1991, 0x13ca: 0x1999, 0x13cb: 0x19a1,
	0x13cc: 0x19a9, 0x13cd: 0x19b1, 0x13ce: 0x19b9, 0x13cf: 0x19c1, 0x13d0: 0x19c9, 0x13d1: 0x19d1,
	0x13d2: 0x19d9, 0x13d3: 0x19e1, 0x13d4: 0x19e9, 0x13d5: 0x19f1, 0x13d6: 0x19f9, 0x13d7: 0x1a01,
	0x13d8: 0x1a09, 0x13d9: 0x1a11, 0x13da: 0x1a19, 0x13db: 0x1a21, 0x13dc: 0x1a29, 0x13dd: 0x1a31,
	0x13de: 0x1a3a, 0x13df: 0x1a42, 0x13e0: 0x1a4a, 0x13e1: 0x1a52, 0x13e2: 0x1a5a, 0x13e3: 0x1a62,
	0x13e4: 0x1a69, 0x13e5: 0x1a71, 0x13e6: 0x1761, 0x13e7: 0x1a79, 0x13e8: 0x1741, 0x13e9: 0x1769,
	0x13ea: 0x1a81, 0x13eb: 0x1a89, 0x13ec: 0x1789, 0x13ed: 0x1a91, 0x13ee: 0x1791, 0x13ef: 0x1799,
	0x13f0: 0x1a99, 0x13f1: 0x1aa1, 0x13f2: 0x17b9, 0x13f3: 0x1aa9, 0x13f4: 0x17c1, 0x13f5: 0x17c9,
	0x13f6: 0x1ab1, 0x13f7: 0x1ab9, 0x13f8: 0x17d9, 0x13f9: 0x1ac1, 0x13fa: 0x17e1, 0x13fb: 0x17e9,
	0x13fc: 0x18d1, 0x13fd: 0x18d9, 0x13fe: 0x18f1, 0x13ff: 0x18f9,
	// Block 0x50, offset 0x1400
	0x1400: 0x1901, 0x1401: 0x1921, 0x1402: 0x1929, 0x1403: 0x1931, 0x1404: 0x1939, 0x1405: 0x1959,
	0x1406: 0x1961, 0x1407: 0x1969, 0x1408: 0x1ac9, 0x1409: 0x1989, 0x140a: 0x1ad1, 0x140b: 0x1ad9,
	0x140c: 0x19b9, 0x140d: 0x1ae1, 0x140e: 0x19c1, 0x140f: 0x19c9, 0x1410: 0x1a31, 0x1411: 0x1ae9,
	0x1412: 0x1af1, 0x1413: 0x1a09, 0x1414: 0x1af9, 0x1415: 0x1a11, 0x1416: 0x1a19, 0x1417: 0x1751,
	0x1418: 0x1759, 0x1419: 0x1b01, 0x141a: 0x1761, 0x141b: 0x1b09, 0x141c: 0x1771, 0x141d: 0x1779,
	0x141e: 0x1781, 0x141f: 0x1789, 0x1420: 0x1b11, 0x1421: 0x17a1, 0x1422: 0x17a9, 0x1423: 0x17b1,
	0x1424: 0x17b9, 0x1425: 0x1b19, 0x1426: 0x17d9, 0x1427: 0x17f1, 0x1428: 0x17f9, 0x1429: 0x1801,
	0x142a: 0x1809, 0x142b: 0x1811, 0x142c: 0x1821, 0x142d: 0x1829, 0x142e: 0x1831, 0x142f: 0x1839,
	0x1430: 0x1841, 0x1431: 0x1849, 0x1432: 0x1b21, 0x1433: 0x1851, 0x1434: 0x1859, 0x1435: 0x1861,
	0x1436: 0x1869, 0x1437: 0x1871, 0x1438: 0x1879, 0x1439: 0x1889, 0x143a: 0x1891, 0x143b: 0x1899,
	0x143c: 0x18a1, 0x143d: 0x18a9, 0x143e: 0x18b1, 0x143f: 0x18b9,
	// Block 0x51, offset 0x1440
	0x1440: 0x18c1, 0x1441: 0x18c9, 0x1442: 0x18e1, 0x1443: 0x18e9, 0x1444: 0x1909, 0x1445: 0x1911,
	0x1446: 0x1919, 0x1447: 0x1921, 0x1448: 0x1929, 0x1449: 0x1941, 0x144a: 0x1949, 0x144b: 0x1951,
	0x144c: 0x1959, 0x144d: 0x1b29, 0x144e: 0x1971, 0x144f: 0x1979, 0x1450: 0x1981, 0x1451: 0x1989,
	0x1452: 0x19a1, 0x1453: 0x19a9, 0x1454: 0x19b1, 0x1455: 0x19b9, 0x1456: 0x1b31, 0x1457: 0x19d1,
	0x1458: 0x19d9, 0x1459: 0x1b39, 0x145a: 0x19f1, 0x145b: 0x19f9, 0x145c: 0x1a01, 0x145d: 0x1a09,
	0x145e: 0x1b41, 0x145f: 0x1761, 0x1460: 0x1b09, 0x1461: 0x1789, 0x1462: 0x1b11, 0x1463: 0x17b9,
	0x1464: 0x1b19, 0x1465: 0x17d9, 0x1466: 0x1b49, 0x1467: 0x1841, 0x1468: 0x1b51, 0x1469: 0x1b59,
	0x146a: 0x1b61, 0x146b: 0x1921, 0x146c: 0x1929, 0x146d: 0x1959, 0x146e: 0x19b9, 0x146f: 0x1b31,
	0x1470: 0x1a09, 0x1471: 0x1b41, 0x1472: 0x1b69, 0x1473: 0x1b71, 0x1474: 0x1b79, 0x1475: 0x1b81,
	0x1476: 0x1b89, 0x1477: 0x1b91, 0x1478: 0x1b99, 0x1479: 0x1ba1, 0x147a: 0x1ba9, 0x147b: 0x1bb1,
	0x147c: 0x1bb9, 0x147d: 0x1bc1, 0x147e: 0x1bc9, 0x147f: 0x1bd1,
	// Block 0x52, offset 0x1480
	0x1480: 0x1bd9, 0x1481: 0x1be1, 0x1482: 0x1be9, 0x1483: 0x1bf1, 0x1484: 0x1bf9, 0x1485: 0x1c01,
	0x1486: 0x1c09, 0x1487: 0x1c11, 0x1488: 0x1c19, 0x1489: 0x1c21, 0x148a: 0x1c29, 0x148b: 0x1c31,
	0x148c: 0x1b59, 0x148d: 0x1c39, 0x148e: 0x1c41, 0x148f: 0x1c49, 0x1490: 0x1c51, 0x1491: 0x1b81,
	0x1492: 0x1b89, 0x1493: 0x1b91, 0x1494: 0x1b99, 0x1495: 0x1ba1, 0x1496: 0x1ba9, 0x1497: 0x1bb1,
	0x1498: 0x1bb9, 0x1499: 0x1bc1, 0x149a: 0x1bc9, 0x149b: 0x1bd1, 0x149c: 0x1bd9, 0x149d: 0x1be1,
	0x149e: 0x1be9, 0x149f: 0x1bf1, 0x14a0: 0x1bf9, 0x14a1: 0x1c01, 0x14a2: 0x1c09, 0x14a3: 0x1c11,
	0x14a4: 0x1c19, 0x14a5: 0x1c21, 0x14a6: 0x1c29, 0x14a7: 0x1c31, 0x14a8: 0x1b59, 0x14a9: 0x1c39,
	0x14aa: 0x1c41, 0x14ab: 0x1c49, 0x14ac: 0x1c51, 0x14ad: 0x1c21, 0x14ae: 0x1c29, 0x14af: 0x1c31,
	0x14b0: 0x1b59, 0x14b1: 0x1b51, 0x14b2: 0x1b61, 0x14b3: 0x1881, 0x14b4: 0x1829, 0x14b5: 0x1831,
	0x14b6: 0x1839, 0x14b7: 0x1c21, 0x14b8: 0x1c29, 0x14b9: 0x1c31, 0x14ba: 0x1881, 0x14bb: 0x1889,
	0x14bc: 0x1c59, 0x14bd: 0x1c59, 0x14be: 0x0018, 0x14bf: 0x0018,
	// Block 0x53, offset 0x14c0
	0x14c0: 0x0018, 0x14c1: 0x0018, 0x14c2: 0x0018, 0x14c3: 0x0018, 0x14c4: 0x0018, 0x14c5: 0x0018,
	0x14c6: 0x0018, 0x14c7: 0x0018, 0x14c8: 0x0018, 0x14c9: 0x0018, 0x14ca: 0x0018, 0x14cb: 0x0018,
	0x14cc: 0x0018, 0x14cd: 0x0018, 0x14ce: 0x0018, 0x14cf: 0x0018, 0x14d0: 0x1c61, 0x14d1: 0x1c69,
	0x14d2: 0x1c69, 0x14d3: 0x1c71, 0x14d4: 0x1c79, 0x14d5: 0x1c81, 0x14d6: 0x1c89, 0x14d7: 0x1c91,
	0x14d8: 0x1c99, 0x14d9: 0x1c99, 0x14da: 0x1ca1, 0x14db: 0x1ca9, 0x14dc: 0x1cb1, 0x14dd: 0x1cb9,
	0x14de: 0x1cc1, 0x14df: 0x1cc9, 0x14e0: 0x1cc9, 0x14e1: 0x1cd1, 0x14e2: 0x1cd9, 0x14e3: 0x1cd9,
	0x14e4: 0x1ce1, 0x14e5: 0x1ce1, 0x14e6: 0x1ce9, 0x14e7: 0x1cf1, 0x14e8: 0x1cf1, 0x14e9: 0x1cf9,
	0x14ea: 0x1d01, 0x14eb: 0x1d01, 0x14ec: 0x1d09, 0x14ed: 0x1d09, 0x14ee: 0x1d11, 0x14ef: 0x1d19,
	0x14f0: 0x1d19, 0x14f1: 0x1d21, 0x14f2: 0x1d21, 0x14f3: 0x1d29, 0x14f4: 0x1d31, 0x14f5: 0x1d39,
	0x14f6: 0x1d41, 0x14f7: 0x1d41, 0x14f8: 0x1d49, 0x14f9: 0x1d51, 0x14fa: 0x1d59, 0x14fb: 0x1d61,
	0x14fc: 0x1d69, 0x14fd: 0x1d69, 0x14fe: 0x1d71, 0x14ff: 0x1d79,
	// Block 0x54, offset 0x1500
	0x1500: 0x1f29, 0x1501: 0x1f31, 0x1502: 0x1f39, 0x1503: 0x1f11, 0x1504: 0x1d39, 0x1505: 0x1ce9,
	0x1506: 0x1f41, 0x1507: 0x1f49, 0x1508: 0x0040, 0x1509: 0x0040, 0x150a: 0x0040, 0x150b: 0x0040,
	0x150c: 0x0040, 0x150d: 0x0040, 0x150e: 0x0040, 0x150f: 0x0018, 0x1510: 0x0040, 0x1511: 0x0040,
	0x1512: 0x0040, 0x1513: 0x0040, 0x1514: 0x0040, 0x1515: 0x0040, 0x1516: 0x0040, 0x1517: 0x0040,
	0x1518: 0x0040, 0x1519: 0x0040, 0x151a: 0x0040, 0x151b: 0x0040, 0x151c: 0x0040, 0x151d: 0x0040,
	0x151e: 0x0040, 0x151f: 0x0040, 0x1520: 0x0040, 0x1521: 0x0040, 0x1522: 0x0040, 0x1523: 0x0040,
	0x1524: 0x0040, 0x1525: 0x0040, 0x1526: 0x0040, 0x1527: 0x0040, 0x1528: 0x0040, 0x1529: 0x0040,
	0x152a: 0x0040, 0x152b: 0x0040, 0x152c: 0x0040, 0x152d: 0x0040, 0x152e: 0x0040, 0x152f: 0x0040,
	0x1530: 0x1f51, 0x1531: 0x1f59, 0x1532: 0x1f61, 0x1533: 0x1f69, 0x1534: 0x1f71, 0x1535: 0x1f79,
	0x1536: 0x1f81, 0x1537: 0x1f89, 0x1538: 0x1f91, 0x1539: 0x1f99, 0x153a: 0x1fa2, 0x153b: 0x1faa,
	0x153c: 0x1fb1, 0x153d: 0x0018, 0x153e: 0x0018, 0x153f: 0x0018,
	// Block 0x55, offset 0x1540
	0x1540: 0x33c0, 0x1541: 0x33c0, 0x1542: 0x33c0, 0x1543: 0x33c0, 0x1544: 0x33c0, 0x1545: 0x33c0,
	0x1546: 0x33c0, 0x1547: 0x33c0, 0x1548: 0x33c0, 0x1549: 0x33c0, 0x154a: 0x33c0, 0x154b: 0x33c0,
	0x154c: 0x33c0, 0x154d: 0x33c0, 0x154e: 0x33c0, 0x154f: 0x33c0, 0x1550: 0x1fba, 0x1551: 0x7d8d,
	0x1552: 0x0040, 0x1553: 0x1fc2, 0x1554: 0x0122, 0x1555: 0x1fca, 0x1556: 0x1fd2, 0x1557: 0x7dad,
	0x1558: 0x7dcd, 0x1559: 0x0040, 0x155a: 0x0040, 0x155b: 0x0040, 0x155c: 0x0040, 0x155d: 0x0040,
	0x155e: 0x0040, 0x155f: 0x0040, 0x1560: 0x3308, 0x1561: 0x3308, 0x1562: 0x3308, 0x1563: 0x3308,
	0x1564: 0x3308, 0x1565: 0x3308, 0x1566: 0x3308, 0x1567: 0x3308, 0x1568: 0x3308, 0x1569: 0x3308,
	0x156a: 0x3308, 0x156b: 0x3308, 0x156c: 0x3308, 0x156d: 0x3308, 0x156e: 0x3308, 0x156f: 0x3308,
	0x1570: 0x0040, 0x1571: 0x7ded, 0x1572: 0x7e0d, 0x1573: 0x1fda, 0x1574: 0x1fda, 0x1575: 0x072a,
	0x1576: 0x0732, 0x1577: 0x1fe2, 0x1578: 0x1fea, 0x1579: 0x7e2d, 0x157a: 0x7e4d, 0x157b: 0x7e6d,
	0x157c: 0x7e2d, 0x157d: 0x7e8d, 0x157e: 0x7ead, 0x157f: 0x7e8d,
	// Block 0x56, offset 0x1580
	0x1580: 0x7ecd, 0x1581: 0x7eed, 0x1582: 0x7f0d, 0x1583: 0x7eed, 0x1584: 0x7f2d, 0x1585: 0x0018,
	0x1586: 0x0018, 0x1587: 0x1ff2, 0x1588: 0x1ffa, 0x1589: 0x7f4e, 0x158a: 0x7f6e, 0x158b: 0x7f8e,
	0x158c: 0x7fae, 0x158d: 0x1fda, 0x158e: 0x1fda, 0x158f: 0x1fda, 0x1590: 0x1fba, 0x1591: 0x7fcd,
	0x1592: 0x0040, 0x1593: 0x0040, 0x1594: 0x0122, 0x1595: 0x1fc2, 0x1596: 0x1fd2, 0x1597: 0x1fca,
	0x1598: 0x7fed, 0x1599: 0x072a, 0x159a: 0x0732, 0x159b: 0x1fe2, 0x159c: 0x1fea, 0x159d: 0x7ecd,
	0x159e: 0x7f2d, 0x159f: 0x2002, 0x15a0: 0x200a, 0x15a1: 0x2012, 0x15a2: 0x071a, 0x15a3: 0x2019,
	0x15a4: 0x2022, 0x15a5: 0x202a, 0x15a6: 0x0722, 0x15a7: 0x0040, 0x15a8: 0x2032, 0x15a9: 0x203a,
	0x15aa: 0x2042, 0x15ab: 0x204a, 0x15ac: 0x0040, 0x15ad: 0x0040, 0x15ae: 0x0040, 0x15af: 0x0040,
	0x15b0: 0x800e, 0x15b1: 0x2051, 0x15b2: 0x802e, 0x15b3: 0x0808, 0x15b4: 0x804e, 0x15b5: 0x0040,
	0x15b6: 0x806e, 0x15b7: 0x2059, 0x15b8: 0x808e, 0x15b9: 0x2061, 0x15ba: 0x80ae, 0x15bb: 0x2069,
	0x15bc: 0x80ce, 0x15bd: 0x2071, 0x15be: 0x80ee, 0x15bf: 0x2079,
	// Block 0x57, offset 0x15c0
	0x15c0: 0x2081, 0x15c1: 0x2089, 0x15c2: 0x2089, 0x15c3: 0x2091, 0x15c4: 0x2091, 0x15c5: 0x2099,
	0x15c6: 0x2099, 0x15c7: 0x20a1, 0x15c8: 0x20a1, 0x15c9: 0x20a9, 0x15ca: 0x20a9, 0x15cb: 0x20a9,
	0x15cc: 0x20a9, 0x15cd: 0x20b1, 0x15ce: 0x20b1, 0x15cf: 0x20b9, 0x15d0: 0x20b9, 0x15d1: 0x20b9,
	0x15d2: 0x20b9, 0x15d3: 0x20c1, 0x15d4: 0x20c1, 0x15d5: 0x20c9, 0x15d6: 0x20c9, 0x15d7: 0x20c9,
	0x15d8: 0x20c9, 0x15d9: 0x20d1, 0x15da: 0x20d1, 0x15db: 0x20d1, 0x15dc: 0x20d1, 0x15dd: 0x20d9,
	0x15de: 0x20d9, 0x15df: 0x20d9, 0x15e0: 0x20d9, 0x15e1: 0x20e1, 0x15e2: 0x20e1, 0x15e3: 0x20e1,
	0x15e4: 0x20e1, 0x15e5: 0x20e9, 0x15e6: 0x20e9, 0x15e7: 0x20e9, 0x15e8: 0x20e9, 0x15e9: 0x20f1,
	0x15ea: 0x20f1, 0x15eb: 0x20f9, 0x15ec: 0x20f9, 0x15ed: 0x2101, 0x15ee: 0x2101, 0x15ef: 0x2109,
	0x15f0: 0x2109, 0x15f1: 0x2111, 0x15f2: 0x2111, 0x15f3: 0x2111, 0x15f4: 0x2111, 0x15f5: 0x2119,
	0x15f6: 0x2119, 0x15f7: 0x2119, 0x15f8: 0x2119, 0x15f9: 0x2121, 0x15fa: 0x2121, 0x15fb: 0x2121,
	0x15fc: 0x2121, 0x15fd: 0x2129, 0x15fe: 0x2129, 0x15ff: 0x2129,
	// Block 0x58, offset 0x1600
	0x1600: 0x2129, 0x1601: 0x2131, 0x1602: 0x2131, 0x1603: 0x2131, 0x1604: 0x2131, 0x1605: 0x2139,
	0x1606: 0x2139, 0x1607: 0x2139, 0x1608: 0x2139, 0x1609: 0x2141, 0x160a: 0x2141, 0x160b: 0x2141,
	0x160c: 0x2141, 0x160d: 0x2149, 0x160e: 0x2149, 0x160f: 0x2149, 0x1610: 0x2149, 0x1611: 0x2151,
	0x1612: 0x2151, 0x1613: 0x2151, 0x1614: 0x2151, 0x1615: 0x2159, 0x1616: 0x2159, 0x1617: 0x2159,
	0x1618: 0x2159, 0x1619: 0x2161, 0x161a: 0x2161, 0x161b: 0x2161, 0x161c: 0x2161, 0x161d: 0x2169,
	0x161e: 0x2169, 0x161f: 0x2169, 0x1620: 0x2169, 0x1621: 0x2171, 0x1622: 0x2171, 0x1623: 0x2171,
	0x1624: 0x2171, 0x1625: 0x2179, 0x1626: 0x2179, 0x1627: 0x2179, 0x1628: 0x2179, 0x1629: 0x2181,
	0x162a: 0x2181, 0x162b: 0x2181, 0x162c: 0x2181, 0x162d: 0x2189, 0x162e: 0x2189, 0x162f: 0x1701,
	0x1630: 0x1701, 0x1631: 0x2191, 0x1632: 0x2191, 0x1633: 0x2191, 0x1634: 0x2191, 0x1635: 0x2199,
	0x1636: 0x2199, 0x1637: 0x21a1, 0x1638: 0x21a1, 0x1639: 0x21a9, 0x163a: 0x21a9, 0x163b: 0x21b1,
	0x163c: 0x21b1, 0x163d: 0x0040, 0x163e: 0x0040, 0x163f: 0x03c0,
	// Block 0x59, offset 0x1640
	0x1640: 0x0040, 0x1641: 0x1fca, 0x1642: 0x21ba, 0x1643: 0x2002, 0x1644: 0x203a, 0x1645: 0x2042,
	0x1646: 0x200a, 0x1647: 0x21c2, 0x1648: 0x072a, 0x1649: 0x0732, 0x164a: 0x2012, 0x164b: 0x071a,
	0x164c: 0x1fba, 0x164d: 0x2019, 0x164e: 0x0961, 0x164f: 0x21ca, 0x1650: 0x06e1, 0x1651: 0x0049,
	0x1652: 0x0029, 0x1653: 0x0031, 0x1654: 0x06e9, 0x1655: 0x06f1, 0x1656: 0x06f9, 0x1657: 0x0701,
	0x1658: 0x0709, 0x1659: 0x0711, 0x165a: 0x1fc2, 0x165b: 0x0122, 0x165c: 0x2022, 0x165d: 0x0722,
	0x165e: 0x202a, 0x165f: 0x1fd2, 0x1660: 0x204a, 0x1661: 0x0019, 0x1662: 0x02e9, 0x1663: 0x03d9,
	0x1664: 0x02f1, 0x1665: 0x02f9, 0x1666: 0x03f1, 0x1667: 0x0309, 0x1668: 0x00a9, 0x1669: 0x0311,
	0x166a: 0x00b1, 0x166b: 0x0319, 0x166c: 0x0101, 0x166d: 0x0321, 0x166e: 0x0329, 0x166f: 0x0051,
	0x1670: 0x0339, 0x1671: 0x0751, 0x1672: 0x00b9, 0x1673: 0x0089, 0x1674: 0x0341, 0x1675: 0x0349,
	0x1676: 0x0391, 0x1677: 0x00c1, 0x1678: 0x0109, 0x1679: 0x00c9, 0x167a: 0x04b1, 0x167b: 0x1ff2,
	0x167c: 0x2032, 0x167d: 0x1ffa, 0x167e: 0x21d2, 0x167f: 0x1fda,
	// Block 0x5a, offset 0x1680
	0x1680: 0x0672, 0x1681: 0x0019, 0x1682: 0x02e9, 0x1683: 0x03d9, 0x1684: 0x02f1, 0x1685: 0x02f9,
	0x1686: 0x03f1, 0x1687: 0x0309, 0x1688: 0x00a9, 0x1689: 0x0311, 0x168a: 0x00b1, 0x168b: 0x0319,
	0x168c: 0x0101, 0x168d: 0x0321, 0x168e: 0x0329, 0x168f: 0x0051, 0x1690: 0x0339, 0x1691: 0x0751,
	0x1692: 0x00b9, 0x1693: 0x0089, 0x1694: 0x0341, 0x1695: 0x0349, 0x1696: 0x0391, 0x1697: 0x00c1,
	0x1698: 0x0109, 0x1699: 0x00c9, 0x169a: 0x04b1, 0x169b: 0x1fe2, 0x169c: 0x21da, 0x169d: 0x1fea,
	0x169e: 0x21e2, 0x169f: 0x810d, 0x16a0: 0x812d, 0x16a1: 0x0961, 0x16a2: 0x814d, 0x16a3: 0x814d,
	0x16a4: 0x816d, 0x16a5: 0x818d, 0x16a6: 0x81ad, 0x16a7: 0x81cd, 0x16a8: 0x81ed, 0x16a9: 0x820d,
	0x16aa: 0x822d, 0x16ab: 0x824d, 0x16ac: 0x826d, 0x16ad: 0x828d, 0x16ae: 0x82ad, 0x16af: 0x82cd,
	0x16b0: 0x82ed, 0x16b1: 0x830d, 0x16b2: 0x832d, 0x16b3: 0x834d, 0x16b4: 0x836d, 0x16b5: 0x838d,
	0x16b6: 0x83ad, 0x16b7: 0x83cd, 0x16b8: 0x83ed, 0x16b9: 0x840d, 0x16ba: 0x842d, 0x16bb: 0x844d,
	0x16bc: 0x81ed, 0x16bd: 0x846d, 0x16be: 0x848d, 0x16bf: 0x824d,
	// Block 0x5b, offset 0x16c0
	0x16c0: 0x84ad, 0x16c1: 0x84cd, 0x16c2: 0x84ed, 0x16c3: 0x850d, 0x16c4: 0x852d, 0x16c5: 0x854d,
	0x16c6: 0x856d, 0x16c7: 0x858d, 0x16c8: 0x850d, 0x16c9: 0x85ad, 0x16ca: 0x850d, 0x16cb: 0x85cd,
	0x16cc: 0x85cd, 0x16cd: 0x85ed, 0x16ce: 0x85ed, 0x16cf: 0x860d, 0x16d0: 0x854d, 0x16d1: 0x862d,
	0x16d2: 0x864d, 0x16d3: 0x862d, 0x16d4: 0x866d, 0x16d5: 0x864d, 0x16d6: 0x868d, 0x16d7: 0x868d,
	0x16d8: 0x86ad, 0x16d9: 0x86ad, 0x16da: 0x86cd, 0x16db: 0x86cd, 0x16dc: 0x864d, 0x16dd: 0x814d,
	0x16de: 0x86ed, 0x16df: 0x870d, 0x16e0: 0x0040, 0x16e1: 0x872d, 0x16e2: 0x874d, 0x16e3: 0x876d,
	0x16e4: 0x878d, 0x16e5: 0x876d, 0x16e6: 0x87ad, 0x16e7: 0x87cd, 0x16e8: 0x87ed, 0x16e9: 0x87ed,
	0x16ea: 0x880d, 0x16eb: 0x880d, 0x16ec: 0x882d, 0x16ed: 0x882d, 0x16ee: 0x880d, 0x16ef: 0x880d,
	0x16f0: 0x884d, 0x16f1: 0x886d, 0x16f2: 0x888d, 0x16f3: 0x88ad, 0x16f4: 0x88cd, 0x16f5: 0x88ed,
	0x16f6: 0x88ed, 0x16f7: 0x88ed, 0x16f8: 0x890d, 0x16f9: 0x890d, 0x16fa: 0x890d, 0x16fb: 0x890d,
	0x16fc: 0x87ed, 0x16fd: 0x87ed, 0x16fe: 0x87ed, 0x16ff: 0x0040,
	// Block 0x5c, offset 0x1700
	0x1700: 0x0040, 0x1701: 0x0040, 0x1702: 0x874d, 0x1703: 0x872d, 0x1704: 0x892d, 0x1705: 0x872d,
	0x1706: 0x874d, 0x1707: 0x872d, 0x1708: 0x0040, 0x1709: 0x0040, 0x170a: 0x894d, 0x170b: 0x874d,
	0x170c: 0x896d, 0x170d: 0x892d, 0x170e: 0x896d, 0x170f: 0x874d, 0x1710: 0x0040, 0x1711: 0x0040,
	0x1712: 0x898d, 0x1713: 0x89ad, 0x1714: 0x88ad, 0x1715: 0x896d, 0x1716: 0x892d, 0x1717: 0x896d,
	0x1718: 0x0040, 0x1719: 0x0040, 0x171a: 0x89cd, 0x171b: 0x89ed, 0x171c: 0x89cd, 0x171d: 0x0040,
	0x171e: 0x0040, 0x171f: 0x0040, 0x1720: 0x21e9, 0x1721: 0x21f1, 0x1722: 0x21f9, 0x1723: 0x8a0e,
	0x1724: 0x2201, 0x1725: 0x2209, 0x1726: 0x8a2d, 0x1727: 0x0040, 0x1728: 0x8a4d, 0x1729: 0x8a6d,
	0x172a: 0x8a8d, 0x172b: 0x8a6d, 0x172c: 0x8aad, 0x172d: 0x8acd, 0x172e: 0x8aed, 0x172f: 0x0040,
	0x1730: 0x0040, 0x1731: 0x0040, 0x1732: 0x0040, 0x1733: 0x0040, 0x1734: 0x0040, 0x1735: 0x0040,
	0x1736: 0x0040, 0x1737: 0x0040, 0x1738: 0x0040, 0x1739: 0x0340, 0x173a: 0x0340, 0x173b: 0x0340,
	0x173c: 0x0040, 0x173d: 0x0040, 0x173e: 0x0040, 0x173f: 0x0040,
	// Block 0x5d, offset 0x1740
	0x1740: 0x0008, 0x1741: 0x0008, 0x1742: 0x0008, 0x1743: 0x0008, 0x1744: 0x0008, 0x1745: 0x0008,
	0x1746: 0x0008, 0x1747: 0x0008, 0x1748: 0x0008, 0x1749: 0x0008, 0x174a: 0x0008, 0x174b: 0x0008,
	0x174c: 0x0008, 0x174d: 0x0008, 0x174e: 0x0008, 0x174f: 0x0008, 0x1750: 0x0008, 0x1751: 0x0008,
	0x1752: 0x0008, 0x1753: 0x0008, 0x1754: 0x0008, 0x1755: 0x0008, 0x1756: 0x0008, 0x1757: 0x0008,
	0x1758: 0x0008, 0x1759: 0x0008, 0x175a: 0x0008, 0x175b: 0x0008, 0x175c: 0x0008, 0x175d: 0x0008,
	0x175e: 0x0008, 0x175f: 0x0008, 0x1760: 0x0008, 0x1761: 0x0008, 0x1762: 0x0008, 0x1763: 0x0008,
	0x1764: 0x0040, 0x1765: 0x0040, 0x1766: 0x0040, 0x1767: 0x0040, 0x1768: 0x0040, 0x1769: 0x0040,
	0x176a: 0x0040, 0x176b: 0x0040, 0x176c: 0x0040, 0x176d: 0x0040, 0x176e: 0x0040, 0x176f: 0x0018,
	0x1770: 0x8b3d, 0x1771: 0x8b55, 0x1772: 0x8b6d, 0x1773: 0x8b55, 0x1774: 0x8b85, 0x1775: 0x8b55,
	0x1776: 0x8b6d, 0x1777: 0x8b55, 0x1778: 0x8b3d, 0x1779: 0x8b9d, 0x177a: 0x8bb5, 0x177b: 0x0040,
	0x177c: 0x8bcd, 0x177d: 0x8b9d, 0x177e: 0x8bb5, 0x177f: 0x8b9d,
	// Block 0x5e, offset 0x1780
	0x1780: 0xe13d, 0x1781: 0xe14d, 0x1782: 0xe15d, 0x1783: 0xe14d, 0x1784: 0xe17d, 0x1785: 0xe14d,
	0x1786: 0xe15d, 0x1787: 0xe14d, 0x1788: 0xe13d, 0x1789: 0xe1cd, 0x178a: 0xe1dd, 0x178b: 0x0040,
	0x178c: 0xe1fd, 0x178d: 0xe1cd, 0x178e: 0xe1dd, 0x178f: 0xe1cd, 0x1790: 0xe13d, 0x1791: 0xe14d,
	0x1792: 0xe15d, 0x1793: 0x0040, 0x1794: 0xe17d, 0x1795: 0xe14d, 0x1796: 0x0040, 0x1797: 0x0008,
	0x1798: 0x0008, 0x1799: 0x0008, 0x179a: 0x0008, 0x179b: 0x0008, 0x179c: 0x0008, 0x179d: 0x0008,
	0x179e: 0x0008, 0x179f: 0x0008, 0x17a0: 0x0008, 0x17a1: 0x0008, 0x17a2: 0x0040, 0x17a3: 0x0008,
	0x17a4: 0x0008, 0x17a5: 0x0008, 0x17a6: 0x0008, 0x17a7: 0x0008, 0x17a8: 0x0008, 0x17a9: 0x0008,
	0x17aa: 0x0008, 0x17ab: 0x0008, 0x17ac: 0x0008, 0x17ad: 0x0008, 0x17ae: 0x0008, 0x17af: 0x0008,
	0x17b0: 0x0008, 0x17b1: 0x0008, 0x17b2: 0x0040, 0x17b3: 0x0008, 0x17b4: 0x0008, 0x17b5: 0x0008,
	0x17b6: 0x0008, 0x17b7: 0x0008, 0x17b8: 0x0008, 0x17b9: 0x0008, 0x17ba: 0x0040, 0x17bb: 0x0008,
	0x17bc: 0x0008, 0x17bd: 0x0040, 0x17be: 0x0040, 0x17bf: 0x0040,
	// Block 0x5f, offset 0x17c0
	0x17c0: 0x0008, 0x17c1: 0x2211, 0x17c2: 0x2219, 0x17c3: 0x02e1, 0x17c4: 0x2221, 0x17c5: 0x2229,
	0x17c6: 0x0040, 0x17c7: 0x2231, 0x17c8: 0x2239, 0x17c9: 0x2241, 0x17ca: 0x2249, 0x17cb: 0x2251,
	0x17cc: 0x2259, 0x17cd: 0x2261, 0x17ce: 0x2269, 0x17cf: 0x2271, 0x17d0: 0x2279, 0x17d1: 0x2281,
	0x17d2: 0x2289, 0x17d3: 0x2291, 0x17d4: 0x2299, 0x17d5: 0x0741, 0x17d6: 0x22a1, 0x17d7: 0x22a9,
	0x17d8: 0x22b1, 0x17d9: 0x22b9, 0x17da: 0x22c1, 0x17db: 0x13d9, 0x17dc: 0x8be5, 0x17dd: 0x22c9,
	0x17de: 0x22d1, 0x17df: 0x8c05, 0x17e0: 0x22d9, 0x17e1: 0x8c25, 0x17e2: 0x22e1, 0x17e3: 0x22e9,
	0x17e4: 0x22f1, 0x17e5: 0x0751, 0x17e6: 0x22f9, 0x17e7: 0x8c45, 0x17e8: 0x0949, 0x17e9: 0x2301,
	0x17ea: 0x2309, 0x17eb: 0x2311, 0x17ec: 0x2319, 0x17ed: 0x2321, 0x17ee: 0x2329, 0x17ef: 0x2331,
	0x17f0: 0x2339, 0x17f1: 0x0040, 0x17f2: 0x2341, 0x17f3: 0x2349, 0x17f4: 0x2351, 0x17f5: 0x2359,
	0x17f6: 0x2361, 0x17f7: 0x2369, 0x17f8: 0x2371, 0x17f9: 0x8c65, 0x17fa: 0x8c85, 0x17fb: 0x0040,
	0x17fc: 0x0040, 0x17fd: 0x0040, 0x17fe: 0x0040, 0x17ff: 0x0040,
	// Block 0x60, offset 0x1800
	0x1800: 0x0a08, 0x1801: 0x0a08, 0x1802: 0x0a08, 0x1803: 0x0a08, 0x1804: 0x0a08, 0x1805: 0x0c08,
	0x1806: 0x0808, 0x1807: 0x0c08, 0x1808: 0x0818, 0x1809: 0x0c08, 0x180a: 0x0c08, 0x180b: 0x0808,
	0x180c: 0x0808, 0x180d: 0x0908, 0x180e: 0x0c08, 0x180f: 0x0c08, 0x1810: 0x0c08, 0x1811: 0x0c08,
	0x1812: 0x0c08, 0x1813: 0x0a08, 0x1814: 0x0a08, 0x1815: 0x0a08, 0x1816: 0x0a08, 0x1817: 0x0908,
	0x1818: 0x0a08, 0x1819: 0x0a08, 0x181a: 0x0a08, 0x181b: 0x0a08, 0x181c: 0x0a08, 0x181d: 0x0c08,
	0x181e: 0x0a08, 0x181f: 0x0a08, 0x1820: 0x0a08, 0x1821: 0x0c08, 0x1822: 0x0808, 0x1823: 0x0808,
	0x1824: 0x0c08, 0x1825: 0x3308, 0x1826: 0x3308, 0x1827: 0x0040, 0x1828: 0x0040, 0x1829: 0x0040,
	0x182a: 0x0040, 0x182b: 0x0a18, 0x182c: 0x0a18, 0x182d: 0x0a18, 0x182e: 0x0a18, 0x182f: 0x0c18,
	0x1830: 0x0818, 0x1831: 0x0818, 0x1832: 0x0818, 0x1833: 0x0818, 0x1834: 0x0818, 0x1835: 0x0818,
	0x1836: 0x0818, 0x1837: 0x0040, 0x1838: 0x0040, 0x1839: 0x0040, 0x183a: 0x0040, 0x183b: 0x0040,
	0x183c: 0x0040, 0x183d: 0x0040, 0x183e: 0x0040, 0x183f: 0x0040,
	// Block 0x61, offset 0x1840
	0x1840: 0x0a08, 0x1841: 0x0c08, 0x1842: 0x0a08, 0x1843: 0x0c08, 0x1844: 0x0c08, 0x1845: 0x0c08,
	0x1846: 0x0a08, 0x1847: 0x0a08, 0x1848: 0x0a08, 0x1849: 0x0c08, 0x184a: 0x0a08, 0x184b: 0x0a08,
	0x184c: 0x0c08, 0x184d: 0x0a08, 0x184e: 0x0c08, 0x184f: 0x0c08, 0x1850: 0x0a08, 0x1851: 0x0c08,
	0x1852: 0x0040, 0x1853: 0x0040, 0x1854: 0x0040, 0x1855: 0x0040, 0x1856: 0x0040, 0x1857: 0x0040,
	0x1858: 0x0040, 0x1859: 0x0818, 0x185a: 0x0818, 0x185b: 0x0818, 0x185c: 0x0818, 0x185d: 0x0040,
	0x185e: 0x0040, 0x185f: 0x0040, 0x1860: 0x0040, 0x1861: 0x0040, 0x1862: 0x0040, 0x1863: 0x0040,
	0x1864: 0x0040, 0x1865: 0x0040, 0x1866: 0x0040, 0x1867: 0x0040, 0x1868: 0x0040, 0x1869: 0x0c18,
	0x186a: 0x0c18, 0x186b: 0x0c18, 0x186c: 0x0c18, 0x186d: 0x0a18, 0x186e: 0x0a18, 0x186f: 0x0818,
	0x1870: 0x0040, 0x1871: 0x0040, 0x1872: 0x0040, 0x1873: 0x0040, 0x1874: 0x0040, 0x1875: 0x0040,
	0x1876: 0x0040, 0x1877: 0x0040, 0x1878: 0x0040, 0x1879: 0x0040, 0x187a: 0x0040, 0x187b: 0x0040,
	0x187c: 0x0040, 0x187d: 0x0040, 0x187e: 0x0040, 0x187f: 0x0040,
	// Block 0x62, offset 0x1880
	0x1880: 0x3308, 0x1881: 0x3308, 0x1882: 0x3008, 0x1883: 0x3008, 0x1884: 0x0040, 0x1885: 0x0008,
	0x1886: 0x0008, 0x1887: 0x0008, 0x1888: 0x0008, 0x1889: 0x0008, 0x188a: 0x0008, 0x188b: 0x0008,
	0x188c: 0x0008, 0x188d: 0x0040, 0x188e: 0x0040, 0x188f: 0x0008, 0x1890: 0x0008, 0x1891: 0x0040,
	0x1892: 0x0040, 0x1893: 0x0008, 0x1894: 0x0008, 0x1895: 0x0008, 0x1896: 0x0008, 0x1897: 0x0008,
	0x1898: 0x0008, 0x1899: 0x0008, 0x189a: 0x0008, 0x189b: 0x0008, 0x189c: 0x0008, 0x189d: 0x0008,
	0x189e: 0x0008, 0x189f: 0x0008, 0x18a0: 0x0008, 0x18a1: 0x0008, 0x18a2: 0x0008, 0x18a3: 0x0008,
	0x18a4: 0x0008, 0x18a5: 0x0008, 0x18a6: 0x0008, 0x18a7: 0x0008, 0x18a8: 0x0008, 0x18a9: 0x0040,
	0x18aa: 0x0008, 0x18ab: 0x0008, 0x18ac: 0x0008, 0x18ad: 0x0008, 0x18ae: 0x0008, 0x18af: 0x0008,
	0x18b0: 0x0008, 0x18b1: 0x0040, 0x18b2: 0x0008, 0x18b3: 0x0008, 0x18b4: 0x0040, 0x18b5: 0x0008,
	0x18b6: 0x0008, 0x18b7: 0x0008, 0x18b8: 0x0008, 0x18b9: 0x0008, 0x18ba: 0x0040, 0x18bb: 0x3308,
	0x18bc: 0x3308, 0x18bd: 0x0008, 0x18be: 0x3008, 0x18bf: 0x3008,
	// Block 0x63, offset 0x18c0
	0x18c0: 0x3308, 0x18c1: 0x3008, 0x18c2: 0x3008, 0x18c3: 0x3008, 0x18c4: 0x3008, 0x18c5: 0x0040,
	0x18c6: 0x0040, 0x18c7: 0x3008, 0x18c8: 0x3008, 0x18c9: 0x0040, 0x18ca: 0x0040, 0x18cb: 0x3008,
	0x18cc: 0x3008, 0x18cd: 0x3808, 0x18ce: 0x0040, 0x18cf: 0x0040, 0x18d0: 0x0008, 0x18d1: 0x0040,
	0x18d2: 0x0040, 0x18d3: 0x0040, 0x18d4: 0x0040, 0x18d5: 0x0040, 0x18d6: 0x0040, 0x18d7: 0x3008,
	0x18d8: 0x0040, 0x18d9: 0x0040, 0x18da: 0x0040, 0x18db: 0x0040, 0x18dc: 0x0040, 0x18dd: 0x0008,
	0x18de: 0x0008, 0x18df: 0x0008, 0x18e0: 0x0008, 0x18e1: 0x0008, 0x18e2: 0x3008, 0x18e3: 0x3008,
	0x18e4: 0x0040, 0x18e5: 0x0040, 0x18e6: 0x3308, 0x18e7: 0x3308, 0x18e8: 0x3308, 0x18e9: 0x3308,
	0x18ea: 0x3308, 0x18eb: 0x3308, 0x18ec: 0x3308, 0x18ed: 0x0040, 0x18ee: 0x0040, 0x18ef: 0x0040,
	0x18f0: 0x3308, 0x18f1: 0x3308, 0x18f2: 0x3308, 0x18f3: 0x3308, 0x18f4: 0x3308, 0x18f5: 0x0040,
	0x18f6: 0x0040, 0x18f7: 0x0040, 0x18f8: 0x0040, 0x18f9: 0x0040, 0x18fa: 0x0040, 0x18fb: 0x0040,
	0x18fc: 0x0040, 0x18fd: 0x0040, 0x18fe: 0x0040, 0x18ff: 0x0040,
	// Block 0x64, offset 0x1900
	0x1900: 0x0008, 0x1901: 0x0008, 0x1902: 0x0008, 0x1903: 0x0008, 0x1904: 0x0008, 0x1905: 0x0008,
	0x1906: 0x0008, 0x1907: 0x0040, 0x1908: 0x0040, 0x1909: 0x0008, 0x190a: 0x0040, 0x190b: 0x0040,
	0x190c: 0x0008, 0x190d: 0x0008, 0x190e: 0x0008, 0x190f: 0x0008, 0x1910: 0x0008, 0x1911: 0x0008,
	0x1912: 0x0008, 0x1913: 0x0008, 0x1914: 0x0040, 0x1915: 0x0008, 0x1916: 0x0008, 0x1917: 0x0040,
	0x1918: 0x0008, 0x1919: 0x0008, 0x191a: 0x0008, 0x191b: 0x0008, 0x191c: 0x0008, 0x191d: 0x0008,
	0x191e: 0x0008, 0x191f: 0x0008, 0x1920: 0x0008, 0x1921: 0x0008, 0x1922: 0x0008, 0x1923: 0x0008,
	0x1924: 0x0008, 0x1925: 0x0008, 0x1926: 0x0008, 0x1927: 0x0008, 0x1928: 0x0008, 0x1929: 0x0008,
	0x192a: 0x0008, 0x192b: 0x0008, 0x192c: 0x0008, 0x192d: 0x0008, 0x192e: 0x0008, 0x192f: 0x0008,
	0x1930: 0x3008, 0x1931: 0x3008, 0x1932: 0x3008, 0x1933: 0x3008, 0x1934: 0x3008, 0x1935: 0x3008,
	0x1936: 0x0040, 0x1937: 0x3008, 0x1938: 0x3008, 0x1939: 0x0040, 0x193a: 0x0040, 0x193b: 0x3308,
	0x193c: 0x3308, 0x193d: 0x3808, 0x193e: 0x3b08, 0x193f: 0x0008,
	// Block 0x65, offset 0x1940
	0x1940: 0x0019, 0x1941: 0x02e9, 0x1942: 0x03d9, 0x1943: 0x02f1, 0x1944: 0x02f9, 0x1945: 0x03f1,
	0x1946: 0x0309, 0x1947: 0x00a9, 0x1948: 0x0311, 0x1949: 0x00b1, 0x194a: 0x0319, 0x194b: 0x0101,
	0x194c: 0x0321, 0x194d: 0x0329, 0x194e: 0x0051, 0x194f: 0x0339, 0x1950: 0x0751, 0x1951: 0x00b9,
	0x1952: 0x0089, 0x1953: 0x0341, 0x1954: 0x0349, 0x1955: 0x0391, 0x1956: 0x00c1, 0x1957: 0x0109,
	0x1958: 0x00c9, 0x1959: 0x04b1, 0x195a: 0x0019, 0x195b: 0x02e9, 0x195c: 0x03d9, 0x195d: 0x02f1,
	0x195e: 0x02f9, 0x195f: 0x03f1, 0x1960: 0x0309, 0x1961: 0x00a9, 0x1962: 0x0311, 0x1963: 0x00b1,
	0x1964: 0x0319, 0x1965: 0x0101, 0x1966: 0x0321, 0x1967: 0x0329, 0x1968: 0x0051, 0x1969: 0x0339,
	0x196a: 0x0751, 0x196b: 0x00b9, 0x196c: 0x0089, 0x196d: 0x0341, 0x196e: 0x0349, 0x196f: 0x0391,
	0x1970: 0x00c1, 0x1971: 0x0109, 0x1972: 0x00c9, 0x1973: 0x04b1, 0x1974: 0x0019, 0x1975: 0x02e9,
	0x1976: 0x03d9, 0x1977: 0x02f1, 0x1978: 0x02f9, 0x1979: 0x03f1, 0x197a: 0x0309, 0x197b: 0x00a9,
	0x197c: 0x0311, 0x197d: 0x00b1, 0x197e: 0x0319, 0x197f: 0x0101,
	// Block 0x66, offset 0x1980
	0x1980: 0x0321, 0x1981: 0x0329, 0x1982: 0x0051, 0x1983: 0x0339, 0x1984: 0x0751, 0x1985: 0x00b9,
	0x1986: 0x0089, 0x1987: 0x0341, 0x1988: 0x0349, 0x1989: 0x0391, 0x198a: 0x00c1, 0x198b: 0x0109,
	0x198c: 0x00c9, 0x198d: 0x04b1, 0x198e: 0x0019, 0x198f: 0x02e9, 0x1990: 0x03d9, 0x1991: 0x02f1,
	0x1992: 0x02f9, 0x1993: 0x03f1, 0x1994: 0x0309, 0x1995: 0x0040, 0x1996: 0x0311, 0x1997: 0x00b1,
	0x1998: 0x0319, 0x1999: 0x0101, 0x199a: 0x0321, 0x199b: 0x0329, 0x199c: 0x0051, 0x199d: 0x0339,
	0x199e: 0x0751, 0x199f: 0x00b9, 0x19a0: 0x0089, 0x19a1: 0x0341, 0x19a2: 0x0349, 0x19a3: 0x0391,
	0x19a4: 0x00c1, 0x19a5: 0x0109, 0x19a6: 0x00c9, 0x19a7: 0x04b1, 0x19a8: 0x0019, 0x19a9: 0x02e9,
	0x19aa: 0x03d9, 0x19ab: 0x02f1, 0x19ac: 0x02f9, 0x19ad: 0x03f1, 0x19ae: 0x0309, 0x19af: 0x00a9,
	0x19b0: 0x0311, 0x19b1: 0x00b1, 0x19b2: 0x0319, 0x19b3: 0x0101, 0x19b4: 0x0321, 0x19b5: 0x0329,
	0x19b6: 0x0051, 0x19b7: 0x0339, 0x19b8: 0x0751, 0x19b9: 0x00b9, 0x19ba: 0x0089, 0x19bb: 0x0341,
	0x19bc: 0x0349, 0x19bd: 0x0391, 0x19be: 0x00c1, 0x19bf: 0x0109,
	// Block 0x67, offset 0x19c0
	0x19c0: 0x00c9, 0x19c1: 0x04b1, 0x19c2: 0x0019, 0x19c3: 0x02e9, 0x19c4: 0x03d9, 0x19c5: 0x02f1,
	0x19c6: 0x02f9, 0x19c7: 0x03f1, 0x19c8: 0x0309, 0x19c9: 0x00a9, 0x19ca: 0x0311, 0x19cb: 0x00b1,
	0x19cc: 0x0319, 0x19cd: 0x0101, 0x19ce: 0x0321, 0x19cf: 0x0329, 0x19d0: 0x0051, 0x19d1: 0x0339,
	0x19d2: 0x0751, 0x19d3: 0x00b9, 0x19d4: 0x0089, 0x19d5: 0x0341, 0x19d6: 0x0349, 0x19d7: 0x0391,
	0x19d8: 0x00c1, 0x19d9: 0x0109, 0x19da: 0x00c9, 0x19db: 0x04b1, 0x19dc: 0x0019, 0x19dd: 0x0040,
	0x19de: 0x03d9, 0x19df: 0x02f1, 0x19e0: 0x0040, 0x19e1: 0x0040, 0x19e2: 0x0309, 0x19e3: 0x0040,
	0x19e4: 0x0040, 0x19e5: 0x00b1, 0x19e6: 0x0319, 0x19e7: 0x0040, 0x19e8: 0x0040, 0x19e9: 0x0329,
	0x19ea: 0x0051, 0x19eb: 0x0339, 0x19ec: 0x0751, 0x19ed: 0x0040, 0x19ee: 0x0089, 0x19ef: 0x0341,
	0x19f0: 0x0349, 0x19f1: 0x0391, 0x19f2: 0x00c1, 0x19f3: 0x0109, 0x19f4: 0x00c9, 0x19f5: 0x04b1,
	0x19f6: 0x0019, 0x19f7: 0x02e9, 0x19f8: 0x03d9, 0x19f9: 0x02f1, 0x19fa: 0x0040, 0x19fb: 0x03f1,
	0x19fc: 0x0040, 0x19fd: 0x00a9, 0x19fe: 0x0311, 0x19ff: 0x00b1,
	// Block 0x68, offset 0x1a00
	0x1a00: 0x0319, 0x1a01: 0x0101, 0x1a02: 0x0321, 0x1a03: 0x0329, 0x1a04: 0x0040, 0x1a05: 0x0339,
	0x1a06: 0x0751, 0x1a07: 0x00b9, 0x1a08: 0x0089, 0x1a09: 0x0341, 0x1a0a: 0x0349, 0x1a0b: 0x0391,
	0x1a0c: 0x00c1, 0x1a0d: 0x0109, 0x1a0e: 0x00c9, 0x1a0f: 0x04b1, 0x1a10: 0x0019, 0x1a11: 0x02e9,
	0x1a12: 0x03d9, 0x1a13: 0x02f1, 0x1a14: 0x02f9, 0x1a15: 0x03f1, 0x1a16: 0x0309, 0x1a17: 0x00a9,
	0x1a18: 0x0311, 0x1a19: 0x00b1, 0x1a1a: 0x0319, 0x1a1b: 0x0101, 0x1a1c: 0x0321, 0x1a1d: 0x0329,
	0x1a1e: 0x0051, 0x1a1f: 0x0339, 0x1a20: 0x0751, 0x1a21: 0x00b9, 0x1a22: 0x0089, 0x1a23: 0x0341,
	0x1a24: 0x0349, 0x1a25: 0x0391, 0x1a26: 0x00c1, 0x1a27: 0x0109, 0x1a28: 0x00c9, 0x1a29: 0x04b1,
	0x1a2a: 0x0019, 0x1a2b: 0x02e9, 0x1a2c: 0x03d9, 0x1a2d: 0x02f1, 0x1a2e: 0x02f9, 0x1a2f: 0x03f1,
	0x1a30: 0x0309, 0x1a31: 0x00a9, 0x1a32: 0x0311, 0x1a33: 0x00b1, 0x1a34: 0x0319, 0x1a35: 0x0101,
	0x1a36: 0x0321, 0x1a37: 0x0329, 0x1a38: 0x0051, 0x1a39: 0x0339, 0x1a3a: 0x0751, 0x1a3b: 0x00b9,
	0x1a3c: 0x0089, 0x1a3d: 0x0341, 0x1a3e: 0x0349, 0x1a3f: 0x0391,
	// Block 0x69, offset 0x1a40
	0x1a40: 0x00c1, 0x1a41: 0x0109, 0x1a42: 0x00c9, 0x1a43: 0x04b1, 0x1a44: 0x0019, 0x1a45: 0x02e9,
	0x1a46: 0x0040, 0x1a47: 0x02f1, 0x1a48: 0x02f9, 0x1a49: 0x03f1, 0x1a4a: 0x0309, 0x1a4b: 0x0040,
	0x1a4c: 0x0040, 0x1a4d: 0x00b1, 0x1a4e: 0x0319, 0x1a4f: 0x0101, 0x1a50: 0x0321, 0x1a51: 0x0329,
	0x1a52: 0x0051, 0x1a53: 0x0339, 0x1a54: 0x0751, 0x1a55: 0x0040, 0x1a56: 0x0089, 0x1a57: 0x0341,
	0x1a58: 0x0349, 0x1a59: 0x0391, 0x1a5a: 0x00c1, 0x1a5b: 0x0109, 0x1a5c: 0x00c9, 0x1a5d: 0x0040,
	0x1a5e: 0x0019, 0x1a5f: 0x02e9, 0x1a60: 0x03d9, 0x1a61: 0x02f1, 0x1a62: 0x02f9, 0x1a63: 0x03f1,
	0x1a64: 0x0309, 0x1a65: 0x00a9, 0x1a66: 0x0311, 0x1a67: 0x00b1, 0x1a68: 0x0319, 0x1a69: 0x0101,
	0x1a6a: 0x0321, 0x1a6b: 0x0329, 0x1a6c: 0x0051, 0x1a6d: 0x0339, 0x1a6e: 0x0751, 0x1a6f: 0x00b9,
	0x1a70: 0x0089, 0x1a71: 0x0341, 0x1a72: 0x0349, 0x1a73: 0x0391, 0x1a74: 0x00c1, 0x1a75: 0x0109,
	0x1a76: 0x00c9, 0x1a77: 0x04b1, 0x1a78: 0x0019, 0x1a79: 0x02e9, 0x1a7a: 0x0040, 0x1a7b: 0x02f1,
	0x1a7c: 0x02f9, 0x1a7d: 0x03f1, 0x1a7e: 0x0309, 0x1a7f: 0x0040,
	// Block 0x6a, offset 0x1a80
	0x1a80: 0x0311, 0x1a81: 0x00b1, 0x1a82: 0x0319, 0x1a83: 0x0101, 0x1a84: 0x0321, 0x1a85: 0x0040,
	0x1a86: 0x0051, 0x1a87: 0x0040, 0x1a88: 0x0040, 0x1a89: 0x0040, 0x1a8a: 0x0089, 0x1a8b: 0x0341,
	0x1a8c: 0x0349, 0x1a8d: 0x0391, 0x1a8e: 0x00c1, 0x1a8f: 0x0109, 0x1a90: 0x00c9, 0x1a91: 0x0040,
	0x1a92: 0x0019, 0x1a93: 0x02e9, 0x1a94: 0x03d9, 0x1a95: 0x02f1, 0x1a96: 0x02f9, 0x1a97: 0x03f1,
	0x1a98: 0x0309, 0x1a99: 0x00a9, 0x1a9a: 0x0311, 0x1a9b: 0x00b1, 0x1a9c: 0x0319, 0x1a9d: 0x0101,
	0x1a9e: 0x0321, 0x1a9f: 0x0329, 0x1aa0: 0x0051, 0x1aa1: 0x0339, 0x1aa2: 0x0751, 0x1aa3: 0x00b9,
	0x1aa4: 0x0089, 0x1aa5: 0x0341, 0x1aa6: 0x0349, 0x1aa7: 0x0391, 0x1aa8: 0x00c1, 0x1aa9: 0x0109,
	0x1aaa: 0x00c9, 0x1aab: 0x04b1, 0x1aac: 0x0019, 0x1aad: 0x02e9, 0x1aae: 0x03d9, 0x1aaf: 0x02f1,
	0x1ab0: 0x02f9, 0x1ab1: 0x03f1, 0x1ab2: 0x0309, 0x1ab3: 0x00a9, 0x1ab4: 0x0311, 0x1ab5: 0x00b1,
	0x1ab6: 0x0319, 0x1ab7: 0x0101, 0x1ab8: 0x0321, 0x1ab9: 0x0329, 0x1aba: 0x0051, 0x1abb: 0x0339,
	0x1abc: 0x0751, 0x1abd: 0x00b9, 0x1abe: 0x0089, 0x1abf: 0x0341,
	// Block 0x6b, offset 0x1ac0
	0x1ac0: 0x0349, 0x1ac1: 0x0391, 0x1ac2: 0x00c1, 0x1ac3: 0x0109, 0x1ac4: 0x00c9, 0x1ac5: 0x04b1,
	0x1ac6: 0x0019, 0x1ac7: 0x02e9, 0x1ac8: 0x03d9, 0x1ac9: 0x02f1, 0x1aca: 0x02f9, 0x1acb: 0x03f1,
	0x1acc: 0x0309, 0x1acd: 0x00a9, 0x1ace: 0x0311, 0x1acf: 0x00b1, 0x1ad0: 0x0319, 0x1ad1: 0x0101,
	0x1ad2: 0x0321, 0x1ad3: 0x0329, 0x1ad4: 0x0051, 0x1ad5: 0x0339, 0x1ad6: 0x0751, 0x1ad7: 0x00b9,
	0x1ad8: 0x0089, 0x1ad9: 0x0341, 0x1ada: 0x0349, 0x1adb: 0x0391, 0x1adc: 0x00c1, 0x1add: 0x0109,
	0x1ade: 0x00c9, 0x1adf: 0x04b1, 0x1ae0: 0x0019, 0x1ae1: 0x02e9, 0x1ae2: 0x03d9, 0x1ae3: 0x02f1,
	0x1ae4: 0x02f9, 0x1ae5: 0x03f1, 0x1ae6: 0x0309, 0x1ae7: 0x00a9, 0x1ae8: 0x0311, 0x1ae9: 0x00b1,
	0x1aea: 0x0319, 0x1aeb: 0x0101, 0x1aec: 0x0321, 0x1aed: 0x0329, 0x1aee: 0x0051, 0x1aef: 0x0339,
	0x1af0: 0x0751, 0x1af1: 0x00b9, 0x1af2: 0x0089, 0x1af3: 0x0341, 0x1af4: 0x0349, 0x1af5: 0x0391,
	0x1af6: 0x00c1, 0x1af7: 0x0109, 0x1af8: 0x00c9, 0x1af9: 0x04b1, 0x1afa: 0x0019, 0x1afb: 0x02e9,
	0x1afc: 0x03d9, 0x1afd: 0x02f1, 0x1afe: 0x02f9, 0x1aff: 0x03f1,
	// Block 0x6c, offset 0x1b00
	0x1b00: 0x0309, 0x1b01: 0x00a9, 0x1b02: 0x0311, 0x1b03: 0x00b1, 0x1b04: 0x0319, 0x1b05: 0x0101,
	0x1b06: 0x0321, 0x1b07: 0x0329, 0x1b08: 0x0051, 0x1b09: 0x0339, 0x1b0a: 0x0751, 0x1b0b: 0x00b9,
	0x1b0c: 0x0089, 0x1b0d: 0x0341, 0x1b0e: 0x0349, 0x1b0f: 0x0391, 0x1b10: 0x00c1, 0x1b11: 0x0109,
	0x1b12: 0x00c9, 0x1b13: 0x04b1, 0x1b14: 0x0019, 0x1b15: 0x02e9, 0x1b16: 0x03d9, 0x1b17: 0x02f1,
	0x1b18: 0x02f9, 0x1b19: 0x03f1, 0x1b1a: 0x0309, 0x1b1b: 0x00a9, 0x1b1c: 0x0311, 0x1b1d: 0x00b1,
	0x1b1e: 0x0319, 0x1b1f: 0x0101, 0x1b20: 0x0321, 0x1b21: 0x0329, 0x1b22: 0x0051, 0x1b23: 0x0339,
	0x1b24: 0x0751, 0x1b25: 0x00b9, 0x1b26: 0x0089, 0x1b27: 0x0341, 0x1b28: 0x0349, 0x1b29: 0x0391,
	0x1b2a: 0x00c1, 0x1b2b: 0x0109, 0x1b2c: 0x00c9, 0x1b2d: 0x04b1, 0x1b2e: 0x0019, 0x1b2f: 0x02e9,
	0x1b30: 0x03d9, 0x1b31: 0x02f1, 0x1b32: 0x02f9, 0x1b33: 0x03f1, 0x1b34: 0x0309, 0x1b35: 0x00a9,
	0x1b36: 0x0311, 0x1b37: 0x00b1, 0x1b38: 0x0319, 0x1b39: 0x0101, 0x1b3a: 0x0321, 0x1b3b: 0x0329,
	0x1b3c: 0x0051, 0x1b3d: 0x0339, 0x1b3e: 0x0751, 0x1b3f: 0x00b9,
	// Block 0x6d, offset 0x1b40
	0x1b40: 0x0089, 0x1b41: 0x0341, 0x1b42: 0x0349, 0x1b43: 0x0391, 0x1b44: 0x00c1, 0x1b45: 0x0109,
	0x1b46: 0x00c9, 0x1b47: 0x04b1, 0x1b48: 0x0019, 0x1b49: 0x02e9, 0x1b4a: 0x03d9, 0x1b4b: 0x02f1,
	0x1b4c: 0x02f9, 0x1b4d: 0x03f1, 0x1b4e: 0x0309, 0x1b4f: 0x00a9, 0x1b50: 0x0311, 0x1b51: 0x00b1,
	0x1b52: 0x0319, 0x1b53: 0x0101, 0x1b54: 0x0321, 0x1b55: 0x0329, 0x1b56: 0x0051, 0x1b57: 0x0339,
	0x1b58: 0x0751, 0x1b59: 0x00b9, 0x1b5a: 0x0089, 0x1b5b: 0x0341, 0x1b5c: 0x0349, 0x1b5d: 0x0391,
	0x1b5e: 0x00c1, 0x1b5f: 0x0109, 0x1b60: 0x00c9, 0x1b61: 0x04b1, 0x1b62: 0x0019, 0x1b63: 0x02e9,
	0x1b64: 0x03d9, 0x1b65: 0x02f1, 0x1b66: 0x02f9, 0x1b67: 0x03f1, 0x1b68: 0x0309, 0x1b69: 0x00a9,
	0x1b6a: 0x0311, 0x1b6b: 0x00b1, 0x1b6c: 0x0319, 0x1b6d: 0x0101, 0x1b6e: 0x0321, 0x1b6f: 0x0329,
	0x1b70: 0x0051, 0x1b71: 0x0339, 0x1b72: 0x0751, 0x1b73: 0x00b9, 0x1b74: 0x0089, 0x1b75: 0x0341,
	0x1b76: 0x0349, 0x1b77: 0x0391, 0x1b78: 0x00c1, 0x1b79: 0x0109, 0x1b7a: 0x00c9, 0x1b7b: 0x04b1,
	0x1b7c: 0x0019, 0x1b7d: 0x02e9, 0x1b7e: 0x03d9, 0x1b7f: 0x02f1,
	// Block 0x6e, offset 0x1b80
	0x1b80: 0x02f9, 0x1b81: 0x03f1, 0x1b82: 0x0309, 0x1b83: 0x00a9, 0x1b84: 0x0311, 0x1b85: 0x00b1,
	0x1b86: 0x0319, 0x1b87: 0x0101, 0x1b88: 0x0321, 0x1b89: 0x0329, 0x1b8a: 0x0051, 0x1b8b: 0x0339,
	0x1b8c: 0x0751, 0x1b8d: 0x00b9, 0x1b8e: 0x0089, 0x1b8f: 0x0341, 0x1b90: 0x0349, 0x1b91: 0x0391,
	0x1b92: 0x00c1, 0x1b93: 0x0109, 0x1b94: 0x00c9, 0x1b95: 0x04b1, 0x1b96: 0x0019, 0x1b97: 0x02e9,
	0x1b98: 0x03d9, 0x1b99: 0x02f1, 0x1b9a: 0x02f9, 0x1b9b: 0x03f1, 0x1b9c: 0x0309, 0x1b9d: 0x00a9,
	0x1b9e: 0x0311, 0x1b9f: 0x00b1, 0x1ba0: 0x0319, 0x1ba1: 0x0101, 0x1ba2: 0x0321, 0x1ba3: 0x0329,
	0x1ba4: 0x0051, 0x1ba5: 0x0339, 0x1ba6: 0x0751, 0x1ba7: 0x00b9, 0x1ba8: 0x0089, 0x1ba9: 0x0341,
	0x1baa: 0x0349, 0x1bab: 0x0391, 0x1bac: 0x00c1, 0x1bad: 0x0109, 0x1bae: 0x00c9, 0x1baf: 0x04b1,
	0x1bb0: 0x0019, 0x1bb1: 0x02e9, 0x1bb2: 0x03d9, 0x1bb3: 0x02f1, 0x1bb4: 0x02f9, 0x1bb5: 0x03f1,
	0x1bb6: 0x0309, 0x1bb7: 0x00a9, 0x1bb8: 0x0311, 0x1bb9: 0x00b1, 0x1bba: 0x0319, 0x1bbb: 0x0101,
	0x1bbc: 0x0321, 0x1bbd: 0x0329, 0x1bbe: 0x0051, 0x1bbf: 0x0339,
	// Block 0x6f, offset 0x1bc0
	0x1bc0: 0x0751, 0x1bc1: 0x00b9, 0x1bc2: 0x0089, 0x1bc3: 0x0341, 0x1bc4: 0x0349, 0x1bc5: 0x0391,
	0x1bc6: 0x00c1, 0x1bc7: 0x0109, 0x1bc8: 0x00c9, 0x1bc9: 0x04b1, 0x1bca: 0x0019, 0x1bcb: 0x02e9,
	0x1bcc: 0x03d9, 0x1bcd: 0x02f1, 0x1bce: 0x02f9, 0x1bcf: 0x03f1, 0x1bd0: 0x0309, 0x1bd1: 0x00a9,
	0x1bd2: 0x0311, 0x1bd3: 0x00b1, 0x1bd4: 0x0319, 0x1bd5: 0x0101, 0x1bd6: 0x0321, 0x1bd7: 0x0329,
	0x1bd8: 0x0051, 0x1bd9: 0x0339, 0x1bda: 0x0751, 0x1bdb: 0x00b9, 0x1bdc: 0x0089, 0x1bdd: 0x0341,
	0x1bde: 0x0349, 0x1bdf: 0x0391, 0x1be0: 0x00c1, 0x1be1: 0x0109, 0x1be2: 0x00c9, 0x1be3: 0x04b1,
	0x1be4: 0x23e1, 0x1be5: 0x23e9, 0x1be6: 0x0040, 0x1be7: 0x0040, 0x1be8: 0x23f1, 0x1be9: 0x0399,
	0x1bea: 0x03a1, 0x1beb: 0x03a9, 0x1bec: 0x23f9, 0x1bed: 0x2401, 0x1bee: 0x2409, 0x1bef: 0x04d1,
	0x1bf0: 0x05f9, 0x1bf1: 0x2411, 0x1bf2: 0x2419, 0x1bf3: 0x2421, 0x1bf4: 0x2429, 0x1bf5: 0x2431,
	0x1bf6: 0x2439, 0x1bf7: 0x0799, 0x1bf8: 0x03c1, 0x1bf9: 0x04d1, 0x1bfa: 0x2441, 0x1bfb: 0x2449,
	0x1bfc: 0x2451, 0x1bfd: 0x03b1, 0x1bfe: 0x03b9, 0x1bff: 0x2459,
	// Block 0x70, offset 0x1c00
	0x1c00: 0x0769, 0x1c01: 0x2461, 0x1c02: 0x23f1, 0x1c03: 0x0399, 0x1c04: 0x03a1, 0x1c05: 0x03a9,
	0x1c06: 0x23f9, 0x1c07: 0x2401, 0x1c08: 0x2409, 0x1c09: 0x04d1, 0x1c0a: 0x05f9, 0x1c0b: 0x2411,
	0x1c0c: 0x2419, 0x1c0d: 0x2421, 0x1c0e: 0x2429, 0x1c0f: 0x2431, 0x1c10: 0x2439, 0x1c11: 0x0799,
	0x1c12: 0x03c1, 0x1c13: 0x2441, 0x1c14: 0x2441, 0x1c15: 0x2449, 0x1c16: 0x2451, 0x1c17: 0x03b1,
	0x1c18: 0x03b9, 0x1c19: 0x2459, 0x1c1a: 0x0769, 0x1c1b: 0x2469, 0x1c1c: 0x23f9, 0x1c1d: 0x04d1,
	0x1c1e: 0x2411, 0x1c1f: 0x03b1, 0x1c20: 0x03c1, 0x1c21: 0x0799, 0x1c22: 0x23f1, 0x1c23: 0x0399,
	0x1c24: 0x03a1, 0x1c25: 0x03a9, 0x1c26: 0x23f9, 0x1c27: 0x2401, 0x1c28: 0x2409, 0x1c29: 0x04d1,
	0x1c2a: 0x05f9, 0x1c2b: 0x2411, 0x1c2c: 0x2419, 0x1c2d: 0x2421, 0x1c2e: 0x2429, 0x1c2f: 0x2431,
	0x1c30: 0x2439, 0x1c31: 0x0799, 0x1c32: 0x03c1, 0x1c33: 0x04d1, 0x1c34: 0x2441, 0x1c35: 0x2449,
	0x1c36: 0x2451, 0x1c37: 0x03b1, 0x1c38: 0x03b9, 0x1c39: 0x2459, 0x1c3a: 0x0769, 0x1c3b: 0x2461,
	0x1c3c: 0x23f1, 0x1c3d: 0x0399, 0x1c3e: 0x03a1, 0x1c3f: 0x03a9,
	// Block 0x71, offset 0x1c40
	0x1c40: 0x23f9, 0x1c41: 0x2401, 0x1c42: 0x2409, 0x1c43: 0x04d1, 0x1c44: 0x05f9, 0x1c45: 0x2411,
	0x1c46: 0x2419, 0x1c47: 0x2421, 0x1c48: 0x2429, 0x1c49: 0x2431, 0x1c4a: 0x2439, 0x1c4b: 0x0799,
	0x1c4c: 0x03c1, 0x1c4d: 0x2441, 0x1c4e: 0x2441, 0x1c4f: 0x2449, 0x1c50: 0x2451, 0x1c51: 0x03b1,
	0x1c52: 0x03b9, 0x1c53: 0x2459, 0x1c54: 0x0769, 0x1c55: 0x2469, 0x1c56: 0x23f9, 0x1c57: 0x04d1,
	0x1c58: 0x2411, 0x1c59: 0x03b1, 0x1c5a: 0x03c1, 0x1c5b: 0x0799, 0x1c5c: 0x23f1, 0x1c5d: 0x0399,
	0x1c5e: 0x03a1, 0x1c5f: 0x03a9, 0x1c60: 0x23f9, 0x1c61: 0x2401, 0x1c62: 0x2409, 0x1c63: 0x04d1,
	0x1c64: 0x05f9, 0x1c65: 0x2411, 0x1c66: 0x2419, 0x1c67: 0x2421, 0x1c68: 0x2429, 0x1c69: 0x2431,
	0x1c6a: 0x2439, 0x1c6b: 0x0799, 0x1c6c: 0x03c1, 0x1c6d: 0x04d1, 0x1c6e: 0x2441, 0x1c6f: 0x2449,
	0x1c70: 0x2451, 0x1c71: 0x03b1, 0x1c72: 0x03b9, 0x1c73: 0x2459, 0x1c74: 0x0769, 0x1c75: 0x2461,
	0x1c76: 0x23f1, 0x1c77: 0x0399, 0x1c78: 0x03a1, 0x1c79: 0x03a9, 0x1c7a: 0x23f9, 0x1c7b: 0x2401,
	0x1c7c: 0x2409, 0x1c7d: 0x04d1, 0x1c7e: 0x05f9, 0x1c7f: 0x2411,
	// Block 0x72, offset 0x1c80
	0x1c80: 0x2419, 0x1c81: 0x2421, 0x1c82: 0x2429, 0x1c83: 0x2431, 0x1c84: 0x2439, 0x1c85: 0x0799,
	0x1c86: 0x03c1, 0x1c87: 0x2441, 0x1c88: 0x2441, 0x1c89: 0x2449, 0x1c8a: 0x2451, 0x1c8b: 0x03b1,
	0x1c8c: 0x03b9, 0x1c8d: 0x2459, 0x1c8e: 0x0769, 0x1c8f: 0x2469, 0x1c90: 0x23f9, 0x1c91: 0x04d1,
	0x1c92: 0x2411, 0x1c93: 0x03b1, 0x1c94: 0x03c1, 0x1c95: 0x0799, 0x1c96: 0x23f1, 0x1c97: 0x0399,
	0x1c98: 0x03a1, 0x1c99: 0x03a9, 0x1c9a: 0x23f9, 0x1c9b: 0x2401, 0x1c9c: 0x2409, 0x1c9d: 0x04d1,
	0x1c9e: 0x05f9, 0x1c9f: 0x2411, 0x1ca0: 0x2419, 0x1ca1: 0x2421, 0x1ca2: 0x2429, 0x1ca3: 0x2431,
	0x1ca4: 0x2439, 0x1ca5: 0x0799, 0x1ca6: 0x03c1, 0x1ca7: 0x04d1, 0x1ca8: 0x2441, 0x1ca9: 0x2449,
	0x1caa: 0x2451, 0x1cab: 0x03b1, 0x1cac: 0x03b9, 0x1cad: 0x2459, 0x1cae: 0x0769, 0x1caf: 0x2461,
	0x1cb0: 0x23f1, 0x1cb1: 0x0399, 0x1cb2: 0x03a1, 0x1cb3: 0x03a9, 0x1cb4: 0x23f9, 0x1cb5: 0x2401,
	0x1cb6: 0x2409, 0x1cb7: 0x04d1, 0x1cb8: 0x05f9, 0x1cb9: 0x2411, 0x1cba: 0x2419, 0x1cbb: 0x2421,
	0x1cbc: 0x2429, 0x1cbd: 0x2431, 0x1cbe: 0x2439, 0x1cbf: 0x0799,
	// Block 0x73, offset 0x1cc0
	0x1cc0: 0x03c1, 0x1cc1: 0x2441, 0x1cc2: 0x2441, 0x1cc3: 0x2449, 0x1cc4: 0x2451, 0x1cc5: 0x03b1,
	0x1cc6: 0x03b9, 0x1cc7: 0x2459, 0x1cc8: 0x0769, 0x1cc9: 0x2469, 0x1cca: 0x23f9, 0x1ccb: 0x04d1,
	0x1ccc: 0x2411, 0x1ccd: 0x03b1, 0x1cce: 0x03c1, 0x1ccf: 0x0799, 0x1cd0: 0x23f1, 0x1cd1: 0x0399,
	0x1cd2: 0x03a1, 0x1cd3: 0x03a9, 0x1cd4: 0x23f9, 0x1cd5: 0x2401, 0x1cd6: 0x2409, 0x1cd7: 0x04d1,
	0x1cd8: 0x05f9, 0x1cd9: 0x2411, 0x1cda: 0x2419, 0x1cdb: 0x2421, 0x1cdc: 0x2429, 0x1cdd: 0x2431,
	0x1cde: 0x2439, 0x1cdf: 0x0799, 0x1ce0: 0x03c1, 0x1ce1: 0x04d1, 0x1ce2: 0x2441, 0x1ce3: 0x2449,
	0x1ce4: 0x2451, 0x1ce5: 0x03b1, 0x1ce6: 0x03b9, 0x1ce7: 0x2459, 0x1ce8: 0x0769, 0x1ce9: 0x2461,
	0x1cea: 0x23f1, 0x1ceb: 0x0399, 0x1cec: 0x03a1, 0x1ced: 0x03a9, 0x1cee: 0x23f9, 0x1cef: 0x2401,
	0x1cf0: 0x2409, 0x1cf1: 0x04d1, 0x1cf2: 0x05f9, 0x1cf3: 0x2411, 0x1cf4: 0x2419, 0x1cf5: 0x2421,
	0x1cf6: 0x2429, 0x1cf7: 0x2431, 0x1cf8: 0x2439, 0x1cf9: 0x0799, 0x1cfa: 0x03c1, 0x1cfb: 0x2441,
	0x1cfc: 0x2441, 0x1cfd: 0x2449, 0x1cfe: 0x2451, 0x1cff: 0x03b1,
	// Block 0x74, offset 0x1d00
	0x1d00: 0x03b9, 0x1d01: 0x2459, 0x1d02: 0x0769, 0x1d03: 0x2469, 0x1d04: 0x23f9, 0x1d05: 0x04d1,
	0x1d06: 0x2411, 0x1d07: 0x03b1, 0x1d08: 0x03c1, 0x1d09: 0x0799, 0x1d0a: 0x2471, 0x1d0b: 0x2471,
	0x1d0c: 0x0040, 0x1d0d: 0x0040, 0x1d0e: 0x06e1, 0x1d0f: 0x0049, 0x1d10: 0x0029, 0x1d11: 0x0031,
	0x1d12: 0x06e9, 0x1d13: 0x06f1, 0x1d14: 0x06f9, 0x1d15: 0x0701, 0x1d16: 0x0709, 0x1d17: 0x0711,
	0x1d18: 0x06e1, 0x1d19: 0x0049, 0x1d1a: 0x0029, 0x1d1b: 0x0031, 0x1d1c: 0x06e9, 0x1d1d: 0x06f1,
	0x1d1e: 0x06f9, 0x1d1f: 0x0701, 0x1d20: 0x0709, 0x1d21: 0x0711, 0x1d22: 0x06e1, 0x1d23: 0x0049,
	0x1d24: 0x0029, 0x1d25: 0x0031, 0x1d26: 0x06e9, 0x1d27: 0x06f1, 0x1d28: 0x06f9, 0x1d29: 0x0701,
	0x1d2a: 0x0709, 0x1d2b: 0x0711, 0x1d2c: 0x06e1, 0x1d2d: 0x0049, 0x1d2e: 0x0029, 0x1d2f: 0x0031,
	0x1d30: 0x06e9, 0x1d31: 0x06f1, 0x1d32: 0x06f9, 0x1d33: 0x0701, 0x1d34: 0x0709, 0x1d35: 0x0711,
	0x1d36: 0x06e1, 0x1d37: 0x0049, 0x1d38: 0x0029, 0x1d39: 0x0031, 0x1d3a: 0x06e9, 0x1d3b: 0x06f1,
	0x1d3c: 0x06f9, 0x1d3d: 0x0701, 0x1d3e: 0x0709, 0x1d3f: 0x0711,
	// Block 0x75, offset 0x1d40
	0x1d40: 0x3308, 0x1d41: 0x3308, 0x1d42: 0x3308, 0x1d43: 0x3308, 0x1d44: 0x3308, 0x1d45: 0x3308,
	0x1d46: 0x3308, 0x1d47: 0x0040, 0x1d48: 0x3308, 0x1d49: 0x3308, 0x1d4a: 0x3308, 0x1d4b: 0x3308,
	0x1d4c: 0x3308, 0x1d4d: 0x3308, 0x1d4e: 0x3308, 0x1d4f: 0x3308, 0x1d50: 0x3308, 0x1d51: 0x3308,
	0x1d52: 0x3308, 0x1d53: 0x3308, 0x1d54: 0x3308, 0x1d55: 0x3308, 0x1d56: 0x3308, 0x1d57: 0x3308,
	0x1d58: 0x3308, 0x1d59: 0x0040, 0x1d5a: 0x0040, 0x1d5b: 0x3308, 0x1d5c: 0x3308, 0x1d5d: 0x3308,
	0x1d5e: 0x3308, 0x1d5f: 0x3308, 0x1d60: 0x3308, 0x1d61: 0x3308, 0x1d62: 0x0040, 0x1d63: 0x3308,
	0x1d64: 0x3308, 0x1d65: 0x0040, 0x1d66: 0x3308, 0x1d67: 0x3308, 0x1d68: 0x3308, 0x1d69: 0x3308,
	0x1d6a: 0x3308, 0x1d6b: 0x0040, 0x1d6c: 0x0040, 0x1d6d: 0x0040, 0x1d6e: 0x0040, 0x1d6f: 0x0040,
	0x1d70: 0x2479, 0x1d71: 0x2481, 0x1d72: 0x02a9, 0x1d73: 0x2489, 0x1d74: 0x02b1, 0x1d75: 0x2491,
	0x1d76: 0x2499, 0x1d77: 0x24a1, 0x1d78: 0x24a9, 0x1d79: 0x24b1, 0x1d7a: 0x24b9, 0x1d7b: 0x24c1,
	0x1d7c: 0x02b9, 0x1d7d: 0x24c9, 0x1d7e: 0x24d1, 0x1d7f: 0x02c1,
	// Block 0x76, offset 0x1d80
	0x1d80: 0x02c9, 0x1d81: 0x24d9, 0x1d82: 0x24e1, 0x1d83: 0x24e9, 0x1d84: 0x24f1, 0x1d85: 0x24f9,
	0x1d86: 0x2501, 0x1d87: 0x2509, 0x1d88: 0x2511, 0x1d89: 0x2519, 0x1d8a: 0x2521, 0x1d8b: 0x2529,
	0x1d8c: 0x2531, 0x1d8d: 0x2539, 0x1d8e: 0x2541, 0x1d8f: 0x2549, 0x1d90: 0x2551, 0x1d91: 0x2479,
	0x1d92: 0x2481, 0x1d93: 0x02a9, 0x1d94: 0x2489, 0x1d95: 0x02b1, 0x1d96: 0x2491, 0x1d97: 0x2499,
	0x1d98: 0x24a1, 0x1d99: 0x24a9, 0x1d9a: 0x24b1, 0x1d9b: 0x24b9, 0x1d9c: 0x02b9, 0x1d9d: 0x24c9,
	0x1d9e: 0x02c1, 0x1d9f: 0x24d9, 0x1da0: 0x24e1, 0x1da1: 0x24e9, 0x1da2: 0x24f1, 0x1da3: 0x24f9,
	0x1da4: 0x2501, 0x1da5: 0x02d1, 0x1da6: 0x2509, 0x1da7: 0x2559, 0x1da8: 0x2531, 0x1da9: 0x2561,
	0x1daa: 0x2569, 0x1dab: 0x2571, 0x1dac: 0x2579, 0x1dad: 0x2581, 0x1dae: 0x0040, 0x1daf: 0x0040,
	0x1db0: 0x0040, 0x1db1: 0x0040, 0x1db2: 0x0040, 0x1db3: 0x0040, 0x1db4: 0x0040, 0x1db5: 0x0040,
	0x1db6: 0x0040, 0x1db7: 0x0040, 0x1db8: 0x0040, 0x1db9: 0x0040, 0x1dba: 0x0040, 0x1dbb: 0x0040,
	0x1dbc: 0x0040, 0x1dbd: 0x0040, 0x1dbe: 0x0040, 0x1dbf: 0x0040,
	// Block 0x77, offset 0x1dc0
	0x1dc0: 0xe115, 0x1dc1: 0xe115, 0x1dc2: 0xe135, 0x1dc3: 0xe135, 0x1dc4: 0xe115, 0x1dc5: 0xe115,
	0x1dc6: 0xe175, 0x1dc7: 0xe175, 0x1dc8: 0xe115, 0x1dc9: 0xe115, 0x1dca: 0xe135, 0x1dcb: 0xe135,
	0x1dcc: 0xe115, 0x1dcd: 0xe115, 0x1dce: 0xe1f5, 0x1dcf: 0xe1f5, 0x1dd0: 0xe115, 0x1dd1: 0xe115,
	0x1dd2: 0xe135, 0x1dd3: 0xe135, 0x1dd4: 0xe115, 0x1dd5: 0xe115, 0x1dd6: 0xe175, 0x1dd7: 0xe175,
	0x1dd8: 0xe115, 0x1dd9: 0xe115, 0x1dda: 0xe135, 0x1ddb: 0xe135, 0x1ddc: 0xe115, 0x1ddd: 0xe115,
	0x1dde: 0x8ca5, 0x1ddf: 0x8ca5, 0x1de0: 0x04b5, 0x1de1: 0x04b5, 0x1de2: 0x0a08, 0x1de3: 0x0a08,
	0x1de4: 0x0a08, 0x1de5: 0x0a08, 0x1de6: 0x0a08, 0x1de7: 0x0a08, 0x1de8: 0x0a08, 0x1de9: 0x0a08,
	0x1dea: 0x0a08, 0x1deb: 0x0a08, 0x1dec: 0x0a08, 0x1ded: 0x0a08, 0x1dee: 0x0a08, 0x1def: 0x0a08,
	0x1df0: 0x0a08, 0x1df1: 0x0a08, 0x1df2: 0x0a08, 0x1df3: 0x0a08, 0x1df4: 0x0a08, 0x1df5: 0x0a08,
	0x1df6: 0x0a08, 0x1df7: 0x0a08, 0x1df8: 0x0a08, 0x1df9: 0x0a08, 0x1dfa: 0x0a08, 0x1dfb: 0x0a08,
	0x1dfc: 0x0a08, 0x1dfd: 0x0a08, 0x1dfe: 0x0a08, 0x1dff: 0x0a08,
	// Block 0x78, offset 0x1e00
	0x1e00: 0x20b1, 0x1e01: 0x20b9, 0x1e02: 0x20d9, 0x1e03: 0x20f1, 0x1e04: 0x0040, 0x1e05: 0x2189,
	0x1e06: 0x2109, 0x1e07: 0x20e1, 0x1e08: 0x2131, 0x1e09: 0x2191, 0x1e0a: 0x2161, 0x1e0b: 0x2169,
	0x1e0c: 0x2171, 0x1e0d: 0x2179, 0x1e0e: 0x2111, 0x1e0f: 0x2141, 0x1e10: 0x2151, 0x1e11: 0x2121,
	0x1e12: 0x2159, 0x1e13: 0x2101, 0x1e14: 0x2119, 0x1e15: 0x20c9, 0x1e16: 0x20d1, 0x1e17: 0x20e9,
	0x1e18: 0x20f9, 0x1e19: 0x2129, 0x1e1a: 0x2139, 0x1e1b: 0x2149, 0x1e1c: 0x2589, 0x1e1d: 0x1689,
	0x1e1e: 0x2591, 0x1e1f: 0x2599, 0x1e20: 0x0040, 0x1e21: 0x20b9, 0x1e22: 0x20d9, 0x1e23: 0x0040,
	0x1e24: 0x2181, 0x1e25: 0x0040, 0x1e26: 0x0040, 0x1e27: 0x20e1, 0x1e28: 0x0040, 0x1e29: 0x2191,
	0x1e2a: 0x2161, 0x1e2b: 0x2169, 0x1e2c: 0x2171, 0x1e2d: 0x2179, 0x1e2e: 0x2111, 0x1e2f: 0x2141,
	0x1e30: 0x2151, 0x1e31: 0x2121, 0x1e32: 0x2159, 0x1e33: 0x0040, 0x1e34: 0x2119, 0x1e35: 0x20c9,
	0x1e36: 0x20d1, 0x1e37: 0x20e9, 0x1e38: 0x0040, 0x1e39: 0x2129, 0x1e3a: 0x0040, 0x1e3b: 0x2149,
	0x1e3c: 0x0040, 0x1e3d: 0x0040, 0x1e3e: 0x0040, 0x1e3f: 0x0040,
	// Block 0x79, offset 0x1e40
	0x1e40: 0x0040, 0x1e41: 0x0040, 0x1e42: 0x20d9, 0x1e43: 0x0040, 0x1e44: 0x0040, 0x1e45: 0x0040,
	0x1e46: 0x0040, 0x1e47: 0x20e1, 0x1e48: 0x0040, 0x1e49: 0x2191, 0x1e4a: 0x0040, 0x1e4b: 0x2169,
	0x1e4c: 0x0040, 0x1e4d: 0x2179, 0x1e4e: 0x2111, 0x1e4f: 0x2141, 0x1e50: 0x0040, 0x1e51: 0x2121,
	0x1e52: 0x2159, 0x1e53: 0x0040, 0x1e54: 0x2119, 0x1e55: 0x0040, 0x1e56: 0x0040, 0x1e57: 0x20e9,
	0x1e58: 0x0040, 0x1e59: 0x2129, 0x1e5a: 0x0040, 0x1e5b: 0x2149, 0x1e5c: 0x0040, 0x1e5d: 0x1689,
	0x1e5e: 0x0040, 0x1e5f: 0x2599, 0x1e60: 0x0040, 0x1e61: 0x20b9, 0x1e62: 0x20d9, 0x1e63: 0x0040,
	0x1e64: 0x2181, 0x1e65: 0x0040, 0x1e66: 0x0040, 0x1e67: 0x20e1, 0x1e68: 0x2131, 0x1e69: 0x2191,
	0x1e6a: 0x2161, 0x1e6b: 0x0040, 0x1e6c: 0x2171, 0x1e6d: 0x2179, 0x1e6e: 0x2111, 0x1e6f: 0x2141,
	0x1e70: 0x2151, 0x1e71: 0x2121, 0x1e72: 0x2159, 0x1e73: 0x0040, 0x1e74: 0x2119, 0x1e75: 0x20c9,
	0x1e76: 0x20d1, 0x1e77: 0x20e9, 0x1e78: 0x0040, 0x1e79: 0x2129, 0x1e7a: 0x2139, 0x1e7b: 0x2149,
	0x1e7c: 0x2589, 0x1e7d: 0x0040, 0x1e7e: 0x2591, 0x1e7f: 0x0040,
	// Block 0x7a, offset 0x1e80
	0x1e80: 0x20b1, 0x1e81: 0x20b9, 0x1e82: 0x20d9, 0x1e83: 0x20f1, 0x1e84: 0x2181, 0x1e85: 0x2189,
	0x1e86: 0x2109, 0x1e87: 0x20e1, 0x1e88: 0x2131, 0x1e89: 0x2191, 0x1e8a: 0x0040, 0x1e8b: 0x2169,
	0x1e8c: 0x2171, 0x1e8d: 0x2179, 0x1e8e: 0x2111, 0x1e8f: 0x2141, 0x1e90: 0x2151, 0x1e91: 0x2121,
	0x1e92: 0x2159, 0x1e93: 0x2101, 0x1e94: 0x2119, 0x1e95: 0x20c9, 0x1e96: 0x20d1, 0x1e97: 0x20e9,
	0x1e98: 0x20f9, 0x1e99: 0x2129, 0x1e9a: 0x2139, 0x1e9b: 0x2149, 0x1e9c: 0x0040, 0x1e9d: 0x0040,
	0x1e9e: 0x0040, 0x1e9f: 0x0040, 0x1ea0: 0x0040, 0x1ea1: 0x20b9, 0x1ea2: 0x20d9, 0x1ea3: 0x20f1,
	0x1ea4: 0x0040, 0x1ea5: 0x2189, 0x1ea6: 0x2109, 0x1ea7: 0x20e1, 0x1ea8: 0x2131, 0x1ea9: 0x2191,
	0x1eaa: 0x0040, 0x1eab: 0x2169, 0x1eac: 0x2171, 0x1ead: 0x2179, 0x1eae: 0x2111, 0x1eaf: 0x2141,
	0x1eb0: 0x2151, 0x1eb1: 0x2121, 0x1eb2: 0x2159, 0x1eb3: 0x2101, 0x1eb4: 0x2119, 0x1eb5: 0x20c9,
	0x1eb6: 0x20d1, 0x1eb7: 0x20e9, 0x1eb8: 0x20f9, 0x1eb9: 0x2129, 0x1eba: 0x2139, 0x1ebb: 0x2149,
	0x1ebc: 0x0040, 0x1ebd: 0x0040, 0x1ebe: 0x0040, 0x1ebf: 0x0040,
	// Block 0x7b, offset 0x1ec0
	0x1ec0: 0x0040, 0x1ec1: 0x25a2, 0x1ec2: 0x25aa, 0x1ec3: 0x25b2, 0x1ec4: 0x25ba, 0x1ec5: 0x25c2,
	0x1ec6: 0x25ca, 0x1ec7: 0x25d2, 0x1ec8: 0x25da, 0x1ec9: 0x25e2, 0x1eca: 0x25ea, 0x1ecb: 0x0018,
	0x1ecc: 0x0018, 0x1ecd: 0x0018, 0x1ece: 0x0018, 0x1ecf: 0x0018, 0x1ed0: 0x25f2, 0x1ed1: 0x25fa,
	0x1ed2: 0x2602, 0x1ed3: 0x260a, 0x1ed4: 0x2612, 0x1ed5: 0x261a, 0x1ed6: 0x2622, 0x1ed7: 0x262a,
	0x1ed8: 0x2632, 0x1ed9: 0x263a, 0x1eda: 0x2642, 0x1edb: 0x264a, 0x1edc: 0x2652, 0x1edd: 0x265a,
	0x1ede: 0x2662, 0x1edf: 0x266a, 0x1ee0: 0x2672, 0x1ee1: 0x267a, 0x1ee2: 0x2682, 0x1ee3: 0x268a,
	0x1ee4: 0x2692, 0x1ee5: 0x269a, 0x1ee6: 0x26a2, 0x1ee7: 0x26aa, 0x1ee8: 0x26b2, 0x1ee9: 0x26ba,
	0x1eea: 0x26c1, 0x1eeb: 0x03d9, 0x1eec: 0x00b9, 0x1eed: 0x1239, 0x1eee: 0x26c9, 0x1eef: 0x0018,
	0x1ef0: 0x0019, 0x1ef1: 0x02e9, 0x1ef2: 0x03d9, 0x1ef3: 0x02f1, 0x1ef4: 0x02f9, 0x1ef5: 0x03f1,
	0x1ef6: 0x0309, 0x1ef7: 0x00a9, 0x1ef8: 0x0311, 0x1ef9: 0x00b1, 0x1efa: 0x0319, 0x1efb: 0x0101,
	0x1efc: 0x0321, 0x1efd: 0x0329, 0x1efe: 0x0051, 0x1eff: 0x0339,
	// Block 0x7c, offset 0x1f00
	0x1f00: 0x0751, 0x1f01: 0x00b9, 0x1f02: 0x0089, 0x1f03: 0x0341, 0x1f04: 0x0349, 0x1f05: 0x0391,
	0x1f06: 0x00c1, 0x1f07: 0x0109, 0x1f08: 0x00c9, 0x1f09: 0x04b1, 0x1f0a: 0x26d1, 0x1f0b: 0x11f9,
	0x1f0c: 0x26d9, 0x1f0d: 0x04d9, 0x1f0e: 0x26e1, 0x1f0f: 0x26e9, 0x1f10: 0x0018, 0x1f11: 0x0018,
	0x1f12: 0x0018, 0x1f13: 0x0018, 0x1f14: 0x0018, 0x1f15: 0x0018, 0x1f16: 0x0018, 0x1f17: 0x0018,
	0x1f18: 0x0018, 0x1f19: 0x0018, 0x1f1a: 0x0018, 0x1f1b: 0x0018, 0x1f1c: 0x0018, 0x1f1d: 0x0018,
	0x1f1e: 0x0018, 0x1f1f: 0x0018, 0x1f20: 0x0018, 0x1f21: 0x0018, 0x1f22: 0x0018, 0x1f23: 0x0018,
	0x1f24: 0x0018, 0x1f25: 0x0018, 0x1f26: 0x0018, 0x1f27: 0x0018, 0x1f28: 0x0018, 0x1f29: 0x0018,
	0x1f2a: 0x26f1, 0x1f2b: 0x26f9, 0x1f2c: 0x2701, 0x1f2d: 0x0018, 0x1f2e: 0x0018, 0x1f2f: 0x0018,
	0x1f30: 0x0018, 0x1f31: 0x0018, 0x1f32: 0x0018, 0x1f33: 0x0018, 0x1f34: 0x0018, 0x1f35: 0x0018,
	0x1f36: 0x0018, 0x1f37: 0x0018, 0x1f38: 0x0018, 0x1f39: 0x0018, 0x1f3a: 0x0018, 0x1f3b: 0x0018,
	0x1f3c: 0x0018, 0x1f3d: 0x0018, 0x1f3e: 0x0018, 0x1f3f: 0x0018,
	// Block 0x7d, offset 0x1f40
	0x1f40: 0x2711, 0x1f41: 0x2719, 0x1f42: 0x2721, 0x1f43: 0x0040, 0x1f44: 0x0040, 0x1f45: 0x0040,
	0x1f46: 0x0040, 0x1f47: 0x0040, 0x1f48: 0x0040, 0x1f49: 0x0040, 0x1f4a: 0x0040, 0x1f4b: 0x0040,
	0x1f4c: 0x0040, 0x1f4d: 0x0040, 0x1f4e: 0x0040, 0x1f4f: 0x0040, 0x1f50: 0x2729, 0x1f51: 0x2731,
	0x1f52: 0x2739, 0x1f53: 0x2741, 0x1f54: 0x2749, 0x1f55: 0x2751, 0x1f56: 0x2759, 0x1f57: 0x2761,
	0x1f58: 0x2769, 0x1f59: 0x2771, 0x1f5a: 0x2779, 0x1f5b: 0x2781, 0x1f5c: 0x2789, 0x1f5d: 0x2791,
	0x1f5e: 0x2799, 0x1f5f: 0x27a1, 0x1f60: 0x27a9, 0x1f61: 0x27b1, 0x1f62: 0x27b9, 0x1f63: 0x27c1,
	0x1f64: 0x27c9, 0x1f65: 0x27d1, 0x1f66: 0x27d9, 0x1f67: 0x27e1, 0x1f68: 0x27e9, 0x1f69: 0x27f1,
	0x1f6a: 0x27f9, 0x1f6b: 0x2801, 0x1f6c: 0x2809, 0x1f6d: 0x2811, 0x1f6e: 0x2819, 0x1f6f: 0x2821,
	0x1f70: 0x2829, 0x1f71: 0x2831, 0x1f72: 0x2839, 0x1f73: 0x2841, 0x1f74: 0x2849, 0x1f75: 0x2851,
	0x1f76: 0x2859, 0x1f77: 0x2861, 0x1f78: 0x2869, 0x1f79: 0x2871, 0x1f7a: 0x2879, 0x1f7b: 0x2881,
	0x1f7c: 0x0040, 0x1f7d: 0x0040, 0x1f7e: 0x0040, 0x1f7f: 0x0040,
	// Block 0x7e, offset 0x1f80
	0x1f80: 0x28e1, 0x1f81: 0x28e9, 0x1f82: 0x28f1, 0x1f83: 0x8cbd, 0x1f84: 0x28f9, 0x1f85: 0x2901,
	0x1f86: 0x2909, 0x1f87: 0x2911, 0x1f88: 0x2919, 0x1f89: 0x2921, 0x1f8a: 0x2929, 0x1f8b: 0x2931,
	0x1f8c: 0x2939, 0x1f8d: 0x8cdd, 0x1f8e: 0x2941, 0x1f8f: 0x2949, 0x1f90: 0x2951, 0x1f91: 0x2959,
	0x1f92: 0x8cfd, 0x1f93: 0x2961, 0x1f94: 0x2969, 0x1f95: 0x2799, 0x1f96: 0x8d1d, 0x1f97: 0x2971,
	0x1f98: 0x2979, 0x1f99: 0x2981, 0x1f9a: 0x2989, 0x1f9b: 0x2991, 0x1f9c: 0x8d3d, 0x1f9d: 0x2999,
	0x1f9e: 0x29a1, 0x1f9f: 0x29a9, 0x1fa0: 0x29b1, 0x1fa1: 0x29b9, 0x1fa2: 0x2871, 0x1fa3: 0x29c1,
	0x1fa4: 0x29c9, 0x1fa5: 0x29d1, 0x1fa6: 0x29d9, 0x1fa7: 0x29e1, 0x1fa8: 0x29e9, 0x1fa9: 0x29f1,
	0x1faa: 0x29f9, 0x1fab: 0x2a01, 0x1fac: 0x2a09, 0x1fad: 0x2a11, 0x1fae: 0x2a19, 0x1faf: 0x2a21,
	0x1fb0: 0x2a29, 0x1fb1: 0x2a31, 0x1fb2: 0x2a31, 0x1fb3: 0x2a31, 0x1fb4: 0x8d5d, 0x1fb5: 0x2a39,
	0x1fb6: 0x2a41, 0x1fb7: 0x2a49, 0x1fb8: 0x8d7d, 0x1fb9: 0x2a51, 0x1fba: 0x2a59, 0x1fbb: 0x2a61,
	0x1fbc: 0x2a69, 0x1fbd: 0x2a71, 0x1fbe: 0x2a79, 0x1fbf: 0x2a81,
	// Block 0x7f, offset 0x1fc0
	0x1fc0: 0x2a89, 0x1fc1: 0x2a91, 0x1fc2: 0x2a99, 0x1fc3: 0x2aa1, 0x1fc4: 0x2aa9, 0x1fc5: 0x2ab1,
	0x1fc6: 0x2ab1, 0x1fc7: 0x2ab9, 0x1fc8: 0x2ac1, 0x1fc9: 0x2ac9, 0x1fca: 0x2ad1, 0x1fcb: 0x2ad9,
	0x1fcc: 0x2ae1, 0x1fcd: 0x2ae9, 0x1fce: 0x2af1, 0x1fcf: 0x2af9, 0x1fd0: 0x2b01, 0x1fd1: 0x2b09,
	0x1fd2: 0x2b11, 0x1fd3: 0x2b19, 0x1fd4: 0x2b21, 0x1fd5: 0x2b29, 0x1fd6: 0x2b31, 0x1fd7: 0x2b39,
	0x1fd8: 0x2b41, 0x1fd9: 0x8d9d, 0x1fda: 0x2b49, 0x1fdb: 0x2b51, 0x1fdc: 0x2b59, 0x1fdd: 0x2751,
	0x1fde: 0x2b61, 0x1fdf: 0x2b69, 0x1fe0: 0x8dbd, 0x1fe1: 0x8ddd, 0x1fe2: 0x2b71, 0x1fe3: 0x2b79,
	0x1fe4: 0x2b81, 0x1fe5: 0x2b89, 0x1fe6: 0x2b91, 0x1fe7: 0x2b99, 0x1fe8: 0x2040, 0x1fe9: 0x2ba1,
	0x1fea: 0x2ba9, 0x1feb: 0x2ba9, 0x1fec: 0x8dfd, 0x1fed: 0x2bb1, 0x1fee: 0x2bb9, 0x1fef: 0x2bc1,
	0x1ff0: 0x2bc9, 0x1ff1: 0x8e1d, 0x1ff2: 0x2bd1, 0x1ff3: 0x2bd9, 0x1ff4: 0x2040, 0x1ff5: 0x2be1,
	0x1ff6: 0x2be9, 0x1ff7: 0x2bf1, 0x1ff8: 0x2bf9, 0x1ff9: 0x2c01, 0x1ffa: 0x2c09, 0x1ffb: 0x8e3d,
	0x1ffc: 0x2c11, 0x1ffd: 0x8e5d, 0x1ffe: 0x2c19, 0x1fff: 0x2c21,
	// Block 0x80, offset 0x2000
	0x2000: 0x2c29, 0x2001: 0x2c31, 0x2002: 0x2c39, 0x2003: 0x2c41, 0x2004: 0x2c49, 0x2005: 0x2c51,
	0x2006: 0x2c59, 0x2007: 0x2c61, 0x2008: 0x2c69, 0x2009: 0x8e7d, 0x200a: 0x2c71, 0x200b: 0x2c79,
	0x200c: 0x2c81, 0x200d: 0x2c89, 0x200e: 0x2c91, 0x200f: 0x8e9d, 0x2010: 0x2c99, 0x2011: 0x8ebd,
	0x2012: 0x8edd, 0x2013: 0x2ca1, 0x2014: 0x2ca9, 0x2015: 0x2ca9, 0x2016: 0x2cb1, 0x2017: 0x8efd,
	0x2018: 0x8f1d, 0x2019: 0x2cb9, 0x201a: 0x2cc1, 0x201b: 0x2cc9, 0x201c: 0x2cd1, 0x201d: 0x2cd9,
	0x201e: 0x2ce1, 0x201f: 0x2ce9, 0x2020: 0x2cf1, 0x2021: 0x2cf9, 0x2022: 0x2d01, 0x2023: 0x2d09,
	0x2024: 0x8f3d, 0x2025: 0x2d11, 0x2026: 0x2d19, 0x2027: 0x2d21, 0x2028: 0x2d29, 0x2029: 0x2d21,
	0x202a: 0x2d31, 0x202b: 0x2d39, 0x202c: 0x2d41, 0x202d: 0x2d49, 0x202e: 0x2d51, 0x202f: 0x2d59,
	0x2030: 0x2d61, 0x2031: 0x2d69, 0x2032: 0x2d71, 0x2033: 0x2d79, 0x2034: 0x2d81, 0x2035: 0x2d89,
	0x2036: 0x2d91, 0x2037: 0x2d99, 0x2038: 0x8f5d, 0x2039: 0x2da1, 0x203a: 0x2da9, 0x203b: 0x2db1,
	0x203c: 0x2db9, 0x203d: 0x2dc1, 0x203e: 0x8f7d, 0x203f: 0x2dc9,
	// Block 0x81, offset 0x2040
	0x2040: 0x2dd1, 0x2041: 0x2dd9, 0x2042: 0x2de1, 0x2043: 0x2de9, 0x2044: 0x2df1, 0x2045: 0x2df9,
	0x2046: 0x2e01, 0x2047: 0x2e09, 0x2048: 0x2e11, 0x2049: 0x2e19, 0x204a: 0x8f9d, 0x204b: 0x2e21,
	0x204c: 0x2e29, 0x204d: 0x2e31, 0x204e: 0x2e39, 0x204f: 0x2e41, 0x2050: 0x2e49, 0x2051: 0x2e51,
	0x2052: 0x2e59, 0x2053: 0x2e61, 0x2054: 0x2e69, 0x2055: 0x2e71, 0x2056: 0x2e79, 0x2057: 0x2e81,
	0x2058: 0x2e89, 0x2059: 0x2e91, 0x205a: 0x2e99, 0x205b: 0x2ea1, 0x205c: 0x2ea9, 0x205d: 0x8fbd,
	0x205e: 0x2eb1, 0x205f: 0x2eb9, 0x2060: 0x2ec1, 0x2061: 0x2ec9, 0x2062: 0x2ed1, 0x2063: 0x8fdd,
	0x2064: 0x2ed9, 0x2065: 0x2ee1, 0x2066: 0x2ee9, 0x2067: 0x2ef1, 0x2068: 0x2ef9, 0x2069: 0x2f01,
	0x206a: 0x2f09, 0x206b: 0x2f11, 0x206c: 0x7f0d, 0x206d: 0x2f19, 0x206e: 0x2f21, 0x206f: 0x2f29,
	0x2070: 0x8ffd, 0x2071: 0x2f31, 0x2072: 0x2f39, 0x2073: 0x2f41, 0x2074: 0x2f49, 0x2075: 0x2f51,
	0x2076: 0x2f59, 0x2077: 0x901d, 0x2078: 0x903d, 0x2079: 0x905d, 0x207a: 0x2f61, 0x207b: 0x907d,
	0x207c: 0x2f69, 0x207d: 0x2f71, 0x207e: 0x2f79, 0x207f: 0x2f81,
	// Block 0x82, offset 0x2080
	0x2080: 0x2f89, 0x2081: 0x2f91, 0x2082: 0x2f99, 0x2083: 0x2fa1, 0x2084: 0x2fa9, 0x2085: 0x2fb1,
	0x2086: 0x909d, 0x2087: 0x2fb9, 0x2088: 0x2fc1, 0x2089: 0x2fc9, 0x208a: 0x2fd1, 0x208b: 0x2fd9,
	0x208c: 0x2fe1, 0x208d: 0x90bd, 0x208e: 0x2fe9, 0x208f: 0x2ff1, 0x2090: 0x90dd, 0x2091: 0x90fd,
	0x2092: 0x2ff9, 0x2093: 0x3001, 0x2094: 0x3009, 0x2095: 0x3011, 0x2096: 0x3019, 0x2097: 0x3021,
	0x2098: 0x3029, 0x2099: 0x3031, 0x209a: 0x3039, 0x209b: 0x911d, 0x209c: 0x3041, 0x209d: 0x913d,
	0x209e: 0x3049, 0x209f: 0x2040, 0x20a0: 0x3051, 0x20a1: 0x3059, 0x20a2: 0x3061, 0x20a3: 0x915d,
	0x20a4: 0x3069, 0x20a5: 0x3071, 0x20a6: 0x917d, 0x20a7: 0x919d, 0x20a8: 0x3079, 0x20a9: 0x3081,
	0x20aa: 0x3089, 0x20ab: 0x3091, 0x20ac: 0x3099, 0x20ad: 0x3099, 0x20ae: 0x30a1, 0x20af: 0x30a9,
	0x20b0: 0x30b1, 0x20b1: 0x30b9, 0x20b2: 0x30c1, 0x20b3: 0x30c9, 0x20b4: 0x30d1, 0x20b5: 0x91bd,
	0x20b6: 0x30d9, 0x20b7: 0x91dd, 0x20b8: 0x30e1, 0x20b9: 0x91fd, 0x20ba: 0x30e9, 0x20bb: 0x921d,
	0x20bc: 0x923d, 0x20bd: 0x925d, 0x20be: 0x30f1, 0x20bf: 0x30f9,
	// Block 0x83, offset 0x20c0
	0x20c0: 0x3101, 0x20c1: 0x927d, 0x20c2: 0x929d, 0x20c3: 0x92bd, 0x20c4: 0x92dd, 0x20c5: 0x3109,
	0x20c6: 0x3111, 0x20c7: 0x3111, 0x20c8: 0x3119, 0x20c9: 0x3121, 0x20ca: 0x3129, 0x20cb: 0x3131,
	0x20cc: 0x3139, 0x20cd: 0x92fd, 0x20ce: 0x3141, 0x20cf: 0x3149, 0x20d0: 0x3151, 0x20d1: 0x3159,
	0x20d2: 0x931d, 0x20d3: 0x3161, 0x20d4: 0x933d, 0x20d5: 0x935d, 0x20d6: 0x3169, 0x20d7: 0x3171,
	0x20d8: 0x3179, 0x20d9: 0x3181, 0x20da: 0x3189, 0x20db: 0x3191, 0x20dc: 0x937d, 0x20dd: 0x939d,
	0x20de: 0x93bd, 0x20df: 0x2040, 0x20e0: 0x3199, 0x20e1: 0x93dd, 0x20e2: 0x31a1, 0x20e3: 0x31a9,
	0x20e4: 0x31b1, 0x20e5: 0x93fd, 0x20e6: 0x31b9, 0x20e7: 0x31c1, 0x20e8: 0x31c9, 0x20e9: 0x31d1,
	0x20ea: 0x31d9, 0x20eb: 0x941d, 0x20ec: 0x31e1, 0x20ed: 0x31e9, 0x20ee: 0x31f1, 0x20ef: 0x31f9,
	0x20f0: 0x3201, 0x20f1: 0x3209, 0x20f2: 0x943d, 0x20f3: 0x945d, 0x20f4: 0x3211, 0x20f5: 0x947d,
	0x20f6: 0x3219, 0x20f7: 0x949d, 0x20f8: 0x3221, 0x20f9: 0x3229, 0x20fa: 0x3231, 0x20fb: 0x94bd,
	0x20fc: 0x94dd, 0x20fd: 0x3239, 0x20fe: 0x94fd, 0x20ff: 0x3241,
	// Block 0x84, offset 0x2100
	0x2100: 0x951d, 0x2101: 0x3249, 0x2102: 0x3251, 0x2103: 0x3259, 0x2104: 0x3261, 0x2105: 0x3269,
	0x2106: 0x3271, 0x2107: 0x953d, 0x2108: 0x955d, 0x2109: 0x957d, 0x210a: 0x959d, 0x210b: 0x2ca1,
	0x210c: 0x3279, 0x210d: 0x3281, 0x210e: 0x3289, 0x210f: 0x3291, 0x2110: 0x3299, 0x2111: 0x32a1,
	0x2112: 0x32a9, 0x2113: 0x32b1, 0x2114: 0x32b9, 0x2115: 0x32c1, 0x2116: 0x32c9, 0x2117: 0x95bd,
	0x2118: 0x32d1, 0x2119: 0x32d9, 0x211a: 0x32e1, 0x211b: 0x32e9, 0x211c: 0x32f1, 0x211d: 0x32f9,
	0x211e: 0x3301, 0x211f: 0x3309, 0x2120: 0x3311, 0x2121: 0x3319, 0x2122: 0x3321, 0x2123: 0x3329,
	0x2124: 0x95dd, 0x2125: 0x95fd, 0x2126: 0x961d, 0x2127: 0x3331, 0x2128: 0x3339, 0x2129: 0x3341,
	0x212a: 0x3349, 0x212b: 0x963d, 0x212c: 0x3351, 0x212d: 0x965d, 0x212e: 0x3359, 0x212f: 0x3361,
	0x2130: 0x967d, 0x2131: 0x969d, 0x2132: 0x3369, 0x2133: 0x3371, 0x2134: 0x3379, 0x2135: 0x3381,
	0x2136: 0x3389, 0x2137: 0x3391, 0x2138: 0x3399, 0x2139: 0x33a1, 0x213a: 0x33a9, 0x213b: 0x33b1,
	0x213c: 0x33b9, 0x213d: 0x33c1, 0x213e: 0x33c9, 0x213f: 0x2040,
	// Block 0x85, offset 0x2140
	0x2140: 0x33d1, 0x2141: 0x33d9, 0x2142: 0x33e1, 0x2143: 0x33e9, 0x2144: 0x33f1, 0x2145: 0x96bd,
	0x2146: 0x33f9, 0x2147: 0x3401, 0x2148: 0x3409, 0x2149: 0x3411, 0x214a: 0x3419, 0x214b: 0x96dd,
	0x214c: 0x96fd, 0x214d: 0x3421, 0x214e: 0x3429, 0x214f: 0x3431, 0x2150: 0x3439, 0x2151: 0x3441,
	0x2152: 0x3449, 0x2153: 0x971d, 0x2154: 0x3451, 0x2155: 0x3459, 0x2156: 0x3461, 0x2157: 0x3469,
	0x2158: 0x973d, 0x2159: 0x975d, 0x215a: 0x3471, 0x215b: 0x3479, 0x215c: 0x3481, 0x215d: 0x977d,
	0x215e: 0x3489, 0x215f: 0x3491, 0x2160: 0x684d, 0x2161: 0x979d, 0x2162: 0x3499, 0x2163: 0x34a1,
	0x2164: 0x34a9, 0x2165: 0x97bd, 0x2166: 0x34b1, 0x2167: 0x34b9, 0x2168: 0x34c1, 0x2169: 0x34c9,
	0x216a: 0x34d1, 0x216b: 0x34d9, 0x216c: 0x34e1, 0x216d: 0x97dd, 0x216e: 0x34e9, 0x216f: 0x34f1,
	0x2170: 0x34f9, 0x2171: 0x97fd, 0x2172: 0x3501, 0x2173: 0x3509, 0x2174: 0x3511, 0x2175: 0x3519,
	0x2176: 0x7b6d, 0x2177: 0x981d, 0x2178: 0x3521, 0x2179: 0x3529, 0x217a: 0x3531, 0x217b: 0x983d,
	0x217c: 0x3539, 0x217d: 0x985d, 0x217e: 0x3541, 0x217f: 0x3541,
	// Block 0x86, offset 0x2180
	0x2180: 0x3549, 0x2181: 0x987d, 0x2182: 0x3551, 0x2183: 0x3559, 0x2184: 0x3561, 0x2185: 0x3569,
	0x2186: 0x3571, 0x2187: 0x3579, 0x2188: 0x3581, 0x2189: 0x989d, 0x218a: 0x3589, 0x218b: 0x3591,
	0x218c: 0x3599, 0x218d: 0x35a1, 0x218e: 0x35a9, 0x218f: 0x35b1, 0x2190: 0x98bd, 0x2191: 0x35b9,
	0x2192: 0x98dd, 0x2193: 0x98fd, 0x2194: 0x991d, 0x2195: 0x35c1, 0x2196: 0x35c9, 0x2197: 0x35d1,
	0x2198: 0x35d9, 0x2199: 0x35e1, 0x219a: 0x35e9, 0x219b: 0x35f1, 0x219c: 0x35f9, 0x219d: 0x993d,
	0x219e: 0x0040, 0x219f: 0x0040, 0x21a0: 0x0040, 0x21a1: 0x0040, 0x21a2: 0x0040, 0x21a3: 0x0040,
	0x21a4: 0x0040, 0x21a5: 0x0040, 0x21a6: 0x0040, 0x21a7: 0x0040, 0x21a8: 0x0040, 0x21a9: 0x0040,
	0x21aa: 0x0040, 0x21ab: 0x0040, 0x21ac: 0x0040, 0x21ad: 0x0040, 0x21ae: 0x0040, 0x21af: 0x0040,
	0x21b0: 0x0040, 0x21b1: 0x0040, 0x21b2: 0x0040, 0x21b3: 0x0040, 0x21b4: 0x0040, 0x21b5: 0x0040,
	0x21b6: 0x0040, 0x21b7: 0x0040, 0x21b8: 0x0040, 0x21b9: 0x0040, 0x21ba: 0x0040, 0x21bb: 0x0040,
	0x21bc: 0x0040, 0x21bd: 0x0040, 0x21be: 0x0040, 0x21bf: 0x0040,
}

// idnaIndex: 39 blocks, 2496 entries, 4992 bytes
// Block 0 is the zero block.
var idnaIndex = [2496]uint16{
	// Block 0x0, offset 0x0
	// Block 0x1, offset 0x40
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc2: 0x01, 0xc3: 0x85, 0xc4: 0x02, 0xc5: 0x03, 0xc6: 0x04, 0xc7: 0x05,
	0xc8: 0x06, 0xc9: 0x86, 0xca: 0x87, 0xcb: 0x07, 0xcc: 0x88, 0xcd: 0x08, 0xce: 0x09, 0xcf: 0x0a,
	0xd0: 0x89, 0xd1: 0x0b, 0xd2: 0x0c, 0xd3: 0x0d, 0xd4: 0x0e, 0xd5: 0x8a, 0xd6: 0x8b, 0xd7: 0x8c,
	0xd8: 0x0f, 0xd9: 0x10, 0xda: 0x8d, 0xdb: 0x11, 0xdc: 0x12, 0xdd: 0x8e, 0xde: 0x8f, 0xdf: 0x90,
	0xe0: 0x02, 0xe1: 0x03, 0xe2: 0x04, 0xe3: 0x05, 0xe4: 0x06, 0xe5: 0x07, 0xe6: 0x07, 0xe7: 0x07,
	0xe8: 0x07, 0xe9: 0x07, 0xea: 0x08, 0xeb: 0x07, 0xec: 0x07, 0xed: 0x09, 0xee: 0x0a, 0xef: 0x0b,
	0xf0: 0x20, 0xf1: 0x21, 0xf2: 0x21, 0xf3: 0x23, 0xf4: 0x24,
	// Block 0x4, offset 0x100
	0x120: 0x91, 0x121: 0x13, 0x122: 0x14, 0x123: 0x92, 0x124: 0x93, 0x125: 0x15, 0x126: 0x16, 0x127: 0x17,
	0x128: 0x18, 0x129: 0x19, 0x12a: 0x1a, 0x12b: 0x1b, 0x12c: 0x1c, 0x12d: 0x1d, 0x12e: 0x1e, 0x12f: 0x94,
	0x130: 0x95, 0x131: 0x1f, 0x132: 0x20, 0x133: 0x21, 0x134: 0x96, 0x135: 0x22, 0x136: 0x97, 0x137: 0x98,
	0x138: 0x99, 0x139: 0x9a, 0x13a: 0x23, 0x13b: 0x9b, 0x13c: 0x9c, 0x13d: 0x24, 0x13e: 0x25, 0x13f: 0x9d,
	// Block 0x5, offset 0x140
	0x140: 0x9e, 0x141: 0x9f, 0x142: 0xa0, 0x143: 0xa1, 0x144: 0xa2, 0x145: 0xa3, 0x146: 0xa4, 0x147: 0xa5,
	0x148: 0xa6, 0x149: 0xa7, 0x14a: 0xa8, 0x14b: 0xa9, 0x14c: 0xaa, 0x14d: 0xab, 0x14e: 0xac, 0x14f: 0xad,
	0x150: 0xae, 0x151: 0xa6, 0x152: 0xa6, 0x153: 0xa6, 0x154: 0xa6, 0x155: 0xa6, 0x156: 0xa6, 0x157: 0xa6,
	0x158: 0xa6, 0x159: 0xaf, 0x15a: 0xb0, 0x15b: 0xb1, 0x15c: 0xb2, 0x15d: 0xb3, 0x15e: 0xb4, 0x15f: 0xb5,
	0x160: 0xb6, 0x161: 0xb7, 0x162: 0xb8, 0x163: 0xb9, 0x164: 0xba, 0x165: 0xbb, 0x166: 0xbc, 0x167: 0xbd,
	0x168: 0xbe, 0x169: 0xbf, 0x16a: 0xc0, 0x16b: 0xc1, 0x16c: 0xc2, 0x16d: 0xc3, 0x16e: 0xc4, 0x16f: 0xc5,
	0x170: 0xc6, 0x171: 0xc7, 0x172: 0xc8, 0x173: 0xc9, 0x174: 0x26, 0x175: 0x27, 0x176: 0x28, 0x177: 0x88,
	0x178: 0x29, 0x179: 0x29, 0x17a: 0x2a, 0x17b: 0x29, 0x17c: 0xca, 0x17d: 0x2b, 0x17e: 0x2c, 0x17f: 0x2d,
	// Block 0x6, offset 0x180
	0x180: 0x2e, 0x181: 0x2f, 0x182: 0x30, 0x183: 0xcb, 0x184: 0x31, 0x185: 0x32, 0x186: 0xcc, 0x187: 0xa2,
	0x188: 0xcd, 0x189: 0xce, 0x18a: 0xa2, 0x18b: 0xa2, 0x18c: 0xcf, 0x18d: 0xa2, 0x18e: 0xa2, 0x18f: 0xa2,
	0x190: 0xd0, 0x191: 0x33, 0x192: 0x34, 0x193: 0x35, 0x194: 0xa2, 0x195: 0xa2, 0x196: 0xa2, 0x197: 0xa2,
	0x198: 0xa2, 0x199: 0xa2, 0x19a: 0xa2, 0x19b: 0xa2, 0x19c: 0xa2, 0x19d: 0xa2, 0x19e: 0xa2, 0x19f: 0xa2,
	0x1a0: 0xa2, 0x1a1: 0xa2, 0x1a2: 0xa2, 0x1a3: 0xa2, 0x1a4: 0xa2, 0x1a5: 0xa2, 0x1a6: 0xa2, 0x1a7: 0xa2,
	0x1a8: 0xd1, 0x1a9: 0xd2, 0x1aa: 0xa2, 0x1ab: 0xd3, 0x1ac: 0xa2, 0x1ad: 0xd4, 0x1ae: 0xd5, 0x1af: 0xa2,
	0x1b0: 0xd6, 0x1b1: 0x36, 0x1b2: 0x29, 0x1b3: 0x37, 0x1b4: 0xd7, 0x1b5: 0xd8, 0x1b6: 0xd9, 0x1b7: 0xda,
	0x1b8: 0xdb, 0x1b9: 0xdc, 0x1ba: 0xdd, 0x1bb: 0xde, 0x1bc: 0xdf, 0x1bd: 0xe0, 0x1be: 0xe1, 0x1bf: 0x38,
	// Block 0x7, offset 0x1c0
	0x1c0: 0x39, 0x1c1: 0xe2, 0x1c2: 0xe3, 0x1c3: 0xe4, 0x1c4: 0xe5, 0x1c5: 0x3a, 0x1c6: 0x3b, 0x1c7: 0xe6,
	0x1c8: 0xe7, 0x1c9: 0x3c, 0x1ca: 0x3d, 0x1cb: 0x3e, 0x1cc: 0xe8, 0x1cd: 0xe9, 0x1ce: 0x3f, 0x1cf: 0x40,
	0x1d0: 0xa6, 0x1d1: 0xa6, 0x1d2: 0xa6, 0x1d3: 0xa6, 0x1d4: 0xa6, 0x1d5: 0xa6, 0x1d6: 0xa6, 0x1d7: 0xa6,
	0x1d8: 0xa6, 0x1d9: 0xa6, 0x1da: 0xa6, 0x1db: 0xa6, 0x1dc: 0xa6, 0x1dd: 0xa6, 0x1de: 0xa6, 0x1df: 0xa6,
	0x1e0: 0xa6, 0x1e1: 0xa6, 0x1e2: 0xa6, 0x1e3: 0xa6, 0x1e4: 0xa6, 0x1e5: 0xa6, 0x1e6: 0xa6, 0x1e7: 0xa6,
	0x1e8: 0xa6, 0x1e9: 0xa6, 0x1ea: 0xa6, 0x1eb: 0xa6, 0x1ec: 0xa6, 0x1ed: 0xa6, 0x1ee: 0xa6, 0x1ef: 0xa6,
	0x1f0: 0xa6, 0x1f1: 0xa6, 0x1f2: 0xa6, 0x1f3: 0xa6, 0x1f4: 0xa6, 0x1f5: 0xa6, 0x1f6: 0xa6, 0x1f7: 0xa6,
	0x1f8: 0xa6, 0x1f9: 0xa6, 0x1fa: 0xa6, 0x1fb: 0xa6, 0x1fc: 0xa6, 0x1fd: 0xa6, 0x1fe: 0xa6, 0x1ff: 0xa6,
	// Block 0x8, offset 0x200
	0x200: 0xa6, 0x201: 0xa6, 0x202: 0xa6, 0x203: 0xa6, 0x204: 0xa6, 0x205: 0xa6, 0x206: 0xa6, 0x207: 0xa6,
	0x208: 0xa6, 0x209: 0xa6, 0x20a: 0xa6, 0x20b: 0xa6, 0x20c: 0xa6, 0x20d: 0xa6, 0x20e: 0xa6, 0x20f: 0xa6,
	0x210: 0xa6, 0x211: 0xa6, 0x212: 0xa6, 0x213: 0xa6, 0x214: 0xa6, 0x215: 0xa6, 0x216: 0xa6, 0x217: 0xa6,
	0x218: 0xa6, 0x219: 0xa6, 0x21a: 0xa6, 0x21b: 0xa6, 0x21c: 0xa6, 0x21d: 0xa6, 0x21e: 0xa6, 0x21f: 0xa6,
	0x220: 0xa6, 0x221: 0xa6, 0x222: 0xa6, 0x223: 0xa6, 0x224: 0xa6, 0x225: 0xa6, 0x226: 0xa6, 0x227: 0xa6,
	0x228: 0xa6, 0x229: 0xa6, 0x22a: 0xa6, 0x22b: 0xa6, 0x22c: 0xa6, 0x22d: 0xa6, 0x22e: 0xa6, 0x22f: 0xa6,
	0x230: 0xa6, 0x231: 0xa6, 0x232: 0xa6, 0x233: 0xa6, 0x234: 0xa6, 0x235: 0xa6, 0x236: 0xa6, 0x237: 0xa2,
	0x238: 0xa6, 0x239: 0xa6, 0x23a: 0xa6, 0x23b: 0xa6, 0x23c: 0xa6, 0x23d: 0xa6, 0x23e: 0xa6, 0x23f: 0xa6,
	// Block 0x9, offset 0x240
	0x240: 0xa6, 0x241: 0xa6, 0x242: 0xa6, 0x243: 0xa6, 0x244: 0xa6, 0x245: 0xa6, 0x246: 0xa6, 0x247: 0xa6,
	0x248: 0xa6, 0x249: 0xa6, 0x24a: 0xa6, 0x24b: 0xa6, 0x24c: 0xa6, 0x24d: 0xa6, 0x24e: 0xa6, 0x24f: 0xa6,
	0x250: 0xa6, 0x251: 0xa6, 0x252: 0xa6, 0x253: 0xa6, 0x254: 0xa6, 0x255: 0xa6, 0x256: 0xa6, 0x257: 0xa6,
	0x258: 0xa6, 0x259: 0xa6, 0x25a: 0xa6, 0x25b: 0xa6, 0x25c: 0xa6, 0x25d: 0xa6, 0x25e: 0xa6, 0x25f: 0xa6,
	0x260: 0xa6, 0x261: 0xa6, 0x262: 0xa6, 0x263: 0xa6, 0x264: 0xa6, 0x265: 0xa6, 0x266: 0xa6, 0x267: 0xa6,
	0x268: 0xa6, 0x269: 0xa6, 0x26a: 0xa6, 0x26b: 0xa6, 0x26c: 0xa6, 0x26d: 0xa6, 0x26e: 0xa6, 0x26f: 0xa6,
	0x270: 0xa6, 0x271: 0xa6, 0x272: 0xa6, 0x273: 0xa6, 0x274: 0xa6, 0x275: 0xa6, 0x276: 0xa6, 0x277: 0xa6,
	0x278: 0xa6, 0x279: 0xa6, 0x27a: 0xa6, 0x27b: 0xa6, 0x27c: 0xa6, 0x27d: 0xa6, 0x27e: 0xa6, 0x27f: 0xa6,
	// Block 0xa, offset 0x280
	0x280: 0xa6, 0x281: 0xa6, 0x282: 0xa6, 0x283: 0xa6, 0x284: 0xa6, 0x285: 0xa6, 0x286: 0xa6, 0x287: 0xa6,
	0x288: 0xa6, 0x289: 0xa6, 0x28a: 0xa6, 0x28b: 0xa6, 0x28c: 0xa6, 0x28d: 0xa6, 0x28e: 0xa6, 0x28f: 0xa6,
	0x290: 0xa6, 0x291: 0xa6, 0x292: 0xea, 0x293: 0xeb, 0x294: 0xa6, 0x295: 0xa6, 0x296: 0xa6, 0x297: 0xa6,
	0x298: 0xec, 0x299: 0x41, 0x29a: 0x42, 0x29b: 0xed, 0x29c: 0x43, 0x29d: 0x44, 0x29e: 0x45, 0x29f: 0x46,
	0x2a0: 0xee, 0x2a1: 0xef, 0x2a2: 0xf0, 0x2a3: 0xf1, 0x2a4: 0xf2, 0x2a5: 0xf3, 0x2a6: 0xf4, 0x2a7: 0xf5,
	0x2a8: 0xf6, 0x2a9: 0xf7, 0x2aa: 0xf8, 0x2ab: 0xf9, 0x2ac: 0xfa, 0x2ad: 0xfb, 0x2ae: 0xfc, 0x2af: 0xfd,
	0x2b0: 0xa6, 0x2b1: 0xa6, 0x2b2: 0xa6, 0x2b3: 0xa6, 0x2b4: 0xa6, 0x2b5: 0xa6, 0x2b6: 0xa6, 0x2b7: 0xa6,
	0x2b8: 0xa6, 0x2b9: 0xa6, 0x2ba: 0xa6, 0x2bb: 0xa6, 0x2bc: 0xa6, 0x2bd: 0xa6, 0x2be: 0xa6, 0x2bf: 0xa6,
	// Block 0xb, offset 0x2c0
	0x2c0: 0xa6, 0x2c1: 0xa6, 0x2c2: 0xa6, 0x2c3: 0xa6, 0x2c4: 0xa6, 0x2c5: 0xa6, 0x2c6: 0xa6, 0x2c7: 0xa6,
	0x2c8: 0xa6, 0x2c9: 0xa6, 0x2ca: 0xa6, 0x2cb: 0xa6, 0x2cc: 0xa6, 0x2cd: 0xa6, 0x2ce: 0xa6, 0x2cf: 0xa6,
	0x2d0: 0xa6, 0x2d1: 0xa6, 0x2d2: 0xa6, 0x2d3: 0xa6, 0x2d4: 0xa6, 0x2d5: 0xa6, 0x2d6: 0xa6, 0x2d7: 0xa6,
	0x2d8: 0xa6, 0x2d9: 0xa6, 0x2da: 0xa6, 0x2db: 0xa6, 0x2dc: 0xa6, 0x2dd: 0xa6, 0x2de: 0xfe, 0x2df: 0xff,
	// Block 0xc, offset 0x300
	0x300: 0x100, 0x301: 0x100, 0x302: 0x100, 0x303: 0x100, 0x304: 0x100, 0x305: 0x100, 0x306: 0x100, 0x307: 0x100,
	0x308: 0x100, 0x309: 0x100, 0x30a: 0x100, 0x30b: 0x100, 0x30c: 0x100, 0x30d: 0x100, 0x30e: 0x100, 0x30f: 0x100,
	0x310: 0x100, 0x311: 0x100, 0x312: 0x100, 0x313: 0x100, 0x314: 0x100, 0x315: 0x100, 0x316: 0x100, 0x317: 0x100,
	0x318: 0x100, 0x319: 0x100, 0x31a: 0x100, 0x31b: 0x100, 0x31c: 0x100, 0x31d: 0x100, 0x31e: 0x100, 0x31f: 0x100,
	0x320: 0x100, 0x321: 0x100, 0x322: 0x100, 0x323: 0x100, 0x324: 0x100, 0x325: 0x100, 0x326: 0x100, 0x327: 0x100,
	0x328: 0x100, 0x329: 0x100, 0x32a: 0x100, 0x32b: 0x100, 0x32c: 0x100, 0x32d: 0x100, 0x32e: 0x100, 0x32f: 0x100,
	0x330: 0x100, 0x331: 0x100, 0x332: 0x100, 0x333: 0x100, 0x334: 0x100, 0x335: 0x100, 0x336: 0x100, 0x337: 0x100,
	0x338: 0x100, 0x339: 0x100, 0x33a: 0x100, 0x33b: 0x100, 0x33c: 0x100, 0x33d: 0x100, 0x33e: 0x100, 0x33f: 0x100,
	// Block 0xd, offset 0x340
	0x340: 0x100, 0x341: 0x100, 0x342: 0x100, 0x343: 0x100, 0x344: 0x100, 0x345: 0x100, 0x346: 0x100, 0x347: 0x100,
	0x348: 0x100, 0x349: 0x100, 0x34a: 0x100, 0x34b: 0x100, 0x34c: 0x100, 0x34d: 0x100, 0x34e: 0x100, 0x34f: 0x100,
	0x350: 0x100, 0x351: 0x100, 0x352: 0x100, 0x353: 0x100, 0x354: 0x100, 0x355: 0x100, 0x356: 0x100, 0x357: 0x100,
	0x358: 0x100, 0x359: 0x100, 0x35a: 0x100, 0x35b: 0x100, 0x35c: 0x100, 0x35d: 0x100, 0x35e: 0x100, 0x35f: 0x100,
	0x360: 0x100, 0x361: 0x100, 0x362: 0x100, 0x363: 0x100, 0x364: 0x101, 0x365: 0x102, 0x366: 0x103, 0x367: 0x104,
	0x368: 0x47, 0x369: 0x105, 0x36a: 0x106, 0x36b: 0x48, 0x36c: 0x49, 0x36d: 0x4a, 0x36e: 0x4b, 0x36f: 0x4c,
	0x370: 0x107, 0x371: 0x4d, 0x372: 0x4e, 0x373: 0x4f, 0x374: 0x50, 0x375: 0x51, 0x376: 0x108, 0x377: 0x52,
	0x378: 0x53, 0x379: 0x54, 0x37a: 0x55, 0x37b: 0x56, 0x37c: 0x57, 0x37d: 0x58, 0x37e: 0x59, 0x37f: 0x5a,
	// Block 0xe, offset 0x380
	0x380: 0x109, 0x381: 0x10a, 0x382: 0xa6, 0x383: 0x10b, 0x384: 0x10c, 0x385: 0xa2, 0x386: 0x10d, 0x387: 0x10e,
	0x388: 0x100, 0x389: 0x100, 0x38a: 0x10f, 0x38b: 0x110, 0x38c: 0x111, 0x38d: 0x112, 0x38e: 0x113, 0x38f: 0x114,
	0x390: 0x115, 0x391: 0xa6, 0x392: 0x116, 0x393: 0x117, 0x394: 0x118, 0x395: 0x5b, 0x396: 0x5c, 0x397: 0x100,
	0x398: 0xa6, 0x399: 0xa6, 0x39a: 0xa6, 0x39b: 0xa6, 0x39c: 0x119, 0x39d: 0x11a, 0x39e: 0x5d, 0x39f: 0x100,
	0x3a0: 0x11b, 0x3a1: 0x11c, 0x3a2: 0x11d, 0x3a3: 0x11e, 0x3a4: 0x11f, 0x3a5: 0x100, 0x3a6: 0x120, 0x3a7: 0x121,
	0x3a8: 0x122, 0x3a9: 0x123, 0x3aa: 0x124, 0x3ab: 0x5e, 0x3ac: 0x125, 0x3ad: 0x126, 0x3ae: 0x5f, 0x3af: 0x100,
	0x3b0: 0x127, 0x3b1: 0x128, 0x3b2: 0x129, 0x3b3: 0x12a, 0x3b4: 0x12b, 0x3b5: 0x100, 0x3b6: 0x100, 0x3b7: 0x100,
	0x3b8: 0x100, 0x3b9: 0x12c, 0x3ba: 0x12d, 0x3bb: 0x12e, 0x3bc: 0x12f, 0x3bd: 0x130, 0x3be: 0x131, 0x3bf: 0x132,
	// Block 0xf, offset 0x3c0
	0x3c0: 0x133, 0x3c1: 0x134, 0x3c2: 0x135, 0x3c3: 0x136, 0x3c4: 0x137, 0x3c5: 0x138, 0x3c6: 0x139, 0x3c7: 0x13a,
	0x3c8: 0x13b, 0x3c9: 0x13c, 0x3ca: 0x13d, 0x3cb: 0x13e, 0x3cc: 0x60, 0x3cd: 0x61, 0x3ce: 0x100, 0x3cf: 0x100,
	0x3d0: 0x13f, 0x3d1: 0x140, 0x3d2: 0x141, 0x3d3: 0x142, 0x3d4: 0x100, 0x3d5: 0x100, 0x3d6: 0x143, 0x3d7: 0x144,
	0x3d8: 0x145, 0x3d9: 0x146, 0x3da: 0x147, 0x3db: 0x148, 0x3dc: 0x149, 0x3dd: 0x14a, 0x3de: 0x100, 0x3df: 0x100,
	0x3e0: 0x14b, 0x3e1: 0x100, 0x3e2: 0x14c, 0x3e3: 0x14d, 0x3e4: 0x62, 0x3e5: 0x14e, 0x3e6: 0x14f, 0x3e7: 0x150,
	0x3e8: 0x151, 0x3e9: 0x152, 0x3ea: 0x153, 0x3eb: 0x154, 0x3ec: 0x155, 0x3ed: 0x100, 0x3ee: 0x100, 0x3ef: 0x100,
	0x3f0: 0x156, 0x3f1: 0x157, 0x3f2: 0x158, 0x3f3: 0x100, 0x3f4: 0x159, 0x3f5: 0x15a, 0x3f6: 0x15b, 0x3f7: 0x100,
	0x3f8: 0x100, 0x3f9: 0x100, 0x3fa: 0x100, 0x3fb: 0x15c, 0x3fc: 0x15d, 0x3fd: 0x15e, 0x3fe: 0x15f, 0x3ff: 0x160,
	// Block 0x10, offset 0x400
	0x400: 0xa6, 0x401: 0xa6, 0x402: 0xa6, 0x403: 0xa6, 0x404: 0xa6, 0x405: 0xa6, 0x406: 0xa6, 0x407: 0xa6,
	0x408: 0xa6, 0x409: 0xa6, 0x40a: 0xa6, 0x40b: 0xa6, 0x40c: 0xa6, 0x40d: 0xa6, 0x40e: 0x161, 0x40f: 0x100,
	0x410: 0xa2, 0x411: 0x162, 0x412: 0xa6, 0x413: 0xa6, 0x414: 0xa6, 0x415: 0x163, 0x416: 0x100, 0x417: 0x100,
	0x418: 0x100, 0x419: 0x100, 0x41a: 0x100, 0x41b: 0x100, 0x41c: 0x100, 0x41d: 0x100, 0x41e: 0x100, 0x41f: 0x100,
	0x420: 0x100, 0x421: 0x100, 0x422: 0x100, 0x423: 0x100, 0x424: 0x100, 0x425: 0x100, 0x426: 0x100, 0x427: 0x100,
	0x428: 0x100, 0x429: 0x100, 0x42a: 0x100, 0x42b: 0x100, 0x42c: 0x100, 0x42d: 0x100, 0x42e: 0x100, 0x42f: 0x100,
	0x430: 0x100, 0x431: 0x100, 0x432: 0x100, 0x433: 0x100, 0x434: 0x100, 0x435: 0x100, 0x436: 0x100, 0x437: 0x100,
	0x438: 0x100, 0x439: 0x100, 0x43a: 0x100, 0x43b: 0x100, 0x43c: 0x100, 0x43d: 0x100, 0x43e: 0x164, 0x43f: 0x165,
	// Block 0x11, offset 0x440
	0x440: 0xa6, 0x441: 0xa6, 0x442: 0xa6, 0x443: 0xa6, 0x444: 0xa6, 0x445: 0xa6, 0x446: 0xa6, 0x447: 0xa6,
	0x448: 0xa6, 0x449: 0xa6, 0x44a: 0xa6, 0x44b: 0xa6, 0x44c: 0xa6, 0x44d: 0xa6, 0x44e: 0xa6, 0x44f: 0xa6,
	0x450: 0x166, 0x451: 0x167, 0x452: 0x100, 0x453: 0x100, 0x454: 0x100, 0x455: 0x100, 0x456: 0x100, 0x457: 0x100,
	0x458: 0x100, 0x459: 0x100, 0x45a: 0x100, 0x45b: 0x100, 0x45c: 0x100, 0x45d: 0x100, 0x45e: 0x100, 0x45f: 0x100,
	0x460: 0x100, 0x461: 0x100, 0x462: 0x100, 0x463: 0x100, 0x464: 0x100, 0x465: 0x100, 0x466: 0x100, 0x467: 0x100,
	0x468: 0x100, 0x469: 0x100, 0x46a: 0x100, 0x46b: 0x100, 0x46c: 0x100, 0x46d: 0x100, 0x46e: 0x100, 0x46f: 0x100,
	0x470: 0x100, 0x471: 0x100, 0x472: 0x100, 0x473: 0x100, 0x474: 0x100, 0x475: 0x100, 0x476: 0x100, 0x477: 0x100,
	0x478: 0x100, 0x479: 0x100, 0x47a: 0x100, 0x47b: 0x100, 0x47c: 0x100, 0x47d: 0x100, 0x47e: 0x100, 0x47f: 0x100,
	// Block 0x12, offset 0x480
	0x480: 0x100, 0x481: 0x100, 0x482: 0x100, 0x483: 0x100, 0x484: 0x100, 0x485: 0x100, 0x486: 0x100, 0x487: 0x100,
	0x488: 0x100, 0x489: 0x100, 0x48a: 0x100, 0x48b: 0x100, 0x48c: 0x100, 0x48d: 0x100, 0x48e: 0x100, 0x48f: 0x100,
	0x490: 0xa6, 0x491: 0xa6, 0x492: 0xa6, 0x493: 0xa6, 0x494: 0xa6, 0x495: 0xa6, 0x496: 0xa6, 0x497: 0xa6,
	0x498: 0xa6, 0x499: 0x14a, 0x49a: 0x100, 0x49b: 0x100, 0x49c: 0x100, 0x49d: 0x100, 0x49e: 0x100, 0x49f: 0x100,
	0x4a0: 0x100, 0x4a1: 0x100, 0x4a2: 0x100, 0x4a3: 0x100, 0x4a4: 0x100, 0x4a5: 0x100, 0x4a6: 0x100, 0x4a7: 0x100,
	0x4a8: 0x100, 0x4a9: 0x100, 0x4aa: 0x100, 0x4ab: 0x100, 0x4ac: 0x100, 0x4ad: 0x100, 0x4ae: 0x100, 0x4af: 0x100,
	0x4b0: 0x100, 0x4b1: 0x100, 0x4b2: 0x100, 0x4b3: 0x100, 0x4b4: 0x100, 0x4b5: 0x100, 0x4b6: 0x100, 0x4b7: 0x100,
	0x4b8: 0x100, 0x4b9: 0x100, 0x4ba: 0x100, 0x4bb: 0x100, 0x4bc: 0x100, 0x4bd: 0x100, 0x4be: 0x100, 0x4bf: 0x100,
	// Block 0x13, offset 0x4c0
	0x4c0: 0x100, 0x4c1: 0x100, 0x4c2: 0x100, 0x4c3: 0x100, 0x4c4: 0x100, 0x4c5: 0x100, 0x4c6: 0x100, 0x4c7: 0x100,
	0x4c8: 0x100, 0x4c9: 0x100, 0x4ca: 0x100, 0x4cb: 0x100, 0x4cc: 0x100, 0x4cd: 0x100, 0x4ce: 0x100, 0x4cf: 0x100,
	0x4d0: 0x100, 0x4d1: 0x100, 0x4d2: 0x100, 0x4d3: 0x100, 0x4d4: 0x100, 0x4d5: 0x100, 0x4d6: 0x100, 0x4d7: 0x100,
	0x4d8: 0x100, 0x4d9: 0x100, 0x4da: 0x100, 0x4db: 0x100, 0x4dc: 0x100, 0x4dd: 0x100, 0x4de: 0x100, 0x4df: 0x100,
	0x4e0: 0xa6, 0x4e1: 0xa6, 0x4e2: 0xa6, 0x4e3: 0xa6, 0x4e4: 0xa6, 0x4e5: 0xa6, 0x4e6: 0xa6, 0x4e7: 0xa6,
	0x4e8: 0x154, 0x4e9: 0x168, 0x4ea: 0x169, 0x4eb: 0x16a, 0x4ec: 0x16b, 0x4ed: 0x16c, 0x4ee: 0x16d, 0x4ef: 0x100,
	0x4f0: 0x100, 0x4f1: 0x100, 0x4f2: 0x100, 0x4f3: 0x100, 0x4f4: 0x100, 0x4f5: 0x100, 0x4f6: 0x100, 0x4f7: 0x100,
	0x4f8: 0x100, 0x4f9: 0x16e, 0x4fa: 0x16f, 0x4fb: 0x100, 0x4fc: 0xa6, 0x4fd: 0x170, 0x4fe: 0x171, 0x4ff: 0x172,
	// Block 0x14, offset 0x500
	0x500: 0xa6, 0x501: 0xa6, 0x502: 0xa6, 0x503: 0xa6, 0x504: 0xa6, 0x505: 0xa6, 0x506: 0xa6, 0x507: 0xa6,
	0x508: 0xa6, 0x509: 0xa6, 0x50a: 0xa6, 0x50b: 0xa6, 0x50c: 0xa6, 0x50d: 0xa6, 0x50e: 0xa6, 0x50f: 0xa6,
	0x510: 0xa6, 0x511: 0xa6, 0x512: 0xa6, 0x513: 0xa6, 0x514: 0xa6, 0x515: 0xa6, 0x516: 0xa6, 0x517: 0xa6,
	0x518: 0xa6, 0x519: 0xa6, 0x51a: 0xa6, 0x51b: 0xa6, 0x51c: 0xa6, 0x51d: 0xa6, 0x51e: 0xa6, 0x51f: 0x173,
	0x520: 0xa6, 0x521: 0xa6, 0x522: 0xa6, 0x523: 0xa6, 0x524: 0xa6, 0x525: 0xa6, 0x526: 0xa6, 0x527: 0xa6,
	0x528: 0xa6, 0x529: 0xa6, 0x52a: 0xa6, 0x52b: 0xa6, 0x52c: 0xa6, 0x52d: 0xa6, 0x52e: 0xa6, 0x52f: 0xa6,
	0x530: 0xa6, 0x531: 0xa6, 0x532: 0xa6, 0x533: 0x174, 0x534: 0x175, 0x535: 0x100, 0x536: 0x100, 0x537: 0x100,
	0x538: 0x100, 0x539: 0x100, 0x53a: 0x100, 0x53b: 0x100, 0x53c: 0x100, 0x53d: 0x100, 0x53e: 0x100, 0x53f: 0x100,
	// Block 0x15, offset 0x540
	0x540: 0x100, 0x541: 0x100, 0x542: 0x100, 0x543: 0x100, 0x544: 0x100, 0x545: 0x100, 0x546: 0x100, 0x547: 0x100,
	0x548: 0x100, 0x549: 0x100, 0x54a: 0x100, 0x54b: 0x100, 0x54c: 0x100, 0x54d: 0x100, 0x54e: 0x100, 0x54f: 0x100,
	0x550: 0x100, 0x551: 0x100, 0x552: 0x100, 0x553: 0x100, 0x554: 0x100, 0x555: 0x100, 0x556: 0x100, 0x557: 0x100,
	0x558: 0x100, 0x559: 0x100, 0x55a: 0x100, 0x55b: 0x100, 0x55c: 0x100, 0x55d: 0x100, 0x55e: 0x100, 0x55f: 0x100,
	0x560: 0x100, 0x561: 0x100, 0x562: 0x100, 0x563: 0x100, 0x564: 0x100, 0x565: 0x100, 0x566: 0x100, 0x567: 0x100,
	0x568: 0x100, 0x569: 0x100, 0x56a: 0x100, 0x56b: 0x100, 0x56c: 0x100, 0x56d: 0x100, 0x56e: 0x100, 0x56f: 0x100,
	0x570: 0x100, 0x571: 0x100, 0x572: 0x100, 0x573: 0x100, 0x574: 0x100, 0x575: 0x100, 0x576: 0x100, 0x577: 0x100,
	0x578: 0x100, 0x579: 0x100, 0x57a: 0x100, 0x57b: 0x100, 0x57c: 0x100, 0x57d: 0x100, 0x57e: 0x100, 0x57f: 0x176,
	// Block 0x16, offset 0x580
	0x580: 0xa6, 0x581: 0xa6, 0x582: 0xa6, 0x583: 0xa6, 0x584: 0x177, 0x585: 0x178, 0x586: 0xa6, 0x587: 0xa6,
	0x588: 0xa6, 0x589: 0xa6, 0x58a: 0xa6, 0x58b: 0x179, 0x58c: 0x100, 0x58d: 0x100, 0x58e: 0x100, 0x58f: 0x100,
	0x590: 0x100, 0x591: 0x100, 0x592: 0x100, 0x593: 0x100, 0x594: 0x100, 0x595: 0x100, 0x596: 0x100, 0x597: 0x100,
	0x598: 0x100, 0x599: 0x100, 0x59a: 0x100, 0x59b: 0x100, 0x59c: 0x100, 0x59d: 0x100, 0x59e: 0x100, 0x59f: 0x100,
	0x5a0: 0x100, 0x5a1: 0x100, 0x5a2: 0x100, 0x5a3: 0x100, 0x5a4: 0x100, 0x5a5: 0x100, 0x5a6: 0x100, 0x5a7: 0x100,
	0x5a8: 0x100, 0x5a9: 0x100, 0x5aa: 0x100, 0x5ab: 0x100, 0x5ac: 0x100, 0x5ad: 0x100, 0x5ae: 0x100, 0x5af: 0x100,
	0x5b0: 0xa6, 0x5b1: 0x17a, 0x5b2: 0x17b, 0x5b3: 0x100, 0x5b4: 0x100, 0x5b5: 0x100, 0x5b6: 0x100, 0x5b7: 0x100,
	0x5b8: 0x100, 0x5b9: 0x100, 0x5ba: 0x100, 0x5bb: 0x100, 0x5bc: 0x100, 0x5bd: 0x100, 0x5be: 0x100, 0x5bf: 0x100,
	// Block 0x17, offset 0x5c0
	0x5c0: 0x100, 0x5c1: 0x100, 0x5c2: 0x100, 0x5c3: 0x100, 0x5c4: 0x100, 0x5c5: 0x100, 0x5c6: 0x100, 0x5c7: 0x100,
	0x5c8: 0x100, 0x5c9: 0x100, 0x5ca: 0x100, 0x5cb: 0x100, 0x5cc: 0x100, 0x5cd: 0x100, 0x5ce: 0x100, 0x5cf: 0x100,
	0x5d0: 0x100, 0x5d1: 0x100, 0x5d2: 0x100, 0x5d3: 0x100, 0x5d4: 0x100, 0x5d5: 0x100, 0x5d6: 0x100, 0x5d7: 0x100,
	0x5d8: 0x100, 0x5d9: 0x100, 0x5da: 0x100, 0x5db: 0x100, 0x5dc: 0x100, 0x5dd: 0x100, 0x5de: 0x100, 0x5df: 0x100,
	0x5e0: 0x100, 0x5e1: 0x100, 0x5e2: 0x100, 0x5e3: 0x100, 0x5e4: 0x100, 0x5e5: 0x100, 0x5e6: 0x100, 0x5e7: 0x100,
	0x5e8: 0x100, 0x5e9: 0x100, 0x5ea: 0x100, 0x5eb: 0x100, 0x5ec: 0x100, 0x5ed: 0x100, 0x5ee: 0x100, 0x5ef: 0x100,
	0x5f0: 0x100, 0x5f1: 0x100, 0x5f2: 0x100, 0x5f3: 0x100, 0x5f4: 0x100, 0x5f5: 0x100, 0x5f6: 0x100, 0x5f7: 0x100,
	0x5f8: 0x100, 0x5f9: 0x100, 0x5fa: 0x100, 0x5fb: 0x100, 0x5fc: 0x17c, 0x5fd: 0x17d, 0x5fe: 0xa2, 0x5ff: 0x17e,
	// Block 0x18, offset 0x600
	0x600: 0xa2, 0x601: 0xa2, 0x602: 0xa2, 0x603: 0x17f, 0x604: 0x180, 0x605: 0x181, 0x606: 0x182, 0x607: 0x183,
	0x608: 0xa2, 0x609: 0x184, 0x60a: 0x100, 0x60b: 0x185, 0x60c: 0xa2, 0x60d: 0x186, 0x60e: 0x100, 0x60f: 0x100,
	0x610: 0x63, 0x611: 0x64, 0x612: 0x65, 0x613: 0x66, 0x614: 0x67, 0x615: 0x68, 0x616: 0x69, 0x617: 0x6a,
	0x618: 0x6b, 0x619: 0x6c, 0x61a: 0x6d, 0x61b: 0x6e, 0x61c: 0x6f, 0x61d: 0x70, 0x61e: 0x71, 0x61f: 0x72,
	0x620: 0xa2, 0x621: 0xa2, 0x622: 0xa2, 0x623: 0xa2, 0x624: 0xa2, 0x625: 0xa2, 0x626: 0xa2, 0x627: 0xa2,
	0x628: 0x187, 0x629: 0x188, 0x62a: 0x189, 0x62b: 0x100, 0x62c: 0x100, 0x62d: 0x100, 0x62e: 0x100, 0x62f: 0x100,
	0x630: 0x100, 0x631: 0x100, 0x632: 0x100, 0x633: 0x100, 0x634: 0x100, 0x635: 0x100, 0x636: 0x100, 0x637: 0x100,
	0x638: 0x100, 0x639: 0x100, 0x63a: 0x100, 0x63b: 0x100, 0x63c: 0x18a, 0x63d: 0x100, 0x63e: 0x100, 0x63f: 0x100,
	// Block 0x19, offset 0x640
	0x640: 0x73, 0x641: 0x74, 0x642: 0x18b, 0x643: 0x100, 0x644: 0x18c, 0x645: 0x18d, 0x646: 0x100, 0x647: 0x100,
	0x648: 0x100, 0x649: 0x100, 0x64a: 0x18e, 0x64b: 0x18f, 0x64c: 0x100, 0x64d: 0x100, 0x64e: 0x100, 0x64f: 0x100,
	0x650: 0x100, 0x651: 0x100, 0x652: 0x100, 0x653: 0x190, 0x654: 0x100, 0x655: 0x100, 0x656: 0x100, 0x657: 0x100,
	0x658: 0x100, 0x659: 0x100, 0x65a: 0x100, 0x65b: 0x100, 0x65c: 0x100, 0x65d: 0x100, 0x65e: 0x100, 0x65f: 0x191,
	0x660: 0x127, 0x661: 0x127, 0x662: 0x127, 0x663: 0x192, 0x664: 0x75, 0x665: 0x193, 0x666: 0x100, 0x667: 0x100,
	0x668: 0x100, 0x669: 0x100, 0x66a: 0x100, 0x66b: 0x100, 0x66c: 0x100, 0x66d: 0x100, 0x66e: 0x100, 0x66f: 0x100,
	0x670: 0x100, 0x671: 0x194, 0x672: 0x195, 0x673: 0x100, 0x674: 0x196, 0x675: 0x100, 0x676: 0x100, 0x677: 0x100,
	0x678: 0x76, 0x679: 0x77, 0x67a: 0x78, 0x67b: 0x197, 0x67c: 0x100, 0x67d: 0x100, 0x67e: 0x100, 0x67f: 0x100,
	// Block 0x1a, offset 0x680
	0x680: 0x198, 0x681: 0xa2, 0x682: 0x199, 0x683: 0x19a, 0x684: 0x79, 0x685: 0x7a, 0x686: 0x19b, 0x687: 0x19c,
	0x688: 0x7b, 0x689: 0x19d, 0x68a: 0x100, 0x68b: 0x100, 0x68c: 0xa2, 0x68d: 0xa2, 0x68e: 0xa2, 0x68f: 0xa2,
	0x690: 0xa2, 0x691: 0xa2, 0x692: 0xa2, 0x693: 0xa2, 0x694: 0xa2, 0x695: 0xa2, 0x696: 0xa2, 0x697: 0xa2,
	0x698: 0xa2, 0x699: 0xa2, 0x69a: 0xa2, 0x69b: 0x19e, 0x69c: 0xa2, 0x69d: 0x19f, 0x69e: 0xa2, 0x69f: 0x1a0,
	0x6a0: 0x1a1, 0x6a1: 0x1a2, 0x6a2: 0x1a3, 0x6a3: 0x100, 0x6a4: 0xa2, 0x6a5: 0xa2, 0x6a6: 0xa2, 0x6a7: 0xa2,
	0x6a8: 0xa2, 0x6a9: 0x1a4, 0x6aa: 0x1a5, 0x6ab: 0x1a6, 0x6ac: 0xa2, 0x6ad: 0xa2, 0x6ae: 0x1a7, 0x6af: 0x1a8,
	0x6b0: 0x100, 0x6b1: 0x100, 0x6b2: 0x100, 0x6b3: 0x100, 0x6b4: 0x100, 0x6b5: 0x100, 0x6b6: 0x100, 0x6b7: 0x100,
	0x6b8: 0x100, 0x6b9: 0x100, 0x6ba: 0x100, 0x6bb: 0x100, 0x6bc: 0x100, 0x6bd: 0x100, 0x6be: 0x100, 0x6bf: 0x100,
	// Block 0x1b, offset 0x6c0
	0x6c0: 0xa6, 0x6c1: 0xa6, 0x6c2: 0xa6, 0x6c3: 0xa6, 0x6c4: 0xa6, 0x6c5: 0xa6, 0x6c6: 0xa6, 0x6c7: 0xa6,
	0x6c8: 0xa6, 0x6c9: 0xa6, 0x6ca: 0xa6, 0x6cb: 0xa6, 0x6cc: 0xa6, 0x6cd: 0xa6, 0x6ce: 0xa6, 0x6cf: 0xa6,
	0x6d0: 0xa6, 0x6d1: 0xa6, 0x6d2: 0xa6, 0x6d3: 0xa6, 0x6d4: 0xa6, 0x6d5: 0xa6, 0x6d6: 0xa6, 0x6d7: 0xa6,
	0x6d8: 0xa6, 0x6d9: 0xa6, 0x6da: 0xa6, 0x6db: 0x1a9, 0x6dc: 0xa6, 0x6dd: 0xa6, 0x6de: 0xa6, 0x6df: 0xa6,
	0x6e0: 0xa6, 0x6e1: 0xa6, 0x6e2: 0xa6, 0x6e3: 0xa6, 0x6e4: 0xa6, 0x6e5: 0xa6, 0x6e6: 0xa6, 0x6e7: 0xa6,
	0x6e8: 0xa6, 0x6e9: 0xa6, 0x6ea: 0xa6, 0x6eb: 0xa6, 0x6ec: 0xa6, 0x6ed: 0xa6, 0x6ee: 0xa6, 0x6ef: 0xa6,
	0x6f0: 0xa6, 0x6f1: 0xa6, 0x6f2: 0xa6, 0x6f3: 0xa6, 0x6f4: 0xa6, 0x6f5: 0xa6, 0x6f6: 0xa6, 0x6f7: 0xa6,
	0x6f8: 0xa6, 0x6f9: 0xa6, 0x6fa: 0xa6, 0x6fb: 0xa6, 0x6fc: 0xa6, 0x6fd: 0xa6, 0x6fe: 0xa6, 0x6ff: 0xa6,
	// Block 0x1c, offset 0x700
	0x700: 0xa6, 0x701: 0xa6, 0x702: 0xa6, 0x703: 0xa6, 0x704: 0xa6, 0x705: 0xa6, 0x706: 0xa6, 0x707: 0xa6,
	0x708: 0xa6, 0x709: 0xa6, 0x70a: 0xa6, 0x70b: 0xa6, 0x70c: 0xa6, 0x70d: 0xa6, 0x70e: 0xa6, 0x70f: 0xa6,
	0x710: 0xa6, 0x711: 0xa6, 0x712: 0xa6, 0x713: 0xa6, 0x714: 0xa6, 0x715: 0xa6, 0x716: 0xa6, 0x717: 0xa6,
	0x718: 0xa6, 0x719: 0xa6, 0x71a: 0xa6, 0x71b: 0xa6, 0x71c: 0x1aa, 0x71d: 0xa6, 0x71e: 0xa6, 0x71f: 0xa6,
	0x720: 0x1ab, 0x721: 0xa6, 0x722: 0xa6, 0x723: 0xa6, 0x724: 0xa6, 0x725: 0xa6, 0x726: 0xa6, 0x727: 0xa6,
	0x728: 0xa6, 0x729: 0xa6, 0x72a: 0xa6, 0x72b: 0xa6, 0x72c: 0xa6, 0x72d: 0xa6, 0x72e: 0xa6, 0x72f: 0xa6,
	0x730: 0xa6, 0x731: 0xa6, 0x732: 0xa6, 0x733: 0xa6, 0x734: 0xa6, 0x735: 0xa6, 0x736: 0xa6, 0x737: 0xa6,
	0x738: 0xa6, 0x739: 0xa6, 0x73a: 0xa6, 0x73b: 0xa6, 0x73c: 0xa6, 0x73d: 0xa6, 0x73e: 0xa6, 0x73f: 0xa6,
	// Block 0x1d, offset 0x740
	0x740: 0xa6, 0x741: 0xa6, 0x742: 0xa6, 0x743: 0xa6, 0x744: 0xa6, 0x745: 0xa6, 0x746: 0xa6, 0x747: 0xa6,
	0x748: 0xa6, 0x749: 0xa6, 0x74a: 0xa6, 0x74b: 0xa6, 0x74c: 0xa6, 0x74d: 0xa6, 0x74e: 0xa6, 0x74f: 0xa6,
	0x750: 0xa6, 0x751: 0xa6, 0x752: 0xa6, 0x753: 0xa6, 0x754: 0xa6, 0x755: 0xa6, 0x756: 0xa6, 0x757: 0xa6,
	0x758: 0xa6, 0x759: 0xa6, 0x75a: 0xa6, 0x75b: 0xa6, 0x75c: 0xa6, 0x75d: 0xa6, 0x75e: 0xa6, 0x75f: 0xa6,
	0x760: 0xa6, 0x761: 0xa6, 0x762: 0xa6, 0x763: 0xa6, 0x764: 0xa6, 0x765: 0xa6, 0x766: 0xa6, 0x767: 0xa6,
	0x768: 0xa6, 0x769: 0xa6, 0x76a: 0xa6, 0x76b: 0xa6, 0x76c: 0xa6, 0x76d: 0xa6, 0x76e: 0xa6, 0x76f: 0xa6,
	0x770: 0xa6, 0x771: 0xa6, 0x772: 0xa6, 0x773: 0xa6, 0x774: 0xa6, 0x775: 0xa6, 0x776: 0xa6, 0x777: 0xa6,
	0x778: 0xa6, 0x779: 0xa6, 0x77a: 0x1ac, 0x77b: 0xa6, 0x77c: 0xa6, 0x77d: 0xa6, 0x77e: 0xa6, 0x77f: 0xa6,
	// Block 0x1e, offset 0x780
	0x780: 0xa6, 0x781: 0xa6, 0x782: 0xa6, 0x783: 0xa6, 0x784: 0xa6, 0x785: 0xa6, 0x786: 0xa6, 0x787: 0xa6,
	0x788: 0xa6, 0x789: 0xa6, 0x78a: 0xa6, 0x78b: 0xa6, 0x78c: 0xa6, 0x78d: 0xa6, 0x78e: 0xa6, 0x78f: 0xa6,
	0x790: 0xa6, 0x791: 0xa6, 0x792: 0xa6, 0x793: 0xa6, 0x794: 0xa6, 0x795: 0xa6, 0x796: 0xa6, 0x797: 0xa6,
	0x798: 0xa6, 0x799: 0xa6, 0x79a: 0xa6, 0x79b: 0xa6, 0x79c: 0xa6, 0x79d: 0xa6, 0x79e: 0xa6, 0x79f: 0xa6,
	0x7a0: 0xa6, 0x7a1: 0xa6, 0x7a2: 0xa6, 0x7a3: 0xa6, 0x7a4: 0xa6, 0x7a5: 0xa6, 0x7a6: 0xa6, 0x7a7: 0xa6,
	0x7a8: 0xa6, 0x7a9: 0xa6, 0x7aa: 0xa6, 0x7ab: 0xa6, 0x7ac: 0xa6, 0x7ad: 0xa6, 0x7ae: 0xa6, 0x7af: 0x1ad,
	0x7b0: 0x100, 0x7b1: 0x100, 0x7b2: 0x100, 0x7b3: 0x100, 0x7b4: 0x100, 0x7b5: 0x100, 0x7b6: 0x100, 0x7b7: 0x100,
	0x7b8: 0x100, 0x7b9: 0x100, 0x7ba: 0x100, 0x7bb: 0x100, 0x7bc: 0x100, 0x7bd: 0x100, 0x7be: 0x100, 0x7bf: 0x100,
	// Block 0x1f, offset 0x7c0
	0x7c0: 0x100, 0x7c1: 0x100, 0x7c2: 0x100, 0x7c3: 0x100, 0x7c4: 0x100, 0x7c5: 0x100, 0x7c6: 0x100, 0x7c7: 0x100,
	0x7c8: 0x100, 0x7c9: 0x100, 0x7ca: 0x100, 0x7cb: 0x100, 0x7cc: 0x100, 0x7cd: 0x100, 0x7ce: 0x100, 0x7cf: 0x100,
	0x7d0: 0x100, 0x7d1: 0x100, 0x7d2: 0x100, 0x7d3: 0x100, 0x7d4: 0x100, 0x7d5: 0x100, 0x7d6: 0x100, 0x7d7: 0x100,
	0x7d8: 0x100, 0x7d9: 0x100, 0x7da: 0x100, 0x7db: 0x100, 0x7dc: 0x100, 0x7dd: 0x100, 0x7de: 0x100, 0x7df: 0x100,
	0x7e0: 0x7c, 0x7e1: 0x7d, 0x7e2: 0x7e, 0x7e3: 0x7f, 0x7e4: 0x80, 0x7e5: 0x81, 0x7e6: 0x82, 0x7e7: 0x83,
	0x7e8: 0x84, 0x7e9: 0x100, 0x7ea: 0x100, 0x7eb: 0x100, 0x7ec: 0x100, 0x7ed: 0x100, 0x7ee: 0x100, 0x7ef: 0x100,
	0x7f0: 0x100, 0x7f1: 0x100, 0x7f2: 0x100, 0x7f3: 0x100, 0x7f4: 0x100, 0x7f5: 0x100, 0x7f6: 0x100, 0x7f7: 0x100,
	0x7f8: 0x100, 0x7f9: 0x100, 0x7fa: 0x100, 0x7fb: 0x100, 0x7fc: 0x100, 0x7fd: 0x100, 0x7fe: 0x100, 0x7ff: 0x100,
	// Block 0x20, offset 0x800
	0x800: 0xa6, 0x801: 0xa6, 0x802: 0xa6, 0x803: 0xa6, 0x804: 0xa6, 0x805: 0xa6, 0x806: 0xa6, 0x807: 0xa6,
	0x808: 0xa6, 0x809: 0xa6, 0x80a: 0xa6, 0x80b: 0xa6, 0x80c: 0xa6, 0x80d: 0x1ae, 0x80e: 0xa6, 0x80f: 0xa6,
	0x810: 0xa6, 0x811: 0xa6, 0x812: 0xa6, 0x813: 0xa6, 0x814: 0xa6, 0x815: 0xa6, 0x816: 0xa6, 0x817: 0xa6,
	0x818: 0xa6, 0x819: 0xa6, 0x81a: 0xa6, 0x81b: 0xa6, 0x81c: 0xa6, 0x81d: 0xa6, 0x81e: 0xa6, 0x81f: 0xa6,
	0x820: 0xa6, 0x821: 0xa6, 0x822: 0xa6, 0x823: 0xa6, 0x824: 0xa6, 0x825: 0xa6, 0x826: 0xa6, 0x827: 0xa6,
	0x828: 0xa6, 0x829: 0xa6, 0x82a: 0xa6, 0x82b: 0xa6, 0x82c: 0xa6, 0x82d: 0xa6, 0x82e: 0xa6, 0x82f: 0xa6,
	0x830: 0xa6, 0x831: 0xa6, 0x832: 0xa6, 0x833: 0xa6, 0x834: 0xa6, 0x835: 0xa6, 0x836: 0xa6, 0x837: 0xa6,
	0x838: 0xa6, 0x839: 0xa6, 0x83a: 0xa6, 0x83b: 0xa6, 0x83c: 0xa6, 0x83d: 0xa6, 0x83e: 0xa6, 0x83f: 0xa6,
	// Block 0x21, offset 0x840
	0x840: 0xa6, 0x841: 0xa6, 0x842: 0xa6, 0x843: 0xa6, 0x844: 0xa6, 0x845: 0xa6, 0x846: 0xa6, 0x847: 0xa6,
	0x848: 0xa6, 0x849: 0xa6, 0x84a: 0xa6, 0x84b: 0xa6, 0x84c: 0xa6, 0x84d: 0xa6, 0x84e: 0x1af, 0x84f: 0x100,
	0x850: 0x100, 0x851: 0x100, 0x852: 0x100, 0x853: 0x100, 0x854: 0x100, 0x855: 0x100, 0x856: 0x100, 0x857: 0x100,
	0x858: 0x100, 0x859: 0x100, 0x85a: 0x100, 0x85b: 0x100, 0x85c: 0x100, 0x85d: 0x100, 0x85e: 0x100, 0x85f: 0x100,
	0x860: 0x100, 0x861: 0x100, 0x862: 0x100, 0x863: 0x100, 0x864: 0x100, 0x865: 0x100, 0x866: 0x100, 0x867: 0x100,
	0x868: 0x100, 0x869: 0x100, 0x86a: 0x100, 0x86b: 0x100, 0x86c: 0x100, 0x86d: 0x100, 0x86e: 0x100, 0x86f: 0x100,
	0x870: 0x100, 0x871: 0x100, 0x872: 0x100, 0x873: 0x100, 0x874: 0x100, 0x875: 0x100, 0x876: 0x100, 0x877: 0x100,
	0x878: 0x100, 0x879: 0x100, 0x87a: 0x100, 0x87b: 0x100, 0x87c: 0x100, 0x87d: 0x100, 0x87e: 0x100, 0x87f: 0x100,
	// Block 0x22, offset 0x880
	0x890: 0x0c, 0x891: 0x0d, 0x892: 0x0e, 0x893: 0x0f, 0x894: 0x10, 0x895: 0x0a, 0x896: 0x11, 0x897: 0x07,
	0x898: 0x12, 0x899: 0x0a, 0x89a: 0x13, 0x89b: 0x14, 0x89c: 0x15, 0x89d: 0x16, 0x89e: 0x17, 0x89f: 0x18,
	0x8a0: 0x07, 0x8a1: 0x07, 0x8a2: 0x07, 0x8a3: 0x07, 0x8a4: 0x07, 0x8a5: 0x07, 0x8a6: 0x07, 0x8a7: 0x07,
	0x8a8: 0x07, 0x8a9: 0x07, 0x8aa: 0x19, 0x8ab: 0x1a, 0x8ac: 0x1b, 0x8ad: 0x07, 0x8ae: 0x1c, 0x8af: 0x1d,
	0x8b0: 0x07, 0x8b1: 0x1e, 0x8b2: 0x1f, 0x8b3: 0x0a, 0x8b4: 0x0a, 0x8b5: 0x0a, 0x8b6: 0x0a, 0x8b7: 0x0a,
	0x8b8: 0x0a, 0x8b9: 0x0a, 0x8ba: 0x0a, 0x8bb: 0x0a, 0x8bc: 0x0a, 0x8bd: 0x0a, 0x8be: 0x0a, 0x8bf: 0x0a,
	// Block 0x23, offset 0x8c0
	0x8c0: 0x0a, 0x8c1: 0x0a, 0x8c2: 0x0a, 0x8c3: 0x0a, 0x8c4: 0x0a, 0x8c5: 0x0a, 0x8c6: 0x0a, 0x8c7: 0x0a,
	0x8c8: 0x0a, 0x8c9: 0x0a, 0x8ca: 0x0a, 0x8cb: 0x0a, 0x8cc: 0x0a, 0x8cd: 0x0a, 0x8ce: 0x0a, 0x8cf: 0x0a,
	0x8d0: 0x0a, 0x8d1: 0x0a, 0x8d2: 0x0a, 0x8d3: 0x0a, 0x8d4: 0x0a, 0x8d5: 0x0a, 0x8d6: 0x0a, 0x8d7: 0x0a,
	0x8d8: 0x0a, 0x8d9: 0x0a, 0x8da: 0x0a, 0x8db: 0x0a, 0x8dc: 0x0a, 0x8dd: 0x0a, 0x8de: 0x0a, 0x8df: 0x0a,
	0x8e0: 0x0a, 0x8e1: 0x0a, 0x8e2: 0x0a, 0x8e3: 0x0a, 0x8e4: 0x0a, 0x8e5: 0x0a, 0x8e6: 0x0a, 0x8e7: 0x0a,
	0x8e8: 0x0a, 0x8e9: 0x0a, 0x8ea: 0x0a, 0x8eb: 0x0a, 0x8ec: 0x0a, 0x8ed: 0x0a, 0x8ee: 0x0a, 0x8ef: 0x0a,
	0x8f0: 0x0a, 0x8f1: 0x0a, 0x8f2: 0x0a, 0x8f3: 0x0a, 0x8f4: 0x0a, 0x8f5: 0x0a, 0x8f6: 0x0a, 0x8f7: 0x0a,
	0x8f8: 0x0a, 0x8f9: 0x0a, 0x8fa: 0x0a, 0x8fb: 0x0a, 0x8fc: 0x0a, 0x8fd: 0x0a, 0x8fe: 0x0a, 0x8ff: 0x0a,
	// Block 0x24, offset 0x900
	0x900: 0x1b0, 0x901: 0x1b1, 0x902: 0x100, 0x903: 0x100, 0x904: 0x1b2, 0x905: 0x1b2, 0x906: 0x1b2, 0x907: 0x1b3,
	0x908: 0x100, 0x909: 0x100, 0x90a: 0x100, 0x90b: 0x100, 0x90c: 0x100, 0x90d: 0x100, 0x90e: 0x100, 0x90f: 0x100,
	0x910: 0x100, 0x911: 0x100, 0x912: 0x100, 0x913: 0x100, 0x914: 0x100, 0x915: 0x100, 0x916: 0x100, 0x917: 0x100,
	0x918: 0x100, 0x919: 0x100, 0x91a: 0x100, 0x91b: 0x100, 0x91c: 0x100, 0x91d: 0x100, 0x91e: 0x100, 0x91f: 0x100,
	0x920: 0x100, 0x921: 0x100, 0x922: 0x100, 0x923: 0x100, 0x924: 0x100, 0x925: 0x100, 0x926: 0x100, 0x927: 0x100,
	0x928: 0x100, 0x929: 0x100, 0x92a: 0x100, 0x92b: 0x100, 0x92c: 0x100, 0x92d: 0x100, 0x92e: 0x100, 0x92f: 0x100,
	0x930: 0x100, 0x931: 0x100, 0x932: 0x100, 0x933: 0x100, 0x934: 0x100, 0x935: 0x100, 0x936: 0x100, 0x937: 0x100,
	0x938: 0x100, 0x939: 0x100, 0x93a: 0x100, 0x93b: 0x100, 0x93c: 0x100, 0x93d: 0x100, 0x93e: 0x100, 0x93f: 0x100,
	// Block 0x25, offset 0x940
	0x940: 0x0a, 0x941: 0x0a, 0x942: 0x0a, 0x943: 0x0a, 0x944: 0x0a, 0x945: 0x0a, 0x946: 0x0a, 0x947: 0x0a,
	0x948: 0x0a, 0x949: 0x0a, 0x94a: 0x0a, 0x94b: 0x0a, 0x94c: 0x0a, 0x94d: 0x0a, 0x94e: 0x0a, 0x94f: 0x0a,
	0x950: 0x0a, 0x951: 0x0a, 0x952: 0x0a, 0x953: 0x0a, 0x954: 0x0a, 0x955: 0x0a, 0x956: 0x0a, 0x957: 0x0a,
	0x958: 0x0a, 0x959: 0x0a, 0x95a: 0x0a, 0x95b: 0x0a, 0x95c: 0x0a, 0x95d: 0x0a, 0x95e: 0x0a, 0x95f: 0x0a,
	0x960: 0x22, 0x961: 0x0a, 0x962: 0x0a, 0x963: 0x0a, 0x964: 0x0a, 0x965: 0x0a, 0x966: 0x0a, 0x967: 0x0a,
	0x968: 0x0a, 0x969: 0x0a, 0x96a: 0x0a, 0x96b: 0x0a, 0x96c: 0x0a, 0x96d: 0x0a, 0x96e: 0x0a, 0x96f: 0x0a,
	0x970: 0x0a, 0x971: 0x0a, 0x972: 0x0a, 0x973: 0x0a, 0x974: 0x0a, 0x975: 0x0a, 0x976: 0x0a, 0x977: 0x0a,
	0x978: 0x0a, 0x979: 0x0a, 0x97a: 0x0a, 0x97b: 0x0a, 0x97c: 0x0a, 0x97d: 0x0a, 0x97e: 0x0a, 0x97f: 0x0a,
	// Block 0x26, offset 0x980
	0x980: 0x0a, 0x981: 0x0a, 0x982: 0x0a, 0x983: 0x0a, 0x984: 0x0a, 0x985: 0x0a, 0x986: 0x0a, 0x987: 0x0a,
	0x988: 0x0a, 0x989: 0x0a, 0x98a: 0x0a, 0x98b: 0x0a, 0x98c: 0x0a, 0x98d: 0x0a, 0x98e: 0x0a, 0x98f: 0x0a,
}

// idnaSparseOffset: 303 entries, 606 bytes
var idnaSparseOffset = []uint16{0x0, 0x8, 0x19, 0x25, 0x27, 0x2c, 0x33, 0x3e, 0x4a, 0x4e, 0x5d, 0x62, 0x6c, 0x78, 0x7e, 0x87, 0x97, 0xa6, 0xb1, 0xbe, 0xcf, 0xd9, 0xe0, 0xed, 0xfe, 0x105, 0x110, 0x11f, 0x12d, 0x137, 0x139, 0x13e, 0x141, 0x144, 0x146, 0x152, 0x15d, 0x165, 0x16b, 0x171, 0x176, 0x17b, 0x17e, 0x182, 0x188, 0x18d, 0x198, 0x1a2, 0x1a8, 0x1b9, 0x1c4, 0x1c7, 0x1cf, 0x1d2, 0x1df, 0x1e7, 0x1eb, 0x1f2, 0x1fa, 0x20a, 0x216, 0x219, 0x223, 0x22f, 0x23b, 0x247, 0x24f, 0x254, 0x261, 0x272, 0x27d, 0x282, 0x28b, 0x293, 0x299, 0x29e, 0x2a1, 0x2a5, 0x2ab, 0x2af, 0x2b3, 0x2b7, 0x2bc, 0x2c4, 0x2cb, 0x2d6, 0x2e0, 0x2e4, 0x2e7, 0x2ed, 0x2f1, 0x2f3, 0x2f6, 0x2f8, 0x2fb, 0x305, 0x308, 0x317, 0x31b, 0x31f, 0x321, 0x32a, 0x32e, 0x333, 0x338, 0x33e, 0x34e, 0x354, 0x358, 0x367, 0x36c, 0x374, 0x37e, 0x389, 0x391, 0x3a2, 0x3ab, 0x3bb, 0x3c8, 0x3d4, 0x3d9, 0x3e6, 0x3ea, 0x3ef, 0x3f1, 0x3f3, 0x3f7, 0x3f9, 0x3fd, 0x406, 0x40c, 0x410, 0x420, 0x42a, 0x42f, 0x432, 0x438, 0x43f, 0x444, 0x448, 0x44e, 0x453, 0x45c, 0x461, 0x467, 0x46e, 0x475, 0x47c, 0x480, 0x483, 0x488, 0x494, 0x49a, 0x49f, 0x4a6, 0x4ae, 0x4b3, 0x4b7, 0x4c7, 0x4ce, 0x4d2, 0x4d6, 0x4dd, 0x4df, 0x4e2, 0x4e5, 0x4e9, 0x4f2, 0x4f6, 0x4fe, 0x501, 0x509, 0x514, 0x523, 0x52f, 0x535, 0x542, 0x54e, 0x556, 0x55f, 0x56a, 0x571, 0x580, 0x58d, 0x591, 0x59e, 0x5a7, 0x5ab, 0x5ba, 0x5c2, 0x5cd, 0x5d6, 0x5dc, 0x5e4, 0x5ed, 0x5f9, 0x5fc, 0x608, 0x60b, 0x614, 0x617, 0x61c, 0x625, 0x62a, 0x637, 0x642, 0x64b, 0x656, 0x659, 0x65c, 0x666, 0x66f, 0x67b, 0x688, 0x695, 0x6a3, 0x6aa, 0x6b5, 0x6bc, 0x6c0, 0x6c4, 0x6c7, 0x6cc, 0x6cf, 0x6d2, 0x6d6, 0x6d9, 0x6de, 0x6e5, 0x6e8, 0x6f0, 0x6f4, 0x6ff, 0x702, 0x705, 0x708, 0x70e, 0x714, 0x71d, 0x720, 0x723, 0x726, 0x72e, 0x733, 0x73c, 0x73f, 0x744, 0x74e, 0x752, 0x756, 0x759, 0x75c, 0x760, 0x76f, 0x77b, 0x77f, 0x784, 0x789, 0x78e, 0x792, 0x797, 0x7a0, 0x7a5, 0x7a9, 0x7af, 0x7b5, 0x7ba, 0x7c0, 0x7c6, 0x7d0, 0x7d6, 0x7df, 0x7e2, 0x7e5, 0x7e9, 0x7ed, 0x7f1, 0x7f7, 0x7fd, 0x802, 0x805, 0x815, 0x81c, 0x820, 0x827, 0x82b, 0x831, 0x838, 0x83f, 0x845, 0x84e, 0x852, 0x860, 0x863, 0x866, 0x86a, 0x86e, 0x871, 0x875, 0x878, 0x87d, 0x87f, 0x881}

// idnaSparseValues: 2180 entries, 8720 bytes
var idnaSparseValues = [2180]valueRange{
	// Block 0x0, offset 0x0
	{value: 0x0000, lo: 0x07},
	{value: 0xe105, lo: 0x80, hi: 0x96},
	{value: 0x0018, lo: 0x97, hi: 0x97},
	{value: 0xe105, lo: 0x98, hi: 0x9e},
	{value: 0x001f, lo: 0x9f, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xb6},
	{value: 0x0018, lo: 0xb7, hi: 0xb7},
	{value: 0x0008, lo: 0xb8, hi: 0xbf},
	// Block 0x1, offset 0x8
	{value: 0x0000, lo: 0x10},
	{value: 0x0008, lo: 0x80, hi: 0x80},
	{value: 0xe01d, lo: 0x81, hi: 0x81},
	{value: 0x0008, lo: 0x82, hi: 0x82},
	{value: 0x0335, lo: 0x83, hi: 0x83},
	{value: 0x034d, lo: 0x84, hi: 0x84},
	{value: 0x0365, lo: 0x85, hi: 0x85},
	{value: 0xe00d, lo: 0x86, hi: 0x86},
	{value: 0x0008, lo: 0x87, hi: 0x87},
	{value: 0xe00d, lo: 0x88, hi: 0x88},
	{value: 0x0008, lo: 0x89, hi: 0x89},
	{value: 0xe00d, lo: 0x8a, hi: 0x8a},
	{value: 0x0008, lo: 0x8b, hi: 0x8b},
	{value: 0xe00d, lo: 0x8c, hi: 0x8c},
	{value: 0x0008, lo: 0x8d, hi: 0x8d},
	{value: 0xe00d, lo: 0x8e, hi: 0x8e},
	{value: 0x0008, lo: 0x8f, hi: 0xbf},
	// Block 0x2, offset 0x19
	{value: 0x0000, lo: 0x0b},
	{value: 0x0008, lo: 0x80, hi: 0xaf},
	{value: 0x00a9, lo: 0xb0, hi: 0xb0},
	{value: 0x037d, lo: 0xb1, hi: 0xb1},
	{value: 0x00b1, lo: 0xb2, hi: 0xb2},
	{value: 0x00b9, lo: 0xb3, hi: 0xb3},
	{value: 0x034d, lo: 0xb4, hi: 0xb4},
	{value: 0x0395, lo: 0xb5, hi: 0xb5},
	{value: 0xe1bd, lo: 0xb6, hi: 0xb6},
	{value: 0x00c1, lo: 0xb7, hi: 0xb7},
	{value: 0x00c9, lo: 0xb8, hi: 0xb8},
	{value: 0x0008, lo: 0xb9, hi: 0xbf},
	// Block 0x3, offset 0x25
	{value: 0x0000, lo: 0x01},
	{value: 0x3308, lo: 0x80, hi: 0xbf},
	// Block 0x4, offset 0x27
	{value: 0x0000, lo: 0x04},
	{value: 0x03f5, lo: 0x80, hi: 0x8f},
	{value: 0xe105, lo: 0x90, hi: 0x9f},
	{value: 0x049d, lo: 0xa0, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbf},
	// Block 0x5, offset 0x2c
	{value: 0x0000, lo: 0x06},
	{value: 0xe185, lo: 0x80, hi: 0x8f},
	{value: 0x0545, lo: 0x90, hi: 0x96},
	{value: 0x0040, lo: 0x97, hi: 0x98},
	{value: 0x0008, lo: 0x99, hi: 0x99},
	{value: 0x0018, lo: 0x9a, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xbf},
	// Block 0x6, offset 0x33
	{value: 0x0000, lo: 0x0a},
	{value: 0x0008, lo: 0x80, hi: 0x86},
	{value: 0x0131, lo: 0x87, hi: 0x87},
	{value: 0x0008, lo: 0x88, hi: 0x88},
	{value: 0x0018, lo: 0x89, hi: 0x8a},
	{value: 0x0040, lo: 0x8b, hi: 0x8c},
	{value: 0x0018, lo: 0x8d, hi: 0x8f},
	{value: 0x0040, lo: 0x90, hi: 0x90},
	{value: 0x3308, lo: 0x91, hi: 0xbd},
	{value: 0x0818, lo: 0xbe, hi: 0xbe},
	{value: 0x3308, lo: 0xbf, hi: 0xbf},
	// Block 0x7, offset 0x3e
	{value: 0x0000, lo: 0x0b},
	{value: 0x0818, lo: 0x80, hi: 0x80},
	{value: 0x3308, lo: 0x81, hi: 0x82},
	{value: 0x0818, lo: 0x83, hi: 0x83},
	{value: 0x3308, lo: 0x84, hi: 0x85},
	{value: 0x0818, lo: 0x86, hi: 0x86},
	{value: 0x3308, lo: 0x87, hi: 0x87},
	{value: 0x0040, lo: 0x88, hi: 0x8f},
	{value: 0x0808, lo: 0x90, hi: 0xaa},
	{value: 0x0040, lo: 0xab, hi: 0xae},
	{value: 0x0808, lo: 0xaf, hi: 0xb4},
	{value: 0x0040, lo: 0xb5, hi: 0xbf},
	// Block 0x8, offset 0x4a
	{value: 0x0000, lo: 0x03},
	{value: 0x0a08, lo: 0x80, hi: 0x87},
	{value: 0x0c08, lo: 0x88, hi: 0x99},
	{value: 0x0a08, lo: 0x9a, hi: 0xbf},
	// Block 0x9, offset 0x4e
	{value: 0x0000, lo: 0x0e},
	{value: 0x3308, lo: 0x80, hi: 0x8a},
	{value: 0x0040, lo: 0x8b, hi: 0x8c},
	{value: 0x0c08, lo: 0x8d, hi: 0x8d},
	{value: 0x0a08, lo: 0x8e, hi: 0x98},
	{value: 0x0c08, lo: 0x99, hi: 0x9b},
	{value: 0x0a08, lo: 0x9c, hi: 0xaa},
	{value: 0x0c08, lo: 0xab, hi: 0xac},
	{value: 0x0a08, lo: 0xad, hi: 0xb0},
	{value: 0x0c08, lo: 0xb1, hi: 0xb1},
	{value: 0x0a08, lo: 0xb2, hi: 0xb2},
	{value: 0x0c08, lo: 0xb3, hi: 0xb4},
	{value: 0x0a08, lo: 0xb5, hi: 0xb7},
	{value: 0x0c08, lo: 0xb8, hi: 0xb9},
	{value: 0x0a08, lo: 0xba, hi: 0xbf},
	// Block 0xa, offset 0x5d
	{value: 0x0000, lo: 0x04},
	{value: 0x0808, lo: 0x80, hi: 0xa5},
	{value: 0x3308, lo: 0xa6, hi: 0xb0},
	{value: 0x0808, lo: 0xb1, hi: 0xb1},
	{value: 0x0040, lo: 0xb2, hi: 0xbf},
	// Block 0xb, offset 0x62
	{value: 0x0000, lo: 0x09},
	{value: 0x0808, lo: 0x80, hi: 0x89},
	{value: 0x0a08, lo: 0x8a, hi: 0xaa},
	{value: 0x3308, lo: 0xab, hi: 0xb3},
	{value: 0x0808, lo: 0xb4, hi: 0xb5},
	{value: 0x0018, lo: 0xb6, hi: 0xb9},
	{value: 0x0818, lo: 0xba, hi: 0xba},
	{value: 0x0040, lo: 0xbb, hi: 0xbc},
	{value: 0x3308, lo: 0xbd, hi: 0xbd},
	{value: 0x0818, lo: 0xbe, hi: 0xbf},
	// Block 0xc, offset 0x6c
	{value: 0x0000, lo: 0x0b},
	{value: 0x0808, lo: 0x80, hi: 0x95},
	{value: 0x3308, lo: 0x96, hi: 0x99},
	{value: 0x0808, lo: 0x9a, hi: 0x9a},
	{value: 0x3308, lo: 0x9b, hi: 0xa3},
	{value: 0x0808, lo: 0xa4, hi: 0xa4},
	{value: 0x3308, lo: 0xa5, hi: 0xa7},
	{value: 0x0808, lo: 0xa8, hi: 0xa8},
	{value: 0x3308, lo: 0xa9, hi: 0xad},
	{value: 0x0040, lo: 0xae, hi: 0xaf},
	{value: 0x0818, lo: 0xb0, hi: 0xbe},
	{value: 0x0040, lo: 0xbf, hi: 0xbf},
	// Block 0xd, offset 0x78
	{value: 0x0000, lo: 0x05},
	{value: 0x0a08, lo: 0x80, hi: 0x88},
	{value: 0x0808, lo: 0x89, hi: 0x89},
	{value: 0x3308, lo: 0x8a, hi: 0xa1},
	{value: 0x0840, lo: 0xa2, hi: 0xa2},
	{value: 0x3308, lo: 0xa3, hi: 0xbf},
	// Block 0xe, offset 0x7e
	{value: 0x0000, lo: 0x08},
	{value: 0x3308, lo: 0x80, hi: 0x82},
	{value: 0x3008, lo: 0x83, hi: 0x83},
	{value: 0x0008, lo: 0x84, hi: 0xb9},
	{value: 0x3308, lo: 0xba, hi: 0xba},
	{value: 0x3008, lo: 0xbb, hi: 0xbb},
	{value: 0x3308, lo: 0xbc, hi: 0xbc},
	{value: 0x0008, lo: 0xbd, hi: 0xbd},
	{value: 0x3008, lo: 0xbe, hi: 0xbf},
	// Block 0xf, offset 0x87
	{value: 0x0000, lo: 0x0f},
	{value: 0x3308, lo: 0x80, hi: 0x80},
	{value: 0x3008, lo: 0x81, hi: 0x82},
	{value: 0x0040, lo: 0x83, hi: 0x85},
	{value: 0x3008, lo: 0x86, hi: 0x88},
	{value: 0x0040, lo: 0x89, hi: 0x89},
	{value: 0x3008, lo: 0x8a, hi: 0x8c},
	{value: 0x3b08, lo: 0x8d, hi: 0x8d},
	{value: 0x0040, lo: 0x8e, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x90},
	{value: 0x0040, lo: 0x91, hi: 0x96},
	{value: 0x3008, lo: 0x97, hi: 0x97},
	{value: 0x0040, lo: 0x98, hi: 0xa5},
	{value: 0x0008, lo: 0xa6, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xba},
	{value: 0x0040, lo: 0xbb, hi: 0xbf},
	// Block 0x10, offset 0x97
	{value: 0x0000, lo: 0x0e},
	{value: 0x3308, lo: 0x80, hi: 0x80},
	{value: 0x3008, lo: 0x81, hi: 0x83},
	{value: 0x3308, lo: 0x84, hi: 0x84},
	{value: 0x0008, lo: 0x85, hi: 0x8c},
	{value: 0x0040, lo: 0x8d, hi: 0x8d},
	{value: 0x0008, lo: 0x8e, hi: 0x90},
	{value: 0x0040, lo: 0x91, hi: 0x91},
	{value: 0x0008, lo: 0x92, hi: 0xa8},
	{value: 0x0040, lo: 0xa9, hi: 0xa9},
	{value: 0x0008, lo: 0xaa, hi: 0xb9},
	{value: 0x0040, lo: 0xba, hi: 0xbb},
	{value: 0x3308, lo: 0xbc, hi: 0xbc},
	{value: 0x0008, lo: 0xbd, hi: 0xbd},
	{value: 0x3308, lo: 0xbe, hi: 0xbf},
	// Block 0x11, offset 0xa6
	{value: 0x0000, lo: 0x0a},
	{value: 0x3308, lo: 0x80, hi: 0x81},
	{value: 0x3008, lo: 0x82, hi: 0x83},
	{value: 0x0008, lo: 0x84, hi: 0x8c},
	{value: 0x0040, lo: 0x8d, hi: 0x8d},
	{value: 0x0008, lo: 0x8e, hi: 0x90},
	{value: 0x0040, lo: 0x91, hi: 0x91},
	{value: 0x0008, lo: 0x92, hi: 0xba},
	{value: 0x3b08, lo: 0xbb, hi: 0xbc},
	{value: 0x0008, lo: 0xbd, hi: 0xbd},
	{value: 0x3008, lo: 0xbe, hi: 0xbf},
	// Block 0x12, offset 0xb1
	{value: 0x0000, lo: 0x0c},
	{value: 0x0040, lo: 0x80, hi: 0x80},
	{value: 0x3308, lo: 0x81, hi: 0x81},
	{value: 0x3008, lo: 0x82, hi: 0x83},
	{value: 0x0040, lo: 0x84, hi: 0x84},
	{value: 0x0008, lo: 0x85, hi: 0x96},
	{value: 0x0040, lo: 0x97, hi: 0x99},
	{value: 0x0008, lo: 0x9a, hi: 0xb1},
	{value: 0x0040, lo: 0xb2, hi: 0xb2},
	{value: 0x0008, lo: 0xb3, hi: 0xbb},
	{value: 0x0040, lo: 0xbc, hi: 0xbc},
	{value: 0x0008, lo: 0xbd, hi: 0xbd},
	{value: 0x0040, lo: 0xbe, hi: 0xbf},
	// Block 0x13, offset 0xbe
	{value: 0x0000, lo: 0x10},
	{value: 0x0008, lo: 0x80, hi: 0x86},
	{value: 0x0040, lo: 0x87, hi: 0x89},
	{value: 0x3b08, lo: 0x8a, hi: 0x8a},
	{value: 0x0040, lo: 0x8b, hi: 0x8e},
	{value: 0x3008, lo: 0x8f, hi: 0x91},
	{value: 0x3308, lo: 0x92, hi: 0x94},
	{value: 0x0040, lo: 0x95, hi: 0x95},
	{value: 0x3308, lo: 0x96, hi: 0x96},
	{value: 0x0040, lo: 0x97, hi: 0x97},
	{value: 0x3008, lo: 0x98, hi: 0x9f},
	{value: 0x0040, lo: 0xa0, hi: 0xa5},
	{value: 0x0008, lo: 0xa6, hi: 0xaf},
	{value: 0x0040, lo: 0xb0, hi: 0xb1},
	{value: 0x3008, lo: 0xb2, hi: 0xb3},
	{value: 0x0018, lo: 0xb4, hi: 0xb4},
	{value: 0x0040, lo: 0xb5, hi: 0xbf},
	// Block 0x14, offset 0xcf
	{value: 0x0000, lo: 0x09},
	{value: 0x0040, lo: 0x80, hi: 0x80},
	{value: 0x0008, lo: 0x81, hi: 0xb0},
	{value: 0x3308, lo: 0xb1, hi: 0xb1},
	{value: 0x0008, lo: 0xb2, hi: 0xb2},
	{value: 0x01f1, lo: 0xb3, hi: 0xb3},
	{value: 0x3308, lo: 0xb4, hi: 0xb9},
	{value: 0x3b08, lo: 0xba, hi: 0xba},
	{value: 0x0040, lo: 0xbb, hi: 0xbe},
	{value: 0x0018, lo: 0xbf, hi: 0xbf},
	// Block 0x15, offset 0xd9
	{value: 0x0000, lo: 0x06},
	{value: 0x0008, lo: 0x80, hi: 0x86},
	{value: 0x3308, lo: 0x87, hi: 0x8e},
	{value: 0x0018, lo: 0x8f, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0018, lo: 0x9a, hi: 0x9b},
	{value: 0x0040, lo: 0x9c, hi: 0xbf},
	// Block 0x16, offset 0xe0
	{value: 0x0000, lo: 0x0c},
	{value: 0x0008, lo: 0x80, hi: 0x84},
	{value: 0x0040, lo: 0x85, hi: 0x85},
	{value: 0x0008, lo: 0x86, hi: 0x86},
	{value: 0x0040, lo: 0x87, hi: 0x87},
	{value: 0x3308, lo: 0x88, hi: 0x8e},
	{value: 0x0040, lo: 0x8f, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0x9b},
	{value: 0x0201, lo: 0x9c, hi: 0x9c},
	{value: 0x0209, lo: 0x9d, hi: 0x9d},
	{value: 0x0008, lo: 0x9e, hi: 0x9f},
	{value: 0x0040, lo: 0xa0, hi: 0xbf},
	// Block 0x17, offset 0xed
	{value: 0x0000, lo: 0x10},
	{value: 0x0008, lo: 0x80, hi: 0x80},
	{value: 0x0018, lo: 0x81, hi: 0x8a},
	{value: 0x0008, lo: 0x8b, hi: 0x8b},
	{value: 0xe03d, lo: 0x8c, hi: 0x8c},
	{value: 0x0018, lo: 0x8d, hi: 0x97},
	{value: 0x3308, lo: 0x98, hi: 0x99},
	{value: 0x0018, lo: 0x9a, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xa9},
	{value: 0x0018, lo: 0xaa, hi: 0xb4},
	{value: 0x3308, lo: 0xb5, hi: 0xb5},
	{value: 0x0018, lo: 0xb6, hi: 0xb6},
	{value: 0x3308, lo: 0xb7, hi: 0xb7},
	{value: 0x0018, lo: 0xb8, hi: 0xb8},
	{value: 0x3308, lo: 0xb9, hi: 0xb9},
	{value: 0x0018, lo: 0xba, hi: 0xbd},
	{value: 0x3008, lo: 0xbe, hi: 0xbf},
	// Block 0x18, offset 0xfe
	{value: 0x0000, lo: 0x06},
	{value: 0x0018, lo: 0x80, hi: 0x85},
	{value: 0x3308, lo: 0x86, hi: 0x86},
	{value: 0x0018, lo: 0x87, hi: 0x8c},
	{value: 0x0040, lo: 0x8d, hi: 0x8d},
	{value: 0x0018, lo: 0x8e, hi: 0x9a},
	{value: 0x0040, lo: 0x9b, hi: 0xbf},
	// Block 0x19, offset 0x105
	{value: 0x0000, lo: 0x0a},
	{value: 0x0008, lo: 0x80, hi: 0xaa},
	{value: 0x3008, lo: 0xab, hi: 0xac},
	{value: 0x3308, lo: 0xad, hi: 0xb0},
	{value: 0x3008, lo: 0xb1, hi: 0xb1},
	{value: 0x3308, lo: 0xb2, hi: 0xb7},
	{value: 0x3008, lo: 0xb8, hi: 0xb8},
	{value: 0x3b08, lo: 0xb9, hi: 0xba},
	{value: 0x3008, lo: 0xbb, hi: 0xbc},
	{value: 0x3308, lo: 0xbd, hi: 0xbe},
	{value: 0x0008, lo: 0xbf, hi: 0xbf},
	// Block 0x1a, offset 0x110
	{value: 0x0000, lo: 0x0e},
	{value: 0x0008, lo: 0x80, hi: 0x89},
	{value: 0x0018, lo: 0x8a, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x95},
	{value: 0x3008, lo: 0x96, hi: 0x97},
	{value: 0x3308, lo: 0x98, hi: 0x99},
	{value: 0x0008, lo: 0x9a, hi: 0x9d},
	{value: 0x3308, lo: 0x9e, hi: 0xa0},
	{value: 0x0008, lo: 0xa1, hi: 0xa1},
	{value: 0x3008, lo: 0xa2, hi: 0xa4},
	{value: 0x0008, lo: 0xa5, hi: 0xa6},
	{value: 0x3008, lo: 0xa7, hi: 0xad},
	{value: 0x0008, lo: 0xae, hi: 0xb0},
	{value: 0x3308, lo: 0xb1, hi: 0xb4},
	{value: 0x0008, lo: 0xb5, hi: 0xbf},
	// Block 0x1b, offset 0x11f
	{value: 0x0000, lo: 0x0d},
	{value: 0x0008, lo: 0x80, hi: 0x81},
	{value: 0x3308, lo: 0x82, hi: 0x82},
	{value: 0x3008, lo: 0x83, hi: 0x84},
	{value: 0x3308, lo: 0x85, hi: 0x86},
	{value: 0x3008, lo: 0x87, hi: 0x8c},
	{value: 0x3308, lo: 0x8d, hi: 0x8d},
	{value: 0x0008, lo: 0x8e, hi: 0x8e},
	{value: 0x3008, lo: 0x8f, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x3008, lo: 0x9a, hi: 0x9c},
	{value: 0x3308, lo: 0x9d, hi: 0x9d},
	{value: 0x0018, lo: 0x9e, hi: 0x9f},
	{value: 0x0040, lo: 0xa0, hi: 0xbf},
	// Block 0x1c, offset 0x12d
	{value: 0x0000, lo: 0x09},
	{value: 0x0040, lo: 0x80, hi: 0x86},
	{value: 0x055d, lo: 0x87, hi: 0x87},
	{value: 0x0040, lo: 0x88, hi: 0x8c},
	{value: 0x055d, lo: 0x8d, hi: 0x8d},
	{value: 0x0040, lo: 0x8e, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0xba},
	{value: 0x0018, lo: 0xbb, hi: 0xbb},
	{value: 0xe105, lo: 0xbc, hi: 0xbc},
	{value: 0x0008, lo: 0xbd, hi: 0xbf},
	// Block 0x1d, offset 0x137
	{value: 0x0000, lo: 0x01},
	{value: 0x0018, lo: 0x80, hi: 0xbf},
	// Block 0x1e, offset 0x139
	{value: 0x0000, lo: 0x04},
	{value: 0x0018, lo: 0x80, hi: 0x9e},
	{value: 0x0040, lo: 0x9f, hi: 0xa0},
	{value: 0x2018, lo: 0xa1, hi: 0xb5},
	{value: 0x0018, lo: 0xb6, hi: 0xbf},
	// Block 0x1f, offset 0x13e
	{value: 0x0000, lo: 0x02},
	{value: 0x0018, lo: 0x80, hi: 0xa7},
	{value: 0x2018, lo: 0xa8, hi: 0xbf},
	// Block 0x20, offset 0x141
	{value: 0x0000, lo: 0x02},
	{value: 0x2018, lo: 0x80, hi: 0x82},
	{value: 0x0018, lo: 0x83, hi: 0xbf},
	// Block 0x21, offset 0x144
	{value: 0x0000, lo: 0x01},
	{value: 0x0008, lo: 0x80, hi: 0xbf},
	// Block 0x22, offset 0x146
	{value: 0x0000, lo: 0x0b},
	{value: 0x0008, lo: 0x80, hi: 0x88},
	{value: 0x0040, lo: 0x89, hi: 0x89},
	{value: 0x0008, lo: 0x8a, hi: 0x8d},
	{value: 0x0040, lo: 0x8e, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x96},
	{value: 0x0040, lo: 0x97, hi: 0x97},
	{value: 0x0008, lo: 0x98, hi: 0x98},
	{value: 0x0040, lo: 0x99, hi: 0x99},
	{value: 0x0008, lo: 0x9a, hi: 0x9d},
	{value: 0x0040, lo: 0x9e, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xbf},
	// Block 0x23, offset 0x152
	{value: 0x0000, lo: 0x0a},
	{value: 0x0008, lo: 0x80, hi: 0x88},
	{value: 0x0040, lo: 0x89, hi: 0x89},
	{value: 0x0008, lo: 0x8a, hi: 0x8d},
	{value: 0x0040, lo: 0x8e, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0xb0},
	{value: 0x0040, lo: 0xb1, hi: 0xb1},
	{value: 0x0008, lo: 0xb2, hi: 0xb5},
	{value: 0x0040, lo: 0xb6, hi: 0xb7},
	{value: 0x0008, lo: 0xb8, hi: 0xbe},
	{value: 0x0040, lo: 0xbf, hi: 0xbf},
	// Block 0x24, offset 0x15d
	{value: 0x0000, lo: 0x07},
	{value: 0x0008, lo: 0x80, hi: 0x80},
	{value: 0x0040, lo: 0x81, hi: 0x81},
	{value: 0x0008, lo: 0x82, hi: 0x85},
	{value: 0x0040, lo: 0x86, hi: 0x87},
	{value: 0x0008, lo: 0x88, hi: 0x96},
	{value: 0x0040, lo: 0x97, hi: 0x97},
	{value: 0x0008, lo: 0x98, hi: 0xbf},
	// Block 0x25, offset 0x165
	{value: 0x0000, lo: 0x05},
	{value: 0x0008, lo: 0x80, hi: 0x90},
	{value: 0x0040, lo: 0x91, hi: 0x91},
	{value: 0x0008, lo: 0x92, hi: 0x95},
	{value: 0x0040, lo: 0x96, hi: 0x97},
	{value: 0x0008, lo: 0x98, hi: 0xbf},
	// Block 0x26, offset 0x16b
	{value: 0x0000, lo: 0x05},
	{value: 0x0008, lo: 0x80, hi: 0x9a},
	{value: 0x0040, lo: 0x9b, hi: 0x9c},
	{value: 0x3308, lo: 0x9d, hi: 0x9f},
	{value: 0x0018, lo: 0xa0, hi: 0xbc},
	{value: 0x0040, lo: 0xbd, hi: 0xbf},
	// Block 0x27, offset 0x171
	{value: 0x0000, lo: 0x04},
	{value: 0x0008, lo: 0x80, hi: 0x8f},
	{value: 0x0018, lo: 0x90, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xbf},
	// Block 0x28, offset 0x176
	{value: 0x0000, lo: 0x04},
	{value: 0x0008, lo: 0x80, hi: 0xb5},
	{value: 0x0040, lo: 0xb6, hi: 0xb7},
	{value: 0xe045, lo: 0xb8, hi: 0xbd},
	{value: 0x0040, lo: 0xbe, hi: 0xbf},
	// Block 0x29, offset 0x17b
	{value: 0x0000, lo: 0x02},
	{value: 0x0018, lo: 0x80, hi: 0x80},
	{value: 0x0008, lo: 0x81, hi: 0xbf},
	// Block 0x2a, offset 0x17e
	{value: 0x0000, lo: 0x03},
	{value: 0x0008, lo: 0x80, hi: 0xac},
	{value: 0x0018, lo: 0xad, hi: 0xae},
	{value: 0x0008, lo: 0xaf, hi: 0xbf},
	// Block 0x2b, offset 0x182
	{value: 0x0000, lo: 0x05},
	{value: 0x0040, lo: 0x80, hi: 0x80},
	{value: 0x0008, lo: 0x81, hi: 0x9a},
	{value: 0x0018, lo: 0x9b, hi: 0x9c},
	{value: 0x0040, lo: 0x9d, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xbf},
	// Block 0x2c, offset 0x188
	{value: 0x0000, lo: 0x04},
	{value: 0x0008, lo: 0x80, hi: 0xaa},
	{value: 0x0018, lo: 0xab, hi: 0xb0},
	{value: 0x0008, lo: 0xb1, hi: 0xb8},
	{value: 0x0040, lo: 0xb9, hi: 0xbf},
	// Block 0x2d, offset 0x18d
	{value: 0x0000, lo: 0x0a},
	{value: 0x0008, lo: 0x80, hi: 0x91},
	{value: 0x3308, lo: 0x92, hi: 0x93},
	{value: 0x3b08, lo: 0x94, hi: 0x94},
	{value: 0x3808, lo: 0x95, hi: 0x95},
	{value: 0x0040, lo: 0x96, hi: 0x9e},
	{value: 0x0008, lo: 0x9f, hi: 0xb1},
	{value: 0x3308, lo: 0xb2, hi: 0xb3},
	{value: 0x3808, lo: 0xb4, hi: 0xb4},
	{value: 0x0018, lo: 0xb5, hi: 0xb6},
	{value: 0x0040, lo: 0xb7, hi: 0xbf},
	// Block 0x2e, offset 0x198
	{value: 0x0000, lo: 0x09},
	{value: 0x0008, lo: 0x80, hi: 0x91},
	{value: 0x3308, lo: 0x92, hi: 0x93},
	{value: 0x0040, lo: 0x94, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xac},
	{value: 0x0040, lo: 0xad, hi: 0xad},
	{value: 0x0008, lo: 0xae, hi: 0xb0},
	{value: 0x0040, lo: 0xb1, hi: 0xb1},
	{value: 0x3308, lo: 0xb2, hi: 0xb3},
	{value: 0x0040, lo: 0xb4, hi: 0xbf},
	// Block 0x2f, offset 0x1a2
	{value: 0x0000, lo: 0x05},
	{value: 0x0008, lo: 0x80, hi: 0xb3},
	{value: 0x3340, lo: 0xb4, hi: 0xb5},
	{value: 0x3008, lo: 0xb6, hi: 0xb6},
	{value: 0x3308, lo: 0xb7, hi: 0xbd},
	{value: 0x3008, lo: 0xbe, hi: 0xbf},
	// Block 0x30, offset 0x1a8
	{value: 0x0000, lo: 0x10},
	{value: 0x3008, lo: 0x80, hi: 0x85},
	{value: 0x3308, lo: 0x86, hi: 0x86},
	{value: 0x3008, lo: 0x87, hi: 0x88},
	{value: 0x3308, lo: 0x89, hi: 0x91},
	{value: 0x3b08, lo: 0x92, hi: 0x92},
	{value: 0x3308, lo: 0x93, hi: 0x93},
	{value: 0x0018, lo: 0x94, hi: 0x96},
	{value: 0x0008, lo: 0x97, hi: 0x97},
	{value: 0x0018, lo: 0x98, hi: 0x9b},
	{value: 0x0008, lo: 0x9c, hi: 0x9c},
	{value: 0x3308, lo: 0x9d, hi: 0x9d},
	{value: 0x0040, lo: 0x9e, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xa9},
	{value: 0x0040, lo: 0xaa, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xb9},
	{value: 0x0040, lo: 0xba, hi: 0xbf},
	// Block 0x31, offset 0x1b9
	{value: 0x0000, lo: 0x0a},
	{value: 0x0018, lo: 0x80, hi: 0x85},
	{value: 0x0040, lo: 0x86, hi: 0x86},
	{value: 0x0218, lo: 0x87, hi: 0x87},
	{value: 0x0018, lo: 0x88, hi: 0x8a},
	{value: 0x33c0, lo: 0x8b, hi: 0x8d},
	{value: 0x0040, lo: 0x8e, hi: 0x8e},
	{value: 0x33c0, lo: 0x8f, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0x9f},
	{value: 0x0208, lo: 0xa0, hi: 0xbf},
	// Block 0x32, offset 0x1c4
	{value: 0x0000, lo: 0x02},
	{value: 0x0208, lo: 0x80, hi: 0xb8},
	{value: 0x0040, lo: 0xb9, hi: 0xbf},
	// Block 0x33, offset 0x1c7
	{value: 0x0000, lo: 0x07},
	{value: 0x0008, lo: 0x80, hi: 0x84},
	{value: 0x3308, lo: 0x85, hi: 0x86},
	{value: 0x0208, lo: 0x87, hi: 0xa8},
	{value: 0x3308, lo: 0xa9, hi: 0xa9},
	{value: 0x0208, lo: 0xaa, hi: 0xaa},
	{value: 0x0040, lo: 0xab, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbf},
	// Block 0x34, offset 0x1cf
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0xb5},
	{value: 0x0040, lo: 0xb6, hi: 0xbf},
	// Block 0x35, offset 0x1d2
	{value: 0x0000, lo: 0x0c},
	{value: 0x0008, lo: 0x80, hi: 0x9e},
	{value: 0x0040, lo: 0x9f, hi: 0x9f},
	{value: 0x3308, lo: 0xa0, hi: 0xa2},
	{value: 0x3008, lo: 0xa3, hi: 0xa6},
	{value: 0x3308, lo: 0xa7, hi: 0xa8},
	{value: 0x3008, lo: 0xa9, hi: 0xab},
	{value: 0x0040, lo: 0xac, hi: 0xaf},
	{value: 0x3008, lo: 0xb0, hi: 0xb1},
	{value: 0x3308, lo: 0xb2, hi: 0xb2},
	{value: 0x3008, lo: 0xb3, hi: 0xb8},
	{value: 0x3308, lo: 0xb9, hi: 0xbb},
	{value: 0x0040, lo: 0xbc, hi: 0xbf},
	// Block 0x36, offset 0x1df
	{value: 0x0000, lo: 0x07},
	{value: 0x0018, lo: 0x80, hi: 0x80},
	{value: 0x0040, lo: 0x81, hi: 0x83},
	{value: 0x0018, lo: 0x84, hi: 0x85},
	{value: 0x0008, lo: 0x86, hi: 0xad},
	{value: 0x0040, lo: 0xae, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xb4},
	{value: 0x0040, lo: 0xb5, hi: 0xbf},
	// Block 0x37, offset 0x1e7
	{value: 0x0000, lo: 0x03},
	{value: 0x0008, lo: 0x80, hi: 0xab},
	{value: 0x0040, lo: 0xac, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbf},
	// Block 0x38, offset 0x1eb
	{value: 0x0000, lo: 0x06},
	{value: 0x0008, lo: 0x80, hi: 0x89},
	{value: 0x0040, lo: 0x8a, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0028, lo: 0x9a, hi: 0x9a},
	{value: 0x0040, lo: 0x9b, hi: 0x9d},
	{value: 0x0018, lo: 0x9e, hi: 0xbf},
	// Block 0x39, offset 0x1f2
	{value: 0x0000, lo: 0x07},
	{value: 0x0008, lo: 0x80, hi: 0x96},
	{value: 0x3308, lo: 0x97, hi: 0x98},
	{value: 0x3008, lo: 0x99, hi: 0x9a},
	{value: 0x3308, lo: 0x9b, hi: 0x9b},
	{value: 0x0040, lo: 0x9c, hi: 0x9d},
	{value: 0x0018, lo: 0x9e, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xbf},
	// Block 0x3a, offset 0x1fa
	{value: 0x0000, lo: 0x0f},
	{value: 0x0008, lo: 0x80, hi: 0x94},
	{value: 0x3008, lo: 0x95, hi: 0x95},
	{value: 0x3308, lo: 0x96, hi: 0x96},
	{value: 0x3008, lo: 0x97, hi: 0x97},
	{value: 0x3308, lo: 0x98, hi: 0x9e},
	{value: 0x0040, lo: 0x9f, hi: 0x9f},
	{value: 0x3b08, lo: 0xa0, hi: 0xa0},
	{value: 0x3008, lo: 0xa1, hi: 0xa1},
	{value: 0x3308, lo: 0xa2, hi: 0xa2},
	{value: 0x3008, lo: 0xa3, hi: 0xa4},
	{value: 0x3308, lo: 0xa5, hi: 0xac},
	{value: 0x3008, lo: 0xad, hi: 0xb2},
	{value: 0x3308, lo: 0xb3, hi: 0xbc},
	{value: 0x0040, lo: 0xbd, hi: 0xbe},
	{value: 0x3308, lo: 0xbf, hi: 0xbf},
	// Block 0x3b, offset 0x20a
	{value: 0x0000, lo: 0x0b},
	{value: 0x0008, lo: 0x80, hi: 0x89},
	{value: 0x0040, lo: 0x8a, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0x9f},
	{value: 0x0018, lo: 0xa0, hi: 0xa6},
	{value: 0x0008, lo: 0xa7, hi: 0xa7},
	{value: 0x0018, lo: 0xa8, hi: 0xad},
	{value: 0x0040, lo: 0xae, hi: 0xaf},
	{value: 0x3308, lo: 0xb0, hi: 0xbd},
	{value: 0x3318, lo: 0xbe, hi: 0xbe},
	{value: 0x3308, lo: 0xbf, hi: 0xbf},
	// Block 0x3c, offset 0x216
	{value: 0x0000, lo: 0x02},
	{value: 0x3308, lo: 0x80, hi: 0x8e},
	{value: 0x0040, lo: 0x8f, hi: 0xbf},
	// Block 0x3d, offset 0x219
	{value: 0x0000, lo: 0x09},
	{value: 0x3308, lo: 0x80, hi: 0x83},
	{value: 0x3008, lo: 0x84, hi: 0x84},
	{value: 0x0008, lo: 0x85, hi: 0xb3},
	{value: 0x3308, lo: 0xb4, hi: 0xb4},
	{value: 0x3008, lo: 0xb5, hi: 0xb5},
	{value: 0x3308, lo: 0xb6, hi: 0xba},
	{value: 0x3008, lo: 0xbb, hi: 0xbb},
	{value: 0x3308, lo: 0xbc, hi: 0xbc},
	{value: 0x3008, lo: 0xbd, hi: 0xbf},
	// Block 0x3e, offset 0x223
	{value: 0x0000, lo: 0x0b},
	{value: 0x3008, lo: 0x80, hi: 0x81},
	{value: 0x3308, lo: 0x82, hi: 0x82},
	{value: 0x3008, lo: 0x83, hi: 0x83},
	{value: 0x3808, lo: 0x84, hi: 0x84},
	{value: 0x0008, lo: 0x85, hi: 0x8c},
	{value: 0x0040, lo: 0x8d, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0018, lo: 0x9a, hi: 0xaa},
	{value: 0x3308, lo: 0xab, hi: 0xb3},
	{value: 0x0018, lo: 0xb4, hi: 0xbe},
	{value: 0x0040, lo: 0xbf, hi: 0xbf},
	// Block 0x3f, offset 0x22f
	{value: 0x0000, lo: 0x0b},
	{value: 0x3308, lo: 0x80, hi: 0x81},
	{value: 0x3008, lo: 0x82, hi: 0x82},
	{value: 0x0008, lo: 0x83, hi: 0xa0},
	{value: 0x3008, lo: 0xa1, hi: 0xa1},
	{value: 0x3308, lo: 0xa2, hi: 0xa5},
	{value: 0x3008, lo: 0xa6, hi: 0xa7},
	{value: 0x3308, lo: 0xa8, hi: 0xa9},
	{value: 0x3808, lo: 0xaa, hi: 0xaa},
	{value: 0x3b08, lo: 0xab, hi: 0xab},
	{value: 0x3308, lo: 0xac, hi: 0xad},
	{value: 0x0008, lo: 0xae, hi: 0xbf},
	// Block 0x40, offset 0x23b
	{value: 0x0000, lo: 0x0b},
	{value: 0x0008, lo: 0x80, hi: 0xa5},
	{value: 0x3308, lo: 0xa6, hi: 0xa6},
	{value: 0x3008, lo: 0xa7, hi: 0xa7},
	{value: 0x3308, lo: 0xa8, hi: 0xa9},
	{value: 0x3008, lo: 0xaa, hi: 0xac},
	{value: 0x3308, lo: 0xad, hi: 0xad},
	{value: 0x3008, lo: 0xae, hi: 0xae},
	{value: 0x3308, lo: 0xaf, hi: 0xb1},
	{value: 0x3808, lo: 0xb2, hi: 0xb3},
	{value: 0x0040, lo: 0xb4, hi: 0xbb},
	{value: 0x0018, lo: 0xbc, hi: 0xbf},
	// Block 0x41, offset 0x247
	{value: 0x0000, lo: 0x07},
	{value: 0x0008, lo: 0x80, hi: 0xa3},
	{value: 0x3008, lo: 0xa4, hi: 0xab},
	{value: 0x3308, lo: 0xac, hi: 0xb3},
	{value: 0x3008, lo: 0xb4, hi: 0xb5},
	{value: 0x3308, lo: 0xb6, hi: 0xb7},
	{value: 0x0040, lo: 0xb8, hi: 0xba},
	{value: 0x0018, lo: 0xbb, hi: 0xbf},
	// Block 0x42, offset 0x24f
	{value: 0x0000, lo: 0x04},
	{value: 0x0008, lo: 0x80, hi: 0x89},
	{value: 0x0040, lo: 0x8a, hi: 0x8c},
	{value: 0x0008, lo: 0x8d, hi: 0xbd},
	{value: 0x0018, lo: 0xbe, hi: 0xbf},
	// Block 0x43, offset 0x254
	{value: 0x0000, lo: 0x0c},
	{value: 0x02a9, lo: 0x80, hi: 0x80},
	{value: 0x02b1, lo: 0x81, hi: 0x81},
	{value: 0x02b9, lo: 0x82, hi: 0x82},
	{value: 0x02c1, lo: 0x83, hi: 0x83},
	{value: 0x02c9, lo: 0x84, hi: 0x85},
	{value: 0x02d1, lo: 0x86, hi: 0x86},
	{value: 0x02d9, lo: 0x87, hi: 0x87},
	{value: 0x057d, lo: 0x88, hi: 0x88},
	{value: 0x0040, lo: 0x89, hi: 0x8f},
	{value: 0x059d, lo: 0x90, hi: 0xba},
	{value: 0x0040, lo: 0xbb, hi: 0xbc},
	{value: 0x059d, lo: 0xbd, hi: 0xbf},
	// Block 0x44, offset 0x261
	{value: 0x0000, lo: 0x10},
	{value: 0x0018, lo: 0x80, hi: 0x87},
	{value: 0x0040, lo: 0x88, hi: 0x8f},
	{value: 0x3308, lo: 0x90, hi: 0x92},
	{value: 0x0018, lo: 0x93, hi: 0x93},
	{value: 0x3308, lo: 0x94, hi: 0xa0},
	{value: 0x3008, lo: 0xa1, hi: 0xa1},
	{value: 0x3308, lo: 0xa2, hi: 0xa8},
	{value: 0x0008, lo: 0xa9, hi: 0xac},
	{value: 0x3308, lo: 0xad, hi: 0xad},
	{value: 0x0008, lo: 0xae, hi: 0xb3},
	{value: 0x3308, lo: 0xb4, hi: 0xb4},
	{value: 0x0008, lo: 0xb5, hi: 0xb6},
	{value: 0x3008, lo: 0xb7, hi: 0xb7},
	{value: 0x3308, lo: 0xb8, hi: 0xb9},
	{value: 0x0008, lo: 0xba, hi: 0xba},
	{value: 0x0040, lo: 0xbb, hi: 0xbf},
	// Block 0x45, offset 0x272
	{value: 0x0000, lo: 0x0a},
	{value: 0x0008, lo: 0x80, hi: 0x87},
	{value: 0xe045, lo: 0x88, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x95},
	{value: 0x0040, lo: 0x96, hi: 0x97},
	{value: 0xe045, lo: 0x98, hi: 0x9d},
	{value: 0x0040, lo: 0x9e, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xa7},
	{value: 0xe045, lo: 0xa8, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xb7},
	{value: 0xe045, lo: 0xb8, hi: 0xbf},
	// Block 0x46, offset 0x27d
	{value: 0x0000, lo: 0x04},
	{value: 0x0018, lo: 0x80, hi: 0x80},
	{value: 0x0040, lo: 0x81, hi: 0x8f},
	{value: 0x3318, lo: 0x90, hi: 0xb0},
	{value: 0x0040, lo: 0xb1, hi: 0xbf},
	// Block 0x47, offset 0x282
	{value: 0x0000, lo: 0x08},
	{value: 0x0018, lo: 0x80, hi: 0x82},
	{value: 0x0040, lo: 0x83, hi: 0x83},
	{value: 0x0008, lo: 0x84, hi: 0x84},
	{value: 0x0018, lo: 0x85, hi: 0x88},
	{value: 0x0851, lo: 0x89, hi: 0x89},
	{value: 0x0018, lo: 0x8a, hi: 0x8b},
	{value: 0x0040, lo: 0x8c, hi: 0x8f},
	{value: 0x0018, lo: 0x90, hi: 0xbf},
	// Block 0x48, offset 0x28b
	{value: 0x0000, lo: 0x07},
	{value: 0x0018, lo: 0x80, hi: 0xab},
	{value: 0x0859, lo: 0xac, hi: 0xac},
	{value: 0x0861, lo: 0xad, hi: 0xad},
	{value: 0x0018, lo: 0xae, hi: 0xae},
	{value: 0x0869, lo: 0xaf, hi: 0xaf},
	{value: 0x0871, lo: 0xb0, hi: 0xb0},
	{value: 0x0018, lo: 0xb1, hi: 0xbf},
	// Block 0x49, offset 0x293
	{value: 0x0000, lo: 0x05},
	{value: 0x0018, lo: 0x80, hi: 0x9f},
	{value: 0x0080, lo: 0xa0, hi: 0xa0},
	{value: 0x0018, lo: 0xa1, hi: 0xad},
	{value: 0x0080, lo: 0xae, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xbf},
	// Block 0x4a, offset 0x299
	{value: 0x0000, lo: 0x04},
	{value: 0x0018, lo: 0x80, hi: 0xa8},
	{value: 0x09dd, lo: 0xa9, hi: 0xa9},
	{value: 0x09fd, lo: 0xaa, hi: 0xaa},
	{value: 0x0018, lo: 0xab, hi: 0xbf},
	// Block 0x4b, offset 0x29e
	{value: 0x0000, lo: 0x02},
	{value: 0x0018, lo: 0x80, hi: 0xa6},
	{value: 0x0040, lo: 0xa7, hi: 0xbf},
	// Block 0x4c, offset 0x2a1
	{value: 0x0000, lo: 0x03},
	{value: 0x0018, lo: 0x80, hi: 0x8b},
	{value: 0x0929, lo: 0x8c, hi: 0x8c},
	{value: 0x0018, lo: 0x8d, hi: 0xbf},
	// Block 0x4d, offset 0x2a5
	{value: 0x0000, lo: 0x05},
	{value: 0x0018, lo: 0x80, hi: 0xb3},
	{value: 0x0e7e, lo: 0xb4, hi: 0xb4},
	{value: 0x0932, lo: 0xb5, hi: 0xb5},
	{value: 0x0e9e, lo: 0xb6, hi: 0xb6},
	{value: 0x0018, lo: 0xb7, hi: 0xbf},
	// Block 0x4e, offset 0x2ab
	{value: 0x0000, lo: 0x03},
	{value: 0x0018, lo: 0x80, hi: 0x9b},
	{value: 0x0939, lo: 0x9c, hi: 0x9c},
	{value: 0x0018, lo: 0x9d, hi: 0xbf},
	// Block 0x4f, offset 0x2af
	{value: 0x0000, lo: 0x03},
	{value: 0x0018, lo: 0x80, hi: 0xb3},
	{value: 0x0040, lo: 0xb4, hi: 0xb5},
	{value: 0x0018, lo: 0xb6, hi: 0xbf},
	// Block 0x50, offset 0x2b3
	{value: 0x0000, lo: 0x03},
	{value: 0x0018, lo: 0x80, hi: 0x95},
	{value: 0x0040, lo: 0x96, hi: 0x96},
	{value: 0x0018, lo: 0x97, hi: 0xbf},
	// Block 0x51, offset 0x2b7
	{value: 0x0000, lo: 0x04},
	{value: 0xe185, lo: 0x80, hi: 0x8f},
	{value: 0x03f5, lo: 0x90, hi: 0x9f},
	{value: 0x0ebd, lo: 0xa0, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbf},
	// Block 0x52, offset 0x2bc
	{value: 0x0000, lo: 0x07},
	{value: 0x0008, lo: 0x80, hi: 0xa5},
	{value: 0x0040, lo: 0xa6, hi: 0xa6},
	{value: 0x0008, lo: 0xa7, hi: 0xa7},
	{value: 0x0040, lo: 0xa8, hi: 0xac},
	{value: 0x0008, lo: 0xad, hi: 0xad},
	{value: 0x0040, lo: 0xae, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbf},
	// Block 0x53, offset 0x2c4
	{value: 0x0000, lo: 0x06},
	{value: 0x0008, lo: 0x80, hi: 0xa7},
	{value: 0x0040, lo: 0xa8, hi: 0xae},
	{value: 0xe075, lo: 0xaf, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xb0},
	{value: 0x0040, lo: 0xb1, hi: 0xbe},
	{value: 0x3b08, lo: 0xbf, hi: 0xbf},
	// Block 0x54, offset 0x2cb
	{value: 0x0000, lo: 0x0a},
	{value: 0x0008, lo: 0x80, hi: 0x96},
	{value: 0x0040, lo: 0x97, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xa6},
	{value: 0x0040, lo: 0xa7, hi: 0xa7},
	{value: 0x0008, lo: 0xa8, hi: 0xae},
	{value: 0x0040, lo: 0xaf, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xb6},
	{value: 0x0040, lo: 0xb7, hi: 0xb7},
	{value: 0x0008, lo: 0xb8, hi: 0xbe},
	{value: 0x0040, lo: 0xbf, hi: 0xbf},
	// Block 0x55, offset 0x2d6
	{value: 0x0000, lo: 0x09},
	{value: 0x0008, lo: 0x80, hi: 0x86},
	{value: 0x0040, lo: 0x87, hi: 0x87},
	{value: 0x0008, lo: 0x88, hi: 0x8e},
	{value: 0x0040, lo: 0x8f, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x96},
	{value: 0x0040, lo: 0x97, hi: 0x97},
	{value: 0x0008, lo: 0x98, hi: 0x9e},
	{value: 0x0040, lo: 0x9f, hi: 0x9f},
	{value: 0x3308, lo: 0xa0, hi: 0xbf},
	// Block 0x56, offset 0x2e0
	{value: 0x0000, lo: 0x03},
	{value: 0x0018, lo: 0x80, hi: 0xae},
	{value: 0x0008, lo: 0xaf, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xbf},
	// Block 0x57, offset 0x2e4
	{value: 0x0000, lo: 0x02},
	{value: 0x0018, lo: 0x80, hi: 0x9d},
	{value: 0x0040, lo: 0x9e, hi: 0xbf},
	// Block 0x58, offset 0x2e7
	{value: 0x0000, lo: 0x05},
	{value: 0x0018, lo: 0x80, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0x9a},
	{value: 0x0018, lo: 0x9b, hi: 0x9e},
	{value: 0x0ef5, lo: 0x9f, hi: 0x9f},
	{value: 0x0018, lo: 0xa0, hi: 0xbf},
	// Block 0x59, offset 0x2ed
	{value: 0x0000, lo: 0x03},
	{value: 0x0018, lo: 0x80, hi: 0xb2},
	{value: 0x0f15, lo: 0xb3, hi: 0xb3},
	{value: 0x0040, lo: 0xb4, hi: 0xbf},
	// Block 0x5a, offset 0x2f1
	{value: 0x0020, lo: 0x01},
	{value: 0x0f35, lo: 0x80, hi: 0xbf},
	// Block 0x5b, offset 0x2f3
	{value: 0x0020, lo: 0x02},
	{value: 0x1735, lo: 0x80, hi: 0x8f},
	{value: 0x1915, lo: 0x90, hi: 0xbf},
	// Block 0x5c, offset 0x2f6
	{value: 0x0020, lo: 0x01},
	{value: 0x1f15, lo: 0x80, hi: 0xbf},
	// Block 0x5d, offset 0x2f8
	{value: 0x0000, lo: 0x02},
	{value: 0x0040, lo: 0x80, hi: 0x80},
	{value: 0x0008, lo: 0x81, hi: 0xbf},
	// Block 0x5e, offset 0x2fb
	{value: 0x0000, lo: 0x09},
	{value: 0x0008, lo: 0x80, hi: 0x96},
	{value: 0x0040, lo: 0x97, hi: 0x98},
	{value: 0x3308, lo: 0x99, hi: 0x9a},
	{value: 0x096a, lo: 0x9b, hi: 0x9b},
	{value: 0x0972, lo: 0x9c, hi: 0x9c},
	{value: 0x0008, lo: 0x9d, hi: 0x9e},
	{value: 0x0979, lo: 0x9f, hi: 0x9f},
	{value: 0x0018, lo: 0xa0, hi: 0xa0},
	{value: 0x0008, lo: 0xa1, hi: 0xbf},
	// Block 0x5f, offset 0x305
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0xbe},
	{value: 0x0981, lo: 0xbf, hi: 0xbf},
	// Block 0x60, offset 0x308
	{value: 0x0000, lo: 0x0e},
	{value: 0x0040, lo: 0x80, hi: 0x84},
	{value: 0x0008, lo: 0x85, hi: 0xaf},
	{value: 0x0040, lo: 0xb0, hi: 0xb0},
	{value: 0x2a35, lo: 0xb1, hi: 0xb1},
	{value: 0x2a55, lo: 0xb2, hi: 0xb2},
	{value: 0x2a75, lo: 0xb3, hi: 0xb3},
	{value: 0x2a95, lo: 0xb4, hi: 0xb4},
	{value: 0x2a75, lo: 0xb5, hi: 0xb5},
	{value: 0x2ab5, lo: 0xb6, hi: 0xb6},
	{value: 0x2ad5, lo: 0xb7, hi: 0xb7},
	{value: 0x2af5, lo: 0xb8, hi: 0xb9},
	{value: 0x2b15, lo: 0xba, hi: 0xbb},
	{value: 0x2b35, lo: 0xbc, hi: 0xbd},
	{value: 0x2b15, lo: 0xbe, hi: 0xbf},
	// Block 0x61, offset 0x317
	{value: 0x0000, lo: 0x03},
	{value: 0x0018, lo: 0x80, hi: 0xa3},
	{value: 0x0040, lo: 0xa4, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbf},
	// Block 0x62, offset 0x31b
	{value: 0x0008, lo: 0x03},
	{value: 0x098a, lo: 0x80, hi: 0x9e},
	{value: 0x0040, lo: 0x9f, hi: 0x9f},
	{value: 0x0a82, lo: 0xa0, hi: 0xbf},
	// Block 0x63, offset 0x31f
	{value: 0x0008, lo: 0x01},
	{value: 0x0d19, lo: 0x80, hi: 0xbf},
	// Block 0x64, offset 0x321
	{value: 0x0008, lo: 0x08},
	{value: 0x0f19, lo: 0x80, hi: 0xb0},
	{value: 0x4045, lo: 0xb1, hi: 0xb1},
	{value: 0x10a1, lo: 0xb2, hi: 0xb3},
	{value: 0x4065, lo: 0xb4, hi: 0xb4},
	{value: 0x10b1, lo: 0xb5, hi: 0xb7},
	{value: 0x4085, lo: 0xb8, hi: 0xb8},
	{value: 0x4085, lo: 0xb9, hi: 0xb9},
	{value: 0x10c9, lo: 0xba, hi: 0xbf},
	// Block 0x65, offset 0x32a
	{value: 0x0000, lo: 0x03},
	{value: 0x0008, lo: 0x80, hi: 0x8c},
	{value: 0x0040, lo: 0x8d, hi: 0x8f},
	{value: 0x0018, lo: 0x90, hi: 0xbf},
	// Block 0x66, offset 0x32e
	{value: 0x0000, lo: 0x04},
	{value: 0x0018, lo: 0x80, hi: 0x86},
	{value: 0x0040, lo: 0x87, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0xbd},
	{value: 0x0018, lo: 0xbe, hi: 0xbf},
	// Block 0x67, offset 0x333
	{value: 0x0000, lo: 0x04},
	{value: 0x0008, lo: 0x80, hi: 0x8c},
	{value: 0x0018, lo: 0x8d, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0xab},
	{value: 0x0040, lo: 0xac, hi: 0xbf},
	// Block 0x68, offset 0x338
	{value: 0x0000, lo: 0x05},
	{value: 0x0008, lo: 0x80, hi: 0xa5},
	{value: 0x0018, lo: 0xa6, hi: 0xaf},
	{value: 0x3308, lo: 0xb0, hi: 0xb1},
	{value: 0x0018, lo: 0xb2, hi: 0xb7},
	{value: 0x0040, lo: 0xb8, hi: 0xbf},
	// Block 0x69, offset 0x33e
	{value: 0x0000, lo: 0x0f},
	{value: 0x0008, lo: 0x80, hi: 0x81},
	{value: 0x3308, lo: 0x82, hi: 0x82},
	{value: 0x0008, lo: 0x83, hi: 0x85},
	{value: 0x3b08, lo: 0x86, hi: 0x86},
	{value: 0x0008, lo: 0x87, hi: 0x8a},
	{value: 0x3308, lo: 0x8b, hi: 0x8b},
	{value: 0x0008, lo: 0x8c, hi: 0xa2},
	{value: 0x3008, lo: 0xa3, hi: 0xa4},
	{value: 0x3308, lo: 0xa5, hi: 0xa6},
	{value: 0x3008, lo: 0xa7, hi: 0xa7},
	{value: 0x0018, lo: 0xa8, hi: 0xab},
	{value: 0x3b08, lo: 0xac, hi: 0xac},
	{value: 0x0040, lo: 0xad, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xb9},
	{value: 0x0040, lo: 0xba, hi: 0xbf},
	// Block 0x6a, offset 0x34e
	{value: 0x0000, lo: 0x05},
	{value: 0x0208, lo: 0x80, hi: 0xb1},
	{value: 0x0108, lo: 0xb2, hi: 0xb2},
	{value: 0x0008, lo: 0xb3, hi: 0xb3},
	{value: 0x0018, lo: 0xb4, hi: 0xb7},
	{value: 0x0040, lo: 0xb8, hi: 0xbf},
	// Block 0x6b, offset 0x354
	{value: 0x0000, lo: 0x03},
	{value: 0x3008, lo: 0x80, hi: 0x81},
	{value: 0x0008, lo: 0x82, hi: 0xb3},
	{value: 0x3008, lo: 0xb4, hi: 0xbf},
	// Block 0x6c, offset 0x358
	{value: 0x0000, lo: 0x0e},
	{value: 0x3008, lo: 0x80, hi: 0x83},
	{value: 0x3b08, lo: 0x84, hi: 0x84},
	{value: 0x3308, lo: 0x85, hi: 0x85},
	{value: 0x0040, lo: 0x86, hi: 0x8d},
	{value: 0x0018, lo: 0x8e, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0x9f},
	{value: 0x3308, lo: 0xa0, hi: 0xb1},
	{value: 0x0008, lo: 0xb2, hi: 0xb7},
	{value: 0x0018, lo: 0xb8, hi: 0xba},
	{value: 0x0008, lo: 0xbb, hi: 0xbb},
	{value: 0x0018, lo: 0xbc, hi: 0xbc},
	{value: 0x0008, lo: 0xbd, hi: 0xbe},
	{value: 0x3308, lo: 0xbf, hi: 0xbf},
	// Block 0x6d, offset 0x367
	{value: 0x0000, lo: 0x04},
	{value: 0x0008, lo: 0x80, hi: 0xa5},
	{value: 0x3308, lo: 0xa6, hi: 0xad},
	{value: 0x0018, lo: 0xae, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbf},
	// Block 0x6e, offset 0x36c
	{value: 0x0000, lo: 0x07},
	{value: 0x0008, lo: 0x80, hi: 0x86},
	{value: 0x3308, lo: 0x87, hi: 0x91},
	{value: 0x3008, lo: 0x92, hi: 0x92},
	{value: 0x3808, lo: 0x93, hi: 0x93},
	{value: 0x0040, lo: 0x94, hi: 0x9e},
	{value: 0x0018, lo: 0x9f, hi: 0xbc},
	{value: 0x0040, lo: 0xbd, hi: 0xbf},
	// Block 0x6f, offset 0x374
	{value: 0x0000, lo: 0x09},
	{value: 0x3308, lo: 0x80, hi: 0x82},
	{value: 0x3008, lo: 0x83, hi: 0x83},
	{value: 0x0008, lo: 0x84, hi: 0xb2},
	{value: 0x3308, lo: 0xb3, hi: 0xb3},
	{value: 0x3008, lo: 0xb4, hi: 0xb5},
	{value: 0x3308, lo: 0xb6, hi: 0xb9},
	{value: 0x3008, lo: 0xba, hi: 0xbb},
	{value: 0x3308, lo: 0xbc, hi: 0xbd},
	{value: 0x3008, lo: 0xbe, hi: 0xbf},
	// Block 0x70, offset 0x37e
	{value: 0x0000, lo: 0x0a},
	{value: 0x3808, lo: 0x80, hi: 0x80},
	{value: 0x0018, lo: 0x81, hi: 0x8d},
	{value: 0x0040, lo: 0x8e, hi: 0x8e},
	{value: 0x0008, lo: 0x8f, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0x9d},
	{value: 0x0018, lo: 0x9e, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xa4},
	{value: 0x3308, lo: 0xa5, hi: 0xa5},
	{value: 0x0008, lo: 0xa6, hi: 0xbe},
	{value: 0x0040, lo: 0xbf, hi: 0xbf},
	// Block 0x71, offset 0x389
	{value: 0x0000, lo: 0x07},
	{value: 0x0008, lo: 0x80, hi: 0xa8},
	{value: 0x3308, lo: 0xa9, hi: 0xae},
	{value: 0x3008, lo: 0xaf, hi: 0xb0},
	{value: 0x3308, lo: 0xb1, hi: 0xb2},
	{value: 0x3008, lo: 0xb3, hi: 0xb4},
	{value: 0x3308, lo: 0xb5, hi: 0xb6},
	{value: 0x0040, lo: 0xb7, hi: 0xbf},
	// Block 0x72, offset 0x391
	{value: 0x0000, lo: 0x10},
	{value: 0x0008, lo: 0x80, hi: 0x82},
	{value: 0x3308, lo: 0x83, hi: 0x83},
	{value: 0x0008, lo: 0x84, hi: 0x8b},
	{value: 0x3308, lo: 0x8c, hi: 0x8c},
	{value: 0x3008, lo: 0x8d, hi: 0x8d},
	{value: 0x0040, lo: 0x8e, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0x9b},
	{value: 0x0018, lo: 0x9c, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xb6},
	{value: 0x0018, lo: 0xb7, hi: 0xb9},
	{value: 0x0008, lo: 0xba, hi: 0xba},
	{value: 0x3008, lo: 0xbb, hi: 0xbb},
	{value: 0x3308, lo: 0xbc, hi: 0xbc},
	{value: 0x3008, lo: 0xbd, hi: 0xbd},
	{value: 0x0008, lo: 0xbe, hi: 0xbf},
	// Block 0x73, offset 0x3a2
	{value: 0x0000, lo: 0x08},
	{value: 0x0008, lo: 0x80, hi: 0xaf},
	{value: 0x3308, lo: 0xb0, hi: 0xb0},
	{value: 0x0008, lo: 0xb1, hi: 0xb1},
	{value: 0x3308, lo: 0xb2, hi: 0xb4},
	{value: 0x0008, lo: 0xb5, hi: 0xb6},
	{value: 0x3308, lo: 0xb7, hi: 0xb8},
	{value: 0x0008, lo: 0xb9, hi: 0xbd},
	{value: 0x3308, lo: 0xbe, hi: 0xbf},
	// Block 0x74, offset 0x3ab
	{value: 0x0000, lo: 0x0f},
	{value: 0x0008, lo: 0x80, hi: 0x80},
	{value: 0x3308, lo: 0x81, hi: 0x81},
	{value: 0x0008, lo: 0x82, hi: 0x82},
	{value: 0x0040, lo: 0x83, hi: 0x9a},
	{value: 0x0008, lo: 0x9b, hi: 0x9d},
	{value: 0x0018, lo: 0x9e, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xaa},
	{value: 0x3008, lo: 0xab, hi: 0xab},
	{value: 0x3308, lo: 0xac, hi: 0xad},
	{value: 0x3008, lo: 0xae, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xb1},
	{value: 0x0008, lo: 0xb2, hi: 0xb4},
	{value: 0x3008, lo: 0xb5, hi: 0xb5},
	{value: 0x3b08, lo: 0xb6, hi: 0xb6},
	{value: 0x0040, lo: 0xb7, hi: 0xbf},
	// Block 0x75, offset 0x3bb
	{value: 0x0000, lo: 0x0c},
	{value: 0x0040, lo: 0x80, hi: 0x80},
	{value: 0x0008, lo: 0x81, hi: 0x86},
	{value: 0x0040, lo: 0x87, hi: 0x88},
	{value: 0x0008, lo: 0x89, hi: 0x8e},
	{value: 0x0040, lo: 0x8f, hi: 0x90},
	{value: 0x0008, lo: 0x91, hi: 0x96},
	{value: 0x0040, lo: 0x97, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xa6},
	{value: 0x0040, lo: 0xa7, hi: 0xa7},
	{value: 0x0008, lo: 0xa8, hi: 0xae},
	{value: 0x0040, lo: 0xaf, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbf},
	// Block 0x76, offset 0x3c8
	{value: 0x0000, lo: 0x0b},
	{value: 0x0008, lo: 0x80, hi: 0x9a},
	{value: 0x0018, lo: 0x9b, hi: 0x9b},
	{value: 0x449d, lo: 0x9c, hi: 0x9c},
	{value: 0x44b5, lo: 0x9d, hi: 0x9d},
	{value: 0x0941, lo: 0x9e, hi: 0x9e},
	{value: 0xe06d, lo: 0x9f, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xa8},
	{value: 0x13f9, lo: 0xa9, hi: 0xa9},
	{value: 0x0018, lo: 0xaa, hi: 0xab},
	{value: 0x0040, lo: 0xac, hi: 0xaf},
	{value: 0x44cd, lo: 0xb0, hi: 0xbf},
	// Block 0x77, offset 0x3d4
	{value: 0x0000, lo: 0x04},
	{value: 0x44ed, lo: 0x80, hi: 0x8f},
	{value: 0x450d, lo: 0x90, hi: 0x9f},
	{value: 0x452d, lo: 0xa0, hi: 0xaf},
	{value: 0x450d, lo: 0xb0, hi: 0xbf},
	// Block 0x78, offset 0x3d9
	{value: 0x0000, lo: 0x0c},
	{value: 0x0008, lo: 0x80, hi: 0xa2},
	{value: 0x3008, lo: 0xa3, hi: 0xa4},
	{value: 0x3308, lo: 0xa5, hi: 0xa5},
	{value: 0x3008, lo: 0xa6, hi: 0xa7},
	{value: 0x3308, lo: 0xa8, hi: 0xa8},
	{value: 0x3008, lo: 0xa9, hi: 0xaa},
	{value: 0x0018, lo: 0xab, hi: 0xab},
	{value: 0x3008, lo: 0xac, hi: 0xac},
	{value: 0x3b08, lo: 0xad, hi: 0xad},
	{value: 0x0040, lo: 0xae, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xb9},
	{value: 0x0040, lo: 0xba, hi: 0xbf},
	// Block 0x79, offset 0x3e6
	{value: 0x0000, lo: 0x03},
	{value: 0x0008, lo: 0x80, hi: 0xa3},
	{value: 0x0040, lo: 0xa4, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xbf},
	// Block 0x7a, offset 0x3ea
	{value: 0x0000, lo: 0x04},
	{value: 0x0018, lo: 0x80, hi: 0x86},
	{value: 0x0040, lo: 0x87, hi: 0x8a},
	{value: 0x0018, lo: 0x8b, hi: 0xbb},
	{value: 0x0040, lo: 0xbc, hi: 0xbf},
	// Block 0x7b, offset 0x3ef
	{value: 0x0000, lo: 0x01},
	{value: 0x0040, lo: 0x80, hi: 0xbf},
	// Block 0x7c, offset 0x3f1
	{value: 0x0020, lo: 0x01},
	{value: 0x454d, lo: 0x80, hi: 0xbf},
	// Block 0x7d, offset 0x3f3
	{value: 0x0020, lo: 0x03},
	{value: 0x4d4d, lo: 0x80, hi: 0x94},
	{value: 0x4b0d, lo: 0x95, hi: 0x95},
	{value: 0x4fed, lo: 0x96, hi: 0xbf},
	// Block 0x7e, offset 0x3f7
	{value: 0x0020, lo: 0x01},
	{value: 0x552d, lo: 0x80, hi: 0xbf},
	// Block 0x7f, offset 0x3f9
	{value: 0x0020, lo: 0x03},
	{value: 0x5d2d, lo: 0x80, hi: 0x84},
	{value: 0x568d, lo: 0x85, hi: 0x85},
	{value: 0x5dcd, lo: 0x86, hi: 0xbf},
	// Block 0x80, offset 0x3fd
	{value: 0x0020, lo: 0x08},
	{value: 0x6b8d, lo: 0x80, hi: 0x8f},
	{value: 0x6d4d, lo: 0x90, hi: 0x90},
	{value: 0x6d8d, lo: 0x91, hi: 0xab},
	{value: 0x1401, lo: 0xac, hi: 0xac},
	{value: 0x70ed, lo: 0xad, hi: 0xad},
	{value: 0x0040, lo: 0xae, hi: 0xae},
	{value: 0x0040, lo: 0xaf, hi: 0xaf},
	{value: 0x710d, lo: 0xb0, hi: 0xbf},
	// Block 0x81, offset 0x406
	{value: 0x0020, lo: 0x05},
	{value: 0x730d, lo: 0x80, hi: 0xad},
	{value: 0x656d, lo: 0xae, hi: 0xae},
	{value: 0x78cd, lo: 0xaf, hi: 0xb5},
	{value: 0x6f8d, lo: 0xb6, hi: 0xb6},
	{value: 0x79ad, lo: 0xb7, hi: 0xbf},
	// Block 0x82, offset 0x40c
	{value: 0x0008, lo: 0x03},
	{value: 0x1751, lo: 0x80, hi: 0x82},
	{value: 0x1741, lo: 0x83, hi: 0x83},
	{value: 0x1769, lo: 0x84, hi: 0xbf},
	// Block 0x83, offset 0x410
	{value: 0x0008, lo: 0x0f},
	{value: 0x1d81, lo: 0x80, hi: 0x83},
	{value: 0x1d99, lo: 0x84, hi: 0x85},
	{value: 0x1da1, lo: 0x86, hi: 0x87},
	{value: 0x1da9, lo: 0x88, hi: 0x8f},
	{value: 0x0040, lo: 0x90, hi: 0x90},
	{value: 0x0040, lo: 0x91, hi: 0x91},
	{value: 0x1de9, lo: 0x92, hi: 0x97},
	{value: 0x1e11, lo: 0x98, hi: 0x9c},
	{value: 0x1e31, lo: 0x9d, hi: 0xb3},
	{value: 0x1d71, lo: 0xb4, hi: 0xb4},
	{value: 0x1d81, lo: 0xb5, hi: 0xb5},
	{value: 0x1ee9, lo: 0xb6, hi: 0xbb},
	{value: 0x1f09, lo: 0xbc, hi: 0xbc},
	{value: 0x1ef9, lo: 0xbd, hi: 0xbd},
	{value: 0x1f19, lo: 0xbe, hi: 0xbf},
	// Block 0x84, offset 0x420
	{value: 0x0000, lo: 0x09},
	{value: 0x0008, lo: 0x80, hi: 0x8b},
	{value: 0x0040, lo: 0x8c, hi: 0x8c},
	{value: 0x0008, lo: 0x8d, hi: 0xa6},
	{value: 0x0040, lo: 0xa7, hi: 0xa7},
	{value: 0x0008, lo: 0xa8, hi: 0xba},
	{value: 0x0040, lo: 0xbb, hi: 0xbb},
	{value: 0x0008, lo: 0xbc, hi: 0xbd},
	{value: 0x0040, lo: 0xbe, hi: 0xbe},
	{value: 0x0008, lo: 0xbf, hi: 0xbf},
	// Block 0x85, offset 0x42a
	{value: 0x0000, lo: 0x04},
	{value: 0x0008, lo: 0x80, hi: 0x8d},
	{value: 0x0040, lo: 0x8e, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x9d},
	{value: 0x0040, lo: 0x9e, hi: 0xbf},
	// Block 0x86, offset 0x42f
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0xba},
	{value: 0x0040, lo: 0xbb, hi: 0xbf},
	// Block 0x87, offset 0x432
	{value: 0x0000, lo: 0x05},
	{value: 0x0018, lo: 0x80, hi: 0x82},
	{value: 0x0040, lo: 0x83, hi: 0x86},
	{value: 0x0018, lo: 0x87, hi: 0xb3},
	{value: 0x0040, lo: 0xb4, hi: 0xb6},
	{value: 0x0018, lo: 0xb7, hi: 0xbf},
	// Block 0x88, offset 0x438
	{value: 0x0000, lo: 0x06},
	{value: 0x0018, lo: 0x80, hi: 0x8e},
	{value: 0x0040, lo: 0x8f, hi: 0x8f},
	{value: 0x0018, lo: 0x90, hi: 0x9c},
	{value: 0x0040, lo: 0x9d, hi: 0x9f},
	{value: 0x0018, lo: 0xa0, hi: 0xa0},
	{value: 0x0040, lo: 0xa1, hi: 0xbf},
	// Block 0x89, offset 0x43f
	{value: 0x0000, lo: 0x04},
	{value: 0x0040, lo: 0x80, hi: 0x8f},
	{value: 0x0018, lo: 0x90, hi: 0xbc},
	{value: 0x3308, lo: 0xbd, hi: 0xbd},
	{value: 0x0040, lo: 0xbe, hi: 0xbf},
	// Block 0x8a, offset 0x444
	{value: 0x0000, lo: 0x03},
	{value: 0x0008, lo: 0x80, hi: 0x9c},
	{value: 0x0040, lo: 0x9d, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xbf},
	// Block 0x8b, offset 0x448
	{value: 0x0000, lo: 0x05},
	{value: 0x0008, lo: 0x80, hi: 0x90},
	{value: 0x0040, lo: 0x91, hi: 0x9f},
	{value: 0x3308, lo: 0xa0, hi: 0xa0},
	{value: 0x0018, lo: 0xa1, hi: 0xbb},
	{value: 0x0040, lo: 0xbc, hi: 0xbf},
	// Block 0x8c, offset 0x44e
	{value: 0x0000, lo: 0x04},
	{value: 0x0008, lo: 0x80, hi: 0x9f},
	{value: 0x0018, lo: 0xa0, hi: 0xa3},
	{value: 0x0040, lo: 0xa4, hi: 0xac},
	{value: 0x0008, lo: 0xad, hi: 0xbf},
	// Block 0x8d, offset 0x453
	{value: 0x0000, lo: 0x08},
	{value: 0x0008, lo: 0x80, hi: 0x80},
	{value: 0x0018, lo: 0x81, hi: 0x81},
	{value: 0x0008, lo: 0x82, hi: 0x89},
	{value: 0x0018, lo: 0x8a, hi: 0x8a},
	{value: 0x0040, lo: 0x8b, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0xb5},
	{value: 0x3308, lo: 0xb6, hi: 0xba},
	{value: 0x0040, lo: 0xbb, hi: 0xbf},
	// Block 0x8e, offset 0x45c
	{value: 0x0000, lo: 0x04},
	{value: 0x0008, lo: 0x80, hi: 0x9d},
	{value: 0x0040, lo: 0x9e, hi: 0x9e},
	{value: 0x0018, lo: 0x9f, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xbf},
	// Block 0x8f, offset 0x461
	{value: 0x0000, lo: 0x05},
	{value: 0x0008, lo: 0x80, hi: 0x83},
	{value: 0x0040, lo: 0x84, hi: 0x87},
	{value: 0x0008, lo: 0x88, hi: 0x8f},
	{value: 0x0018, lo: 0x90, hi: 0x95},
	{value: 0x0040, lo: 0x96, hi: 0xbf},
	// Block 0x90, offset 0x467
	{value: 0x0000, lo: 0x06},
	{value: 0xe145, lo: 0x80, hi: 0x87},
	{value: 0xe1c5, lo: 0x88, hi: 0x8f},
	{value: 0xe145, lo: 0x90, hi: 0x97},
	{value: 0x8b0d, lo: 0x98, hi: 0x9f},
	{value: 0x8b25, lo: 0xa0, hi: 0xa7},
	{value: 0x0008, lo: 0xa8, hi: 0xbf},
	// Block 0x91, offset 0x46e
	{value: 0x0000, lo: 0x06},
	{value: 0x0008, lo: 0x80, hi: 0x9d},
	{value: 0x0040, lo: 0x9e, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xa9},
	{value: 0x0040, lo: 0xaa, hi: 0xaf},
	{value: 0x8b25, lo: 0xb0, hi: 0xb7},
	{value: 0x8b0d, lo: 0xb8, hi: 0xbf},
	// Block 0x92, offset 0x475
	{value: 0x0000, lo: 0x06},
	{value: 0xe145, lo: 0x80, hi: 0x87},
	{value: 0xe1c5, lo: 0x88, hi: 0x8f},
	{value: 0xe145, lo: 0x90, hi: 0x93},
	{value: 0x0040, lo: 0x94, hi: 0x97},
	{value: 0x0008, lo: 0x98, hi: 0xbb},
	{value: 0x0040, lo: 0xbc, hi: 0xbf},
	// Block 0x93, offset 0x47c
	{value: 0x0000, lo: 0x03},
	{value: 0x0008, lo: 0x80, hi: 0xa7},
	{value: 0x0040, lo: 0xa8, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbf},
	// Block 0x94, offset 0x480
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0xb6},
	{value: 0x0040, lo: 0xb7, hi: 0xbf},
	// Block 0x95, offset 0x483
	{value: 0x0000, lo: 0x04},
	{value: 0x0008, lo: 0x80, hi: 0x95},
	{value: 0x0040, lo: 0x96, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xa7},
	{value: 0x0040, lo: 0xa8, hi: 0xbf},
	// Block 0x96, offset 0x488
	{value: 0x0000, lo: 0x0b},
	{value: 0x0808, lo: 0x80, hi: 0x85},
	{value: 0x0040, lo: 0x86, hi: 0x87},
	{value: 0x0808, lo: 0x88, hi: 0x88},
	{value: 0x0040, lo: 0x89, hi: 0x89},
	{value: 0x0808, lo: 0x8a, hi: 0xb5},
	{value: 0x0040, lo: 0xb6, hi: 0xb6},
	{value: 0x0808, lo: 0xb7, hi: 0xb8},
	{value: 0x0040, lo: 0xb9, hi: 0xbb},
	{value: 0x0808, lo: 0xbc, hi: 0xbc},
	{value: 0x0040, lo: 0xbd, hi: 0xbe},
	{value: 0x0808, lo: 0xbf, hi: 0xbf},
	// Block 0x97, offset 0x494
	{value: 0x0000, lo: 0x05},
	{value: 0x0808, lo: 0x80, hi: 0x95},
	{value: 0x0040, lo: 0x96, hi: 0x96},
	{value: 0x0818, lo: 0x97, hi: 0x9f},
	{value: 0x0808, lo: 0xa0, hi: 0xb6},
	{value: 0x0818, lo: 0xb7, hi: 0xbf},
	// Block 0x98, offset 0x49a
	{value: 0x0000, lo: 0x04},
	{value: 0x0808, lo: 0x80, hi: 0x9e},
	{value: 0x0040, lo: 0x9f, hi: 0xa6},
	{value: 0x0818, lo: 0xa7, hi: 0xaf},
	{value: 0x0040, lo: 0xb0, hi: 0xbf},
	// Block 0x99, offset 0x49f
	{value: 0x0000, lo: 0x06},
	{value: 0x0040, lo: 0x80, hi: 0x9f},
	{value: 0x0808, lo: 0xa0, hi: 0xb2},
	{value: 0x0040, lo: 0xb3, hi: 0xb3},
	{value: 0x0808, lo: 0xb4, hi: 0xb5},
	{value: 0x0040, lo: 0xb6, hi: 0xba},
	{value: 0x0818, lo: 0xbb, hi: 0xbf},
	// Block 0x9a, offset 0x4a6
	{value: 0x0000, lo: 0x07},
	{value: 0x0808, lo: 0x80, hi: 0x95},
	{value: 0x0818, lo: 0x96, hi: 0x9b},
	{value: 0x0040, lo: 0x9c, hi: 0x9e},
	{value: 0x0018, lo: 0x9f, hi: 0x9f},
	{value: 0x0808, lo: 0xa0, hi: 0xb9},
	{value: 0x0040, lo: 0xba, hi: 0xbe},
	{value: 0x0818, lo: 0xbf, hi: 0xbf},
	// Block 0x9b, offset 0x4ae
	{value: 0x0000, lo: 0x04},
	{value: 0x0808, lo: 0x80, hi: 0xb7},
	{value: 0x0040, lo: 0xb8, hi: 0xbb},
	{value: 0x0818, lo: 0xbc, hi: 0xbd},
	{value: 0x0808, lo: 0xbe, hi: 0xbf},
	// Block 0x9c, offset 0x4b3
	{value: 0x0000, lo: 0x03},
	{value: 0x0818, lo: 0x80, hi: 0x8f},
	{value: 0x0040, lo: 0x90, hi: 0x91},
	{value: 0x0818, lo: 0x92, hi: 0xbf},
	// Block 0x9d, offset 0x4b7
	{value: 0x0000, lo: 0x0f},
	{value: 0x0808, lo: 0x80, hi: 0x80},
	{value: 0x3308, lo: 0x81, hi: 0x83},
	{value: 0x0040, lo: 0x84, hi: 0x84},
	{value: 0x3308, lo: 0x85, hi: 0x86},
	{value: 0x0040, lo: 0x87, hi: 0x8b},
	{value: 0x3308, lo: 0x8c, hi: 0x8f},
	{value: 0x0808, lo: 0x90, hi: 0x93},
	{value: 0x0040, lo: 0x94, hi: 0x94},
	{value: 0x0808, lo: 0x95, hi: 0x97},
	{value: 0x0040, lo: 0x98, hi: 0x98},
	{value: 0x0808, lo: 0x99, hi: 0xb5},
	{value: 0x0040, lo: 0xb6, hi: 0xb7},
	{value: 0x3308, lo: 0xb8, hi: 0xba},
	{value: 0x0040, lo: 0xbb, hi: 0xbe},
	{value: 0x3b08, lo: 0xbf, hi: 0xbf},
	// Block 0x9e, offset 0x4c7
	{value: 0x0000, lo: 0x06},
	{value: 0x0818, lo: 0x80, hi: 0x88},
	{value: 0x0040, lo: 0x89, hi: 0x8f},
	{value: 0x0818, lo: 0x90, hi: 0x98},
	{value: 0x0040, lo: 0x99, hi: 0x9f},
	{value: 0x0808, lo: 0xa0, hi: 0xbc},
	{value: 0x0818, lo: 0xbd, hi: 0xbf},
	// Block 0x9f, offset 0x4ce
	{value: 0x0000, lo: 0x03},
	{value: 0x0808, lo: 0x80, hi: 0x9c},
	{value: 0x0818, lo: 0x9d, hi: 0x9f},
	{value: 0x0040, lo: 0xa0, hi: 0xbf},
	// Block 0xa0, offset 0x4d2
	{value: 0x0000, lo: 0x03},
	{value: 0x0808, lo: 0x80, hi: 0xb5},
	{value: 0x0040, lo: 0xb6, hi: 0xb8},
	{value: 0x0018, lo: 0xb9, hi: 0xbf},
	// Block 0xa1, offset 0x4d6
	{value: 0x0000, lo: 0x06},
	{value: 0x0808, lo: 0x80, hi: 0x95},
	{value: 0x0040, lo: 0x96, hi: 0x97},
	{value: 0x0818, lo: 0x98, hi: 0x9f},
	{value: 0x0808, lo: 0xa0, hi: 0xb2},
	{value: 0x0040, lo: 0xb3, hi: 0xb7},
	{value: 0x0818, lo: 0xb8, hi: 0xbf},
	// Block 0xa2, offset 0x4dd
	{value: 0x0000, lo: 0x01},
	{value: 0x0808, lo: 0x80, hi: 0xbf},
	// Block 0xa3, offset 0x4df
	{value: 0x0000, lo: 0x02},
	{value: 0x0808, lo: 0x80, hi: 0x88},
	{value: 0x0040, lo: 0x89, hi: 0xbf},
	// Block 0xa4, offset 0x4e2
	{value: 0x0000, lo: 0x02},
	{value: 0x03dd, lo: 0x80, hi: 0xb2},
	{value: 0x0040, lo: 0xb3, hi: 0xbf},
	// Block 0xa5, offset 0x4e5
	{value: 0x0000, lo: 0x03},
	{value: 0x0808, lo: 0x80, hi: 0xb2},
	{value: 0x0040, lo: 0xb3, hi: 0xb9},
	{value: 0x0818, lo: 0xba, hi: 0xbf},
	// Block 0xa6, offset 0x4e9
	{value: 0x0000, lo: 0x08},
	{value: 0x0908, lo: 0x80, hi: 0x80},
	{value: 0x0a08, lo: 0x81, hi: 0xa1},
	{value: 0x0c08, lo: 0xa2, hi: 0xa2},
	{value: 0x0a08, lo: 0xa3, hi: 0xa3},
	{value: 0x3308, lo: 0xa4, hi: 0xa7},
	{value: 0x0040, lo: 0xa8, hi: 0xaf},
	{value: 0x0808, lo: 0xb0, hi: 0xb9},
	{value: 0x0040, lo: 0xba, hi: 0xbf},
	// Block 0xa7, offset 0x4f2
	{value: 0x0000, lo: 0x03},
	{value: 0x0040, lo: 0x80, hi: 0x9f},
	{value: 0x0818, lo: 0xa0, hi: 0xbe},
	{value: 0x0040, lo: 0xbf, hi: 0xbf},
	// Block 0xa8, offset 0x4f6
	{value: 0x0000, lo: 0x07},
	{value: 0x0808, lo: 0x80, hi: 0xa9},
	{value: 0x0040, lo: 0xaa, hi: 0xaa},
	{value: 0x3308, lo: 0xab, hi: 0xac},
	{value: 0x0818, lo: 0xad, hi: 0xad},
	{value: 0x0040, lo: 0xae, hi: 0xaf},
	{value: 0x0808, lo: 0xb0, hi: 0xb1},
	{value: 0x0040, lo: 0xb2, hi: 0xbf},
	// Block 0xa9, offset 0x4fe
	{value: 0x0000, lo: 0x02},
	{value: 0x0040, lo: 0x80, hi: 0xbc},
	{value: 0x3308, lo: 0xbd, hi: 0xbf},
	// Block 0xaa, offset 0x501
	{value: 0x0000, lo: 0x07},
	{value: 0x0808, lo: 0x80, hi: 0x9c},
	{value: 0x0818, lo: 0x9d, hi: 0xa6},
	{value: 0x0808, lo: 0xa7, hi: 0xa7},
	{value: 0x0040, lo: 0xa8, hi: 0xaf},
	{value: 0x0a08, lo: 0xb0, hi: 0xb2},
	{value: 0x0c08, lo: 0xb3, hi: 0xb3},
	{value: 0x0a08, lo: 0xb4, hi: 0xbf},
	// Block 0xab, offset 0x509
	{value: 0x0000, lo: 0x0a},
	{value: 0x0a08, lo: 0x80, hi: 0x84},
	{value: 0x0808, lo: 0x85, hi: 0x85},
	{value: 0x3308, lo: 0x86, hi: 0x90},
	{value: 0x0a18, lo: 0x91, hi: 0x93},
	{value: 0x0c18, lo: 0x94, hi: 0x94},
	{value: 0x0818, lo: 0x95, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0xaf},
	{value: 0x0a08, lo: 0xb0, hi: 0xb3},
	{value: 0x0c08, lo: 0xb4, hi: 0xb5},
	{value: 0x0a08, lo: 0xb6, hi: 0xbf},
	// Block 0xac, offset 0x514
	{value: 0x0000, lo: 0x0e},
	{value: 0x0a08, lo: 0x80, hi: 0x81},
	{value: 0x3308, lo: 0x82, hi: 0x85},
	{value: 0x0818, lo: 0x86, hi: 0x89},
	{value: 0x0040, lo: 0x8a, hi: 0xaf},
	{value: 0x0a08, lo: 0xb0, hi: 0xb0},
	{value: 0x0808, lo: 0xb1, hi: 0xb1},
	{value: 0x0a08, lo: 0xb2, hi: 0xb3},
	{value: 0x0c08, lo: 0xb4, hi: 0xb6},
	{value: 0x0808, lo: 0xb7, hi: 0xb7},
	{value: 0x0a08, lo: 0xb8, hi: 0xb8},
	{value: 0x0c08, lo: 0xb9, hi: 0xba},
	{value: 0x0a08, lo: 0xbb, hi: 0xbc},
	{value: 0x0c08, lo: 0xbd, hi: 0xbd},
	{value: 0x0a08, lo: 0xbe, hi: 0xbf},
	// Block 0xad, offset 0x523
	{value: 0x0000, lo: 0x0b},
	{value: 0x0808, lo: 0x80, hi: 0x80},
	{value: 0x0a08, lo: 0x81, hi: 0x81},
	{value: 0x0c08, lo: 0x82, hi: 0x83},
	{value: 0x0a08, lo: 0x84, hi: 0x84},
	{value: 0x0818, lo: 0x85, hi: 0x88},
	{value: 0x0c18, lo: 0x89, hi: 0x89},
	{value: 0x0a18, lo: 0x8a, hi: 0x8a},
	{value: 0x0918, lo: 0x8b, hi: 0x8b},
	{value: 0x0040, lo: 0x8c, hi: 0x9f},
	{value: 0x0808, lo: 0xa0, hi: 0xb6},
	{value: 0x0040, lo: 0xb7, hi: 0xbf},
	// Block 0xae, offset 0x52f
	{value: 0x0000, lo: 0x05},
	{value: 0x3008, lo: 0x80, hi: 0x80},
	{value: 0x3308, lo: 0x81, hi: 0x81},
	{value: 0x3008, lo: 0x82, hi: 0x82},
	{value: 0x0008, lo: 0x83, hi: 0xb7},
	{value: 0x3308, lo: 0xb8, hi: 0xbf},
	// Block 0xaf, offset 0x535
	{value: 0x0000, lo: 0x0c},
	{value: 0x3308, lo: 0x80, hi: 0x85},
	{value: 0x3b08, lo: 0x86, hi: 0x86},
	{value: 0x0018, lo: 0x87, hi: 0x8d},
	{value: 0x0040, lo: 0x8e, hi: 0x91},
	{value: 0x0018, lo: 0x92, hi: 0xa5},
	{value: 0x0008, lo: 0xa6, hi: 0xaf},
	{value: 0x3b08, lo: 0xb0, hi: 0xb0},
	{value: 0x0008, lo: 0xb1, hi: 0xb2},
	{value: 0x3308, lo: 0xb3, hi: 0xb4},
	{value: 0x0008, lo: 0xb5, hi: 0xb5},
	{value: 0x0040, lo: 0xb6, hi: 0xbe},
	{value: 0x3b08, lo: 0xbf, hi: 0xbf},
	// Block 0xb0, offset 0x542
	{value: 0x0000, lo: 0x0b},
	{value: 0x3308, lo: 0x80, hi: 0x81},
	{value: 0x3008, lo: 0x82, hi: 0x82},
	{value: 0x0008, lo: 0x83, hi: 0xaf},
	{value: 0x3008, lo: 0xb0, hi: 0xb2},
	{value: 0x3308, lo: 0xb3, hi: 0xb6},
	{value: 0x3008, lo: 0xb7, hi: 0xb8},
	{value: 0x3b08, lo: 0xb9, hi: 0xb9},
	{value: 0x3308, lo: 0xba, hi: 0xba},
	{value: 0x0018, lo: 0xbb, hi: 0xbc},
	{value: 0x0040, lo: 0xbd, hi: 0xbd},
	{value: 0x0018, lo: 0xbe, hi: 0xbf},
	// Block 0xb1, offset 0x54e
	{value: 0x0000, lo: 0x07},
	{value: 0x0018, lo: 0x80, hi: 0x81},
	{value: 0x3308, lo: 0x82, hi: 0x82},
	{value: 0x0040, lo: 0x83, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0xa8},
	{value: 0x0040, lo: 0xa9, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xb9},
	{value: 0x0040, lo: 0xba, hi: 0xbf},
	// Block 0xb2, offset 0x556
	{value: 0x0000, lo: 0x08},
	{value: 0x3308, lo: 0x80, hi: 0x82},
	{value: 0x0008, lo: 0x83, hi: 0xa6},
	{value: 0x3308, lo: 0xa7, hi: 0xab},
	{value: 0x3008, lo: 0xac, hi: 0xac},
	{value: 0x3308, lo: 0xad, hi: 0xb2},
	{value: 0x3b08, lo: 0xb3, hi: 0xb4},
	{value: 0x0040, lo: 0xb5, hi: 0xb5},
	{value: 0x0008, lo: 0xb6, hi: 0xbf},
	// Block 0xb3, offset 0x55f
	{value: 0x0000, lo: 0x0a},
	{value: 0x0018, lo: 0x80, hi: 0x83},
	{value: 0x0008, lo: 0x84, hi: 0x84},
	{value: 0x3008, lo: 0x85, hi: 0x86},
	{value: 0x0008, lo: 0x87, hi: 0x87},
	{value: 0x0040, lo: 0x88, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0xb2},
	{value: 0x3308, lo: 0xb3, hi: 0xb3},
	{value: 0x0018, lo: 0xb4, hi: 0xb5},
	{value: 0x0008, lo: 0xb6, hi: 0xb6},
	{value: 0x0040, lo: 0xb7, hi: 0xbf},
	// Block 0xb4, offset 0x56a
	{value: 0x0000, lo: 0x06},
	{value: 0x3308, lo: 0x80, hi: 0x81},
	{value: 0x3008, lo: 0x82, hi: 0x82},
	{value: 0x0008, lo: 0x83, hi: 0xb2},
	{value: 0x3008, lo: 0xb3, hi: 0xb5},
	{value: 0x3308, lo: 0xb6, hi: 0xbe},
	{value: 0x3008, lo: 0xbf, hi: 0xbf},
	// Block 0xb5, offset 0x571
	{value: 0x0000, lo: 0x0e},
	{value: 0x3808, lo: 0x80, hi: 0x80},
	{value: 0x0008, lo: 0x81, hi: 0x84},
	{value: 0x0018, lo: 0x85, hi: 0x88},
	{value: 0x3308, lo: 0x89, hi: 0x8c},
	{value: 0x0018, lo: 0x8d, hi: 0x8d},
	{value: 0x3008, lo: 0x8e, hi: 0x8e},
	{value: 0x3308, lo: 0x8f, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x9a},
	{value: 0x0018, lo: 0x9b, hi: 0x9b},
	{value: 0x0008, lo: 0x9c, hi: 0x9c},
	{value: 0x0018, lo: 0x9d, hi: 0x9f},
	{value: 0x0040, lo: 0xa0, hi: 0xa0},
	{value: 0x0018, lo: 0xa1, hi: 0xb4},
	{value: 0x0040, lo: 0xb5, hi: 0xbf},
	// Block 0xb6, offset 0x580
	{value: 0x0000, lo: 0x0c},
	{value: 0x0008, lo: 0x80, hi: 0x91},
	{value: 0x0040, lo: 0x92, hi: 0x92},
	{value: 0x0008, lo: 0x93, hi: 0xab},
	{value: 0x3008, lo: 0xac, hi: 0xae},
	{value: 0x3308, lo: 0xaf, hi: 0xb1},
	{value: 0x3008, lo: 0xb2, hi: 0xb3},
	{value: 0x3308, lo: 0xb4, hi: 0xb4},
	{value: 0x3808, lo: 0xb5, hi: 0xb5},
	{value: 0x3308, lo: 0xb6, hi: 0xb7},
	{value: 0x0018, lo: 0xb8, hi: 0xbd},
	{value: 0x3308, lo: 0xbe, hi: 0xbe},
	{value: 0x0008, lo: 0xbf, hi: 0xbf},
	// Block 0xb7, offset 0x58d
	{value: 0x0000, lo: 0x03},
	{value: 0x0008, lo: 0x80, hi: 0x80},
	{value: 0x3308, lo: 0x81, hi: 0x81},
	{value: 0x0040, lo: 0x82, hi: 0xbf},
	// Block 0xb8, offset 0x591
	{value: 0x0000, lo: 0x0c},
	{value: 0x0008, lo: 0x80, hi: 0x86},
	{value: 0x0040, lo: 0x87, hi: 0x87},
	{value: 0x0008, lo: 0x88, hi: 0x88},
	{value: 0x0040, lo: 0x89, hi: 0x89},
	{value: 0x0008, lo: 0x8a, hi: 0x8d},
	{value: 0x0040, lo: 0x8e, hi: 0x8e},
	{value: 0x0008, lo: 0x8f, hi: 0x9d},
	{value: 0x0040, lo: 0x9e, hi: 0x9e},
	{value: 0x0008, lo: 0x9f, hi: 0xa8},
	{value: 0x0018, lo: 0xa9, hi: 0xa9},
	{value: 0x0040, lo: 0xaa, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbf},
	// Block 0xb9, offset 0x59e
	{value: 0x0000, lo: 0x08},
	{value: 0x0008, lo: 0x80, hi: 0x9e},
	{value: 0x3308, lo: 0x9f, hi: 0x9f},
	{value: 0x3008, lo: 0xa0, hi: 0xa2},
	{value: 0x3308, lo: 0xa3, hi: 0xa9},
	{value: 0x3b08, lo: 0xaa, hi: 0xaa},
	{value: 0x0040, lo: 0xab, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xb9},
	{value: 0x0040, lo: 0xba, hi: 0xbf},
	// Block 0xba, offset 0x5a7
	{value: 0x0000, lo: 0x03},
	{value: 0x0008, lo: 0x80, hi: 0xb4},
	{value: 0x3008, lo: 0xb5, hi: 0xb7},
	{value: 0x3308, lo: 0xb8, hi: 0xbf},
	// Block 0xbb, offset 0x5ab
	{value: 0x0000, lo: 0x0e},
	{value: 0x3008, lo: 0x80, hi: 0x81},
	{value: 0x3b08, lo: 0x82, hi: 0x82},
	{value: 0x3308, lo: 0x83, hi: 0x84},
	{value: 0x3008, lo: 0x85, hi: 0x85},
	{value: 0x3308, lo: 0x86, hi: 0x86},
	{value: 0x0008, lo: 0x87, hi: 0x8a},
	{value: 0x0018, lo: 0x8b, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0018, lo: 0x9a, hi: 0x9b},
	{value: 0x0040, lo: 0x9c, hi: 0x9c},
	{value: 0x0018, lo: 0x9d, hi: 0x9d},
	{value: 0x3308, lo: 0x9e, hi: 0x9e},
	{value: 0x0008, lo: 0x9f, hi: 0xa1},
	{value: 0x0040, lo: 0xa2, hi: 0xbf},
	// Block 0xbc, offset 0x5ba
	{value: 0x0000, lo: 0x07},
	{value: 0x0008, lo: 0x80, hi: 0xaf},
	{value: 0x3008, lo: 0xb0, hi: 0xb2},
	{value: 0x3308, lo: 0xb3, hi: 0xb8},
	{value: 0x3008, lo: 0xb9, hi: 0xb9},
	{value: 0x3308, lo: 0xba, hi: 0xba},
	{value: 0x3008, lo: 0xbb, hi: 0xbe},
	{value: 0x3308, lo: 0xbf, hi: 0xbf},
	// Block 0xbd, offset 0x5c2
	{value: 0x0000, lo: 0x0a},
	{value: 0x3308, lo: 0x80, hi: 0x80},
	{value: 0x3008, lo: 0x81, hi: 0x81},
	{value: 0x3b08, lo: 0x82, hi: 0x82},
	{value: 0x3308, lo: 0x83, hi: 0x83},
	{value: 0x0008, lo: 0x84, hi: 0x85},
	{value: 0x0018, lo: 0x86, hi: 0x86},
	{value: 0x0008, lo: 0x87, hi: 0x87},
	{value: 0x0040, lo: 0x88, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0xbf},
	// Block 0xbe, offset 0x5cd
	{value: 0x0000, lo: 0x08},
	{value: 0x0008, lo: 0x80, hi: 0xae},
	{value: 0x3008, lo: 0xaf, hi: 0xb1},
	{value: 0x3308, lo: 0xb2, hi: 0xb5},
	{value: 0x0040, lo: 0xb6, hi: 0xb7},
	{value: 0x3008, lo: 0xb8, hi: 0xbb},
	{value: 0x3308, lo: 0xbc, hi: 0xbd},
	{value: 0x3008, lo: 0xbe, hi: 0xbe},
	{value: 0x3b08, lo: 0xbf, hi: 0xbf},
	// Block 0xbf, offset 0x5d6
	{value: 0x0000, lo: 0x05},
	{value: 0x3308, lo: 0x80, hi: 0x80},
	{value: 0x0018, lo: 0x81, hi: 0x97},
	{value: 0x0008, lo: 0x98, hi: 0x9b},
	{value: 0x3308, lo: 0x9c, hi: 0x9d},
	{value: 0x0040, lo: 0x9e, hi: 0xbf},
	// Block 0xc0, offset 0x5dc
	{value: 0x0000, lo: 0x07},
	{value: 0x0008, lo: 0x80, hi: 0xaf},
	{value: 0x3008, lo: 0xb0, hi: 0xb2},
	{value: 0x3308, lo: 0xb3, hi: 0xba},
	{value: 0x3008, lo: 0xbb, hi: 0xbc},
	{value: 0x3308, lo: 0xbd, hi: 0xbd},
	{value: 0x3008, lo: 0xbe, hi: 0xbe},
	{value: 0x3b08, lo: 0xbf, hi: 0xbf},
	// Block 0xc1, offset 0x5e4
	{value: 0x0000, lo: 0x08},
	{value: 0x3308, lo: 0x80, hi: 0x80},
	{value: 0x0018, lo: 0x81, hi: 0x83},
	{value: 0x0008, lo: 0x84, hi: 0x84},
	{value: 0x0040, lo: 0x85, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0x9f},
	{value: 0x0018, lo: 0xa0, hi: 0xac},
	{value: 0x0040, lo: 0xad, hi: 0xbf},
	// Block 0xc2, offset 0x5ed
	{value: 0x0000, lo: 0x0b},
	{value: 0x0008, lo: 0x80, hi: 0xaa},
	{value: 0x3308, lo: 0xab, hi: 0xab},
	{value: 0x3008, lo: 0xac, hi: 0xac},
	{value: 0x3308, lo: 0xad, hi: 0xad},
	{value: 0x3008, lo: 0xae, hi: 0xaf},
	{value: 0x3308, lo: 0xb0, hi: 0xb5},
	{value: 0x3808, lo: 0xb6, hi: 0xb6},
	{value: 0x3308, lo: 0xb7, hi: 0xb7},
	{value: 0x0008, lo: 0xb8, hi: 0xb8},
	{value: 0x0018, lo: 0xb9, hi: 0xb9},
	{value: 0x0040, lo: 0xba, hi: 0xbf},
	// Block 0xc3, offset 0x5f9
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0x89},
	{value: 0x0040, lo: 0x8a, hi: 0xbf},
	// Block 0xc4, offset 0x5fc
	{value: 0x0000, lo: 0x0b},
	{value: 0x0008, lo: 0x80, hi: 0x9a},
	{value: 0x0040, lo: 0x9b, hi: 0x9c},
	{value: 0x3308, lo: 0x9d, hi: 0x9f},
	{value: 0x3008, lo: 0xa0, hi: 0xa1},
	{value: 0x3308, lo: 0xa2, hi: 0xa5},
	{value: 0x3008, lo: 0xa6, hi: 0xa6},
	{value: 0x3308, lo: 0xa7, hi: 0xaa},
	{value: 0x3b08, lo: 0xab, hi: 0xab},
	{value: 0x0040, lo: 0xac, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xb9},
	{value: 0x0018, lo: 0xba, hi: 0xbf},
	// Block 0xc5, offset 0x608
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0x86},
	{value: 0x0040, lo: 0x87, hi: 0xbf},
	// Block 0xc6, offset 0x60b
	{value: 0x0000, lo: 0x08},
	{value: 0x0008, lo: 0x80, hi: 0xab},
	{value: 0x3008, lo: 0xac, hi: 0xae},
	{value: 0x3308, lo: 0xaf, hi: 0xb7},
	{value: 0x3008, lo: 0xb8, hi: 0xb8},
	{value: 0x3b08, lo: 0xb9, hi: 0xb9},
	{value: 0x3308, lo: 0xba, hi: 0xba},
	{value: 0x0018, lo: 0xbb, hi: 0xbb},
	{value: 0x0040, lo: 0xbc, hi: 0xbf},
	// Block 0xc7, offset 0x614
	{value: 0x0000, lo: 0x02},
	{value: 0x0040, lo: 0x80, hi: 0x9f},
	{value: 0x049d, lo: 0xa0, hi: 0xbf},
	// Block 0xc8, offset 0x617
	{value: 0x0000, lo: 0x04},
	{value: 0x0008, lo: 0x80, hi: 0xa9},
	{value: 0x0018, lo: 0xaa, hi: 0xb2},
	{value: 0x0040, lo: 0xb3, hi: 0xbe},
	{value: 0x0008, lo: 0xbf, hi: 0xbf},
	// Block 0xc9, offset 0x61c
	{value: 0x0000, lo: 0x08},
	{value: 0x3008, lo: 0x80, hi: 0x80},
	{value: 0x0008, lo: 0x81, hi: 0x81},
	{value: 0x3008, lo: 0x82, hi: 0x82},
	{value: 0x3308, lo: 0x83, hi: 0x83},
	{value: 0x0018, lo: 0x84, hi: 0x86},
	{value: 0x0040, lo: 0x87, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0xbf},
	// Block 0xca, offset 0x625
	{value: 0x0000, lo: 0x04},
	{value: 0x0040, lo: 0x80, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xa7},
	{value: 0x0040, lo: 0xa8, hi: 0xa9},
	{value: 0x0008, lo: 0xaa, hi: 0xbf},
	// Block 0xcb, offset 0x62a
	{value: 0x0000, lo: 0x0c},
	{value: 0x0008, lo: 0x80, hi: 0x90},
	{value: 0x3008, lo: 0x91, hi: 0x93},
	{value: 0x3308, lo: 0x94, hi: 0x97},
	{value: 0x0040, lo: 0x98, hi: 0x99},
	{value: 0x3308, lo: 0x9a, hi: 0x9b},
	{value: 0x3008, lo: 0x9c, hi: 0x9f},
	{value: 0x3b08, lo: 0xa0, hi: 0xa0},
	{value: 0x0008, lo: 0xa1, hi: 0xa1},
	{value: 0x0018, lo: 0xa2, hi: 0xa2},
	{value: 0x0008, lo: 0xa3, hi: 0xa3},
	{value: 0x3008, lo: 0xa4, hi: 0xa4},
	{value: 0x0040, lo: 0xa5, hi: 0xbf},
	// Block 0xcc, offset 0x637
	{value: 0x0000, lo: 0x0a},
	{value: 0x0008, lo: 0x80, hi: 0x80},
	{value: 0x3308, lo: 0x81, hi: 0x8a},
	{value: 0x0008, lo: 0x8b, hi: 0xb2},
	{value: 0x3308, lo: 0xb3, hi: 0xb3},
	{value: 0x3b08, lo: 0xb4, hi: 0xb4},
	{value: 0x3308, lo: 0xb5, hi: 0xb8},
	{value: 0x3008, lo: 0xb9, hi: 0xb9},
	{value: 0x0008, lo: 0xba, hi: 0xba},
	{value: 0x3308, lo: 0xbb, hi: 0xbe},
	{value: 0x0018, lo: 0xbf, hi: 0xbf},
	// Block 0xcd, offset 0x642
	{value: 0x0000, lo: 0x08},
	{value: 0x0018, lo: 0x80, hi: 0x86},
	{value: 0x3b08, lo: 0x87, hi: 0x87},
	{value: 0x0040, lo: 0x88, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x90},
	{value: 0x3308, lo: 0x91, hi: 0x96},
	{value: 0x3008, lo: 0x97, hi: 0x98},
	{value: 0x3308, lo: 0x99, hi: 0x9b},
	{value: 0x0008, lo: 0x9c, hi: 0xbf},
	// Block 0xce, offset 0x64b
	{value: 0x0000, lo: 0x0a},
	{value: 0x0008, lo: 0x80, hi: 0x89},
	{value: 0x3308, lo: 0x8a, hi: 0x96},
	{value: 0x3008, lo: 0x97, hi: 0x97},
	{value: 0x3308, lo: 0x98, hi: 0x98},
	{value: 0x3b08, lo: 0x99, hi: 0x99},
	{value: 0x0018, lo: 0x9a, hi: 0x9c},
	{value: 0x0008, lo: 0x9d, hi: 0x9d},
	{value: 0x0018, lo: 0x9e, hi: 0xa2},
	{value: 0x0040, lo: 0xa3, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbf},
	// Block 0xcf, offset 0x656
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0xb8},
	{value: 0x0040, lo: 0xb9, hi: 0xbf},
	// Block 0xd0, offset 0x659
	{value: 0x0000, lo: 0x02},
	{value: 0x0018, lo: 0x80, hi: 0x89},
	{value: 0x0040, lo: 0x8a, hi: 0xbf},
	// Block 0xd1, offset 0x65c
	{value: 0x0000, lo: 0x09},
	{value: 0x0008, lo: 0x80, hi: 0x88},
	{value: 0x0040, lo: 0x89, hi: 0x89},
	{value: 0x0008, lo: 0x8a, hi: 0xae},
	{value: 0x3008, lo: 0xaf, hi: 0xaf},
	{value: 0x3308, lo: 0xb0, hi: 0xb6},
	{value: 0x0040, lo: 0xb7, hi: 0xb7},
	{value: 0x3308, lo: 0xb8, hi: 0xbd},
	{value: 0x3008, lo: 0xbe, hi: 0xbe},
	{value: 0x3b08, lo: 0xbf, hi: 0xbf},
	// Block 0xd2, offset 0x666
	{value: 0x0000, lo: 0x08},
	{value: 0x0008, lo: 0x80, hi: 0x80},
	{value: 0x0018, lo: 0x81, hi: 0x85},
	{value: 0x0040, lo: 0x86, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0018, lo: 0x9a, hi: 0xac},
	{value: 0x0040, lo: 0xad, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xb1},
	{value: 0x0008, lo: 0xb2, hi: 0xbf},
	// Block 0xd3, offset 0x66f
	{value: 0x0000, lo: 0x0b},
	{value: 0x0008, lo: 0x80, hi: 0x8f},
	{value: 0x0040, lo: 0x90, hi: 0x91},
	{value: 0x3308, lo: 0x92, hi: 0xa7},
	{value: 0x0040, lo: 0xa8, hi: 0xa8},
	{value: 0x3008, lo: 0xa9, hi: 0xa9},
	{value: 0x3308, lo: 0xaa, hi: 0xb0},
	{value: 0x3008, lo: 0xb1, hi: 0xb1},
	{value: 0x3308, lo: 0xb2, hi: 0xb3},
	{value: 0x3008, lo: 0xb4, hi: 0xb4},
	{value: 0x3308, lo: 0xb5, hi: 0xb6},
	{value: 0x0040, lo: 0xb7, hi: 0xbf},
	// Block 0xd4, offset 0x67b
	{value: 0x0000, lo: 0x0c},
	{value: 0x0008, lo: 0x80, hi: 0x86},
	{value: 0x0040, lo: 0x87, hi: 0x87},
	{value: 0x0008, lo: 0x88, hi: 0x89},
	{value: 0x0040, lo: 0x8a, hi: 0x8a},
	{value: 0x0008, lo: 0x8b, hi: 0xb0},
	{value: 0x3308, lo: 0xb1, hi: 0xb6},
	{value: 0x0040, lo: 0xb7, hi: 0xb9},
	{value: 0x3308, lo: 0xba, hi: 0xba},
	{value: 0x0040, lo: 0xbb, hi: 0xbb},
	{value: 0x3308, lo: 0xbc, hi: 0xbd},
	{value: 0x0040, lo: 0xbe, hi: 0xbe},
	{value: 0x3308, lo: 0xbf, hi: 0xbf},
	// Block 0xd5, offset 0x688
	{value: 0x0000, lo: 0x0c},
	{value: 0x3308, lo: 0x80, hi: 0x83},
	{value: 0x3b08, lo: 0x84, hi: 0x85},
	{value: 0x0008, lo: 0x86, hi: 0x86},
	{value: 0x3308, lo: 0x87, hi: 0x87},
	{value: 0x0040, lo: 0x88, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xa5},
	{value: 0x0040, lo: 0xa6, hi: 0xa6},
	{value: 0x0008, lo: 0xa7, hi: 0xa8},
	{value: 0x0040, lo: 0xa9, hi: 0xa9},
	{value: 0x0008, lo: 0xaa, hi: 0xbf},
	// Block 0xd6, offset 0x695
	{value: 0x0000, lo: 0x0d},
	{value: 0x0008, lo: 0x80, hi: 0x89},
	{value: 0x3008, lo: 0x8a, hi: 0x8e},
	{value: 0x0040, lo: 0x8f, hi: 0x8f},
	{value: 0x3308, lo: 0x90, hi: 0x91},
	{value: 0x0040, lo: 0x92, hi: 0x92},
	{value: 0x3008, lo: 0x93, hi: 0x94},
	{value: 0x3308, lo: 0x95, hi: 0x95},
	{value: 0x3008, lo: 0x96, hi: 0x96},
	{value: 0x3b08, lo: 0x97, hi: 0x97},
	{value: 0x0008, lo: 0x98, hi: 0x98},
	{value: 0x0040, lo: 0x99, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xa9},
	{value: 0x0040, lo: 0xaa, hi: 0xbf},
	// Block 0xd7, offset 0x6a3
	{value: 0x0000, lo: 0x06},
	{value: 0x0040, lo: 0x80, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xb2},
	{value: 0x3308, lo: 0xb3, hi: 0xb4},
	{value: 0x3008, lo: 0xb5, hi: 0xb6},
	{value: 0x0018, lo: 0xb7, hi: 0xb8},
	{value: 0x0040, lo: 0xb9, hi: 0xbf},
	// Block 0xd8, offset 0x6aa
	{value: 0x0000, lo: 0x0a},
	{value: 0x3308, lo: 0x80, hi: 0x81},
	{value: 0x0008, lo: 0x82, hi: 0x82},
	{value: 0x3008, lo: 0x83, hi: 0x83},
	{value: 0x0008, lo: 0x84, hi: 0x90},
	{value: 0x0040, lo: 0x91, hi: 0x91},
	{value: 0x0008, lo: 0x92, hi: 0xb3},
	{value: 0x3008, lo: 0xb4, hi: 0xb5},
	{value: 0x3308, lo: 0xb6, hi: 0xba},
	{value: 0x0040, lo: 0xbb, hi: 0xbd},
	{value: 0x3008, lo: 0xbe, hi: 0xbf},
	// Block 0xd9, offset 0x6b5
	{value: 0x0000, lo: 0x06},
	{value: 0x3308, lo: 0x80, hi: 0x80},
	{value: 0x3808, lo: 0x81, hi: 0x81},
	{value: 0x3b08, lo: 0x82, hi: 0x82},
	{value: 0x0018, lo: 0x83, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0xbf},
	// Block 0xda, offset 0x6bc
	{value: 0x0000, lo: 0x03},
	{value: 0x0040, lo: 0x80, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xb0},
	{value: 0x0040, lo: 0xb1, hi: 0xbf},
	// Block 0xdb, offset 0x6c0
	{value: 0x0000, lo: 0x03},
	{value: 0x0018, lo: 0x80, hi: 0xb1},
	{value: 0x0040, lo: 0xb2, hi: 0xbe},
	{value: 0x0018, lo: 0xbf, hi: 0xbf},
	// Block 0xdc, offset 0x6c4
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0xbf},
	// Block 0xdd, offset 0x6c7
	{value: 0x0000, lo: 0x04},
	{value: 0x0018, lo: 0x80, hi: 0xae},
	{value: 0x0040, lo: 0xaf, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xb4},
	{value: 0x0040, lo: 0xb5, hi: 0xbf},
	// Block 0xde, offset 0x6cc
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0x83},
	{value: 0x0040, lo: 0x84, hi: 0xbf},
	// Block 0xdf, offset 0x6cf
	{value: 0x0000, lo: 0x02},
	{value: 0x0040, lo: 0x80, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0xbf},
	// Block 0xe0, offset 0x6d2
	{value: 0x0000, lo: 0x03},
	{value: 0x0008, lo: 0x80, hi: 0xb0},
	{value: 0x0018, lo: 0xb1, hi: 0xb2},
	{value: 0x0040, lo: 0xb3, hi: 0xbf},
	// Block 0xe1, offset 0x6d6
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0xaf},
	{value: 0x0340, lo: 0xb0, hi: 0xbf},
	// Block 0xe2, offset 0x6d9
	{value: 0x0000, lo: 0x04},
	{value: 0x3308, lo: 0x80, hi: 0x80},
	{value: 0x0008, lo: 0x81, hi: 0x86},
	{value: 0x3308, lo: 0x87, hi: 0x95},
	{value: 0x0040, lo: 0x96, hi: 0xbf},
	// Block 0xe3, offset 0x6de
	{value: 0x0000, lo: 0x06},
	{value: 0x0008, lo: 0x80, hi: 0x9e},
	{value: 0x0040, lo: 0x9f, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xa9},
	{value: 0x0040, lo: 0xaa, hi: 0xad},
	{value: 0x0018, lo: 0xae, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbf},
	// Block 0xe4, offset 0x6e5
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0xbe},
	{value: 0x0040, lo: 0xbf, hi: 0xbf},
	// Block 0xe5, offset 0x6e8
	{value: 0x0000, lo: 0x07},
	{value: 0x0008, lo: 0x80, hi: 0x89},
	{value: 0x0040, lo: 0x8a, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0xad},
	{value: 0x0040, lo: 0xae, hi: 0xaf},
	{value: 0x3308, lo: 0xb0, hi: 0xb4},
	{value: 0x0018, lo: 0xb5, hi: 0xb5},
	{value: 0x0040, lo: 0xb6, hi: 0xbf},
	// Block 0xe6, offset 0x6f0
	{value: 0x0000, lo: 0x03},
	{value: 0x0008, lo: 0x80, hi: 0xaf},
	{value: 0x3308, lo: 0xb0, hi: 0xb6},
	{value: 0x0018, lo: 0xb7, hi: 0xbf},
	// Block 0xe7, offset 0x6f4
	{value: 0x0000, lo: 0x0a},
	{value: 0x0008, lo: 0x80, hi: 0x83},
	{value: 0x0018, lo: 0x84, hi: 0x85},
	{value: 0x0040, lo: 0x86, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0x9a},
	{value: 0x0018, lo: 0x9b, hi: 0xa1},
	{value: 0x0040, lo: 0xa2, hi: 0xa2},
	{value: 0x0008, lo: 0xa3, hi: 0xb7},
	{value: 0x0040, lo: 0xb8, hi: 0xbc},
	{value: 0x0008, lo: 0xbd, hi: 0xbf},
	// Block 0xe8, offset 0x6ff
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0x8f},
	{value: 0x0040, lo: 0x90, hi: 0xbf},
	// Block 0xe9, offset 0x702
	{value: 0x0000, lo: 0x02},
	{value: 0xe105, lo: 0x80, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xbf},
	// Block 0xea, offset 0x705
	{value: 0x0000, lo: 0x02},
	{value: 0x0018, lo: 0x80, hi: 0x9a},
	{value: 0x0040, lo: 0x9b, hi: 0xbf},
	// Block 0xeb, offset 0x708
	{value: 0x0000, lo: 0x05},
	{value: 0x0008, lo: 0x80, hi: 0x8a},
	{value: 0x0040, lo: 0x8b, hi: 0x8e},
	{value: 0x3308, lo: 0x8f, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x90},
	{value: 0x3008, lo: 0x91, hi: 0xbf},
	// Block 0xec, offset 0x70e
	{value: 0x0000, lo: 0x05},
	{value: 0x3008, lo: 0x80, hi: 0x87},
	{value: 0x0040, lo: 0x88, hi: 0x8e},
	{value: 0x3308, lo: 0x8f, hi: 0x92},
	{value: 0x0008, lo: 0x93, hi: 0x9f},
	{value: 0x0040, lo: 0xa0, hi: 0xbf},
	// Block 0xed, offset 0x714
	{value: 0x0000, lo: 0x08},
	{value: 0x0040, lo: 0x80, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xa1},
	{value: 0x0018, lo: 0xa2, hi: 0xa2},
	{value: 0x0008, lo: 0xa3, hi: 0xa3},
	{value: 0x3308, lo: 0xa4, hi: 0xa4},
	{value: 0x0040, lo: 0xa5, hi: 0xaf},
	{value: 0x3008, lo: 0xb0, hi: 0xb1},
	{value: 0x0040, lo: 0xb2, hi: 0xbf},
	// Block 0xee, offset 0x71d
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0xb7},
	{value: 0x0040, lo: 0xb8, hi: 0xbf},
	// Block 0xef, offset 0x720
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0x95},
	{value: 0x0040, lo: 0x96, hi: 0xbf},
	// Block 0xf0, offset 0x723
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0x88},
	{value: 0x0040, lo: 0x89, hi: 0xbf},
	// Block 0xf1, offset 0x726
	{value: 0x0000, lo: 0x07},
	{value: 0x0040, lo: 0x80, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xb3},
	{value: 0x0040, lo: 0xb4, hi: 0xb4},
	{value: 0x0008, lo: 0xb5, hi: 0xbb},
	{value: 0x0040, lo: 0xbc, hi: 0xbc},
	{value: 0x0008, lo: 0xbd, hi: 0xbe},
	{value: 0x0040, lo: 0xbf, hi: 0xbf},
	// Block 0xf2, offset 0x72e
	{value: 0x0000, lo: 0x04},
	{value: 0x0008, lo: 0x80, hi: 0xa2},
	{value: 0x0040, lo: 0xa3, hi: 0xb1},
	{value: 0x0008, lo: 0xb2, hi: 0xb2},
	{value: 0x0040, lo: 0xb3, hi: 0xbf},
	// Block 0xf3, offset 0x733
	{value: 0x0000, lo: 0x08},
	{value: 0x0040, lo: 0x80, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x92},
	{value: 0x0040, lo: 0x93, hi: 0x94},
	{value: 0x0008, lo: 0x95, hi: 0x95},
	{value: 0x0040, lo: 0x96, hi: 0xa3},
	{value: 0x0008, lo: 0xa4, hi: 0xa7},
	{value: 0x0040, lo: 0xa8, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbf},
	// Block 0xf4, offset 0x73c
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0xbb},
	{value: 0x0040, lo: 0xbc, hi: 0xbf},
	// Block 0xf5, offset 0x73f
	{value: 0x0000, lo: 0x04},
	{value: 0x0008, lo: 0x80, hi: 0xaa},
	{value: 0x0040, lo: 0xab, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbc},
	{value: 0x0040, lo: 0xbd, hi: 0xbf},
	// Block 0xf6, offset 0x744
	{value: 0x0000, lo: 0x09},
	{value: 0x0008, lo: 0x80, hi: 0x88},
	{value: 0x0040, lo: 0x89, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0x9b},
	{value: 0x0018, lo: 0x9c, hi: 0x9c},
	{value: 0x3308, lo: 0x9d, hi: 0x9e},
	{value: 0x0018, lo: 0x9f, hi: 0x9f},
	{value: 0x03c0, lo: 0xa0, hi: 0xa3},
	{value: 0x0040, lo: 0xa4, hi: 0xbf},
	// Block 0xf7, offset 0x74e
	{value: 0x0000, lo: 0x03},
	{value: 0x3308, lo: 0x80, hi: 0xad},
	{value: 0x0040, lo: 0xae, hi: 0xaf},
	{value: 0x3308, lo: 0xb0, hi: 0xbf},
	// Block 0xf8, offset 0x752
	{value: 0x0000, lo: 0x03},
	{value: 0x3308, lo: 0x80, hi: 0x86},
	{value: 0x0040, lo: 0x87, hi: 0x8f},
	{value: 0x0018, lo: 0x90, hi: 0xbf},
	// Block 0xf9, offset 0x756
	{value: 0x0000, lo: 0x02},
	{value: 0x0018, lo: 0x80, hi: 0x83},
	{value: 0x0040, lo: 0x84, hi: 0xbf},
	// Block 0xfa, offset 0x759
	{value: 0x0000, lo: 0x02},
	{value: 0x0018, lo: 0x80, hi: 0xb5},
	{value: 0x0040, lo: 0xb6, hi: 0xbf},
	// Block 0xfb, offset 0x75c
	{value: 0x0000, lo: 0x03},
	{value: 0x0018, lo: 0x80, hi: 0xa6},
	{value: 0x0040, lo: 0xa7, hi: 0xa8},
	{value: 0x0018, lo: 0xa9, hi: 0xbf},
	// Block 0xfc, offset 0x760
	{value: 0x0000, lo: 0x0e},
	{value: 0x0018, lo: 0x80, hi: 0x9d},
	{value: 0x2379, lo: 0x9e, hi: 0x9e},
	{value: 0x2381, lo: 0x9f, hi: 0x9f},
	{value: 0x2389, lo: 0xa0, hi: 0xa0},
	{value: 0x2391, lo: 0xa1, hi: 0xa1},
	{value: 0x2399, lo: 0xa2, hi: 0xa2},
	{value: 0x23a1, lo: 0xa3, hi: 0xa3},
	{value: 0x23a9, lo: 0xa4, hi: 0xa4},
	{value: 0x3018, lo: 0xa5, hi: 0xa6},
	{value: 0x3318, lo: 0xa7, hi: 0xa9},
	{value: 0x0018, lo: 0xaa, hi: 0xac},
	{value: 0x3018, lo: 0xad, hi: 0xb2},
	{value: 0x0340, lo: 0xb3, hi: 0xba},
	{value: 0x3318, lo: 0xbb, hi: 0xbf},
	// Block 0xfd, offset 0x76f
	{value: 0x0000, lo: 0x0b},
	{value: 0x3318, lo: 0x80, hi: 0x82},
	{value: 0x0018, lo: 0x83, hi: 0x84},
	{value: 0x3318, lo: 0x85, hi: 0x8b},
	{value: 0x0018, lo: 0x8c, hi: 0xa9},
	{value: 0x3318, lo: 0xaa, hi: 0xad},
	{value: 0x0018, lo: 0xae, hi: 0xba},
	{value: 0x23b1, lo: 0xbb, hi: 0xbb},
	{value: 0x23b9, lo: 0xbc, hi: 0xbc},
	{value: 0x23c1, lo: 0xbd, hi: 0xbd},
	{value: 0x23c9, lo: 0xbe, hi: 0xbe},
	{value: 0x23d1, lo: 0xbf, hi: 0xbf},
	// Block 0xfe, offset 0x77b
	{value: 0x0000, lo: 0x03},
	{value: 0x23d9, lo: 0x80, hi: 0x80},
	{value: 0x0018, lo: 0x81, hi: 0xaa},
	{value: 0x0040, lo: 0xab, hi: 0xbf},
	// Block 0xff, offset 0x77f
	{value: 0x0000, lo: 0x04},
	{value: 0x0018, lo: 0x80, hi: 0x81},
	{value: 0x3318, lo: 0x82, hi: 0x84},
	{value: 0x0018, lo: 0x85, hi: 0x85},
	{value: 0x0040, lo: 0x86, hi: 0xbf},
	// Block 0x100, offset 0x784
	{value: 0x0000, lo: 0x04},
	{value: 0x0018, lo: 0x80, hi: 0x93},
	{value: 0x0040, lo: 0x94, hi: 0x9f},
	{value: 0x0018, lo: 0xa0, hi: 0xb3},
	{value: 0x0040, lo: 0xb4, hi: 0xbf},
	// Block 0x101, offset 0x789
	{value: 0x0000, lo: 0x04},
	{value: 0x0018, lo: 0x80, hi: 0x96},
	{value: 0x0040, lo: 0x97, hi: 0x9f},
	{value: 0x0018, lo: 0xa0, hi: 0xb8},
	{value: 0x0040, lo: 0xb9, hi: 0xbf},
	// Block 0x102, offset 0x78e
	{value: 0x0000, lo: 0x03},
	{value: 0x3308, lo: 0x80, hi: 0xb6},
	{value: 0x0018, lo: 0xb7, hi: 0xba},
	{value: 0x3308, lo: 0xbb, hi: 0xbf},
	// Block 0x103, offset 0x792
	{value: 0x0000, lo: 0x04},
	{value: 0x3308, lo: 0x80, hi: 0xac},
	{value: 0x0018, lo: 0xad, hi: 0xb4},
	{value: 0x3308, lo: 0xb5, hi: 0xb5},
	{value: 0x0018, lo: 0xb6, hi: 0xbf},
	// Block 0x104, offset 0x797
	{value: 0x0000, lo: 0x08},
	{value: 0x0018, lo: 0x80, hi: 0x83},
	{value: 0x3308, lo: 0x84, hi: 0x84},
	{value: 0x0018, lo: 0x85, hi: 0x8b},
	{value: 0x0040, lo: 0x8c, hi: 0x9a},
	{value: 0x3308, lo: 0x9b, hi: 0x9f},
	{value: 0x0040, lo: 0xa0, hi: 0xa0},
	{value: 0x3308, lo: 0xa1, hi: 0xaf},
	{value: 0x0040, lo: 0xb0, hi: 0xbf},
	// Block 0x105, offset 0x7a0
	{value: 0x0000, lo: 0x04},
	{value: 0x0008, lo: 0x80, hi: 0x9e},
	{value: 0x0040, lo: 0x9f, hi: 0xa4},
	{value: 0x0008, lo: 0xa5, hi: 0xaa},
	{value: 0x0040, lo: 0xab, hi: 0xbf},
	// Block 0x106, offset 0x7a5
	{value: 0x0000, lo: 0x03},
	{value: 0x0040, lo: 0x80, hi: 0x8e},
	{value: 0x3308, lo: 0x8f, hi: 0x8f},
	{value: 0x0040, lo: 0x90, hi: 0xbf},
	// Block 0x107, offset 0x7a9
	{value: 0x0000, lo: 0x05},
	{value: 0x0008, lo: 0x80, hi: 0xac},
	{value: 0x0040, lo: 0xad, hi: 0xaf},
	{value: 0x3308, lo: 0xb0, hi: 0xb6},
	{value: 0x0008, lo: 0xb7, hi: 0xbd},
	{value: 0x0040, lo: 0xbe, hi: 0xbf},
	// Block 0x108, offset 0x7af
	{value: 0x0000, lo: 0x05},
	{value: 0x0008, lo: 0x80, hi: 0x89},
	{value: 0x0040, lo: 0x8a, hi: 0x8d},
	{value: 0x0008, lo: 0x8e, hi: 0x8e},
	{value: 0x0018, lo: 0x8f, hi: 0x8f},
	{value: 0x0040, lo: 0x90, hi: 0xbf},
	// Block 0x109, offset 0x7b5
	{value: 0x0000, lo: 0x04},
	{value: 0x0040, lo: 0x80, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0xad},
	{value: 0x3308, lo: 0xae, hi: 0xae},
	{value: 0x0040, lo: 0xaf, hi: 0xbf},
	// Block 0x10a, offset 0x7ba
	{value: 0x0000, lo: 0x05},
	{value: 0x0008, lo: 0x80, hi: 0xab},
	{value: 0x3308, lo: 0xac, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xb9},
	{value: 0x0040, lo: 0xba, hi: 0xbe},
	{value: 0x0018, lo: 0xbf, hi: 0xbf},
	// Block 0x10b, offset 0x7c0
	{value: 0x0000, lo: 0x05},
	{value: 0x0040, lo: 0x80, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0xab},
	{value: 0x3308, lo: 0xac, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xb9},
	{value: 0x0040, lo: 0xba, hi: 0xbf},
	// Block 0x10c, offset 0x7c6
	{value: 0x0000, lo: 0x09},
	{value: 0x0040, lo: 0x80, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xa6},
	{value: 0x0040, lo: 0xa7, hi: 0xa7},
	{value: 0x0008, lo: 0xa8, hi: 0xab},
	{value: 0x0040, lo: 0xac, hi: 0xac},
	{value: 0x0008, lo: 0xad, hi: 0xae},
	{value: 0x0040, lo: 0xaf, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbe},
	{value: 0x0040, lo: 0xbf, hi: 0xbf},
	// Block 0x10d, offset 0x7d0
	{value: 0x0000, lo: 0x05},
	{value: 0x0808, lo: 0x80, hi: 0x84},
	{value: 0x0040, lo: 0x85, hi: 0x86},
	{value: 0x0818, lo: 0x87, hi: 0x8f},
	{value: 0x3308, lo: 0x90, hi: 0x96},
	{value: 0x0040, lo: 0x97, hi: 0xbf},
	// Block 0x10e, offset 0x7d6
	{value: 0x0000, lo: 0x08},
	{value: 0x0a08, lo: 0x80, hi: 0x83},
	{value: 0x3308, lo: 0x84, hi: 0x8a},
	{value: 0x0b08, lo: 0x8b, hi: 0x8b},
	{value: 0x0040, lo: 0x8c, hi: 0x8f},
	{value: 0x0808, lo: 0x90, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0x9d},
	{value: 0x0818, lo: 0x9e, hi: 0x9f},
	{value: 0x0040, lo: 0xa0, hi: 0xbf},
	// Block 0x10f, offset 0x7df
	{value: 0x0000, lo: 0x02},
	{value: 0x0040, lo: 0x80, hi: 0xb0},
	{value: 0x0818, lo: 0xb1, hi: 0xbf},
	// Block 0x110, offset 0x7e2
	{value: 0x0000, lo: 0x02},
	{value: 0x0818, lo: 0x80, hi: 0xb4},
	{value: 0x0040, lo: 0xb5, hi: 0xbf},
	// Block 0x111, offset 0x7e5
	{value: 0x0000, lo: 0x03},
	{value: 0x0040, lo: 0x80, hi: 0x80},
	{value: 0x0818, lo: 0x81, hi: 0xbd},
	{value: 0x0040, lo: 0xbe, hi: 0xbf},
	// Block 0x112, offset 0x7e9
	{value: 0x0000, lo: 0x03},
	{value: 0x0040, lo: 0x80, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xb1},
	{value: 0x0040, lo: 0xb2, hi: 0xbf},
	// Block 0x113, offset 0x7ed
	{value: 0x0000, lo: 0x03},
	{value: 0x0018, lo: 0x80, hi: 0xab},
	{value: 0x0040, lo: 0xac, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xbf},
	// Block 0x114, offset 0x7f1
	{value: 0x0000, lo: 0x05},
	{value: 0x0018, lo: 0x80, hi: 0x93},
	{value: 0x0040, lo: 0x94, hi: 0x9f},
	{value: 0x0018, lo: 0xa0, hi: 0xae},
	{value: 0x0040, lo: 0xaf, hi: 0xb0},
	{value: 0x0018, lo: 0xb1, hi: 0xbf},
	// Block 0x115, offset 0x7f7
	{value: 0x0000, lo: 0x05},
	{value: 0x0040, lo: 0x80, hi: 0x80},
	{value: 0x0018, lo: 0x81, hi: 0x8f},
	{value: 0x0040, lo: 0x90, hi: 0x90},
	{value: 0x0018, lo: 0x91, hi: 0xb5},
	{value: 0x0040, lo: 0xb6, hi: 0xbf},
	// Block 0x116, offset 0x7fd
	{value: 0x0000, lo: 0x04},
	{value: 0x0018, lo: 0x80, hi: 0x8f},
	{value: 0x2709, lo: 0x90, hi: 0x90},
	{value: 0x0018, lo: 0x91, hi: 0xad},
	{value: 0x0040, lo: 0xae, hi: 0xbf},
	// Block 0x117, offset 0x802
	{value: 0x0000, lo: 0x02},
	{value: 0x0040, lo: 0x80, hi: 0xa5},
	{value: 0x0018, lo: 0xa6, hi: 0xbf},
	// Block 0x118, offset 0x805
	{value: 0x0000, lo: 0x0f},
	{value: 0x2889, lo: 0x80, hi: 0x80},
	{value: 0x2891, lo: 0x81, hi: 0x81},
	{value: 0x2899, lo: 0x82, hi: 0x82},
	{value: 0x28a1, lo: 0x83, hi: 0x83},
	{value: 0x28a9, lo: 0x84, hi: 0x84},
	{value: 0x28b1, lo: 0x85, hi: 0x85},
	{value: 0x28b9, lo: 0x86, hi: 0x86},
	{value: 0x28c1, lo: 0x87, hi: 0x87},
	{value: 0x28c9, lo: 0x88, hi: 0x88},
	{value: 0x0040, lo: 0x89, hi: 0x8f},
	{value: 0x28d1, lo: 0x90, hi: 0x90},
	{value: 0x28d9, lo: 0x91, hi: 0x91},
	{value: 0x0040, lo: 0x92, hi: 0x9f},
	{value: 0x0018, lo: 0xa0, hi: 0xa5},
	{value: 0x0040, lo: 0xa6, hi: 0xbf},
	// Block 0x119, offset 0x815
	{value: 0x0000, lo: 0x06},
	{value: 0x0018, lo: 0x80, hi: 0x97},
	{value: 0x0040, lo: 0x98, hi: 0x9b},
	{value: 0x0018, lo: 0x9c, hi: 0xac},
	{value: 0x0040, lo: 0xad, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xbc},
	{value: 0x0040, lo: 0xbd, hi: 0xbf},
	// Block 0x11a, offset 0x81c
	{value: 0x0000, lo: 0x03},
	{value: 0x0018, lo: 0x80, hi: 0xb6},
	{value: 0x0040, lo: 0xb7, hi: 0xba},
	{value: 0x0018, lo: 0xbb, hi: 0xbf},
	// Block 0x11b, offset 0x820
	{value: 0x0000, lo: 0x06},
	{value: 0x0018, lo: 0x80, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0x9f},
	{value: 0x0018, lo: 0xa0, hi: 0xab},
	{value: 0x0040, lo: 0xac, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xb0},
	{value: 0x0040, lo: 0xb1, hi: 0xbf},
	// Block 0x11c, offset 0x827
	{value: 0x0000, lo: 0x03},
	{value: 0x0018, lo: 0x80, hi: 0x8b},
	{value: 0x0040, lo: 0x8c, hi: 0x8f},
	{value: 0x0018, lo: 0x90, hi: 0xbf},
	// Block 0x11d, offset 0x82b
	{value: 0x0000, lo: 0x05},
	{value: 0x0018, lo: 0x80, hi: 0x87},
	{value: 0x0040, lo: 0x88, hi: 0x8f},
	{value: 0x0018, lo: 0x90, hi: 0x99},
	{value: 0x0040, lo: 0x9a, hi: 0x9f},
	{value: 0x0018, lo: 0xa0, hi: 0xbf},
	// Block 0x11e, offset 0x831
	{value: 0x0000, lo: 0x06},
	{value: 0x0018, lo: 0x80, hi: 0x87},
	{value: 0x0040, lo: 0x88, hi: 0x8f},
	{value: 0x0018, lo: 0x90, hi: 0xad},
	{value: 0x0040, lo: 0xae, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xb1},
	{value: 0x0040, lo: 0xb2, hi: 0xbf},
	// Block 0x11f, offset 0x838
	{value: 0x0000, lo: 0x06},
	{value: 0x0018, lo: 0x80, hi: 0x93},
	{value: 0x0040, lo: 0x94, hi: 0x9f},
	{value: 0x0018, lo: 0xa0, hi: 0xad},
	{value: 0x0040, lo: 0xae, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xbc},
	{value: 0x0040, lo: 0xbd, hi: 0xbf},
	// Block 0x120, offset 0x83f
	{value: 0x0000, lo: 0x05},
	{value: 0x0018, lo: 0x80, hi: 0x88},
	{value: 0x0040, lo: 0x89, hi: 0x8f},
	{value: 0x0018, lo: 0x90, hi: 0xbd},
	{value: 0x0040, lo: 0xbe, hi: 0xbe},
	{value: 0x0018, lo: 0xbf, hi: 0xbf},
	// Block 0x121, offset 0x845
	{value: 0x0000, lo: 0x08},
	{value: 0x0018, lo: 0x80, hi: 0x85},
	{value: 0x0040, lo: 0x86, hi: 0x8d},
	{value: 0x0018, lo: 0x8e, hi: 0x9b},
	{value: 0x0040, lo: 0x9c, hi: 0x9f},
	{value: 0x0018, lo: 0xa0, hi: 0xa8},
	{value: 0x0040, lo: 0xa9, hi: 0xaf},
	{value: 0x0018, lo: 0xb0, hi: 0xb8},
	{value: 0x0040, lo: 0xb9, hi: 0xbf},
	// Block 0x122, offset 0x84e
	{value: 0x0000, lo: 0x03},
	{value: 0x0018, lo: 0x80, hi: 0x92},
	{value: 0x0040, lo: 0x93, hi: 0x93},
	{value: 0x0018, lo: 0x94, hi: 0xbf},
	// Block 0x123, offset 0x852
	{value: 0x0000, lo: 0x0d},
	{value: 0x0018, lo: 0x80, hi: 0x8a},
	{value: 0x0040, lo: 0x8b, hi: 0xaf},
	{value: 0x06e1, lo: 0xb0, hi: 0xb0},
	{value: 0x0049, lo: 0xb1, hi: 0xb1},
	{value: 0x0029, lo: 0xb2, hi: 0xb2},
	{value: 0x0031, lo: 0xb3, hi: 0xb3},
	{value: 0x06e9, lo: 0xb4, hi: 0xb4},
	{value: 0x06f1, lo: 0xb5, hi: 0xb5},
	{value: 0x06f9, lo: 0xb6, hi: 0xb6},
	{value: 0x0701, lo: 0xb7, hi: 0xb7},
	{value: 0x0709, lo: 0xb8, hi: 0xb8},
	{value: 0x0711, lo: 0xb9, hi: 0xb9},
	{value: 0x0040, lo: 0xba, hi: 0xbf},
	// Block 0x124, offset 0x860
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0x9f},
	{value: 0x0040, lo: 0xa0, hi: 0xbf},
	// Block 0x125, offset 0x863
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0xb9},
	{value: 0x0040, lo: 0xba, hi: 0xbf},
	// Block 0x126, offset 0x866
	{value: 0x0000, lo: 0x03},
	{value: 0x0008, lo: 0x80, hi: 0x9d},
	{value: 0x0040, lo: 0x9e, hi: 0x9f},
	{value: 0x0008, lo: 0xa0, hi: 0xbf},
	// Block 0x127, offset 0x86a
	{value: 0x0000, lo: 0x03},
	{value: 0x0008, lo: 0x80, hi: 0xa1},
	{value: 0x0040, lo: 0xa2, hi: 0xaf},
	{value: 0x0008, lo: 0xb0, hi: 0xbf},
	// Block 0x128, offset 0x86e
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0xa0},
	{value: 0x0040, lo: 0xa1, hi: 0xbf},
	// Block 0x129, offset 0x871
	{value: 0x0000, lo: 0x03},
	{value: 0x0008, lo: 0x80, hi: 0x8a},
	{value: 0x0040, lo: 0x8b, hi: 0x8f},
	{value: 0x0008, lo: 0x90, hi: 0xbf},
	// Block 0x12a, offset 0x875
	{value: 0x0000, lo: 0x02},
	{value: 0x0008, lo: 0x80, hi: 0xaf},
	{value: 0x0040, lo: 0xb0, hi: 0xbf},
	// Block 0x12b, offset 0x878
	{value: 0x0000, lo: 0x04},
	{value: 0x0040, lo: 0x80, hi: 0x80},
	{value: 0x0340, lo: 0x81, hi: 0x81},
	{value: 0x0040, lo: 0x82, hi: 0x9f},
	{value: 0x0340, lo: 0xa0, hi: 0xbf},
	// Block 0x12c, offset 0x87d
	{value: 0x0000, lo: 0x01},
	{value: 0x0340, lo: 0x80, hi: 0xbf},
	// Block 0x12d, offset 0x87f
	{value: 0x0000, lo: 0x01},
	{value: 0x33c0, lo: 0x80, hi: 0xbf},
	// Block 0x12e, offset 0x881
	{value: 0x0000, lo: 0x02},
	{value: 0x33c0, lo: 0x80, hi: 0xaf},
	{value: 0x0040, lo: 0xb0, hi: 0xbf},
}

// Total table size 46723 bytes (45KiB); checksum: 4CF3143A
