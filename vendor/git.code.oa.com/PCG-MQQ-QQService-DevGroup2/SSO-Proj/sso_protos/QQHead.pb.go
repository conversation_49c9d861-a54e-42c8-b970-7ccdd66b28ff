// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.12.3
// source: QQHead.proto

package sso_protos

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 通道流量类型标记
type SsoMessageType int32

const (
	// 不用这个值，为了提供给 pb 工具生成代码
	SsoMessageType_SSO_DEFAULT SsoMessageType = 0
	// 染色 由SSO设置，表示该用户为染色用户
	SsoMessageType_SSO_DYEING_MESSAGE SsoMessageType = 1
	// 调用链 由客户端设置，表示请求携带全链路信息
	SsoMessageType_SSO_TRACE_MESSAGE SsoMessageType = 2
	// 多环境 由客户端设置，表示请求携带环境ID信息
	SsoMessageType_SSO_MULTI_ENV_MESSAGE SsoMessageType = 4
	// 灰度 由客户端设置，表示客户端版本为灰度版本
	SsoMessageType_SSO_GRAY_MESSAGE SsoMessageType = 8
	// 压测流量标记 由客户端设置
	SsoMessageType_SSO_PRESSURE_MESSAGE SsoMessageType = 16
	// NT功能标记 由客户端设置，用于标记在手Q上走NT特性的请求
	SsoMessageType_SSO_NT_FUNC_MESSAGE SsoMessageType = 32
)

// Enum value maps for SsoMessageType.
var (
	SsoMessageType_name = map[int32]string{
		0:  "SSO_DEFAULT",
		1:  "SSO_DYEING_MESSAGE",
		2:  "SSO_TRACE_MESSAGE",
		4:  "SSO_MULTI_ENV_MESSAGE",
		8:  "SSO_GRAY_MESSAGE",
		16: "SSO_PRESSURE_MESSAGE",
		32: "SSO_NT_FUNC_MESSAGE",
	}
	SsoMessageType_value = map[string]int32{
		"SSO_DEFAULT":           0,
		"SSO_DYEING_MESSAGE":    1,
		"SSO_TRACE_MESSAGE":     2,
		"SSO_MULTI_ENV_MESSAGE": 4,
		"SSO_GRAY_MESSAGE":      8,
		"SSO_PRESSURE_MESSAGE":  16,
		"SSO_NT_FUNC_MESSAGE":   32,
	}
)

func (x SsoMessageType) Enum() *SsoMessageType {
	p := new(SsoMessageType)
	*p = x
	return p
}

func (x SsoMessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SsoMessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_QQHead_proto_enumTypes[0].Descriptor()
}

func (SsoMessageType) Type() protoreflect.EnumType {
	return &file_QQHead_proto_enumTypes[0]
}

func (x SsoMessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *SsoMessageType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = SsoMessageType(num)
	return nil
}

// Deprecated: Use SsoMessageType.Descriptor instead.
func (SsoMessageType) EnumDescriptor() ([]byte, []int) {
	return file_QQHead_proto_rawDescGZIP(), []int{0}
}

type QQHead struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	BytesClientIp       []byte      `protobuf:"bytes,1,opt,name=bytes_client_ip,json=bytesClientIp" json:"bytes_client_ip,omitempty"`                       // 客户端IP 通过长度区分协议，ipv6 16字节 ；ipv4 4字节 网络序
	Uint32ClientPlat    *uint32     `protobuf:"varint,2,opt,name=uint32_client_plat,json=uint32ClientPlat" json:"uint32_client_plat,omitempty"`             // 客户端平台 PC=1;Android=2;Iphone=3;Ipad=4;WinPhone=5;Mac=6;tLinux=7;Windows=8;WxMiniApp=9;其他=0;
	BytesClientVersion  []byte      `protobuf:"bytes,3,opt,name=bytes_client_version,json=bytesClientVersion" json:"bytes_client_version,omitempty"`        // 客户端版本号(例如"8.3.9")
	Uint32ProductId     *uint32     `protobuf:"varint,4,opt,name=uint32_product_id,json=uint32ProductId" json:"uint32_product_id,omitempty"`                // 产品ID
	Uint32RouteId       *uint32     `protobuf:"varint,6,opt,name=uint32_route_id,json=uint32RouteId" json:"uint32_route_id,omitempty"`                      // 测试环境路由规则id
	Uint32LocaleId      *uint32     `protobuf:"varint,7,opt,name=uint32_locale_id,json=uint32LocaleId" json:"uint32_locale_id,omitempty"`                   // 多语言locale id (en_US=1033, zh_CN=2052, zh_TW=1028, 完整LCID列表: https://docs.microsoft.com/en-us/openspecs/office_standards/ms-oe376/6c085406-a698-4e12-9d4d-c3b0ee3dbc4a)
	BytesTraceId        []byte      `protobuf:"bytes,8,opt,name=bytes_trace_id,json=bytesTraceId" json:"bytes_trace_id,omitempty"`                          // 全链路跟踪trace_id
	BytesParentId       []byte      `protobuf:"bytes,10,opt,name=bytes_parent_id,json=bytesParentId" json:"bytes_parent_id,omitempty"`                      // 全链路跟踪parent_span_id
	BytesClientGuid     []byte      `protobuf:"bytes,11,opt,name=bytes_client_guid,json=bytesClientGuid" json:"bytes_client_guid,omitempty"`                // 从A2取出Guid带给后端
	BytesQimei          []byte      `protobuf:"bytes,12,opt,name=bytes_qimei,json=bytesQimei" json:"bytes_qimei,omitempty"`                                 // 36位qimei 需要配置才携带
	Uint32ClientPlatId  *uint32     `protobuf:"varint,13,opt,name=uint32_client_plat_id,json=uint32ClientPlatId" json:"uint32_client_plat_id,omitempty"`    // 客户端平台 完整APPID对应平台、子平台参照：http://mqqserver.oa.com/web_mqq_admin/web/index.jsp
	BytesClientSubPlat  []byte      `protobuf:"bytes,14,opt,name=bytes_client_sub_plat,json=bytesClientSubPlat" json:"bytes_client_sub_plat,omitempty"`     // 客户端子平台 APPID中的子平台
	BytesRouteEnv       []byte      `protobuf:"bytes,15,opt,name=bytes_route_env,json=bytesRouteEnv" json:"bytes_route_env,omitempty"`                      // 测试环境路由规则env，字符串形式环境id
	BytesUid            []byte      `protobuf:"bytes,16,opt,name=bytes_uid,json=bytesUid" json:"bytes_uid,omitempty"`                                       // NT客户端uid
	BytesTraceParent    []byte      `protobuf:"bytes,17,opt,name=bytes_trace_parent,json=bytesTraceParent" json:"bytes_trace_parent,omitempty"`             // 全链路跟踪2.0 trace_parent 格式: 00-32位traceid-16位span-2位flag
	BoolIsNtProduct     *bool       `protobuf:"varint,18,opt,name=bool_is_nt_product,json=boolIsNtProduct" json:"bool_is_nt_product,omitempty"`             // 是否是NT独立版客户端
	BytesLoginSig       []byte      `protobuf:"bytes,19,opt,name=bytes_login_sig,json=bytesLoginSig" json:"bytes_login_sig,omitempty"`                      // 登录票据信息序列化结果，结构体和LoginSig一致
	Uint32LoginType     *uint32     `protobuf:"varint,20,opt,name=uint32_login_type,json=uint32LoginType" json:"uint32_login_type,omitempty"`               // 登录来源
	Uint32InvalidAppid  *uint32     `protobuf:"varint,21,opt,name=uint32_invalid_appid,json=uint32InvalidAppid" json:"uint32_invalid_appid,omitempty"`      // 是否是无效的appid, 0:否 非0:是, wtlogin用于返回封停wording
	SecureInfo          *SecureInfo `protobuf:"bytes,22,opt,name=secure_info,json=secureInfo" json:"secure_info,omitempty"`                                 // 安全相关信息
	Uint32NtCoreVersion *uint32     `protobuf:"varint,23,opt,name=uint32_nt_core_version,json=uint32NtCoreVersion" json:"uint32_nt_core_version,omitempty"` // nt内核版本id
	MessageType         *uint32     `protobuf:"varint,24,opt,name=message_type,json=messageType" json:"message_type,omitempty"`                             // 通道流量类型标志，具体值和SsoMessageType对应
	BytesOpenid         []byte      `protobuf:"bytes,25,opt,name=bytes_openid,json=bytesOpenid" json:"bytes_openid,omitempty"`                              // open id for 小程序
	BytesCmd            []byte      `protobuf:"bytes,26,opt,name=bytes_cmd,json=bytesCmd" json:"bytes_cmd,omitempty"`                                       // SSO 命令字
	Uint32CmdCount      *uint32     `protobuf:"varint,27,opt,name=uint32_cmd_count,json=uint32CmdCount" json:"uint32_cmd_count,omitempty"`                  // 指定周期内(SSO当前配置为10分钟)，SSO单机当前用户当前命令字出现的次数。
	BytesDummyBytes     []byte      `protobuf:"bytes,999,opt,name=bytes_dummy_bytes,json=bytesDummyBytes" json:"bytes_dummy_bytes,omitempty"`               // 填充字段用于拨测sso包头安全长度
}

func (x *QQHead) Reset() {
	*x = QQHead{}
	if protoimpl.UnsafeEnabled {
		mi := &file_QQHead_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QQHead) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QQHead) ProtoMessage() {}

func (x *QQHead) ProtoReflect() protoreflect.Message {
	mi := &file_QQHead_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QQHead.ProtoReflect.Descriptor instead.
func (*QQHead) Descriptor() ([]byte, []int) {
	return file_QQHead_proto_rawDescGZIP(), []int{0}
}

func (x *QQHead) GetBytesClientIp() []byte {
	if x != nil {
		return x.BytesClientIp
	}
	return nil
}

func (x *QQHead) GetUint32ClientPlat() uint32 {
	if x != nil && x.Uint32ClientPlat != nil {
		return *x.Uint32ClientPlat
	}
	return 0
}

func (x *QQHead) GetBytesClientVersion() []byte {
	if x != nil {
		return x.BytesClientVersion
	}
	return nil
}

func (x *QQHead) GetUint32ProductId() uint32 {
	if x != nil && x.Uint32ProductId != nil {
		return *x.Uint32ProductId
	}
	return 0
}

func (x *QQHead) GetUint32RouteId() uint32 {
	if x != nil && x.Uint32RouteId != nil {
		return *x.Uint32RouteId
	}
	return 0
}

func (x *QQHead) GetUint32LocaleId() uint32 {
	if x != nil && x.Uint32LocaleId != nil {
		return *x.Uint32LocaleId
	}
	return 0
}

func (x *QQHead) GetBytesTraceId() []byte {
	if x != nil {
		return x.BytesTraceId
	}
	return nil
}

func (x *QQHead) GetBytesParentId() []byte {
	if x != nil {
		return x.BytesParentId
	}
	return nil
}

func (x *QQHead) GetBytesClientGuid() []byte {
	if x != nil {
		return x.BytesClientGuid
	}
	return nil
}

func (x *QQHead) GetBytesQimei() []byte {
	if x != nil {
		return x.BytesQimei
	}
	return nil
}

func (x *QQHead) GetUint32ClientPlatId() uint32 {
	if x != nil && x.Uint32ClientPlatId != nil {
		return *x.Uint32ClientPlatId
	}
	return 0
}

func (x *QQHead) GetBytesClientSubPlat() []byte {
	if x != nil {
		return x.BytesClientSubPlat
	}
	return nil
}

func (x *QQHead) GetBytesRouteEnv() []byte {
	if x != nil {
		return x.BytesRouteEnv
	}
	return nil
}

func (x *QQHead) GetBytesUid() []byte {
	if x != nil {
		return x.BytesUid
	}
	return nil
}

func (x *QQHead) GetBytesTraceParent() []byte {
	if x != nil {
		return x.BytesTraceParent
	}
	return nil
}

func (x *QQHead) GetBoolIsNtProduct() bool {
	if x != nil && x.BoolIsNtProduct != nil {
		return *x.BoolIsNtProduct
	}
	return false
}

func (x *QQHead) GetBytesLoginSig() []byte {
	if x != nil {
		return x.BytesLoginSig
	}
	return nil
}

func (x *QQHead) GetUint32LoginType() uint32 {
	if x != nil && x.Uint32LoginType != nil {
		return *x.Uint32LoginType
	}
	return 0
}

func (x *QQHead) GetUint32InvalidAppid() uint32 {
	if x != nil && x.Uint32InvalidAppid != nil {
		return *x.Uint32InvalidAppid
	}
	return 0
}

func (x *QQHead) GetSecureInfo() *SecureInfo {
	if x != nil {
		return x.SecureInfo
	}
	return nil
}

func (x *QQHead) GetUint32NtCoreVersion() uint32 {
	if x != nil && x.Uint32NtCoreVersion != nil {
		return *x.Uint32NtCoreVersion
	}
	return 0
}

func (x *QQHead) GetMessageType() uint32 {
	if x != nil && x.MessageType != nil {
		return *x.MessageType
	}
	return 0
}

func (x *QQHead) GetBytesOpenid() []byte {
	if x != nil {
		return x.BytesOpenid
	}
	return nil
}

func (x *QQHead) GetBytesCmd() []byte {
	if x != nil {
		return x.BytesCmd
	}
	return nil
}

func (x *QQHead) GetUint32CmdCount() uint32 {
	if x != nil && x.Uint32CmdCount != nil {
		return *x.Uint32CmdCount
	}
	return 0
}

func (x *QQHead) GetBytesDummyBytes() []byte {
	if x != nil {
		return x.BytesDummyBytes
	}
	return nil
}

type LoginSig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uint32Type  *uint32 `protobuf:"varint,1,opt,name=uint32_type,json=uint32Type" json:"uint32_type,omitempty"`    //登录态类型 A2为8
	BytesSig    []byte  `protobuf:"bytes,2,opt,name=bytes_sig,json=bytesSig" json:"bytes_sig,omitempty"`           //登录态内容
	Uint32Appid *uint32 `protobuf:"varint,3,opt,name=uint32_appid,json=uint32Appid" json:"uint32_appid,omitempty"` //第三方调用的appid
}

func (x *LoginSig) Reset() {
	*x = LoginSig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_QQHead_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginSig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginSig) ProtoMessage() {}

func (x *LoginSig) ProtoReflect() protoreflect.Message {
	mi := &file_QQHead_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginSig.ProtoReflect.Descriptor instead.
func (*LoginSig) Descriptor() ([]byte, []int) {
	return file_QQHead_proto_rawDescGZIP(), []int{1}
}

func (x *LoginSig) GetUint32Type() uint32 {
	if x != nil && x.Uint32Type != nil {
		return *x.Uint32Type
	}
	return 0
}

func (x *LoginSig) GetBytesSig() []byte {
	if x != nil {
		return x.BytesSig
	}
	return nil
}

func (x *LoginSig) GetUint32Appid() uint32 {
	if x != nil && x.Uint32Appid != nil {
		return *x.Uint32Appid
	}
	return 0
}

type SecureInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SecSig         []byte `protobuf:"bytes,1,opt,name=sec_sig,json=secSig" json:"sec_sig,omitempty"` // 客户端协议签名，按SSO指定命令字携带
	SecDeviceToken []byte `protobuf:"bytes,2,opt,name=sec_device_token,json=secDeviceToken" json:"sec_device_token,omitempty"`
	SecExtra       []byte `protobuf:"bytes,3,opt,name=sec_extra,json=secExtra" json:"sec_extra,omitempty"` // 客户端QSSDK拓展信息
	SecMd5         []byte `protobuf:"bytes,4,opt,name=sec_md5,json=secMd5" json:"sec_md5,omitempty"`       // SSO生成请求包md5供安全验签
}

func (x *SecureInfo) Reset() {
	*x = SecureInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_QQHead_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecureInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecureInfo) ProtoMessage() {}

func (x *SecureInfo) ProtoReflect() protoreflect.Message {
	mi := &file_QQHead_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecureInfo.ProtoReflect.Descriptor instead.
func (*SecureInfo) Descriptor() ([]byte, []int) {
	return file_QQHead_proto_rawDescGZIP(), []int{2}
}

func (x *SecureInfo) GetSecSig() []byte {
	if x != nil {
		return x.SecSig
	}
	return nil
}

func (x *SecureInfo) GetSecDeviceToken() []byte {
	if x != nil {
		return x.SecDeviceToken
	}
	return nil
}

func (x *SecureInfo) GetSecExtra() []byte {
	if x != nil {
		return x.SecExtra
	}
	return nil
}

func (x *SecureInfo) GetSecMd5() []byte {
	if x != nil {
		return x.SecMd5
	}
	return nil
}

var File_QQHead_proto protoreflect.FileDescriptor

var file_QQHead_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x51, 0x51, 0x48, 0x65, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18,
	0x54, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x51, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0xf3, 0x08, 0x0a, 0x06, 0x51, 0x51, 0x48,
	0x65, 0x61, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x62, 0x79,
	0x74, 0x65, 0x73, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x12, 0x2c, 0x0a, 0x12, 0x75,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x6c, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x62, 0x79, 0x74,
	0x65, 0x73, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x12, 0x62, 0x79, 0x74, 0x65, 0x73, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x75,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0d, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x10, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x79, 0x74,
	0x65, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0c, 0x62, 0x79, 0x74, 0x65, 0x73, 0x54, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x62, 0x79, 0x74, 0x65, 0x73, 0x50,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x79, 0x74, 0x65, 0x73,
	0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x75, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x47,
	0x75, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x71, 0x69, 0x6d,
	0x65, 0x69, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x62, 0x79, 0x74, 0x65, 0x73, 0x51,
	0x69, 0x6d, 0x65, 0x69, 0x12, 0x31, 0x0a, 0x15, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x12, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x50, 0x6c, 0x61, 0x74, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x15, 0x62, 0x79, 0x74, 0x65, 0x73,
	0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x12, 0x62, 0x79, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x53, 0x75, 0x62, 0x50, 0x6c, 0x61, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x79,
	0x74, 0x65, 0x73, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x76, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0d, 0x62, 0x79, 0x74, 0x65, 0x73, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x45,
	0x6e, 0x76, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x75, 0x69, 0x64, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x62, 0x79, 0x74, 0x65, 0x73, 0x55, 0x69, 0x64, 0x12,
	0x2c, 0x0a, 0x12, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x10, 0x62, 0x79, 0x74,
	0x65, 0x73, 0x54, 0x72, 0x61, 0x63, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x2b, 0x0a,
	0x12, 0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x69, 0x73, 0x5f, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x62, 0x6f, 0x6f, 0x6c, 0x49,
	0x73, 0x4e, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x79,
	0x74, 0x65, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0d, 0x62, 0x79, 0x74, 0x65, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x53,
	0x69, 0x67, 0x12, 0x2a, 0x0a, 0x11, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x6c, 0x6f, 0x67,
	0x69, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x75,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30,
	0x0a, 0x14, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x5f, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x75, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x70, 0x70, 0x69, 0x64,
	0x12, 0x45, 0x0a, 0x0b, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x54, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2e,
	0x51, 0x51, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a, 0x16, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x5f, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x4e,
	0x74, 0x43, 0x6f, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x62, 0x79, 0x74, 0x65, 0x73, 0x4f, 0x70, 0x65, 0x6e,
	0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x63, 0x6d, 0x64, 0x18,
	0x1a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x62, 0x79, 0x74, 0x65, 0x73, 0x43, 0x6d, 0x64, 0x12,
	0x28, 0x0a, 0x10, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x63, 0x6d, 0x64, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x43, 0x6d, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x62, 0x79, 0x74,
	0x65, 0x73, 0x5f, 0x64, 0x75, 0x6d, 0x6d, 0x79, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0xe7,
	0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x44, 0x75, 0x6d, 0x6d,
	0x79, 0x42, 0x79, 0x74, 0x65, 0x73, 0x2a, 0x06, 0x08, 0xe8, 0x07, 0x10, 0xd0, 0x0f, 0x22, 0x6b,
	0x0a, 0x08, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x53, 0x69, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0a, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62,
	0x79, 0x74, 0x65, 0x73, 0x5f, 0x73, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08,
	0x62, 0x79, 0x74, 0x65, 0x73, 0x53, 0x69, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x5f, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b,
	0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x41, 0x70, 0x70, 0x69, 0x64, 0x22, 0x85, 0x01, 0x0a, 0x0a,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x65,
	0x63, 0x5f, 0x73, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x73, 0x65, 0x63,
	0x53, 0x69, 0x67, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x65, 0x63, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x73,
	0x65, 0x63, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x65, 0x63, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x08, 0x73, 0x65, 0x63, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x65,
	0x63, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x73, 0x65, 0x63,
	0x4d, 0x64, 0x35, 0x2a, 0xb4, 0x01, 0x0a, 0x0e, 0x53, 0x73, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x53, 0x4f, 0x5f, 0x44, 0x45,
	0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x53, 0x4f, 0x5f, 0x44,
	0x59, 0x45, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0x01, 0x12,
	0x15, 0x0a, 0x11, 0x53, 0x53, 0x4f, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x45, 0x53,
	0x53, 0x41, 0x47, 0x45, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x53, 0x4f, 0x5f, 0x4d, 0x55,
	0x4c, 0x54, 0x49, 0x5f, 0x45, 0x4e, 0x56, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10,
	0x04, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x53, 0x4f, 0x5f, 0x47, 0x52, 0x41, 0x59, 0x5f, 0x4d, 0x45,
	0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0x08, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x53, 0x4f, 0x5f, 0x50,
	0x52, 0x45, 0x53, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10,
	0x10, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x53, 0x4f, 0x5f, 0x4e, 0x54, 0x5f, 0x46, 0x55, 0x4e, 0x43,
	0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0x20, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69,
	0x74, 0x2e, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x50, 0x43,
	0x47, 0x2d, 0x4d, 0x51, 0x51, 0x2d, 0x51, 0x51, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2d,
	0x44, 0x65, 0x76, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x32, 0x2f, 0x53, 0x53, 0x4f, 0x2d, 0x50, 0x72,
	0x6f, 0x6a, 0x2f, 0x73, 0x73, 0x6f, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73,
}

var (
	file_QQHead_proto_rawDescOnce sync.Once
	file_QQHead_proto_rawDescData = file_QQHead_proto_rawDesc
)

func file_QQHead_proto_rawDescGZIP() []byte {
	file_QQHead_proto_rawDescOnce.Do(func() {
		file_QQHead_proto_rawDescData = protoimpl.X.CompressGZIP(file_QQHead_proto_rawDescData)
	})
	return file_QQHead_proto_rawDescData
}

var file_QQHead_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_QQHead_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_QQHead_proto_goTypes = []interface{}{
	(SsoMessageType)(0), // 0: Tencent.QQService.Common.SsoMessageType
	(*QQHead)(nil),      // 1: Tencent.QQService.Common.QQHead
	(*LoginSig)(nil),    // 2: Tencent.QQService.Common.LoginSig
	(*SecureInfo)(nil),  // 3: Tencent.QQService.Common.SecureInfo
}
var file_QQHead_proto_depIdxs = []int32{
	3, // 0: Tencent.QQService.Common.QQHead.secure_info:type_name -> Tencent.QQService.Common.SecureInfo
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_QQHead_proto_init() }
func file_QQHead_proto_init() {
	if File_QQHead_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_QQHead_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QQHead); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_QQHead_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginSig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_QQHead_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecureInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_QQHead_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_QQHead_proto_goTypes,
		DependencyIndexes: file_QQHead_proto_depIdxs,
		EnumInfos:         file_QQHead_proto_enumTypes,
		MessageInfos:      file_QQHead_proto_msgTypes,
	}.Build()
	File_QQHead_proto = out.File
	file_QQHead_proto_rawDesc = nil
	file_QQHead_proto_goTypes = nil
	file_QQHead_proto_depIdxs = nil
}
