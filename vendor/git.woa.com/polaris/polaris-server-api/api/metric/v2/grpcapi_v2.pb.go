// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: metric/v2/grpcapi_v2.proto

package v2

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_metric_v2_grpcapi_v2_proto protoreflect.FileDescriptor

var file_metric_v2_grpcapi_v2_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2f, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63,
	0x61, 0x70, 0x69, 0x5f, 0x76, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x1a,
	0x1c, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x61, 0x74, 0x65, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x5f, 0x76, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xca, 0x01,
	0x0a, 0x0f, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x47, 0x52, 0x50, 0x43, 0x56,
	0x32, 0x12, 0x5a, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x23, 0x2e, 0x70,
	0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x24, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x5b, 0x0a,
	0x0a, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x12, 0x24, 0x2e, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x25, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x36, 0x5a, 0x34, 0x67, 0x69,
	0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2f,
	0x76, 0x32, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_metric_v2_grpcapi_v2_proto_goTypes = []interface{}{
	(*RateLimitRequest)(nil),   // 0: polaris.metric.v2.RateLimitRequest
	(*TimeAdjustRequest)(nil),  // 1: polaris.metric.v2.TimeAdjustRequest
	(*RateLimitResponse)(nil),  // 2: polaris.metric.v2.RateLimitResponse
	(*TimeAdjustResponse)(nil), // 3: polaris.metric.v2.TimeAdjustResponse
}
var file_metric_v2_grpcapi_v2_proto_depIdxs = []int32{
	0, // 0: polaris.metric.v2.RateLimitGRPCV2.Service:input_type -> polaris.metric.v2.RateLimitRequest
	1, // 1: polaris.metric.v2.RateLimitGRPCV2.TimeAdjust:input_type -> polaris.metric.v2.TimeAdjustRequest
	2, // 2: polaris.metric.v2.RateLimitGRPCV2.Service:output_type -> polaris.metric.v2.RateLimitResponse
	3, // 3: polaris.metric.v2.RateLimitGRPCV2.TimeAdjust:output_type -> polaris.metric.v2.TimeAdjustResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_metric_v2_grpcapi_v2_proto_init() }
func file_metric_v2_grpcapi_v2_proto_init() {
	if File_metric_v2_grpcapi_v2_proto != nil {
		return
	}
	file_metric_v2_ratelimit_v2_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_metric_v2_grpcapi_v2_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_metric_v2_grpcapi_v2_proto_goTypes,
		DependencyIndexes: file_metric_v2_grpcapi_v2_proto_depIdxs,
	}.Build()
	File_metric_v2_grpcapi_v2_proto = out.File
	file_metric_v2_grpcapi_v2_proto_rawDesc = nil
	file_metric_v2_grpcapi_v2_proto_goTypes = nil
	file_metric_v2_grpcapi_v2_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// RateLimitGRPCV2Client is the client API for RateLimitGRPCV2 service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RateLimitGRPCV2Client interface {
	// 限流接口
	Service(ctx context.Context, opts ...grpc.CallOption) (RateLimitGRPCV2_ServiceClient, error)
	//时间对齐接口
	TimeAdjust(ctx context.Context, in *TimeAdjustRequest, opts ...grpc.CallOption) (*TimeAdjustResponse, error)
}

type rateLimitGRPCV2Client struct {
	cc grpc.ClientConnInterface
}

func NewRateLimitGRPCV2Client(cc grpc.ClientConnInterface) RateLimitGRPCV2Client {
	return &rateLimitGRPCV2Client{cc}
}

func (c *rateLimitGRPCV2Client) Service(ctx context.Context, opts ...grpc.CallOption) (RateLimitGRPCV2_ServiceClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RateLimitGRPCV2_serviceDesc.Streams[0], "/polaris.metric.v2.RateLimitGRPCV2/Service", opts...)
	if err != nil {
		return nil, err
	}
	x := &rateLimitGRPCV2ServiceClient{stream}
	return x, nil
}

type RateLimitGRPCV2_ServiceClient interface {
	Send(*RateLimitRequest) error
	Recv() (*RateLimitResponse, error)
	grpc.ClientStream
}

type rateLimitGRPCV2ServiceClient struct {
	grpc.ClientStream
}

func (x *rateLimitGRPCV2ServiceClient) Send(m *RateLimitRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *rateLimitGRPCV2ServiceClient) Recv() (*RateLimitResponse, error) {
	m := new(RateLimitResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rateLimitGRPCV2Client) TimeAdjust(ctx context.Context, in *TimeAdjustRequest, opts ...grpc.CallOption) (*TimeAdjustResponse, error) {
	out := new(TimeAdjustResponse)
	err := c.cc.Invoke(ctx, "/polaris.metric.v2.RateLimitGRPCV2/TimeAdjust", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RateLimitGRPCV2Server is the server API for RateLimitGRPCV2 service.
type RateLimitGRPCV2Server interface {
	// 限流接口
	Service(RateLimitGRPCV2_ServiceServer) error
	//时间对齐接口
	TimeAdjust(context.Context, *TimeAdjustRequest) (*TimeAdjustResponse, error)
}

// UnimplementedRateLimitGRPCV2Server can be embedded to have forward compatible implementations.
type UnimplementedRateLimitGRPCV2Server struct {
}

func (*UnimplementedRateLimitGRPCV2Server) Service(RateLimitGRPCV2_ServiceServer) error {
	return status.Errorf(codes.Unimplemented, "method Service not implemented")
}
func (*UnimplementedRateLimitGRPCV2Server) TimeAdjust(context.Context, *TimeAdjustRequest) (*TimeAdjustResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TimeAdjust not implemented")
}

func RegisterRateLimitGRPCV2Server(s *grpc.Server, srv RateLimitGRPCV2Server) {
	s.RegisterService(&_RateLimitGRPCV2_serviceDesc, srv)
}

func _RateLimitGRPCV2_Service_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(RateLimitGRPCV2Server).Service(&rateLimitGRPCV2ServiceServer{stream})
}

type RateLimitGRPCV2_ServiceServer interface {
	Send(*RateLimitResponse) error
	Recv() (*RateLimitRequest, error)
	grpc.ServerStream
}

type rateLimitGRPCV2ServiceServer struct {
	grpc.ServerStream
}

func (x *rateLimitGRPCV2ServiceServer) Send(m *RateLimitResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *rateLimitGRPCV2ServiceServer) Recv() (*RateLimitRequest, error) {
	m := new(RateLimitRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _RateLimitGRPCV2_TimeAdjust_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeAdjustRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RateLimitGRPCV2Server).TimeAdjust(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/polaris.metric.v2.RateLimitGRPCV2/TimeAdjust",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RateLimitGRPCV2Server).TimeAdjust(ctx, req.(*TimeAdjustRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _RateLimitGRPCV2_serviceDesc = grpc.ServiceDesc{
	ServiceName: "polaris.metric.v2.RateLimitGRPCV2",
	HandlerType: (*RateLimitGRPCV2Server)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TimeAdjust",
			Handler:    _RateLimitGRPCV2_TimeAdjust_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Service",
			Handler:       _RateLimitGRPCV2_Service_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "metric/v2/grpcapi_v2.proto",
}
