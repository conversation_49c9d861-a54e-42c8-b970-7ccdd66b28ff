// Package photo_struct_wrap comment
// Code generated by trpc4tars 2.0. DO NOT EDIT.
// source: photo_struct_wrap_jce.jce
package photo_struct_wrap

import (
	"fmt"

	"git.woa.com/jce/jce"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = fmt.Errorf
var _ = jce.Marshal

type EXTINFO_KEY int32

const (
	EXTINFO_KEY_EXTINFO_KEY_CAMERATYPE       = 1
	EXTINFO_KEY_EXTINFO_KEY_CENTERPOINT      = 2
	EXTINFO_KEY_EXTINFO_KEY_FINDFACE         = 3
	EXTINFO_KEY_EXTINFO_KEY_POI              = 4
	EXTINFO_KEY_EXTINFO_KEY_PLATFORMINFO     = 5
	EXTINFO_KEY_EXTINFO_KEY_MOBILEEXTINFO    = 6
	EXTINFO_KEY_EXTINFO_KEY_EXIF             = 7
	EXTINFO_KEY_EXTINFO_KEY_GEO              = 8
	EXTINFO_KEY_EXTINFO_KEY_UPLOADUIN        = 9
	EXTINFO_KEY_EXTINFO_KEY_QUN_EXT          = 10
	EXTINFO_KEY_EXTINFO_KEY_PRIVATE_COMMENT  = 11
	EXTINFO_KEY_EXTINFO_KEY_NO_FEEDS         = 12
	EXTINFO_KEY_EXTINFO_KEY_EXTERNAL_EXT     = 13
	EXTINFO_KEY_EXTINFO_KEY_APP_PLATFORMINFO = 14
	EXTINFO_KEY_EXTINFO_KEY_PHOTOS_VIDEO     = 15
	EXTINFO_KEY_EXTINFO_KEY_CLUSTER_FACE     = 16
	EXTINFO_KEY_EXTINFO_KEY_LABEL            = 17
	EXTINFO_KEY_EXTINFO_KEY_ZZ_SOURCE        = 18
	EXTINFO_KEY_EXTINFO_KEY_UPLOAD_ENTRANCE  = 19
	EXTINFO_KEY_EXTINFO_KEY_GEN_URL_EXT      = 20
	EXTINFO_KEY_EXTINFO_KEY_OCR              = 21
	EXTINFO_KEY_EXTINFO_KEY_UPLOAD_UID       = 22
	EXTINFO_KEY_EXTINFO_KEY_SYNC_TIME        = 23
)

type EXTERNAL_JUMP_TYPE int32

const (
	EXTERNAL_JUMP_TYPE_EXTERNAL_JUMP_TYPE_DIANPING_POI_ID = 1
)

type EXIF_KEY int32

const (
	EXIF_KEY_EXIF_KEY_ISO                   = 1
	EXIF_KEY_EXIF_KEY_EXPOSURE_TIME         = 2
	EXIF_KEY_EXIF_KEY_EXPOSURE_COMPENSATION = 3
	EXIF_KEY_EXIF_KEY_EXPOSURE_PROGRAM      = 4
	EXIF_KEY_EXIF_KEY_EXPOSURE_MODE         = 5
	EXIF_KEY_EXIF_KEY_FNUMBER               = 6
	EXIF_KEY_EXIF_KEY_FLASH                 = 7
	EXIF_KEY_EXIF_KEY_FOCAL_LENGTH          = 8
	EXIF_KEY_EXIF_KEY_MAKE                  = 9
	EXIF_KEY_EXIF_KEY_MODEL                 = 10
	EXIF_KEY_EXIF_EKY_ORIGINAL_TIME         = 11
	EXIF_KEY_GPS_KEY_LATITUDEREF            = 12
	EXIF_KEY_GPS_KEY_LATITUDE               = 13
	EXIF_KEY_GPS_KEY_LONGITUDEREF           = 14
	EXIF_KEY_GPS_KEY_LONGITUDE              = 15
	EXIF_KEY_GPS_KEY_ALTITUDEREF            = 16
	EXIF_KEY_GPS_KEY_ALTITUDE               = 17
	EXIF_KEY_EXIF_EKY_METERING_MODE         = 18
	EXIF_KEY_EXIF_KEY_LENS_MODEL            = 19
)

type CENTERPOINT_FLAG int32

const (
	CENTERPOINT_FLAG_CENTERPOINT_FLAG_FACE = 1
)

// StExtinfo struct implement
type StExtinfo struct {
	Extinfo_map map[uint32]string `json:"extinfo_map"`
}

func (st *StExtinfo) MakesureNotNil() {
}
func (st *StExtinfo) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StExtinfo) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have = _is.SkipTo(jce.MAP, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&length, 0, true)
	if err != nil {
		return err
	}

	st.Extinfo_map = make(map[uint32]string)
	for i0, e0 := int32(0), length; i0 < e0; i0++ {
		var k0 uint32
		var v0 string

		err = _is.Read_uint32(&k0, 0, false)
		if err != nil {
			return err
		}

		err = _is.Read_string(&v0, 1, false)
		if err != nil {
			return err
		}

		st.Extinfo_map[k0] = v0
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StExtinfo) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StExtinfo, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StExtinfo) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.MAP, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Extinfo_map)), 0)
	if err != nil {
		return err
	}

	for k1, v1 := range st.Extinfo_map {

		err = _os.Write_uint32(k1, 0)
		if err != nil {
			return err
		}

		err = _os.Write_string(v1, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StExtinfo) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StExtinfo) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StExtinfo) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StExtinfo) WupTypeName() string {
	return "photo_struct_wrap.stExtinfo"
}

// StFaceRect struct implement
type StFaceRect struct {
	Left       uint32 `json:"left"`
	Right      uint32 `json:"right"`
	Top        uint32 `json:"top"`
	Bottom     uint32 `json:"bottom"`
	Confidence uint32 `json:"confidence"`
}

func (st *StFaceRect) MakesureNotNil() {
}
func (st *StFaceRect) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StFaceRect) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_uint32(&st.Left, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Right, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Top, 2, true)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Bottom, 3, true)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Confidence, 4, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StFaceRect) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StFaceRect, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StFaceRect) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_uint32(st.Left, 0)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Right, 1)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Top, 2)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Bottom, 3)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Confidence, 4)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StFaceRect) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StFaceRect) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StFaceRect) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StFaceRect) WupTypeName() string {
	return "photo_struct_wrap.stFaceRect"
}

// StVFaceRect struct implement
type StVFaceRect struct {
	VFaceRect []StFaceRect `json:"vFaceRect"`
}

func (st *StVFaceRect) MakesureNotNil() {
}
func (st *StVFaceRect) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StVFaceRect) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have, ty = _is.SkipToNoCheck(0, true)
	if err != nil {
		return err
	}

	if ty == jce.LIST {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.VFaceRect = make([]StFaceRect, length)
		for i0, e0 := int32(0), length; i0 < e0; i0++ {

			err = st.VFaceRect[i0].ReadBlock(_is, 0, false)
			if err != nil {
				return err
			}

		}
	} else if ty == jce.SIMPLE_LIST {
		err = fmt.Errorf("not support simple_list type")
		if err != nil {
			return err
		}

	} else {
		err = fmt.Errorf("require vector, but not")
		if err != nil {
			return err
		}

	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StVFaceRect) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StVFaceRect, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StVFaceRect) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.LIST, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.VFaceRect)), 0)
	if err != nil {
		return err
	}

	for _, v := range st.VFaceRect {

		err = v.WriteBlock(_os, 0)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StVFaceRect) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StVFaceRect) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StVFaceRect) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StVFaceRect) WupTypeName() string {
	return "photo_struct_wrap.stVFaceRect"
}

// StFaceRect_store struct implement
type StFaceRect_store struct {
	Leftp      uint8 `json:"leftp"`
	Rightp     uint8 `json:"rightp"`
	Topp       uint8 `json:"topp"`
	Bottomp    uint8 `json:"bottomp"`
	Confidence uint8 `json:"confidence"`
}

func (st *StFaceRect_store) MakesureNotNil() {
}
func (st *StFaceRect_store) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StFaceRect_store) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_uint8(&st.Leftp, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_uint8(&st.Rightp, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_uint8(&st.Topp, 2, true)
	if err != nil {
		return err
	}

	err = _is.Read_uint8(&st.Bottomp, 3, true)
	if err != nil {
		return err
	}

	err = _is.Read_uint8(&st.Confidence, 4, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StFaceRect_store) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StFaceRect_store, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StFaceRect_store) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_uint8(st.Leftp, 0)
	if err != nil {
		return err
	}

	err = _os.Write_uint8(st.Rightp, 1)
	if err != nil {
		return err
	}

	err = _os.Write_uint8(st.Topp, 2)
	if err != nil {
		return err
	}

	err = _os.Write_uint8(st.Bottomp, 3)
	if err != nil {
		return err
	}

	err = _os.Write_uint8(st.Confidence, 4)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StFaceRect_store) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StFaceRect_store) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StFaceRect_store) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StFaceRect_store) WupTypeName() string {
	return "photo_struct_wrap.stFaceRect_store"
}

// StVFaceRect_store struct implement
type StVFaceRect_store struct {
	VFaceRect   []StFaceRect_store `json:"vFaceRect"`
	Haveface    uint32             `json:"haveface"`
	Anno_runned uint32             `json:"anno_runned"`
}

func (st *StVFaceRect_store) MakesureNotNil() {
}
func (st *StVFaceRect_store) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StVFaceRect_store) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have, ty = _is.SkipToNoCheck(0, true)
	if err != nil {
		return err
	}

	if ty == jce.LIST {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.VFaceRect = make([]StFaceRect_store, length)
		for i0, e0 := int32(0), length; i0 < e0; i0++ {

			err = st.VFaceRect[i0].ReadBlock(_is, 0, false)
			if err != nil {
				return err
			}

		}
	} else if ty == jce.SIMPLE_LIST {
		err = fmt.Errorf("not support simple_list type")
		if err != nil {
			return err
		}

	} else {
		err = fmt.Errorf("require vector, but not")
		if err != nil {
			return err
		}

	}

	err = _is.Read_uint32(&st.Haveface, 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Anno_runned, 2, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StVFaceRect_store) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StVFaceRect_store, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StVFaceRect_store) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.LIST, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.VFaceRect)), 0)
	if err != nil {
		return err
	}

	for _, v := range st.VFaceRect {

		err = v.WriteBlock(_os, 0)
		if err != nil {
			return err
		}

	}

	err = _os.Write_uint32(st.Haveface, 1)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Anno_runned, 2)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StVFaceRect_store) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StVFaceRect_store) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StVFaceRect_store) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StVFaceRect_store) WupTypeName() string {
	return "photo_struct_wrap.stVFaceRect_store"
}

// StPoi struct implement
type StPoi struct {
	Id        string `json:"id"`
	Pos_x     string `json:"pos_x"`
	Pos_y     string `json:"pos_y"`
	Idname    string `json:"idname"`
	Name      string `json:"name"`
	Type      int32  `json:"type"`
	Country   string `json:"country"`
	Province  string `json:"province"`
	City      string `json:"city"`
	Place     string `json:"place"`
	Show_data int32  `json:"show_data"`
	Region    string `json:"region"`
}

func (st *StPoi) MakesureNotNil() {
}
func (st *StPoi) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StPoi) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_string(&st.Id, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Pos_x, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Pos_y, 2, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Idname, 3, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Name, 4, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Type, 5, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Country, 6, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Province, 7, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.City, 8, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Place, 9, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Show_data, 10, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Region, 11, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StPoi) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StPoi, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StPoi) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_string(st.Id, 0)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Pos_x, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Pos_y, 2)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Idname, 3)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Name, 4)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Type, 5)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Country, 6)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Province, 7)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.City, 8)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Place, 9)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Show_data, 10)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Region, 11)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StPoi) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StPoi) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StPoi) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StPoi) WupTypeName() string {
	return "photo_struct_wrap.stPoi"
}

// StAppPlatInfo struct implement
type StAppPlatInfo struct {
	Open_appid       uint32 `json:"open_appid"`
	Android_app_name string `json:"android_app_name"`
	Android_schema   string `json:"android_schema"`
	Ios_schema       string `json:"ios_schema"`
	Ios_app_name     string `json:"ios_app_name"`
}

func (st *StAppPlatInfo) MakesureNotNil() {
}
func (st *StAppPlatInfo) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StAppPlatInfo) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_uint32(&st.Open_appid, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Android_app_name, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Android_schema, 2, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Ios_schema, 3, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Ios_app_name, 4, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StAppPlatInfo) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StAppPlatInfo, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StAppPlatInfo) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_uint32(st.Open_appid, 0)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Android_app_name, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Android_schema, 2)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Ios_schema, 3)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Ios_app_name, 4)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StAppPlatInfo) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StAppPlatInfo) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StAppPlatInfo) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StAppPlatInfo) WupTypeName() string {
	return "photo_struct_wrap.stAppPlatInfo"
}

// StPlatformInfo struct implement
type StPlatformInfo struct {
	Platformid    int32  `json:"platformid"`
	Platformsubid int32  `json:"platformsubid"`
	Mobileagent   string `json:"mobileagent"`
}

func (st *StPlatformInfo) MakesureNotNil() {
}
func (st *StPlatformInfo) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StPlatformInfo) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_int32(&st.Platformid, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Platformsubid, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Mobileagent, 2, true)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StPlatformInfo) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StPlatformInfo, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StPlatformInfo) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_int32(st.Platformid, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Platformsubid, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Mobileagent, 2)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StPlatformInfo) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StPlatformInfo) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StPlatformInfo) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StPlatformInfo) WupTypeName() string {
	return "photo_struct_wrap.stPlatformInfo"
}

// StPhotoVideoInfo struct implement
type StPhotoVideoInfo struct {
	Is_video     bool   `json:"is_video"`
	Vid          string `json:"vid"`
	Format       string `json:"format"`
	Time_len     uint32 `json:"time_len"`
	Size         uint32 `json:"size"`
	Status       int32  `json:"status"`
	Cover_height int32  `json:"cover_height"`
	Cover_width  int32  `json:"cover_width"`
	Video_type   int32  `json:"video_type"`
	Is_videoflow bool   `json:"is_videoflow"`
}

func (st *StPhotoVideoInfo) MakesureNotNil() {
}
func (st *StPhotoVideoInfo) ResetDefault() {
	st.MakesureNotNil()
	st.Is_video = false
	st.Is_videoflow = false
}

// ReadFrom reads  from _is and put into struct.
func (st *StPhotoVideoInfo) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_bool(&st.Is_video, 0, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Vid, 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Format, 2, false)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Time_len, 3, false)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Size, 4, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Status, 5, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Cover_height, 6, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Cover_width, 7, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Video_type, 8, false)
	if err != nil {
		return err
	}

	err = _is.Read_bool(&st.Is_videoflow, 9, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StPhotoVideoInfo) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StPhotoVideoInfo, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StPhotoVideoInfo) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_bool(st.Is_video, 0)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Vid, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Format, 2)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Time_len, 3)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Size, 4)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Status, 5)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Cover_height, 6)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Cover_width, 7)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Video_type, 8)
	if err != nil {
		return err
	}

	err = _os.Write_bool(st.Is_videoflow, 9)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StPhotoVideoInfo) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StPhotoVideoInfo) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StPhotoVideoInfo) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StPhotoVideoInfo) WupTypeName() string {
	return "photo_struct_wrap.stPhotoVideoInfo"
}

// StMobileExtInfo struct implement
type StMobileExtInfo struct {
	MapMobileExtInfo map[string]string `json:"mapMobileExtInfo"`
}

func (st *StMobileExtInfo) MakesureNotNil() {
}
func (st *StMobileExtInfo) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StMobileExtInfo) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have = _is.SkipTo(jce.MAP, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&length, 0, true)
	if err != nil {
		return err
	}

	st.MapMobileExtInfo = make(map[string]string)
	for i0, e0 := int32(0), length; i0 < e0; i0++ {
		var k0 string
		var v0 string

		err = _is.Read_string(&k0, 0, false)
		if err != nil {
			return err
		}

		err = _is.Read_string(&v0, 1, false)
		if err != nil {
			return err
		}

		st.MapMobileExtInfo[k0] = v0
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StMobileExtInfo) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StMobileExtInfo, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StMobileExtInfo) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.MAP, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.MapMobileExtInfo)), 0)
	if err != nil {
		return err
	}

	for k1, v1 := range st.MapMobileExtInfo {

		err = _os.Write_string(k1, 0)
		if err != nil {
			return err
		}

		err = _os.Write_string(v1, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StMobileExtInfo) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StMobileExtInfo) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StMobileExtInfo) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StMobileExtInfo) WupTypeName() string {
	return "photo_struct_wrap.stMobileExtInfo"
}

// StExifInfo struct implement
type StExifInfo struct {
	MapExifInfo map[int32]string `json:"mapExifInfo"`
}

func (st *StExifInfo) MakesureNotNil() {
}
func (st *StExifInfo) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StExifInfo) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have = _is.SkipTo(jce.MAP, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&length, 0, true)
	if err != nil {
		return err
	}

	st.MapExifInfo = make(map[int32]string)
	for i0, e0 := int32(0), length; i0 < e0; i0++ {
		var k0 int32
		var v0 string

		err = _is.Read_int32(&k0, 0, false)
		if err != nil {
			return err
		}

		err = _is.Read_string(&v0, 1, false)
		if err != nil {
			return err
		}

		st.MapExifInfo[k0] = v0
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StExifInfo) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StExifInfo, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StExifInfo) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.MAP, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.MapExifInfo)), 0)
	if err != nil {
		return err
	}

	for k1, v1 := range st.MapExifInfo {

		err = _os.Write_int32(k1, 0)
		if err != nil {
			return err
		}

		err = _os.Write_string(v1, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StExifInfo) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StExifInfo) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StExifInfo) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StExifInfo) WupTypeName() string {
	return "photo_struct_wrap.stExifInfo"
}

// StOcrInfo struct implement
type StOcrInfo struct {
	StrOcrContent string `json:"strOcrContent"`
	OcrKey        string `json:"ocrKey"`
	HasOcr        int32  `json:"hasOcr"`
}

func (st *StOcrInfo) MakesureNotNil() {
}
func (st *StOcrInfo) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StOcrInfo) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_string(&st.StrOcrContent, 0, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.OcrKey, 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.HasOcr, 2, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StOcrInfo) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StOcrInfo, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StOcrInfo) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_string(st.StrOcrContent, 0)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.OcrKey, 1)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.HasOcr, 2)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StOcrInfo) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StOcrInfo) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StOcrInfo) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StOcrInfo) WupTypeName() string {
	return "photo_struct_wrap.stOcrInfo"
}

// StQunMapExt struct implement
type StQunMapExt struct {
	QunMapExt map[string]string `json:"qunMapExt"`
}

func (st *StQunMapExt) MakesureNotNil() {
}
func (st *StQunMapExt) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StQunMapExt) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have = _is.SkipTo(jce.MAP, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&length, 0, true)
	if err != nil {
		return err
	}

	st.QunMapExt = make(map[string]string)
	for i0, e0 := int32(0), length; i0 < e0; i0++ {
		var k0 string
		var v0 string

		err = _is.Read_string(&k0, 0, false)
		if err != nil {
			return err
		}

		err = _is.Read_string(&v0, 1, false)
		if err != nil {
			return err
		}

		st.QunMapExt[k0] = v0
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StQunMapExt) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StQunMapExt, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StQunMapExt) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.MAP, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.QunMapExt)), 0)
	if err != nil {
		return err
	}

	for k1, v1 := range st.QunMapExt {

		err = _os.Write_string(k1, 0)
		if err != nil {
			return err
		}

		err = _os.Write_string(v1, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StQunMapExt) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StQunMapExt) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StQunMapExt) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StQunMapExt) WupTypeName() string {
	return "photo_struct_wrap.stQunMapExt"
}

// StExternalMapExt struct implement
type StExternalMapExt struct {
	ExternalMapExt map[string]string `json:"externalMapExt"`
}

func (st *StExternalMapExt) MakesureNotNil() {
}
func (st *StExternalMapExt) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StExternalMapExt) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have = _is.SkipTo(jce.MAP, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&length, 0, true)
	if err != nil {
		return err
	}

	st.ExternalMapExt = make(map[string]string)
	for i0, e0 := int32(0), length; i0 < e0; i0++ {
		var k0 string
		var v0 string

		err = _is.Read_string(&k0, 0, false)
		if err != nil {
			return err
		}

		err = _is.Read_string(&v0, 1, false)
		if err != nil {
			return err
		}

		st.ExternalMapExt[k0] = v0
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StExternalMapExt) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StExternalMapExt, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StExternalMapExt) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.MAP, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.ExternalMapExt)), 0)
	if err != nil {
		return err
	}

	for k1, v1 := range st.ExternalMapExt {

		err = _os.Write_string(k1, 0)
		if err != nil {
			return err
		}

		err = _os.Write_string(v1, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StExternalMapExt) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StExternalMapExt) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StExternalMapExt) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StExternalMapExt) WupTypeName() string {
	return "photo_struct_wrap.stExternalMapExt"
}
