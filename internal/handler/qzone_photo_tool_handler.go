package handler

import (
	"context"
	"fmt"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/QzonePlatform/qzone-backend/common/photo/psa"
	"git.woa.com/fanniliu/output_error_qq_photos/internal/app_config"
	"git.woa.com/fanniliu/output_error_qq_photos/internal/repo"
	"git.woa.com/fanniliu/output_error_qq_photos/internal/util"
	"git.woa.com/social-AIGC/aigc_access/config"
	"git.woa.com/trpcprotocol/qzone/storage_photo"
	"git.woa.com/trpcprotocol/zy_socail/qq_photos"
)

var StopFlag = false
var stopChan chan struct{} = make(chan struct{}, 1)

func ListenForUSREndSignal() {
	<-stopChan
	StopFlag = true
}

type QzonePhotoToolHandlerImpl struct {
	qzoneProxy *repo.QzoneProxy
	config     *config.QzoneConfig
}

func NewQzonePhotoToolHandlerImpl() *QzonePhotoToolHandlerImpl {
	qzoneConfig := &config.QzoneConfig{
		ServiceName:       "trpc.psa.psa.psaphoto",
		BatchSize:         2000,
		Bsid:              "photo",
		UpdateDiffCount:   100,
		UpdateDiffPercent: 0.10,
		RetryTimes:        1,
		Concurrency:       20,
		AlbumNameFilterMap: map[string]bool{
			"贴图相册": true,
			"微博相册": true,
			"圈子相册": true,
			"系统相册": true,
		},
		AlbumNameFilter: []string{"贴图相册", "微博相册", "圈子相册", "系统相册"},
	}
	return &QzonePhotoToolHandlerImpl{
		config:     qzoneConfig,
		qzoneProxy: repo.NewQzoneProxy(qzoneConfig),
	}
}

// todo 修改这个方法
func (this *QzonePhotoToolHandlerImpl) FindErrorPhotosByUins(uins []uint64, concurrency int32) ([]*qq_photos.PhotoErrorInfo, []uint64, []*qq_photos.PhotoUnprocessedInfo, error) {
	var result []*qq_photos.PhotoErrorInfo
	for _, uin := range uins {
		errList, err := this.FindErrorPhotosByUin(uin)
		if err != nil {
			log.Errorf("findErrorPhotosByUin err:%v", err)
			continue
		}
		result = append(result, errList...)
	}
	return result, nil
}

func (this *QzonePhotoToolHandlerImpl) FindErrorPhotosByUin(uin uint64) ([]*qq_photos.PhotoErrorInfo, []*qq_photos.PhotoUnprocessedInfo, error) {
	// 1，调用接口，获得根据uin，获取所有用户所属的照片列表
	if this.qzoneProxy == nil {
		return nil, nil, fmt.Errorf("qzoneProxy proxy not initialized")
	}
	photoList, err := this.qzoneProxy.GetPhotoList(context.Background(), strconv.FormatUint(uin, 10))
	if err != nil {
		return nil, nil, err
	}
	log.DebugContextf(context.Background(), "用户 uin: %d,拉取图片总数量: %d", uin, len(photoList))
	// 2，遍历图片列表，找出所有时间不一致的图片id、lloc，它们可能是有问题的图片
	var possibleErrPhotos []*storage_photo.PhotoInfo = []*storage_photo.PhotoInfo{}
	// var uploadTimeList []uint32 = []uint32{} //todo 输出时间
	// var decodeTimeList []uint32 = []uint32{} //todo 输出时间
	// var lloccodeList []string = []string{}   //todo debug
	for _, photo := range photoList {
		uploadTime := photo.PrimaryInfo.UploadTime //todo

		if photo.PrimaryInfo.UploadTime == nil {
			log.ErrorContextf(context.Background(), "UploadTime is nil, photo: %+v", photo)
			continue
		}

		//不在 2017年4月1日 到 2017年12月31日之间，跳过
		if *photo.PrimaryInfo.UploadTime > 1514764799 || *photo.PrimaryInfo.UploadTime < 1491004800 { //todo 解封，暂时不过滤时间
			continue
		}

		//从llocode中解析出decodeTime
		photoId, _, err := util.ParseQzoneCloudLLOC(string(photo.PrimaryInfo.Lloccode)) //解码photoId

		if err != nil {
			log.ErrorContextf(context.Background(), "ParseQzoneCloudLLOC error, lloccode: %s, err: %v", string(photo.PrimaryInfo.Lloccode), err)
			continue
		}

		_, decodeTime, _, _, err := util.ParsePhotoID(photoId) //todo

		if err != nil {
			log.ErrorContextf(context.Background(), "ParsePhotoID error, photoId: %s, err: %v", photoId, err)
			continue
		}

		//todo 输出时间 不过滤
		// uploadTimeList = append(uploadTimeList, *uploadTime)
		// decodeTimeList = append(decodeTimeList, decodeTime)
		// lloccodeList = append(lloccodeList, string(photo.PrimaryInfo.Lloccode))

		// 如果上传时间与解码时间不一致，说明图片可能有错误 todo 解封
		if *uploadTime != decodeTime {
			possibleErrPhotos = append(possibleErrPhotos, photo)
		}
		// todo 不过滤
		// possibleErrPhotos = append(possibleErrPhotos, photo)
		// possibleErrPhotoIds = append(possibleErrPhotoIds, photoId)
	}
	log.DebugContextf(context.Background(), "用户 uin: %d,经过时间对比，可能有问题的图片总数量: %d", uin, len(possibleErrPhotos))

	// 3，遍历可能有问题的图片，用phash算法，对比图片的相似度，找出相似度很低的图片，它们是有问题的
	// var errPhotos []*storage_photo.PhotoInfo = []*storage_photo.PhotoInfo{}

	resultErrorPhotoList := []*qq_photos.PhotoErrorInfo{}
	resultUnprocessedPhotoList := []*qq_photos.PhotoUnprocessedInfo{}

	for _, possibleErrPhoto := range possibleErrPhotos {
		//拼接图片url
		bigPicUrl := psa.GenCloudURLFromLloc(strconv.FormatUint(uin, 10), string(possibleErrPhoto.PrimaryInfo.Lloccode), *possibleErrPhoto.PrimaryInfo.UploadTime, string(possibleErrPhoto.PrimaryInfo.Albumid), psa.BIG)
		rawPicUrl := psa.GenCloudURLFromLloc(strconv.FormatUint(uin, 10), string(possibleErrPhoto.PrimaryInfo.Lloccode), *possibleErrPhoto.PrimaryInfo.UploadTime, string(possibleErrPhoto.PrimaryInfo.Albumid), psa.RAW)
		//取出图片，调用phash算法，计算他们的相似度
		bigPic, err := util.DownloadImage(bigPicUrl)
		if err != nil {
			log.ErrorContextf(context.Background(), "DownloadImage error, url: %s, err: %v", bigPicUrl, err)
			resultUnprocessedPhotoList = append(resultUnprocessedPhotoList, &qq_photos.PhotoUnprocessedInfo{
				Uin:       strconv.FormatUint(uin, 10),
				AlbumId:   string(possibleErrPhoto.PrimaryInfo.Albumid),
				Lloc:      string(possibleErrPhoto.PrimaryInfo.Lloccode),
				BigPicUrl: bigPicUrl,
				RawPicUrl: rawPicUrl,
			})
			continue
		}

		rawPic, err := util.DownloadImage(rawPicUrl)
		if err != nil {
			if !bigPic.Empty() {
				bigPic.Close()
			}
			log.ErrorContextf(context.Background(), "DownloadImage error, url: %s, err: %v", bigPicUrl, err)
			resultUnprocessedPhotoList = append(resultUnprocessedPhotoList, &qq_photos.PhotoUnprocessedInfo{
				Uin:       strconv.FormatUint(uin, 10),
				AlbumId:   string(possibleErrPhoto.PrimaryInfo.Albumid),
				Lloc:      string(possibleErrPhoto.PrimaryInfo.Lloccode),
				BigPicUrl: bigPicUrl,
				RawPicUrl: rawPicUrl,
			})
			continue
		}

		distance, similar, err := util.CompareImagesByPhash(bigPic, rawPic, app_config.GetConfig().Util.Phash.DistanceThreshold)

		if !bigPic.Empty() {
			bigPic.Close()
		}
		if !rawPic.Empty() {
			rawPic.Close()
		}

		if err != nil {
			log.ErrorContextf(context.Background(), "比较图片失败: %v,bigPicUrl: %s, rawPicUrl: %s", err, bigPicUrl, rawPicUrl)
			resultUnprocessedPhotoList = append(resultUnprocessedPhotoList, &qq_photos.PhotoUnprocessedInfo{
				Uin:       strconv.FormatUint(uin, 10),
				AlbumId:   string(possibleErrPhoto.PrimaryInfo.Albumid),
				Lloc:      string(possibleErrPhoto.PrimaryInfo.Lloccode),
				BigPicUrl: bigPicUrl,
				RawPicUrl: rawPicUrl,
			})
			continue
		}

		if !similar {
			// errPhotos = append(errPhotos, possibleErrPhoto)
			resultErrorPhotoList = append(resultErrorPhotoList, &qq_photos.PhotoErrorInfo{
				Uin:     strconv.FormatUint(uin, 10),
				AlbumId: string(possibleErrPhoto.PrimaryInfo.Albumid),
				Lloc:    string(possibleErrPhoto.PrimaryInfo.Lloccode),
			})
		}

		// log.InfoContextf(context.Background(), "图片差异度: %d, bigPicUrl: %s, rawPicUrl: %s ,uploadtime: %d, decodeTime: %d ,lloccode: %s", distance, bigPicUrl, rawPicUrl, uploadTimeList[i], decodeTimeList[i], lloccodeList[i]) //todo 调试用，后面删除
		log.DebugContextf(context.Background(), "图片差异度: %d, bigPicUrl: %s, rawPicUrl: %s ,uploadtime: %d, decodeTime: %d ,lloccode: %s", distance, bigPicUrl, rawPicUrl) //todo 调试用，后面删除
	}
	// 4，返回有问题的图片id todo 图片id是string，不是uin
	//todo 返回图片id
	// for _, errPhotoId := range errPhotoIds {
	// 	photoIdUint64, err := strconv.ParseUint(errPhotoId, 10, 64)
	// 	if err != nil {
	// 		log.ErrorContextf(context.Background(), "ParseUint error, errPhotoId: %s, err: %v", errPhotoId, err)
	// 		continue
	// 	}
	// 	result = append(result, photoIdUint64)
	// }

	return resultErrorPhotoList, resultUnprocessedPhotoList, nil
}
