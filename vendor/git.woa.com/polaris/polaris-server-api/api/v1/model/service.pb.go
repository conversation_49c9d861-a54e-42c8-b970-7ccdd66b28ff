// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v1/model/service.proto

package polarismodel_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AliasType int32

const (
	AliasType_DEFAULT AliasType = 0
	AliasType_CL5SID  AliasType = 1
)

// Enum value maps for AliasType.
var (
	AliasType_name = map[int32]string{
		0: "DEFAULT",
		1: "CL5SID",
	}
	AliasType_value = map[string]int32{
		"DEFAULT": 0,
		"CL5SID":  1,
	}
)

func (x AliasType) Enum() *AliasType {
	p := new(AliasType)
	*p = x
	return p
}

func (x AliasType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AliasType) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_service_proto_enumTypes[0].Descriptor()
}

func (AliasType) Type() protoreflect.EnumType {
	return &file_v1_model_service_proto_enumTypes[0]
}

func (x AliasType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AliasType.Descriptor instead.
func (AliasType) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_service_proto_rawDescGZIP(), []int{0}
}

type HealthCheck_HealthCheckType int32

const (
	HealthCheck_UNKNOWN   HealthCheck_HealthCheckType = 0
	HealthCheck_HEARTBEAT HealthCheck_HealthCheckType = 1
)

// Enum value maps for HealthCheck_HealthCheckType.
var (
	HealthCheck_HealthCheckType_name = map[int32]string{
		0: "UNKNOWN",
		1: "HEARTBEAT",
	}
	HealthCheck_HealthCheckType_value = map[string]int32{
		"UNKNOWN":   0,
		"HEARTBEAT": 1,
	}
)

func (x HealthCheck_HealthCheckType) Enum() *HealthCheck_HealthCheckType {
	p := new(HealthCheck_HealthCheckType)
	*p = x
	return p
}

func (x HealthCheck_HealthCheckType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HealthCheck_HealthCheckType) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_service_proto_enumTypes[1].Descriptor()
}

func (HealthCheck_HealthCheckType) Type() protoreflect.EnumType {
	return &file_v1_model_service_proto_enumTypes[1]
}

func (x HealthCheck_HealthCheckType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HealthCheck_HealthCheckType.Descriptor instead.
func (HealthCheck_HealthCheckType) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_service_proto_rawDescGZIP(), []int{4, 0}
}

type Namespace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Comment *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=comment,proto3" json:"comment,omitempty"`
	Owners  *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=owners,proto3" json:"owners,omitempty"`
	Token   *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	Ctime   *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Mtime   *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=mtime,proto3" json:"mtime,omitempty"`
}

func (x *Namespace) Reset() {
	*x = Namespace{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Namespace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Namespace) ProtoMessage() {}

func (x *Namespace) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Namespace.ProtoReflect.Descriptor instead.
func (*Namespace) Descriptor() ([]byte, []int) {
	return file_v1_model_service_proto_rawDescGZIP(), []int{0}
}

func (x *Namespace) GetName() *wrapperspb.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *Namespace) GetComment() *wrapperspb.StringValue {
	if x != nil {
		return x.Comment
	}
	return nil
}

func (x *Namespace) GetOwners() *wrapperspb.StringValue {
	if x != nil {
		return x.Owners
	}
	return nil
}

func (x *Namespace) GetToken() *wrapperspb.StringValue {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *Namespace) GetCtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Ctime
	}
	return nil
}

func (x *Namespace) GetMtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Mtime
	}
	return nil
}

type Service struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Namespace  *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Metadata   map[string]string       `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Ports      *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=ports,proto3" json:"ports,omitempty"`
	Business   *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=business,proto3" json:"business,omitempty"`
	Department *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=department,proto3" json:"department,omitempty"`
	CmdbMod1   *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=cmdb_mod1,proto3" json:"cmdb_mod1,omitempty"`
	CmdbMod2   *wrapperspb.StringValue `protobuf:"bytes,8,opt,name=cmdb_mod2,proto3" json:"cmdb_mod2,omitempty"`
	CmdbMod3   *wrapperspb.StringValue `protobuf:"bytes,9,opt,name=cmdb_mod3,proto3" json:"cmdb_mod3,omitempty"`
	Comment    *wrapperspb.StringValue `protobuf:"bytes,10,opt,name=comment,proto3" json:"comment,omitempty"`
	Owners     *wrapperspb.StringValue `protobuf:"bytes,11,opt,name=owners,proto3" json:"owners,omitempty"`
	Token      *wrapperspb.StringValue `protobuf:"bytes,12,opt,name=token,proto3" json:"token,omitempty"`
	Ctime      *wrapperspb.StringValue `protobuf:"bytes,13,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Mtime      *wrapperspb.StringValue `protobuf:"bytes,14,opt,name=mtime,proto3" json:"mtime,omitempty"`
	Revision   *wrapperspb.StringValue `protobuf:"bytes,15,opt,name=revision,proto3" json:"revision,omitempty"`
	PlatformId *wrapperspb.StringValue `protobuf:"bytes,16,opt,name=platform_id,proto3" json:"platform_id,omitempty"`
}

func (x *Service) Reset() {
	*x = Service{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service) ProtoMessage() {}

func (x *Service) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service.ProtoReflect.Descriptor instead.
func (*Service) Descriptor() ([]byte, []int) {
	return file_v1_model_service_proto_rawDescGZIP(), []int{1}
}

func (x *Service) GetName() *wrapperspb.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *Service) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *Service) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Service) GetPorts() *wrapperspb.StringValue {
	if x != nil {
		return x.Ports
	}
	return nil
}

func (x *Service) GetBusiness() *wrapperspb.StringValue {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *Service) GetDepartment() *wrapperspb.StringValue {
	if x != nil {
		return x.Department
	}
	return nil
}

func (x *Service) GetCmdbMod1() *wrapperspb.StringValue {
	if x != nil {
		return x.CmdbMod1
	}
	return nil
}

func (x *Service) GetCmdbMod2() *wrapperspb.StringValue {
	if x != nil {
		return x.CmdbMod2
	}
	return nil
}

func (x *Service) GetCmdbMod3() *wrapperspb.StringValue {
	if x != nil {
		return x.CmdbMod3
	}
	return nil
}

func (x *Service) GetComment() *wrapperspb.StringValue {
	if x != nil {
		return x.Comment
	}
	return nil
}

func (x *Service) GetOwners() *wrapperspb.StringValue {
	if x != nil {
		return x.Owners
	}
	return nil
}

func (x *Service) GetToken() *wrapperspb.StringValue {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *Service) GetCtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Ctime
	}
	return nil
}

func (x *Service) GetMtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Mtime
	}
	return nil
}

func (x *Service) GetRevision() *wrapperspb.StringValue {
	if x != nil {
		return x.Revision
	}
	return nil
}

func (x *Service) GetPlatformId() *wrapperspb.StringValue {
	if x != nil {
		return x.PlatformId
	}
	return nil
}

type ServiceAlias struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Service      *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	Namespace    *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Alias        *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=alias,proto3" json:"alias,omitempty"`
	Type         AliasType               `protobuf:"varint,4,opt,name=type,proto3,enum=api.v1.model.AliasType" json:"type,omitempty"`
	Owners       *wrapperspb.StringValue `protobuf:"bytes,8,opt,name=owners,proto3" json:"owners,omitempty"`
	Comment      *wrapperspb.StringValue `protobuf:"bytes,9,opt,name=comment,proto3" json:"comment,omitempty"`
	ServiceToken *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=service_token,proto3" json:"service_token,omitempty"`
	Ctime        *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Mtime        *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=mtime,proto3" json:"mtime,omitempty"`
}

func (x *ServiceAlias) Reset() {
	*x = ServiceAlias{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceAlias) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceAlias) ProtoMessage() {}

func (x *ServiceAlias) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceAlias.ProtoReflect.Descriptor instead.
func (*ServiceAlias) Descriptor() ([]byte, []int) {
	return file_v1_model_service_proto_rawDescGZIP(), []int{2}
}

func (x *ServiceAlias) GetService() *wrapperspb.StringValue {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *ServiceAlias) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *ServiceAlias) GetAlias() *wrapperspb.StringValue {
	if x != nil {
		return x.Alias
	}
	return nil
}

func (x *ServiceAlias) GetType() AliasType {
	if x != nil {
		return x.Type
	}
	return AliasType_DEFAULT
}

func (x *ServiceAlias) GetOwners() *wrapperspb.StringValue {
	if x != nil {
		return x.Owners
	}
	return nil
}

func (x *ServiceAlias) GetComment() *wrapperspb.StringValue {
	if x != nil {
		return x.Comment
	}
	return nil
}

func (x *ServiceAlias) GetServiceToken() *wrapperspb.StringValue {
	if x != nil {
		return x.ServiceToken
	}
	return nil
}

func (x *ServiceAlias) GetCtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Ctime
	}
	return nil
}

func (x *ServiceAlias) GetMtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Mtime
	}
	return nil
}

type Instance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Service           *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
	Namespace         *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	VpcId             *wrapperspb.StringValue `protobuf:"bytes,21,opt,name=vpc_id,proto3" json:"vpc_id,omitempty"`
	Host              *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=host,proto3" json:"host,omitempty"`
	Port              *wrapperspb.UInt32Value `protobuf:"bytes,5,opt,name=port,proto3" json:"port,omitempty"`
	Protocol          *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=protocol,proto3" json:"protocol,omitempty"`
	Version           *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=version,proto3" json:"version,omitempty"`
	Priority          *wrapperspb.UInt32Value `protobuf:"bytes,8,opt,name=priority,proto3" json:"priority,omitempty"`
	Weight            *wrapperspb.UInt32Value `protobuf:"bytes,9,opt,name=weight,proto3" json:"weight,omitempty"`
	EnableHealthCheck *wrapperspb.BoolValue   `protobuf:"bytes,20,opt,name=enable_health_check,json=enableHealthCheck,proto3" json:"enable_health_check,omitempty"`
	HealthCheck       *HealthCheck            `protobuf:"bytes,10,opt,name=health_check,json=healthCheck,proto3" json:"health_check,omitempty"`
	Healthy           *wrapperspb.BoolValue   `protobuf:"bytes,11,opt,name=healthy,proto3" json:"healthy,omitempty"`
	Isolate           *wrapperspb.BoolValue   `protobuf:"bytes,12,opt,name=isolate,proto3" json:"isolate,omitempty"`
	Location          *Location               `protobuf:"bytes,13,opt,name=location,proto3" json:"location,omitempty"`
	Metadata          map[string]string       `protobuf:"bytes,14,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	LogicSet          *wrapperspb.StringValue `protobuf:"bytes,15,opt,name=logic_set,proto3" json:"logic_set,omitempty"`
	Ctime             *wrapperspb.StringValue `protobuf:"bytes,16,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Mtime             *wrapperspb.StringValue `protobuf:"bytes,17,opt,name=mtime,proto3" json:"mtime,omitempty"`
	Revision          *wrapperspb.StringValue `protobuf:"bytes,18,opt,name=revision,proto3" json:"revision,omitempty"`
	ServiceToken      *wrapperspb.StringValue `protobuf:"bytes,19,opt,name=service_token,proto3" json:"service_token,omitempty"`
	// 实例动态权重，提供给控制台接口使用，SDK接口不使用
	DynamicWeight *wrapperspb.UInt32Value `protobuf:"bytes,23,opt,name=dynamic_weight,proto3" json:"dynamic_weight,omitempty"`
	// 服务注解
	Annotation map[string]string `protobuf:"bytes,24,rep,name=annotation,proto3" json:"annotation,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Instance) Reset() {
	*x = Instance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Instance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Instance) ProtoMessage() {}

func (x *Instance) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Instance.ProtoReflect.Descriptor instead.
func (*Instance) Descriptor() ([]byte, []int) {
	return file_v1_model_service_proto_rawDescGZIP(), []int{3}
}

func (x *Instance) GetId() *wrapperspb.StringValue {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *Instance) GetService() *wrapperspb.StringValue {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *Instance) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *Instance) GetVpcId() *wrapperspb.StringValue {
	if x != nil {
		return x.VpcId
	}
	return nil
}

func (x *Instance) GetHost() *wrapperspb.StringValue {
	if x != nil {
		return x.Host
	}
	return nil
}

func (x *Instance) GetPort() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Port
	}
	return nil
}

func (x *Instance) GetProtocol() *wrapperspb.StringValue {
	if x != nil {
		return x.Protocol
	}
	return nil
}

func (x *Instance) GetVersion() *wrapperspb.StringValue {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *Instance) GetPriority() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Priority
	}
	return nil
}

func (x *Instance) GetWeight() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Weight
	}
	return nil
}

func (x *Instance) GetEnableHealthCheck() *wrapperspb.BoolValue {
	if x != nil {
		return x.EnableHealthCheck
	}
	return nil
}

func (x *Instance) GetHealthCheck() *HealthCheck {
	if x != nil {
		return x.HealthCheck
	}
	return nil
}

func (x *Instance) GetHealthy() *wrapperspb.BoolValue {
	if x != nil {
		return x.Healthy
	}
	return nil
}

func (x *Instance) GetIsolate() *wrapperspb.BoolValue {
	if x != nil {
		return x.Isolate
	}
	return nil
}

func (x *Instance) GetLocation() *Location {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *Instance) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Instance) GetLogicSet() *wrapperspb.StringValue {
	if x != nil {
		return x.LogicSet
	}
	return nil
}

func (x *Instance) GetCtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Ctime
	}
	return nil
}

func (x *Instance) GetMtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Mtime
	}
	return nil
}

func (x *Instance) GetRevision() *wrapperspb.StringValue {
	if x != nil {
		return x.Revision
	}
	return nil
}

func (x *Instance) GetServiceToken() *wrapperspb.StringValue {
	if x != nil {
		return x.ServiceToken
	}
	return nil
}

func (x *Instance) GetDynamicWeight() *wrapperspb.UInt32Value {
	if x != nil {
		return x.DynamicWeight
	}
	return nil
}

func (x *Instance) GetAnnotation() map[string]string {
	if x != nil {
		return x.Annotation
	}
	return nil
}

type HealthCheck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type      HealthCheck_HealthCheckType `protobuf:"varint,1,opt,name=type,proto3,enum=api.v1.model.HealthCheck_HealthCheckType" json:"type,omitempty"`
	Heartbeat *HeartbeatHealthCheck       `protobuf:"bytes,2,opt,name=heartbeat,proto3" json:"heartbeat,omitempty"`
}

func (x *HealthCheck) Reset() {
	*x = HealthCheck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthCheck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheck) ProtoMessage() {}

func (x *HealthCheck) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheck.ProtoReflect.Descriptor instead.
func (*HealthCheck) Descriptor() ([]byte, []int) {
	return file_v1_model_service_proto_rawDescGZIP(), []int{4}
}

func (x *HealthCheck) GetType() HealthCheck_HealthCheckType {
	if x != nil {
		return x.Type
	}
	return HealthCheck_UNKNOWN
}

func (x *HealthCheck) GetHeartbeat() *HeartbeatHealthCheck {
	if x != nil {
		return x.Heartbeat
	}
	return nil
}

type HeartbeatHealthCheck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ttl *wrapperspb.UInt32Value `protobuf:"bytes,1,opt,name=ttl,proto3" json:"ttl,omitempty"`
}

func (x *HeartbeatHealthCheck) Reset() {
	*x = HeartbeatHealthCheck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeartbeatHealthCheck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatHealthCheck) ProtoMessage() {}

func (x *HeartbeatHealthCheck) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatHealthCheck.ProtoReflect.Descriptor instead.
func (*HeartbeatHealthCheck) Descriptor() ([]byte, []int) {
	return file_v1_model_service_proto_rawDescGZIP(), []int{5}
}

func (x *HeartbeatHealthCheck) GetTtl() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Ttl
	}
	return nil
}

var File_v1_model_service_proto protoreflect.FileDescriptor

var file_v1_model_service_proto_rawDesc = []byte{
	0x0a, 0x16, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc7, 0x02, 0x0a,
	0x09, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x07,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x32, 0x0a, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x32,
	0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x63, 0x74, 0x69,
	0x6d, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xd9, 0x07, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x3f, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x32, 0x0a, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x38, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12,
	0x3c, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3a, 0x0a,
	0x09, 0x63, 0x6d, 0x64, 0x62, 0x5f, 0x6d, 0x6f, 0x64, 0x31, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09,
	0x63, 0x6d, 0x64, 0x62, 0x5f, 0x6d, 0x6f, 0x64, 0x31, 0x12, 0x3a, 0x0a, 0x09, 0x63, 0x6d, 0x64,
	0x62, 0x5f, 0x6d, 0x6f, 0x64, 0x32, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x63, 0x6d, 0x64, 0x62,
	0x5f, 0x6d, 0x6f, 0x64, 0x32, 0x12, 0x3a, 0x0a, 0x09, 0x63, 0x6d, 0x64, 0x62, 0x5f, 0x6d, 0x6f,
	0x64, 0x33, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x63, 0x6d, 0x64, 0x62, 0x5f, 0x6d, 0x6f, 0x64,
	0x33, 0x12, 0x36, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x12,
	0x32, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x32, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x72,
	0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x72, 0x65, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x5f, 0x69, 0x64, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xfd, 0x03, 0x0a, 0x0c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x6c,
	0x69, 0x61, 0x73, 0x12, 0x36, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x12, 0x2b, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x36,
	0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x32, 0x0a, 0x05, 0x63, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x32,
	0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x6d, 0x74, 0x69,
	0x6d, 0x65, 0x22, 0xc0, 0x0b, 0x0a, 0x08, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x2c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x02, 0x69, 0x64, 0x12, 0x36, 0x0a,
	0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x12, 0x34, 0x0a, 0x06, 0x76, 0x70, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x06, 0x76, 0x70, 0x63, 0x5f, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x04, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x38, 0x0a, 0x08, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x36, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a,
	0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x34, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x4a, 0x0a,
	0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f,
	0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x11, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x48, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x3c, 0x0a, 0x0c, 0x68, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x0b, 0x68, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x34, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x12, 0x34, 0x0a,
	0x07, 0x69, 0x73, 0x6f, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x69, 0x73, 0x6f, 0x6c,
	0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3a, 0x0a, 0x09, 0x6c, 0x6f, 0x67,
	0x69, 0x63, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69,
	0x63, 0x5f, 0x73, 0x65, 0x74, 0x12, 0x32, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x6d, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x38, 0x0a,
	0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x72,
	0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x44, 0x0a, 0x0e, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x46, 0x0a, 0x0a, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x41, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3d, 0x0a, 0x0f, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbd, 0x01, 0x0a, 0x0b, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x3d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x2e, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x09, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74,
	0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x09, 0x68, 0x65, 0x61,
	0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x22, 0x2d, 0x0a, 0x0f, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x48, 0x45, 0x41, 0x52, 0x54, 0x42,
	0x45, 0x41, 0x54, 0x10, 0x01, 0x22, 0x46, 0x0a, 0x14, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65,
	0x61, 0x74, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x2e, 0x0a,
	0x03, 0x74, 0x74, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x03, 0x74, 0x74, 0x6c, 0x2a, 0x24, 0x0a,
	0x09, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45,
	0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x4c, 0x35, 0x53, 0x49,
	0x44, 0x10, 0x01, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72,
	0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x3b, 0x70, 0x6f, 0x6c, 0x61, 0x72,
	0x69, 0x73, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_v1_model_service_proto_rawDescOnce sync.Once
	file_v1_model_service_proto_rawDescData = file_v1_model_service_proto_rawDesc
)

func file_v1_model_service_proto_rawDescGZIP() []byte {
	file_v1_model_service_proto_rawDescOnce.Do(func() {
		file_v1_model_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_v1_model_service_proto_rawDescData)
	})
	return file_v1_model_service_proto_rawDescData
}

var file_v1_model_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_v1_model_service_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_v1_model_service_proto_goTypes = []interface{}{
	(AliasType)(0),                   // 0: api.v1.model.AliasType
	(HealthCheck_HealthCheckType)(0), // 1: api.v1.model.HealthCheck.HealthCheckType
	(*Namespace)(nil),                // 2: api.v1.model.Namespace
	(*Service)(nil),                  // 3: api.v1.model.Service
	(*ServiceAlias)(nil),             // 4: api.v1.model.ServiceAlias
	(*Instance)(nil),                 // 5: api.v1.model.Instance
	(*HealthCheck)(nil),              // 6: api.v1.model.HealthCheck
	(*HeartbeatHealthCheck)(nil),     // 7: api.v1.model.HeartbeatHealthCheck
	nil,                              // 8: api.v1.model.Service.MetadataEntry
	nil,                              // 9: api.v1.model.Instance.MetadataEntry
	nil,                              // 10: api.v1.model.Instance.AnnotationEntry
	(*wrapperspb.StringValue)(nil),   // 11: google.protobuf.StringValue
	(*wrapperspb.UInt32Value)(nil),   // 12: google.protobuf.UInt32Value
	(*wrapperspb.BoolValue)(nil),     // 13: google.protobuf.BoolValue
	(*Location)(nil),                 // 14: api.v1.model.Location
}
var file_v1_model_service_proto_depIdxs = []int32{
	11, // 0: api.v1.model.Namespace.name:type_name -> google.protobuf.StringValue
	11, // 1: api.v1.model.Namespace.comment:type_name -> google.protobuf.StringValue
	11, // 2: api.v1.model.Namespace.owners:type_name -> google.protobuf.StringValue
	11, // 3: api.v1.model.Namespace.token:type_name -> google.protobuf.StringValue
	11, // 4: api.v1.model.Namespace.ctime:type_name -> google.protobuf.StringValue
	11, // 5: api.v1.model.Namespace.mtime:type_name -> google.protobuf.StringValue
	11, // 6: api.v1.model.Service.name:type_name -> google.protobuf.StringValue
	11, // 7: api.v1.model.Service.namespace:type_name -> google.protobuf.StringValue
	8,  // 8: api.v1.model.Service.metadata:type_name -> api.v1.model.Service.MetadataEntry
	11, // 9: api.v1.model.Service.ports:type_name -> google.protobuf.StringValue
	11, // 10: api.v1.model.Service.business:type_name -> google.protobuf.StringValue
	11, // 11: api.v1.model.Service.department:type_name -> google.protobuf.StringValue
	11, // 12: api.v1.model.Service.cmdb_mod1:type_name -> google.protobuf.StringValue
	11, // 13: api.v1.model.Service.cmdb_mod2:type_name -> google.protobuf.StringValue
	11, // 14: api.v1.model.Service.cmdb_mod3:type_name -> google.protobuf.StringValue
	11, // 15: api.v1.model.Service.comment:type_name -> google.protobuf.StringValue
	11, // 16: api.v1.model.Service.owners:type_name -> google.protobuf.StringValue
	11, // 17: api.v1.model.Service.token:type_name -> google.protobuf.StringValue
	11, // 18: api.v1.model.Service.ctime:type_name -> google.protobuf.StringValue
	11, // 19: api.v1.model.Service.mtime:type_name -> google.protobuf.StringValue
	11, // 20: api.v1.model.Service.revision:type_name -> google.protobuf.StringValue
	11, // 21: api.v1.model.Service.platform_id:type_name -> google.protobuf.StringValue
	11, // 22: api.v1.model.ServiceAlias.service:type_name -> google.protobuf.StringValue
	11, // 23: api.v1.model.ServiceAlias.namespace:type_name -> google.protobuf.StringValue
	11, // 24: api.v1.model.ServiceAlias.alias:type_name -> google.protobuf.StringValue
	0,  // 25: api.v1.model.ServiceAlias.type:type_name -> api.v1.model.AliasType
	11, // 26: api.v1.model.ServiceAlias.owners:type_name -> google.protobuf.StringValue
	11, // 27: api.v1.model.ServiceAlias.comment:type_name -> google.protobuf.StringValue
	11, // 28: api.v1.model.ServiceAlias.service_token:type_name -> google.protobuf.StringValue
	11, // 29: api.v1.model.ServiceAlias.ctime:type_name -> google.protobuf.StringValue
	11, // 30: api.v1.model.ServiceAlias.mtime:type_name -> google.protobuf.StringValue
	11, // 31: api.v1.model.Instance.id:type_name -> google.protobuf.StringValue
	11, // 32: api.v1.model.Instance.service:type_name -> google.protobuf.StringValue
	11, // 33: api.v1.model.Instance.namespace:type_name -> google.protobuf.StringValue
	11, // 34: api.v1.model.Instance.vpc_id:type_name -> google.protobuf.StringValue
	11, // 35: api.v1.model.Instance.host:type_name -> google.protobuf.StringValue
	12, // 36: api.v1.model.Instance.port:type_name -> google.protobuf.UInt32Value
	11, // 37: api.v1.model.Instance.protocol:type_name -> google.protobuf.StringValue
	11, // 38: api.v1.model.Instance.version:type_name -> google.protobuf.StringValue
	12, // 39: api.v1.model.Instance.priority:type_name -> google.protobuf.UInt32Value
	12, // 40: api.v1.model.Instance.weight:type_name -> google.protobuf.UInt32Value
	13, // 41: api.v1.model.Instance.enable_health_check:type_name -> google.protobuf.BoolValue
	6,  // 42: api.v1.model.Instance.health_check:type_name -> api.v1.model.HealthCheck
	13, // 43: api.v1.model.Instance.healthy:type_name -> google.protobuf.BoolValue
	13, // 44: api.v1.model.Instance.isolate:type_name -> google.protobuf.BoolValue
	14, // 45: api.v1.model.Instance.location:type_name -> api.v1.model.Location
	9,  // 46: api.v1.model.Instance.metadata:type_name -> api.v1.model.Instance.MetadataEntry
	11, // 47: api.v1.model.Instance.logic_set:type_name -> google.protobuf.StringValue
	11, // 48: api.v1.model.Instance.ctime:type_name -> google.protobuf.StringValue
	11, // 49: api.v1.model.Instance.mtime:type_name -> google.protobuf.StringValue
	11, // 50: api.v1.model.Instance.revision:type_name -> google.protobuf.StringValue
	11, // 51: api.v1.model.Instance.service_token:type_name -> google.protobuf.StringValue
	12, // 52: api.v1.model.Instance.dynamic_weight:type_name -> google.protobuf.UInt32Value
	10, // 53: api.v1.model.Instance.annotation:type_name -> api.v1.model.Instance.AnnotationEntry
	1,  // 54: api.v1.model.HealthCheck.type:type_name -> api.v1.model.HealthCheck.HealthCheckType
	7,  // 55: api.v1.model.HealthCheck.heartbeat:type_name -> api.v1.model.HeartbeatHealthCheck
	12, // 56: api.v1.model.HeartbeatHealthCheck.ttl:type_name -> google.protobuf.UInt32Value
	57, // [57:57] is the sub-list for method output_type
	57, // [57:57] is the sub-list for method input_type
	57, // [57:57] is the sub-list for extension type_name
	57, // [57:57] is the sub-list for extension extendee
	0,  // [0:57] is the sub-list for field type_name
}

func init() { file_v1_model_service_proto_init() }
func file_v1_model_service_proto_init() {
	if File_v1_model_service_proto != nil {
		return
	}
	file_v1_model_model_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_v1_model_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Namespace); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceAlias); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Instance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthCheck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeartbeatHealthCheck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_model_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v1_model_service_proto_goTypes,
		DependencyIndexes: file_v1_model_service_proto_depIdxs,
		EnumInfos:         file_v1_model_service_proto_enumTypes,
		MessageInfos:      file_v1_model_service_proto_msgTypes,
	}.Build()
	File_v1_model_service_proto = out.File
	file_v1_model_service_proto_rawDesc = nil
	file_v1_model_service_proto_goTypes = nil
	file_v1_model_service_proto_depIdxs = nil
}
