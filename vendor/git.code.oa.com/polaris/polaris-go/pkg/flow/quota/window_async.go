/**
 * <PERSON><PERSON> is pleased to support the open source community by making CL5 available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under thhe License.
 */

package quota

import (
	"time"

	"git.code.oa.com/polaris/polaris-go/pkg/log"
	"git.code.oa.com/polaris/polaris-go/pkg/model"
	"git.code.oa.com/polaris/polaris-go/pkg/plugin/serverconnector"
	rlimitV2 "git.woa.com/polaris/polaris-server-api/api/metric/v2"
	namingpb "git.woa.com/polaris/polaris-server-api/api/v1/model"
)

// 获取发送消息的 sender
func (r *RateLimitWindow) getMessageSender() (serverconnector.RateLimitMsgSender, error) {
	sender, err := r.AsyncRateLimitConnector().GetMessageSender(r.remoteCluster, r.hashValue)
	if nil != err {
		log.GetBaseLogger().Errorf("fail to call RateLimitService.GetMessageSender, service %s, error is %s",
			r.remoteCluster, err)
		return nil, err
	}
	return sender, nil
}

// 异步处理发送init
func (r *RateLimitWindow) DoAsyncRemoteInit() error {
	log.GetNetworkLogger().Infof("[RateLimit]start to init window: %s", r.uniqueKey)

	if r.Rule.GetType() == namingpb.Rule_LOCAL || r.configMode == model.ConfigQuotaLocalMode {
		return nil
	}

	r.AsyncRateLimitConnector().ResetMessageHandler(r)

	sender, err := r.getMessageSender()
	//发生了错误，直接返回
	if err != nil {
		return err
	}
	sender.SetupMessageHandler(r)

	timeDiff := sender.AdjustTime()
	r.allocatingBucket.UpdateTimeDiff(timeDiff)

	request := r.InitializeRequest()
	sender.SendInitRequest(request, r)
	return nil
}

// 异步发送 acquire
func (r *RateLimitWindow) DoAsyncRemoteAcquire() error {
	if r.Rule.GetType() == namingpb.Rule_LOCAL || r.configMode == model.ConfigQuotaLocalMode {
		return nil
	}
	sender, err := r.getMessageSender()
	if err != nil {
		r.AsyncRateLimitConnector().ResetMessageHandler(r)
		return err
	}

	// 这个窗口是否已经注册进了对应的 sender，并且已经有了初始化的 counterKey
	if !sender.HasInitialized(r) {
		r.SetStatus(Initializing)
		return r.DoAsyncRemoteInit()
	}

	timeDiff := sender.AdjustTime()
	r.allocatingBucket.UpdateTimeDiff(timeDiff)

	request := r.acquireRequest()
	err = sender.SendReportRequest(request, r)
	if nil != err {
		log.GetBaseLogger().Errorf(
			"fail to call RateLimitService.Acquire, service %s, labels %s, error is %s",
			r.SvcKey, r.Labels, err)
		return err
	}
	return nil
}

//应答回调函数
func (r *RateLimitWindow) OnInitResponse(counter *rlimitV2.QuotaCounter, duration time.Duration, srvTimeMilli int64) {
	r.SetStatus(Initialized)
	log.GetBaseLogger().Infof("[RateLimit]window %s changed to initialized", r.uniqueKey)
	r.allocatingBucket.SetRemoteQuota(&RemoteQuotaResult{
		Left:            counter.GetLeft(),
		ClientCount:     counter.GetClientCount(),
		ServerTimeMilli: srvTimeMilli,
		DurationMill:    model.ToMilliSeconds(duration),
	})
}

//应答回调函数
func (r *RateLimitWindow) OnReportResponse(counter *rlimitV2.QuotaLeft, duration time.Duration, curTimeMilli int64) {
	r.allocatingBucket.SetRemoteQuota(&RemoteQuotaResult{
		Left:            counter.GetLeft(),
		ClientCount:     counter.GetClientCount(),
		ServerTimeMilli: curTimeMilli,
		DurationMill:    model.ToMilliSeconds(duration),
	})
}

//返回自身的限流交互记录
func (r *RateLimitWindow) GetRemoteMessageHandler() interface{} {
	return r.communicationRecord
}
