// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: metric/v2/ratelimit_v2.proto

package v2

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//命令字
type RateLimitCmd int32

const (
	RateLimitCmd_INIT          RateLimitCmd = 0
	RateLimitCmd_ACQUIRE       RateLimitCmd = 1
	RateLimitCmd_BATCH_INIT    RateLimitCmd = 2
	RateLimitCmd_BATCH_ACQUIRE RateLimitCmd = 3
	RateLimitCmd_LIMIT_NOTIFY  RateLimitCmd = 4
)

// Enum value maps for RateLimitCmd.
var (
	RateLimitCmd_name = map[int32]string{
		0: "INIT",
		1: "ACQUIRE",
		2: "BATCH_INIT",
		3: "BATCH_ACQUIRE",
		4: "LIMIT_NOTIFY",
	}
	RateLimitCmd_value = map[string]int32{
		"INIT":          0,
		"ACQUIRE":       1,
		"BATCH_INIT":    2,
		"BATCH_ACQUIRE": 3,
		"LIMIT_NOTIFY":  4,
	}
)

func (x RateLimitCmd) Enum() *RateLimitCmd {
	p := new(RateLimitCmd)
	*p = x
	return p
}

func (x RateLimitCmd) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RateLimitCmd) Descriptor() protoreflect.EnumDescriptor {
	return file_metric_v2_ratelimit_v2_proto_enumTypes[0].Descriptor()
}

func (RateLimitCmd) Type() protoreflect.EnumType {
	return &file_metric_v2_ratelimit_v2_proto_enumTypes[0]
}

func (x RateLimitCmd) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RateLimitCmd.Descriptor instead.
func (RateLimitCmd) EnumDescriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{0}
}

//限频模式
type Mode int32

const (
	//自适应模式，根据历史流量自动调整
	Mode_ADAPTIVE Mode = 0
	//批量抢占模式，客户端进行拉取，Server返回全量剩余配额
	Mode_BATCH_OCCUPY Mode = 1
	//批量分摊模式，客户端进行拉取，Server按比例进行分摊
	Mode_BATCH_SHARE Mode = 2
)

// Enum value maps for Mode.
var (
	Mode_name = map[int32]string{
		0: "ADAPTIVE",
		1: "BATCH_OCCUPY",
		2: "BATCH_SHARE",
	}
	Mode_value = map[string]int32{
		"ADAPTIVE":     0,
		"BATCH_OCCUPY": 1,
		"BATCH_SHARE":  2,
	}
)

func (x Mode) Enum() *Mode {
	p := new(Mode)
	*p = x
	return p
}

func (x Mode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Mode) Descriptor() protoreflect.EnumDescriptor {
	return file_metric_v2_ratelimit_v2_proto_enumTypes[1].Descriptor()
}

func (Mode) Type() protoreflect.EnumType {
	return &file_metric_v2_ratelimit_v2_proto_enumTypes[1]
}

func (x Mode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Mode.Descriptor instead.
func (Mode) EnumDescriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{1}
}

//阈值模式
type QuotaMode int32

const (
	//整体阈值
	QuotaMode_WHOLE QuotaMode = 0
	//单机均分阈值
	QuotaMode_DIVIDE QuotaMode = 1
	// 实例均分阈值
	QuotaMode_INSTANCE_DIVIDE QuotaMode = 2
)

// Enum value maps for QuotaMode.
var (
	QuotaMode_name = map[int32]string{
		0: "WHOLE",
		1: "DIVIDE",
		2: "INSTANCE_DIVIDE",
	}
	QuotaMode_value = map[string]int32{
		"WHOLE":           0,
		"DIVIDE":          1,
		"INSTANCE_DIVIDE": 2,
	}
)

func (x QuotaMode) Enum() *QuotaMode {
	p := new(QuotaMode)
	*p = x
	return p
}

func (x QuotaMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuotaMode) Descriptor() protoreflect.EnumDescriptor {
	return file_metric_v2_ratelimit_v2_proto_enumTypes[2].Descriptor()
}

func (QuotaMode) Type() protoreflect.EnumType {
	return &file_metric_v2_ratelimit_v2_proto_enumTypes[2]
}

func (x QuotaMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuotaMode.Descriptor instead.
func (QuotaMode) EnumDescriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{2}
}

//限流请求
type RateLimitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//命令字
	Cmd RateLimitCmd `protobuf:"varint,1,opt,name=cmd,proto3,enum=polaris.metric.v2.RateLimitCmd" json:"cmd,omitempty"`
	//初始化请求
	RateLimitInitRequest *RateLimitInitRequest `protobuf:"bytes,2,opt,name=rateLimitInitRequest,proto3" json:"rateLimitInitRequest,omitempty"`
	//上报请求
	RateLimitReportRequest *RateLimitReportRequest `protobuf:"bytes,3,opt,name=rateLimitReportRequest,proto3" json:"rateLimitReportRequest,omitempty"`
	//批量初始化请求
	RateLimitBatchInitRequest *RateLimitBatchInitRequest `protobuf:"bytes,4,opt,name=rateLimitBatchInitRequest,proto3" json:"rateLimitBatchInitRequest,omitempty"`
}

func (x *RateLimitRequest) Reset() {
	*x = RateLimitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimitRequest) ProtoMessage() {}

func (x *RateLimitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimitRequest.ProtoReflect.Descriptor instead.
func (*RateLimitRequest) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{0}
}

func (x *RateLimitRequest) GetCmd() RateLimitCmd {
	if x != nil {
		return x.Cmd
	}
	return RateLimitCmd_INIT
}

func (x *RateLimitRequest) GetRateLimitInitRequest() *RateLimitInitRequest {
	if x != nil {
		return x.RateLimitInitRequest
	}
	return nil
}

func (x *RateLimitRequest) GetRateLimitReportRequest() *RateLimitReportRequest {
	if x != nil {
		return x.RateLimitReportRequest
	}
	return nil
}

func (x *RateLimitRequest) GetRateLimitBatchInitRequest() *RateLimitBatchInitRequest {
	if x != nil {
		return x.RateLimitBatchInitRequest
	}
	return nil
}

//限流应答
type RateLimitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//命令字
	Cmd RateLimitCmd `protobuf:"varint,1,opt,name=cmd,proto3,enum=polaris.metric.v2.RateLimitCmd" json:"cmd,omitempty"`
	//初始化应答
	RateLimitInitResponse *RateLimitInitResponse `protobuf:"bytes,2,opt,name=rateLimitInitResponse,proto3" json:"rateLimitInitResponse,omitempty"`
	//上报应答
	RateLimitReportResponse *RateLimitReportResponse `protobuf:"bytes,3,opt,name=rateLimitReportResponse,proto3" json:"rateLimitReportResponse,omitempty"`
	//批量初始化应答
	RateLimitBatchInitResponse *RateLimitBatchInitResponse `protobuf:"bytes,4,opt,name=rateLimitBatchInitResponse,proto3" json:"rateLimitBatchInitResponse,omitempty"`
	//被限流通知
	RateLimitNotify *RateLimitNotify `protobuf:"bytes,5,opt,name=rateLimitNotify,proto3" json:"rateLimitNotify,omitempty"`
}

func (x *RateLimitResponse) Reset() {
	*x = RateLimitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimitResponse) ProtoMessage() {}

func (x *RateLimitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimitResponse.ProtoReflect.Descriptor instead.
func (*RateLimitResponse) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{1}
}

func (x *RateLimitResponse) GetCmd() RateLimitCmd {
	if x != nil {
		return x.Cmd
	}
	return RateLimitCmd_INIT
}

func (x *RateLimitResponse) GetRateLimitInitResponse() *RateLimitInitResponse {
	if x != nil {
		return x.RateLimitInitResponse
	}
	return nil
}

func (x *RateLimitResponse) GetRateLimitReportResponse() *RateLimitReportResponse {
	if x != nil {
		return x.RateLimitReportResponse
	}
	return nil
}

func (x *RateLimitResponse) GetRateLimitBatchInitResponse() *RateLimitBatchInitResponse {
	if x != nil {
		return x.RateLimitBatchInitResponse
	}
	return nil
}

func (x *RateLimitResponse) GetRateLimitNotify() *RateLimitNotify {
	if x != nil {
		return x.RateLimitNotify
	}
	return nil
}

//初始化请求
type RateLimitInitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//限流目标对象数据
	Target *LimitTarget `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	//客户端唯一标识，批量初始化时为空
	ClientId string `protobuf:"bytes,2,opt,name=clientId,proto3" json:"clientId,omitempty"`
	//限流规则信息
	Totals []*QuotaTotal `protobuf:"bytes,3,rep,name=totals,proto3" json:"totals,omitempty"`
	//客户端可指定滑窗数，不指定用默认值
	SlideCount uint32 `protobuf:"varint,4,opt,name=slideCount,proto3" json:"slideCount,omitempty"`
	//限流模式
	Mode Mode `protobuf:"varint,5,opt,name=mode,proto3,enum=polaris.metric.v2.Mode" json:"mode,omitempty"`
	//是否接收推送
	ReceivePush bool `protobuf:"varint,6,opt,name=receivePush,proto3" json:"receivePush,omitempty"`
	//模式切换阈值
	SwitchThreshold uint32 `protobuf:"varint,7,opt,name=switchThreshold,proto3" json:"switchThreshold,omitempty"`
}

func (x *RateLimitInitRequest) Reset() {
	*x = RateLimitInitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimitInitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimitInitRequest) ProtoMessage() {}

func (x *RateLimitInitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimitInitRequest.ProtoReflect.Descriptor instead.
func (*RateLimitInitRequest) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{2}
}

func (x *RateLimitInitRequest) GetTarget() *LimitTarget {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *RateLimitInitRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *RateLimitInitRequest) GetTotals() []*QuotaTotal {
	if x != nil {
		return x.Totals
	}
	return nil
}

func (x *RateLimitInitRequest) GetSlideCount() uint32 {
	if x != nil {
		return x.SlideCount
	}
	return 0
}

func (x *RateLimitInitRequest) GetMode() Mode {
	if x != nil {
		return x.Mode
	}
	return Mode_ADAPTIVE
}

func (x *RateLimitInitRequest) GetReceivePush() bool {
	if x != nil {
		return x.ReceivePush
	}
	return false
}

func (x *RateLimitInitRequest) GetSwitchThreshold() uint32 {
	if x != nil {
		return x.SwitchThreshold
	}
	return 0
}

//初始化应答
type RateLimitInitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//应答错误码
	Code uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	//限流目标对象，回传给客户端
	Target *LimitTarget `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
	//客户端的标识，与clientId对应，一个server全局唯一，上报时候带入
	ClientKey uint32 `protobuf:"varint,3,opt,name=clientKey,proto3" json:"clientKey,omitempty"`
	//计数器的标识
	Counters []*QuotaCounter `protobuf:"bytes,5,rep,name=counters,proto3" json:"counters,omitempty"`
	//实际滑窗个数
	SlideCount uint32 `protobuf:"varint,6,opt,name=slideCount,proto3" json:"slideCount,omitempty"`
	//限流server绝对时间，单位ms
	Timestamp int64 `protobuf:"varint,7,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *RateLimitInitResponse) Reset() {
	*x = RateLimitInitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimitInitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimitInitResponse) ProtoMessage() {}

func (x *RateLimitInitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimitInitResponse.ProtoReflect.Descriptor instead.
func (*RateLimitInitResponse) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{3}
}

func (x *RateLimitInitResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RateLimitInitResponse) GetTarget() *LimitTarget {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *RateLimitInitResponse) GetClientKey() uint32 {
	if x != nil {
		return x.ClientKey
	}
	return 0
}

func (x *RateLimitInitResponse) GetCounters() []*QuotaCounter {
	if x != nil {
		return x.Counters
	}
	return nil
}

func (x *RateLimitInitResponse) GetSlideCount() uint32 {
	if x != nil {
		return x.SlideCount
	}
	return 0
}

func (x *RateLimitInitResponse) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

//批量初始化请求
type RateLimitBatchInitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//每条规则汇总成一个Init请求
	Request []*RateLimitInitRequest `protobuf:"bytes,1,rep,name=request,proto3" json:"request,omitempty"`
	//客户端唯一标识
	ClientId string `protobuf:"bytes,2,opt,name=clientId,proto3" json:"clientId,omitempty"`
	//是否接收推送
	ReceivePush bool `protobuf:"varint,3,opt,name=receivePush,proto3" json:"receivePush,omitempty"`
}

func (x *RateLimitBatchInitRequest) Reset() {
	*x = RateLimitBatchInitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimitBatchInitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimitBatchInitRequest) ProtoMessage() {}

func (x *RateLimitBatchInitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimitBatchInitRequest.ProtoReflect.Descriptor instead.
func (*RateLimitBatchInitRequest) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{4}
}

func (x *RateLimitBatchInitRequest) GetRequest() []*RateLimitInitRequest {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *RateLimitBatchInitRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *RateLimitBatchInitRequest) GetReceivePush() bool {
	if x != nil {
		return x.ReceivePush
	}
	return false
}

type LabeledQuotaCounter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//自定义标签
	Labels string `protobuf:"bytes,1,opt,name=labels,proto3" json:"labels,omitempty"`
	//计数器的标识
	Counters []*QuotaCounter `protobuf:"bytes,2,rep,name=counters,proto3" json:"counters,omitempty"`
}

func (x *LabeledQuotaCounter) Reset() {
	*x = LabeledQuotaCounter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabeledQuotaCounter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabeledQuotaCounter) ProtoMessage() {}

func (x *LabeledQuotaCounter) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabeledQuotaCounter.ProtoReflect.Descriptor instead.
func (*LabeledQuotaCounter) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{5}
}

func (x *LabeledQuotaCounter) GetLabels() string {
	if x != nil {
		return x.Labels
	}
	return ""
}

func (x *LabeledQuotaCounter) GetCounters() []*QuotaCounter {
	if x != nil {
		return x.Counters
	}
	return nil
}

type BatchInitResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//应答错误码
	Code uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	//限流目标对象，回传给客户端，labels为规则
	Target *LimitTarget `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
	//计数器的标识
	Counters []*LabeledQuotaCounter `protobuf:"bytes,3,rep,name=counters,proto3" json:"counters,omitempty"`
	//实际滑窗个数
	SlideCount uint32 `protobuf:"varint,4,opt,name=slideCount,proto3" json:"slideCount,omitempty"`
}

func (x *BatchInitResult) Reset() {
	*x = BatchInitResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchInitResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchInitResult) ProtoMessage() {}

func (x *BatchInitResult) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchInitResult.ProtoReflect.Descriptor instead.
func (*BatchInitResult) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{6}
}

func (x *BatchInitResult) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BatchInitResult) GetTarget() *LimitTarget {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *BatchInitResult) GetCounters() []*LabeledQuotaCounter {
	if x != nil {
		return x.Counters
	}
	return nil
}

func (x *BatchInitResult) GetSlideCount() uint32 {
	if x != nil {
		return x.SlideCount
	}
	return 0
}

//批量初始化应答
type RateLimitBatchInitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//应答错误码
	Code uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	//客户端的标识，与clientId对应，一个server全局唯一，上报时候带入
	ClientKey uint32 `protobuf:"varint,2,opt,name=clientKey,proto3" json:"clientKey,omitempty"`
	//限流server绝对时间，单位ms
	Timestamp int64 `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	//批量初始化结果
	Result []*BatchInitResult `protobuf:"bytes,4,rep,name=result,proto3" json:"result,omitempty"`
}

func (x *RateLimitBatchInitResponse) Reset() {
	*x = RateLimitBatchInitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimitBatchInitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimitBatchInitResponse) ProtoMessage() {}

func (x *RateLimitBatchInitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimitBatchInitResponse.ProtoReflect.Descriptor instead.
func (*RateLimitBatchInitResponse) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{7}
}

func (x *RateLimitBatchInitResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RateLimitBatchInitResponse) GetClientKey() uint32 {
	if x != nil {
		return x.ClientKey
	}
	return 0
}

func (x *RateLimitBatchInitResponse) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *RateLimitBatchInitResponse) GetResult() []*BatchInitResult {
	if x != nil {
		return x.Result
	}
	return nil
}

//限流上报请求
type RateLimitReportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//客户端标识
	ClientKey uint32 `protobuf:"varint,1,opt,name=clientKey,proto3" json:"clientKey,omitempty"`
	//已使用的配额数
	QuotaUses []*QuotaSum `protobuf:"bytes,2,rep,name=quotaUses,proto3" json:"quotaUses,omitempty"`
	//配额发生的时间，单位ms
	Timestamp int64 `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *RateLimitReportRequest) Reset() {
	*x = RateLimitReportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimitReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimitReportRequest) ProtoMessage() {}

func (x *RateLimitReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimitReportRequest.ProtoReflect.Descriptor instead.
func (*RateLimitReportRequest) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{8}
}

func (x *RateLimitReportRequest) GetClientKey() uint32 {
	if x != nil {
		return x.ClientKey
	}
	return 0
}

func (x *RateLimitReportRequest) GetQuotaUses() []*QuotaSum {
	if x != nil {
		return x.QuotaUses
	}
	return nil
}

func (x *RateLimitReportRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

//限流上报应答
type RateLimitReportResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	//剩余配额数
	QuotaLefts []*QuotaLeft `protobuf:"bytes,2,rep,name=quotaLefts,proto3" json:"quotaLefts,omitempty"`
	//限流server绝对时间，单位ms
	Timestamp int64 `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *RateLimitReportResponse) Reset() {
	*x = RateLimitReportResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimitReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimitReportResponse) ProtoMessage() {}

func (x *RateLimitReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimitReportResponse.ProtoReflect.Descriptor instead.
func (*RateLimitReportResponse) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{9}
}

func (x *RateLimitReportResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RateLimitReportResponse) GetQuotaLefts() []*QuotaLeft {
	if x != nil {
		return x.QuotaLefts
	}
	return nil
}

func (x *RateLimitReportResponse) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type RateLimitNotify struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//应答错误码
	Code uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// 被限流维度
	Target *LimitTarget `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
	//剩余配额数
	QuotaLeft *QuotaLeft `protobuf:"bytes,3,opt,name=quotaLeft,proto3" json:"quotaLeft,omitempty"`
	//限流server绝对时间，单位ms
	Timestamp int64 `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *RateLimitNotify) Reset() {
	*x = RateLimitNotify{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimitNotify) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimitNotify) ProtoMessage() {}

func (x *RateLimitNotify) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimitNotify.ProtoReflect.Descriptor instead.
func (*RateLimitNotify) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{10}
}

func (x *RateLimitNotify) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RateLimitNotify) GetTarget() *LimitTarget {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *RateLimitNotify) GetQuotaLeft() *QuotaLeft {
	if x != nil {
		return x.QuotaLeft
	}
	return nil
}

func (x *RateLimitNotify) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

//限流目标，针对哪部分数据进行限流
type LimitTarget struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//命名空间
	Namespace string `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	//服务名
	Service string `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
	//单窗口初始化时，自定义标签
	Labels string `protobuf:"bytes,3,opt,name=labels,proto3" json:"labels,omitempty"`
	//批量初始化时，匹配同一个规则的多个限流自定义标签
	LabelsList []string `protobuf:"bytes,4,rep,name=labels_list,json=labelsList,proto3" json:"labels_list,omitempty"`
}

func (x *LimitTarget) Reset() {
	*x = LimitTarget{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitTarget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitTarget) ProtoMessage() {}

func (x *LimitTarget) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitTarget.ProtoReflect.Descriptor instead.
func (*LimitTarget) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{11}
}

func (x *LimitTarget) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *LimitTarget) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *LimitTarget) GetLabels() string {
	if x != nil {
		return x.Labels
	}
	return ""
}

func (x *LimitTarget) GetLabelsList() []string {
	if x != nil {
		return x.LabelsList
	}
	return nil
}

//阈值配置的值
type QuotaTotal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//阈值模式
	Mode QuotaMode `protobuf:"varint,1,opt,name=mode,proto3,enum=polaris.metric.v2.QuotaMode" json:"mode,omitempty"`
	//单位秒
	Duration uint32 `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"`
	//限流阈值
	MaxAmount uint32 `protobuf:"varint,3,opt,name=maxAmount,proto3" json:"maxAmount,omitempty"`
}

func (x *QuotaTotal) Reset() {
	*x = QuotaTotal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuotaTotal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuotaTotal) ProtoMessage() {}

func (x *QuotaTotal) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuotaTotal.ProtoReflect.Descriptor instead.
func (*QuotaTotal) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{12}
}

func (x *QuotaTotal) GetMode() QuotaMode {
	if x != nil {
		return x.Mode
	}
	return QuotaMode_WHOLE
}

func (x *QuotaTotal) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *QuotaTotal) GetMaxAmount() uint32 {
	if x != nil {
		return x.MaxAmount
	}
	return 0
}

//限流计数器
type QuotaCounter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//单位秒
	Duration uint32 `protobuf:"varint,1,opt,name=duration,proto3" json:"duration,omitempty"`
	// bucket的标识，上报时候带入
	CounterKey uint32 `protobuf:"varint,2,opt,name=counterKey,proto3" json:"counterKey,omitempty"`
	//剩余配额数，应答返回，允许为负数
	Left int64 `protobuf:"varint,3,opt,name=left,proto3" json:"left,omitempty"`
	//实际限流模式
	Mode Mode `protobuf:"varint,4,opt,name=mode,proto3,enum=polaris.metric.v2.Mode" json:"mode,omitempty"`
	//接入的客户端数量
	ClientCount uint32 `protobuf:"varint,5,opt,name=clientCount,proto3" json:"clientCount,omitempty"`
	//接入客户端的请求总数
	RequestCount uint64 `protobuf:"varint,6,opt,name=requestCount,proto3" json:"requestCount,omitempty"`
}

func (x *QuotaCounter) Reset() {
	*x = QuotaCounter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuotaCounter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuotaCounter) ProtoMessage() {}

func (x *QuotaCounter) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuotaCounter.ProtoReflect.Descriptor instead.
func (*QuotaCounter) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{13}
}

func (x *QuotaCounter) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *QuotaCounter) GetCounterKey() uint32 {
	if x != nil {
		return x.CounterKey
	}
	return 0
}

func (x *QuotaCounter) GetLeft() int64 {
	if x != nil {
		return x.Left
	}
	return 0
}

func (x *QuotaCounter) GetMode() Mode {
	if x != nil {
		return x.Mode
	}
	return Mode_ADAPTIVE
}

func (x *QuotaCounter) GetClientCount() uint32 {
	if x != nil {
		return x.ClientCount
	}
	return 0
}

func (x *QuotaCounter) GetRequestCount() uint64 {
	if x != nil {
		return x.RequestCount
	}
	return 0
}

//客户端阈值使用统计
type QuotaSum struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//计数器的标识，一个server全局唯一，上报时候带入
	CounterKey uint32 `protobuf:"varint,1,opt,name=counterKey,proto3" json:"counterKey,omitempty"`
	//已使用的配额数，上报时候带入
	Used uint32 `protobuf:"varint,2,opt,name=used,proto3" json:"used,omitempty"`
	//被限流数，上报时候带入
	Limited uint32 `protobuf:"varint,3,opt,name=limited,proto3" json:"limited,omitempty"`
}

func (x *QuotaSum) Reset() {
	*x = QuotaSum{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuotaSum) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuotaSum) ProtoMessage() {}

func (x *QuotaSum) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuotaSum.ProtoReflect.Descriptor instead.
func (*QuotaSum) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{14}
}

func (x *QuotaSum) GetCounterKey() uint32 {
	if x != nil {
		return x.CounterKey
	}
	return 0
}

func (x *QuotaSum) GetUsed() uint32 {
	if x != nil {
		return x.Used
	}
	return 0
}

func (x *QuotaSum) GetLimited() uint32 {
	if x != nil {
		return x.Limited
	}
	return 0
}

//客户端阈值使用统计，由服务端返回
type QuotaLeft struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//计数器的标识，一个server全局唯一，上报时候带入
	CounterKey uint32 `protobuf:"varint,1,opt,name=counterKey,proto3" json:"counterKey,omitempty"`
	//剩余配额数，应答返回，允许为负数
	Left int64 `protobuf:"varint,2,opt,name=left,proto3" json:"left,omitempty"`
	//当前限流模式
	Mode Mode `protobuf:"varint,3,opt,name=mode,proto3,enum=polaris.metric.v2.Mode" json:"mode,omitempty"`
	//接入的客户端数量
	ClientCount uint32 `protobuf:"varint,4,opt,name=clientCount,proto3" json:"clientCount,omitempty"`
	//限流周期，单位秒
	Duration uint32 `protobuf:"varint,5,opt,name=duration,proto3" json:"duration,omitempty"`
	//接入客户端的请求总数
	RequestCount uint64 `protobuf:"varint,6,opt,name=requestCount,proto3" json:"requestCount,omitempty"`
}

func (x *QuotaLeft) Reset() {
	*x = QuotaLeft{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuotaLeft) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuotaLeft) ProtoMessage() {}

func (x *QuotaLeft) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuotaLeft.ProtoReflect.Descriptor instead.
func (*QuotaLeft) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{15}
}

func (x *QuotaLeft) GetCounterKey() uint32 {
	if x != nil {
		return x.CounterKey
	}
	return 0
}

func (x *QuotaLeft) GetLeft() int64 {
	if x != nil {
		return x.Left
	}
	return 0
}

func (x *QuotaLeft) GetMode() Mode {
	if x != nil {
		return x.Mode
	}
	return Mode_ADAPTIVE
}

func (x *QuotaLeft) GetClientCount() uint32 {
	if x != nil {
		return x.ClientCount
	}
	return 0
}

func (x *QuotaLeft) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *QuotaLeft) GetRequestCount() uint64 {
	if x != nil {
		return x.RequestCount
	}
	return 0
}

//时间点对齐的请求
type TimeAdjustRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TimeAdjustRequest) Reset() {
	*x = TimeAdjustRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeAdjustRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeAdjustRequest) ProtoMessage() {}

func (x *TimeAdjustRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeAdjustRequest.ProtoReflect.Descriptor instead.
func (*TimeAdjustRequest) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{16}
}

//时间点对齐的应答
type TimeAdjustResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//服务器时间点，毫秒
	ServerTimestamp int64 `protobuf:"varint,1,opt,name=serverTimestamp,proto3" json:"serverTimestamp,omitempty"`
}

func (x *TimeAdjustResponse) Reset() {
	*x = TimeAdjustResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeAdjustResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeAdjustResponse) ProtoMessage() {}

func (x *TimeAdjustResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_ratelimit_v2_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeAdjustResponse.ProtoReflect.Descriptor instead.
func (*TimeAdjustResponse) Descriptor() ([]byte, []int) {
	return file_metric_v2_ratelimit_v2_proto_rawDescGZIP(), []int{17}
}

func (x *TimeAdjustResponse) GetServerTimestamp() int64 {
	if x != nil {
		return x.ServerTimestamp
	}
	return 0
}

var File_metric_v2_ratelimit_v2_proto protoreflect.FileDescriptor

var file_metric_v2_ratelimit_v2_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x61, 0x74, 0x65,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x76, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76,
	0x32, 0x22, 0xf1, 0x02, 0x0a, 0x10, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x43, 0x6d, 0x64, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x5b, 0x0a, 0x14, 0x72, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x14, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x61, 0x0a, 0x16, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73,
	0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x16, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x6a, 0x0a, 0x19, 0x72, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x69, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x70,
	0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49,
	0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x19, 0x72, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xc9, 0x03, 0x0a, 0x11, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x03, 0x63,
	0x6d, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72,
	0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43, 0x6d, 0x64, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x5e,
	0x0a, 0x15, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76,
	0x32, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x15, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64,
	0x0a, 0x17, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x2e, 0x76, 0x32, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x17, 0x72, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6d, 0x0a, 0x1a, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72,
	0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x69, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x1a, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x0f, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70,
	0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x52, 0x0f, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x79, 0x22, 0xba, 0x02, 0x0a, 0x14, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x49,
	0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x06, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x6f, 0x6c,
	0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x35,
	0x0a, 0x06, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e,
	0x76, 0x32, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x06, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x6c, 0x69, 0x64, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x73, 0x6c, 0x69, 0x64, 0x65,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d, 0x6f,
	0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x50, 0x75, 0x73,
	0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x50, 0x75, 0x73, 0x68, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x54, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x73,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x22, 0xfc,
	0x01, 0x0a, 0x15, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x69, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a, 0x06,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70,
	0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x06, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4b, 0x65,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4b,
	0x65, 0x79, 0x12, 0x3b, 0x0a, 0x08, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x65, 0x72, 0x52, 0x08, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x12,
	0x1e, 0x0a, 0x0a, 0x73, 0x6c, 0x69, 0x64, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x73, 0x6c, 0x69, 0x64, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x9c, 0x01,
	0x0a, 0x19, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x49, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x41, 0x0a, 0x07, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70,
	0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x50, 0x75, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x50, 0x75, 0x73, 0x68, 0x22, 0x6a, 0x0a, 0x13,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x65, 0x64, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x3b, 0x0a, 0x08, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76,
	0x32, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x52, 0x08,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x22, 0xc1, 0x01, 0x0a, 0x0f, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x36, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x42, 0x0a, 0x08, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x6f, 0x6c,
	0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x65, 0x64, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x65, 0x72, 0x52, 0x08, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x6c, 0x69, 0x64, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x73, 0x6c, 0x69, 0x64, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xa8, 0x01, 0x0a,
	0x1a, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49,
	0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x3a, 0x0a, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x8f, 0x01, 0x0a, 0x16, 0x52, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79,
	0x12, 0x39, 0x0a, 0x09, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x55, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x75, 0x6d,
	0x52, 0x09, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x55, 0x73, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x89, 0x01, 0x0a, 0x17, 0x52, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x3c, 0x0a, 0x0a, 0x71, 0x75, 0x6f,
	0x74, 0x61, 0x4c, 0x65, 0x66, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76,
	0x32, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x4c, 0x65, 0x66, 0x74, 0x52, 0x0a, 0x71, 0x75, 0x6f,
	0x74, 0x61, 0x4c, 0x65, 0x66, 0x74, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0xb7, 0x01, 0x0a, 0x0f, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a,
	0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76,
	0x32, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x06, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x3a, 0x0a, 0x09, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x4c, 0x65,
	0x66, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72,
	0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x6f,
	0x74, 0x61, 0x4c, 0x65, 0x66, 0x74, 0x52, 0x09, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x4c, 0x65, 0x66,
	0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22,
	0x7e, 0x0a, 0x0b, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x1c,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x78, 0x0a, 0x0a, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x30, 0x0a,
	0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e,
	0x51, 0x75, 0x6f, 0x74, 0x61, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x6d,
	0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x6d, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xd1, 0x01, 0x0a, 0x0c, 0x51, 0x75,
	0x6f, 0x74, 0x61, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65,
	0x72, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x65, 0x72, 0x4b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72,
	0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x58, 0x0a,
	0x08, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x53, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x65, 0x72, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x4b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x75, 0x73, 0x65, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x22, 0xce, 0x01, 0x0a, 0x09, 0x51, 0x75, 0x6f, 0x74,
	0x61, 0x4c, 0x65, 0x66, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72,
	0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x65, 0x72, 0x4b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x6d, 0x6f, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x13, 0x0a, 0x11, 0x54, 0x69, 0x6d, 0x65,
	0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x3e, 0x0a,
	0x12, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2a, 0x5a, 0x0a,
	0x0c, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43, 0x6d, 0x64, 0x12, 0x08, 0x0a,
	0x04, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x43, 0x51, 0x55, 0x49,
	0x52, 0x45, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x42, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x4e,
	0x49, 0x54, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x42, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x41, 0x43,
	0x51, 0x55, 0x49, 0x52, 0x45, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x49, 0x4d, 0x49, 0x54,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x10, 0x04, 0x2a, 0x37, 0x0a, 0x04, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x44, 0x41, 0x50, 0x54, 0x49, 0x56, 0x45, 0x10, 0x00, 0x12,
	0x10, 0x0a, 0x0c, 0x42, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x4f, 0x43, 0x43, 0x55, 0x50, 0x59, 0x10,
	0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x45,
	0x10, 0x02, 0x2a, 0x37, 0x0a, 0x09, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x4d, 0x6f, 0x64, 0x65, 0x12,
	0x09, 0x0a, 0x05, 0x57, 0x48, 0x4f, 0x4c, 0x45, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x49,
	0x56, 0x49, 0x44, 0x45, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x44, 0x49, 0x56, 0x49, 0x44, 0x45, 0x10, 0x02, 0x42, 0x36, 0x5a, 0x34, 0x67,
	0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72,
	0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x2f, 0x76, 0x32, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_metric_v2_ratelimit_v2_proto_rawDescOnce sync.Once
	file_metric_v2_ratelimit_v2_proto_rawDescData = file_metric_v2_ratelimit_v2_proto_rawDesc
)

func file_metric_v2_ratelimit_v2_proto_rawDescGZIP() []byte {
	file_metric_v2_ratelimit_v2_proto_rawDescOnce.Do(func() {
		file_metric_v2_ratelimit_v2_proto_rawDescData = protoimpl.X.CompressGZIP(file_metric_v2_ratelimit_v2_proto_rawDescData)
	})
	return file_metric_v2_ratelimit_v2_proto_rawDescData
}

var file_metric_v2_ratelimit_v2_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_metric_v2_ratelimit_v2_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_metric_v2_ratelimit_v2_proto_goTypes = []interface{}{
	(RateLimitCmd)(0),                  // 0: polaris.metric.v2.RateLimitCmd
	(Mode)(0),                          // 1: polaris.metric.v2.Mode
	(QuotaMode)(0),                     // 2: polaris.metric.v2.QuotaMode
	(*RateLimitRequest)(nil),           // 3: polaris.metric.v2.RateLimitRequest
	(*RateLimitResponse)(nil),          // 4: polaris.metric.v2.RateLimitResponse
	(*RateLimitInitRequest)(nil),       // 5: polaris.metric.v2.RateLimitInitRequest
	(*RateLimitInitResponse)(nil),      // 6: polaris.metric.v2.RateLimitInitResponse
	(*RateLimitBatchInitRequest)(nil),  // 7: polaris.metric.v2.RateLimitBatchInitRequest
	(*LabeledQuotaCounter)(nil),        // 8: polaris.metric.v2.LabeledQuotaCounter
	(*BatchInitResult)(nil),            // 9: polaris.metric.v2.BatchInitResult
	(*RateLimitBatchInitResponse)(nil), // 10: polaris.metric.v2.RateLimitBatchInitResponse
	(*RateLimitReportRequest)(nil),     // 11: polaris.metric.v2.RateLimitReportRequest
	(*RateLimitReportResponse)(nil),    // 12: polaris.metric.v2.RateLimitReportResponse
	(*RateLimitNotify)(nil),            // 13: polaris.metric.v2.RateLimitNotify
	(*LimitTarget)(nil),                // 14: polaris.metric.v2.LimitTarget
	(*QuotaTotal)(nil),                 // 15: polaris.metric.v2.QuotaTotal
	(*QuotaCounter)(nil),               // 16: polaris.metric.v2.QuotaCounter
	(*QuotaSum)(nil),                   // 17: polaris.metric.v2.QuotaSum
	(*QuotaLeft)(nil),                  // 18: polaris.metric.v2.QuotaLeft
	(*TimeAdjustRequest)(nil),          // 19: polaris.metric.v2.TimeAdjustRequest
	(*TimeAdjustResponse)(nil),         // 20: polaris.metric.v2.TimeAdjustResponse
}
var file_metric_v2_ratelimit_v2_proto_depIdxs = []int32{
	0,  // 0: polaris.metric.v2.RateLimitRequest.cmd:type_name -> polaris.metric.v2.RateLimitCmd
	5,  // 1: polaris.metric.v2.RateLimitRequest.rateLimitInitRequest:type_name -> polaris.metric.v2.RateLimitInitRequest
	11, // 2: polaris.metric.v2.RateLimitRequest.rateLimitReportRequest:type_name -> polaris.metric.v2.RateLimitReportRequest
	7,  // 3: polaris.metric.v2.RateLimitRequest.rateLimitBatchInitRequest:type_name -> polaris.metric.v2.RateLimitBatchInitRequest
	0,  // 4: polaris.metric.v2.RateLimitResponse.cmd:type_name -> polaris.metric.v2.RateLimitCmd
	6,  // 5: polaris.metric.v2.RateLimitResponse.rateLimitInitResponse:type_name -> polaris.metric.v2.RateLimitInitResponse
	12, // 6: polaris.metric.v2.RateLimitResponse.rateLimitReportResponse:type_name -> polaris.metric.v2.RateLimitReportResponse
	10, // 7: polaris.metric.v2.RateLimitResponse.rateLimitBatchInitResponse:type_name -> polaris.metric.v2.RateLimitBatchInitResponse
	13, // 8: polaris.metric.v2.RateLimitResponse.rateLimitNotify:type_name -> polaris.metric.v2.RateLimitNotify
	14, // 9: polaris.metric.v2.RateLimitInitRequest.target:type_name -> polaris.metric.v2.LimitTarget
	15, // 10: polaris.metric.v2.RateLimitInitRequest.totals:type_name -> polaris.metric.v2.QuotaTotal
	1,  // 11: polaris.metric.v2.RateLimitInitRequest.mode:type_name -> polaris.metric.v2.Mode
	14, // 12: polaris.metric.v2.RateLimitInitResponse.target:type_name -> polaris.metric.v2.LimitTarget
	16, // 13: polaris.metric.v2.RateLimitInitResponse.counters:type_name -> polaris.metric.v2.QuotaCounter
	5,  // 14: polaris.metric.v2.RateLimitBatchInitRequest.request:type_name -> polaris.metric.v2.RateLimitInitRequest
	16, // 15: polaris.metric.v2.LabeledQuotaCounter.counters:type_name -> polaris.metric.v2.QuotaCounter
	14, // 16: polaris.metric.v2.BatchInitResult.target:type_name -> polaris.metric.v2.LimitTarget
	8,  // 17: polaris.metric.v2.BatchInitResult.counters:type_name -> polaris.metric.v2.LabeledQuotaCounter
	9,  // 18: polaris.metric.v2.RateLimitBatchInitResponse.result:type_name -> polaris.metric.v2.BatchInitResult
	17, // 19: polaris.metric.v2.RateLimitReportRequest.quotaUses:type_name -> polaris.metric.v2.QuotaSum
	18, // 20: polaris.metric.v2.RateLimitReportResponse.quotaLefts:type_name -> polaris.metric.v2.QuotaLeft
	14, // 21: polaris.metric.v2.RateLimitNotify.target:type_name -> polaris.metric.v2.LimitTarget
	18, // 22: polaris.metric.v2.RateLimitNotify.quotaLeft:type_name -> polaris.metric.v2.QuotaLeft
	2,  // 23: polaris.metric.v2.QuotaTotal.mode:type_name -> polaris.metric.v2.QuotaMode
	1,  // 24: polaris.metric.v2.QuotaCounter.mode:type_name -> polaris.metric.v2.Mode
	1,  // 25: polaris.metric.v2.QuotaLeft.mode:type_name -> polaris.metric.v2.Mode
	26, // [26:26] is the sub-list for method output_type
	26, // [26:26] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_metric_v2_ratelimit_v2_proto_init() }
func file_metric_v2_ratelimit_v2_proto_init() {
	if File_metric_v2_ratelimit_v2_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_metric_v2_ratelimit_v2_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimitInitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimitInitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimitBatchInitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabeledQuotaCounter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchInitResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimitBatchInitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimitReportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimitReportResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimitNotify); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitTarget); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuotaTotal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuotaCounter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuotaSum); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuotaLeft); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeAdjustRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_ratelimit_v2_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeAdjustResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_metric_v2_ratelimit_v2_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_metric_v2_ratelimit_v2_proto_goTypes,
		DependencyIndexes: file_metric_v2_ratelimit_v2_proto_depIdxs,
		EnumInfos:         file_metric_v2_ratelimit_v2_proto_enumTypes,
		MessageInfos:      file_metric_v2_ratelimit_v2_proto_msgTypes,
	}.Build()
	File_metric_v2_ratelimit_v2_proto = out.File
	file_metric_v2_ratelimit_v2_proto_rawDesc = nil
	file_metric_v2_ratelimit_v2_proto_goTypes = nil
	file_metric_v2_ratelimit_v2_proto_depIdxs = nil
}
