// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v1/model/domain.proto

package polarismodel_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ServiceDomain struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           *wrapperspb.Int64Value  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Domain       *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=domain,proto3" json:"domain,omitempty"`
	Namespace    *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Service      *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=service,proto3" json:"service,omitempty"`
	ServiceToken *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=service_token,proto3" json:"service_token,omitempty"`
	Ctime        *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Mtime        *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=mtime,proto3" json:"mtime,omitempty"`
}

func (x *ServiceDomain) Reset() {
	*x = ServiceDomain{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_domain_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceDomain) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceDomain) ProtoMessage() {}

func (x *ServiceDomain) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_domain_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceDomain.ProtoReflect.Descriptor instead.
func (*ServiceDomain) Descriptor() ([]byte, []int) {
	return file_v1_model_domain_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceDomain) GetId() *wrapperspb.Int64Value {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *ServiceDomain) GetDomain() *wrapperspb.StringValue {
	if x != nil {
		return x.Domain
	}
	return nil
}

func (x *ServiceDomain) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *ServiceDomain) GetService() *wrapperspb.StringValue {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *ServiceDomain) GetServiceToken() *wrapperspb.StringValue {
	if x != nil {
		return x.ServiceToken
	}
	return nil
}

func (x *ServiceDomain) GetCtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Ctime
	}
	return nil
}

func (x *ServiceDomain) GetMtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Mtime
	}
	return nil
}

var File_v1_model_domain_proto protoreflect.FileDescriptor

var file_v1_model_domain_proto_rawDesc = []byte{
	0x0a, 0x15, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x92, 0x03, 0x0a, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x2b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x34, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x3a, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x36, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x42,
	0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x32, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69,
	0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x3b, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v1_model_domain_proto_rawDescOnce sync.Once
	file_v1_model_domain_proto_rawDescData = file_v1_model_domain_proto_rawDesc
)

func file_v1_model_domain_proto_rawDescGZIP() []byte {
	file_v1_model_domain_proto_rawDescOnce.Do(func() {
		file_v1_model_domain_proto_rawDescData = protoimpl.X.CompressGZIP(file_v1_model_domain_proto_rawDescData)
	})
	return file_v1_model_domain_proto_rawDescData
}

var file_v1_model_domain_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_v1_model_domain_proto_goTypes = []interface{}{
	(*ServiceDomain)(nil),          // 0: api.v1.model.ServiceDomain
	(*wrapperspb.Int64Value)(nil),  // 1: google.protobuf.Int64Value
	(*wrapperspb.StringValue)(nil), // 2: google.protobuf.StringValue
}
var file_v1_model_domain_proto_depIdxs = []int32{
	1, // 0: api.v1.model.ServiceDomain.id:type_name -> google.protobuf.Int64Value
	2, // 1: api.v1.model.ServiceDomain.domain:type_name -> google.protobuf.StringValue
	2, // 2: api.v1.model.ServiceDomain.namespace:type_name -> google.protobuf.StringValue
	2, // 3: api.v1.model.ServiceDomain.service:type_name -> google.protobuf.StringValue
	2, // 4: api.v1.model.ServiceDomain.service_token:type_name -> google.protobuf.StringValue
	2, // 5: api.v1.model.ServiceDomain.ctime:type_name -> google.protobuf.StringValue
	2, // 6: api.v1.model.ServiceDomain.mtime:type_name -> google.protobuf.StringValue
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_v1_model_domain_proto_init() }
func file_v1_model_domain_proto_init() {
	if File_v1_model_domain_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_v1_model_domain_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceDomain); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_model_domain_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v1_model_domain_proto_goTypes,
		DependencyIndexes: file_v1_model_domain_proto_depIdxs,
		MessageInfos:      file_v1_model_domain_proto_msgTypes,
	}.Build()
	File_v1_model_domain_proto = out.File
	file_v1_model_domain_proto_rawDesc = nil
	file_v1_model_domain_proto_goTypes = nil
	file_v1_model_domain_proto_depIdxs = nil
}
