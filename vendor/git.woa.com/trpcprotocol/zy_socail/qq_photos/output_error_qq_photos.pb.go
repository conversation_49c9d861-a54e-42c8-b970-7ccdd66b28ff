// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.6.1
// source: output_error_qq_photos.proto

package qq_photos

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 请求参数
type RawFixToolRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id []uint64 `protobuf:"varint,1,rep,packed,name=id,proto3" json:"id,omitempty"`
}

func (x *RawFixToolRequest) Reset() {
	*x = RawFixToolRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_output_error_qq_photos_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RawFixToolRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawFixToolRequest) ProtoMessage() {}

func (x *RawFixToolRequest) ProtoReflect() protoreflect.Message {
	mi := &file_output_error_qq_photos_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawFixToolRequest.ProtoReflect.Descriptor instead.
func (*RawFixToolRequest) Descriptor() ([]byte, []int) {
	return file_output_error_qq_photos_proto_rawDescGZIP(), []int{0}
}

func (x *RawFixToolRequest) GetId() []uint64 {
	if x != nil {
		return x.Id
	}
	return nil
}

// 响应参数
type RawFixToolReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorList []*PhotoErrorInfo `protobuf:"bytes,1,rep,name=errorList,proto3" json:"errorList,omitempty"`
}

func (x *RawFixToolReply) Reset() {
	*x = RawFixToolReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_output_error_qq_photos_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RawFixToolReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawFixToolReply) ProtoMessage() {}

func (x *RawFixToolReply) ProtoReflect() protoreflect.Message {
	mi := &file_output_error_qq_photos_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawFixToolReply.ProtoReflect.Descriptor instead.
func (*RawFixToolReply) Descriptor() ([]byte, []int) {
	return file_output_error_qq_photos_proto_rawDescGZIP(), []int{1}
}

func (x *RawFixToolReply) GetErrorList() []*PhotoErrorInfo {
	if x != nil {
		return x.ErrorList
	}
	return nil
}

type PhotoErrorInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uin     string `protobuf:"bytes,1,opt,name=uin,proto3" json:"uin,omitempty"`
	AlbumId string `protobuf:"bytes,2,opt,name=albumId,proto3" json:"albumId,omitempty"`
	Lloc    string `protobuf:"bytes,3,opt,name=lloc,proto3" json:"lloc,omitempty"`
}

func (x *PhotoErrorInfo) Reset() {
	*x = PhotoErrorInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_output_error_qq_photos_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhotoErrorInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhotoErrorInfo) ProtoMessage() {}

func (x *PhotoErrorInfo) ProtoReflect() protoreflect.Message {
	mi := &file_output_error_qq_photos_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhotoErrorInfo.ProtoReflect.Descriptor instead.
func (*PhotoErrorInfo) Descriptor() ([]byte, []int) {
	return file_output_error_qq_photos_proto_rawDescGZIP(), []int{2}
}

func (x *PhotoErrorInfo) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *PhotoErrorInfo) GetAlbumId() string {
	if x != nil {
		return x.AlbumId
	}
	return ""
}

func (x *PhotoErrorInfo) GetLloc() string {
	if x != nil {
		return x.Lloc
	}
	return ""
}

var File_output_error_qq_photos_proto protoreflect.FileDescriptor

var file_output_error_qq_photos_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x71,
	0x71, 0x5f, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x7a, 0x79, 0x5f, 0x73, 0x6f, 0x63, 0x61, 0x69, 0x6c, 0x2e, 0x71,
	0x71, 0x5f, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x73, 0x22, 0x23, 0x0a, 0x11, 0x52, 0x61, 0x77, 0x46,
	0x69, 0x78, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0x59, 0x0a,
	0x0f, 0x52, 0x61, 0x77, 0x46, 0x69, 0x78, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x46, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x7a, 0x79, 0x5f, 0x73, 0x6f,
	0x63, 0x61, 0x69, 0x6c, 0x2e, 0x71, 0x71, 0x5f, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x73, 0x2e, 0x50,
	0x68, 0x6f, 0x74, 0x6f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x50, 0x0a, 0x0e, 0x50, 0x68, 0x6f, 0x74,
	0x6f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x6c, 0x62, 0x75, 0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x6c, 0x62, 0x75, 0x6d, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6c, 0x6f, 0x63, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6c, 0x6f, 0x63, 0x32, 0x76, 0x0a, 0x0e, 0x51, 0x7a,
	0x6f, 0x6e, 0x65, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x54, 0x6f, 0x6f, 0x6c, 0x12, 0x64, 0x0a, 0x0a,
	0x52, 0x61, 0x77, 0x46, 0x69, 0x78, 0x54, 0x6f, 0x6f, 0x6c, 0x12, 0x2b, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x7a, 0x79, 0x5f, 0x73, 0x6f, 0x63, 0x61, 0x69, 0x6c, 0x2e, 0x71, 0x71, 0x5f, 0x70,
	0x68, 0x6f, 0x74, 0x6f, 0x73, 0x2e, 0x52, 0x61, 0x77, 0x46, 0x69, 0x78, 0x54, 0x6f, 0x6f, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x7a,
	0x79, 0x5f, 0x73, 0x6f, 0x63, 0x61, 0x69, 0x6c, 0x2e, 0x71, 0x71, 0x5f, 0x70, 0x68, 0x6f, 0x74,
	0x6f, 0x73, 0x2e, 0x52, 0x61, 0x77, 0x46, 0x69, 0x78, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x42, 0x2e, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x74, 0x72, 0x70, 0x63, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x7a,
	0x79, 0x5f, 0x73, 0x6f, 0x63, 0x61, 0x69, 0x6c, 0x2f, 0x71, 0x71, 0x5f, 0x70, 0x68, 0x6f, 0x74,
	0x6f, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_output_error_qq_photos_proto_rawDescOnce sync.Once
	file_output_error_qq_photos_proto_rawDescData = file_output_error_qq_photos_proto_rawDesc
)

func file_output_error_qq_photos_proto_rawDescGZIP() []byte {
	file_output_error_qq_photos_proto_rawDescOnce.Do(func() {
		file_output_error_qq_photos_proto_rawDescData = protoimpl.X.CompressGZIP(file_output_error_qq_photos_proto_rawDescData)
	})
	return file_output_error_qq_photos_proto_rawDescData
}

var file_output_error_qq_photos_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_output_error_qq_photos_proto_goTypes = []interface{}{
	(*RawFixToolRequest)(nil), // 0: trpc.zy_socail.qq_photos.RawFixToolRequest
	(*RawFixToolReply)(nil),   // 1: trpc.zy_socail.qq_photos.RawFixToolReply
	(*PhotoErrorInfo)(nil),    // 2: trpc.zy_socail.qq_photos.PhotoErrorInfo
}
var file_output_error_qq_photos_proto_depIdxs = []int32{
	2, // 0: trpc.zy_socail.qq_photos.RawFixToolReply.errorList:type_name -> trpc.zy_socail.qq_photos.PhotoErrorInfo
	0, // 1: trpc.zy_socail.qq_photos.QzonePhotoTool.RawFixTool:input_type -> trpc.zy_socail.qq_photos.RawFixToolRequest
	1, // 2: trpc.zy_socail.qq_photos.QzonePhotoTool.RawFixTool:output_type -> trpc.zy_socail.qq_photos.RawFixToolReply
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_output_error_qq_photos_proto_init() }
func file_output_error_qq_photos_proto_init() {
	if File_output_error_qq_photos_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_output_error_qq_photos_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RawFixToolRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_output_error_qq_photos_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RawFixToolReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_output_error_qq_photos_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhotoErrorInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_output_error_qq_photos_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_output_error_qq_photos_proto_goTypes,
		DependencyIndexes: file_output_error_qq_photos_proto_depIdxs,
		MessageInfos:      file_output_error_qq_photos_proto_msgTypes,
	}.Build()
	File_output_error_qq_photos_proto = out.File
	file_output_error_qq_photos_proto_rawDesc = nil
	file_output_error_qq_photos_proto_goTypes = nil
	file_output_error_qq_photos_proto_depIdxs = nil
}
