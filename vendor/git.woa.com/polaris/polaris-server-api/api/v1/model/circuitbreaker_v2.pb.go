// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v1/model/circuitbreaker_v2.proto

package polarismodel_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 返回码匹配方式
type CircuitBreakerRule_ErrorRateConfig_ErrorType int32

const (
	// 网络错误配置
	CircuitBreakerRule_ErrorRateConfig_NETWORK_ERROR CircuitBreakerRule_ErrorRateConfig_ErrorType = 0
	// 返回码黑名单方式配置
	CircuitBreakerRule_ErrorRateConfig_ERROR_CODE_LIST CircuitBreakerRule_ErrorRateConfig_ErrorType = 1
	// 返回码白名单方式配置
	CircuitBreakerRule_ErrorRateConfig_OK_CODE_LIST CircuitBreakerRule_ErrorRateConfig_ErrorType = 2
	// 慢调用配置
	CircuitBreakerRule_ErrorRateConfig_SLOW_CALL_ERROR CircuitBreakerRule_ErrorRateConfig_ErrorType = 3
)

// Enum value maps for CircuitBreakerRule_ErrorRateConfig_ErrorType.
var (
	CircuitBreakerRule_ErrorRateConfig_ErrorType_name = map[int32]string{
		0: "NETWORK_ERROR",
		1: "ERROR_CODE_LIST",
		2: "OK_CODE_LIST",
		3: "SLOW_CALL_ERROR",
	}
	CircuitBreakerRule_ErrorRateConfig_ErrorType_value = map[string]int32{
		"NETWORK_ERROR":   0,
		"ERROR_CODE_LIST": 1,
		"OK_CODE_LIST":    2,
		"SLOW_CALL_ERROR": 3,
	}
)

func (x CircuitBreakerRule_ErrorRateConfig_ErrorType) Enum() *CircuitBreakerRule_ErrorRateConfig_ErrorType {
	p := new(CircuitBreakerRule_ErrorRateConfig_ErrorType)
	*p = x
	return p
}

func (x CircuitBreakerRule_ErrorRateConfig_ErrorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CircuitBreakerRule_ErrorRateConfig_ErrorType) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_circuitbreaker_v2_proto_enumTypes[0].Descriptor()
}

func (CircuitBreakerRule_ErrorRateConfig_ErrorType) Type() protoreflect.EnumType {
	return &file_v1_model_circuitbreaker_v2_proto_enumTypes[0]
}

func (x CircuitBreakerRule_ErrorRateConfig_ErrorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CircuitBreakerRule_ErrorRateConfig_ErrorType.Descriptor instead.
func (CircuitBreakerRule_ErrorRateConfig_ErrorType) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_v2_proto_rawDescGZIP(), []int{1, 2, 0}
}

// 需要进行熔断的资源
type CircuitBreakerDestination_CircuitBreakerPolicy_Resource int32

const (
	// 针对SUBSET下的实例进行熔断
	CircuitBreakerDestination_CircuitBreakerPolicy_SUBSET_ALL_INSTANCES CircuitBreakerDestination_CircuitBreakerPolicy_Resource = 0
	// 针对实例自身进行熔断
	CircuitBreakerDestination_CircuitBreakerPolicy_INSTANCE_SELF CircuitBreakerDestination_CircuitBreakerPolicy_Resource = 1
	// 针对服务下所有实例进行熔断
	CircuitBreakerDestination_CircuitBreakerPolicy_SERVICE_ALL_INSTANCES CircuitBreakerDestination_CircuitBreakerPolicy_Resource = 2
)

// Enum value maps for CircuitBreakerDestination_CircuitBreakerPolicy_Resource.
var (
	CircuitBreakerDestination_CircuitBreakerPolicy_Resource_name = map[int32]string{
		0: "SUBSET_ALL_INSTANCES",
		1: "INSTANCE_SELF",
		2: "SERVICE_ALL_INSTANCES",
	}
	CircuitBreakerDestination_CircuitBreakerPolicy_Resource_value = map[string]int32{
		"SUBSET_ALL_INSTANCES":  0,
		"INSTANCE_SELF":         1,
		"SERVICE_ALL_INSTANCES": 2,
	}
)

func (x CircuitBreakerDestination_CircuitBreakerPolicy_Resource) Enum() *CircuitBreakerDestination_CircuitBreakerPolicy_Resource {
	p := new(CircuitBreakerDestination_CircuitBreakerPolicy_Resource)
	*p = x
	return p
}

func (x CircuitBreakerDestination_CircuitBreakerPolicy_Resource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CircuitBreakerDestination_CircuitBreakerPolicy_Resource) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_circuitbreaker_v2_proto_enumTypes[1].Descriptor()
}

func (CircuitBreakerDestination_CircuitBreakerPolicy_Resource) Type() protoreflect.EnumType {
	return &file_v1_model_circuitbreaker_v2_proto_enumTypes[1]
}

func (x CircuitBreakerDestination_CircuitBreakerPolicy_Resource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CircuitBreakerDestination_CircuitBreakerPolicy_Resource.Descriptor instead.
func (CircuitBreakerDestination_CircuitBreakerPolicy_Resource) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_v2_proto_rawDescGZIP(), []int{2, 0, 0}
}

// 当指定的labels接口达到熔断条件时，触发熔断接口范围
type CircuitBreakerDestination_CircuitBreakerPolicy_Scope int32

const (
	// 触发熔断条件，熔断当前服务所有Labels接口
	CircuitBreakerDestination_CircuitBreakerPolicy_SERVICE_ALL_LABELS CircuitBreakerDestination_CircuitBreakerPolicy_Scope = 0
	// 触发熔断条件，熔断当前服务指定Labels接口
	CircuitBreakerDestination_CircuitBreakerPolicy_SERVICE_SPECIFIC_LABELS CircuitBreakerDestination_CircuitBreakerPolicy_Scope = 1
	// 熔断触发条件，熔断服务组所有Labels接口
	CircuitBreakerDestination_CircuitBreakerPolicy_GROUP_ALL_LABELS CircuitBreakerDestination_CircuitBreakerPolicy_Scope = 2
	// 熔断触发条件，熔断服务组指定Labels接口
	CircuitBreakerDestination_CircuitBreakerPolicy_GROUP_SPECIFIC_LABELS CircuitBreakerDestination_CircuitBreakerPolicy_Scope = 3
)

// Enum value maps for CircuitBreakerDestination_CircuitBreakerPolicy_Scope.
var (
	CircuitBreakerDestination_CircuitBreakerPolicy_Scope_name = map[int32]string{
		0: "SERVICE_ALL_LABELS",
		1: "SERVICE_SPECIFIC_LABELS",
		2: "GROUP_ALL_LABELS",
		3: "GROUP_SPECIFIC_LABELS",
	}
	CircuitBreakerDestination_CircuitBreakerPolicy_Scope_value = map[string]int32{
		"SERVICE_ALL_LABELS":      0,
		"SERVICE_SPECIFIC_LABELS": 1,
		"GROUP_ALL_LABELS":        2,
		"GROUP_SPECIFIC_LABELS":   3,
	}
)

func (x CircuitBreakerDestination_CircuitBreakerPolicy_Scope) Enum() *CircuitBreakerDestination_CircuitBreakerPolicy_Scope {
	p := new(CircuitBreakerDestination_CircuitBreakerPolicy_Scope)
	*p = x
	return p
}

func (x CircuitBreakerDestination_CircuitBreakerPolicy_Scope) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CircuitBreakerDestination_CircuitBreakerPolicy_Scope) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_circuitbreaker_v2_proto_enumTypes[2].Descriptor()
}

func (CircuitBreakerDestination_CircuitBreakerPolicy_Scope) Type() protoreflect.EnumType {
	return &file_v1_model_circuitbreaker_v2_proto_enumTypes[2]
}

func (x CircuitBreakerDestination_CircuitBreakerPolicy_Scope) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CircuitBreakerDestination_CircuitBreakerPolicy_Scope.Descriptor instead.
func (CircuitBreakerDestination_CircuitBreakerPolicy_Scope) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_v2_proto_rawDescGZIP(), []int{2, 0, 1}
}

// 单个熔断规则定义
type CircuitBreakerV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 规则版本
	Version *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// 规则名
	Name *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 规则命名空间
	Namespace *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 规则所属服务
	Service          *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=service,proto3" json:"service,omitempty"`
	ServiceNamespace *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=service_namespace,json=serviceNamespace,proto3" json:"service_namespace,omitempty"`
	// 熔断规则可以分为被调规则和主调规则
	// 被调规则针对所有的指定主调生效，假如不指定则对所有的主调生效
	// 主调规则为当前主调方的规则，假如不指定则针对所有被调生效
	Inbounds  []*CircuitBreakerRule   `protobuf:"bytes,7,rep,name=inbounds,proto3" json:"inbounds,omitempty"`
	Outbounds []*CircuitBreakerRule   `protobuf:"bytes,8,rep,name=outbounds,proto3" json:"outbounds,omitempty"`
	Token     *wrapperspb.StringValue `protobuf:"bytes,9,opt,name=token,proto3" json:"token,omitempty"`
	Owners    *wrapperspb.StringValue `protobuf:"bytes,10,opt,name=owners,proto3" json:"owners,omitempty"`
	// 业务
	Business *wrapperspb.StringValue `protobuf:"bytes,11,opt,name=business,proto3" json:"business,omitempty"`
	// 部门
	Department *wrapperspb.StringValue `protobuf:"bytes,12,opt,name=department,proto3" json:"department,omitempty"`
	// 规则描述
	Comment  *wrapperspb.StringValue `protobuf:"bytes,13,opt,name=comment,proto3" json:"comment,omitempty"`
	Ctime    *wrapperspb.StringValue `protobuf:"bytes,14,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Mtime    *wrapperspb.StringValue `protobuf:"bytes,15,opt,name=mtime,proto3" json:"mtime,omitempty"`
	Revision *wrapperspb.StringValue `protobuf:"bytes,16,opt,name=revision,proto3" json:"revision,omitempty"`
}

func (x *CircuitBreakerV2) Reset() {
	*x = CircuitBreakerV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerV2) ProtoMessage() {}

func (x *CircuitBreakerV2) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerV2.ProtoReflect.Descriptor instead.
func (*CircuitBreakerV2) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_v2_proto_rawDescGZIP(), []int{0}
}

func (x *CircuitBreakerV2) GetId() *wrapperspb.StringValue {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *CircuitBreakerV2) GetVersion() *wrapperspb.StringValue {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *CircuitBreakerV2) GetName() *wrapperspb.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *CircuitBreakerV2) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *CircuitBreakerV2) GetService() *wrapperspb.StringValue {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *CircuitBreakerV2) GetServiceNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.ServiceNamespace
	}
	return nil
}

func (x *CircuitBreakerV2) GetInbounds() []*CircuitBreakerRule {
	if x != nil {
		return x.Inbounds
	}
	return nil
}

func (x *CircuitBreakerV2) GetOutbounds() []*CircuitBreakerRule {
	if x != nil {
		return x.Outbounds
	}
	return nil
}

func (x *CircuitBreakerV2) GetToken() *wrapperspb.StringValue {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *CircuitBreakerV2) GetOwners() *wrapperspb.StringValue {
	if x != nil {
		return x.Owners
	}
	return nil
}

func (x *CircuitBreakerV2) GetBusiness() *wrapperspb.StringValue {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *CircuitBreakerV2) GetDepartment() *wrapperspb.StringValue {
	if x != nil {
		return x.Department
	}
	return nil
}

func (x *CircuitBreakerV2) GetComment() *wrapperspb.StringValue {
	if x != nil {
		return x.Comment
	}
	return nil
}

func (x *CircuitBreakerV2) GetCtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Ctime
	}
	return nil
}

func (x *CircuitBreakerV2) GetMtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Mtime
	}
	return nil
}

func (x *CircuitBreakerV2) GetRevision() *wrapperspb.StringValue {
	if x != nil {
		return x.Revision
	}
	return nil
}

// 熔断规则
type CircuitBreakerRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 规则名字，业务配置时需要保证服务下唯一
	Name    *wrapperspb.StringValue             `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Sources []*CircuitBreakerRule_SourceMatcher `protobuf:"bytes,2,rep,name=sources,proto3" json:"sources,omitempty"`
	// 服务分组，可选，用于关联服务进行统一SET熔断切换
	ServiceGroup *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=service_group,json=serviceGroup,proto3" json:"service_group,omitempty"`
	// 多个主调使用此规则时是否合并统计
	CallerCombine *wrapperspb.BoolValue `protobuf:"bytes,4,opt,name=caller_combine,json=callerCombine,proto3" json:"caller_combine,omitempty"`
	// 业务返回码分类，key为类型名字，value为该类型返回码匹配正则
	CodeCategories map[string]*MatchString `protobuf:"bytes,5,rep,name=code_categories,json=codeCategories,proto3" json:"code_categories,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 错误率规则配置
	ErrorRate map[string]*CircuitBreakerRule_ErrorRateConfig `protobuf:"bytes,6,rep,name=error_rate,json=errorRate,proto3" json:"error_rate,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 分布式熔断规则，GLOBAL（分布式决策）,SDK会定期上报统计数据并根据汇总数据进行熔断决策
	GlobalDestination *CircuitBreakerDestination `protobuf:"bytes,7,opt,name=global_destination,json=globalDestination,proto3" json:"global_destination,omitempty"`
	// SDK本地熔断规则，LOCAL(SDK本地决策）
	LocalDestination *CircuitBreakerDestination `protobuf:"bytes,8,opt,name=local_destination,json=localDestination,proto3" json:"local_destination,omitempty"`
}

func (x *CircuitBreakerRule) Reset() {
	*x = CircuitBreakerRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerRule) ProtoMessage() {}

func (x *CircuitBreakerRule) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerRule.ProtoReflect.Descriptor instead.
func (*CircuitBreakerRule) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_v2_proto_rawDescGZIP(), []int{1}
}

func (x *CircuitBreakerRule) GetName() *wrapperspb.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *CircuitBreakerRule) GetSources() []*CircuitBreakerRule_SourceMatcher {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *CircuitBreakerRule) GetServiceGroup() *wrapperspb.StringValue {
	if x != nil {
		return x.ServiceGroup
	}
	return nil
}

func (x *CircuitBreakerRule) GetCallerCombine() *wrapperspb.BoolValue {
	if x != nil {
		return x.CallerCombine
	}
	return nil
}

func (x *CircuitBreakerRule) GetCodeCategories() map[string]*MatchString {
	if x != nil {
		return x.CodeCategories
	}
	return nil
}

func (x *CircuitBreakerRule) GetErrorRate() map[string]*CircuitBreakerRule_ErrorRateConfig {
	if x != nil {
		return x.ErrorRate
	}
	return nil
}

func (x *CircuitBreakerRule) GetGlobalDestination() *CircuitBreakerDestination {
	if x != nil {
		return x.GlobalDestination
	}
	return nil
}

func (x *CircuitBreakerRule) GetLocalDestination() *CircuitBreakerDestination {
	if x != nil {
		return x.LocalDestination
	}
	return nil
}

// 服务熔断规则
type CircuitBreakerDestination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 具体的熔断策略配置
	Policies      []*CircuitBreakerDestination_CircuitBreakerPolicy `protobuf:"bytes,1,rep,name=policies,proto3" json:"policies,omitempty"`
	RecoverConfig *CircuitBreakerDestination_RecoverConfig          `protobuf:"bytes,2,opt,name=recover_config,json=recoverConfig,proto3" json:"recover_config,omitempty"`
	MetricConfig  *CircuitBreakerDestination_MetricConfig           `protobuf:"bytes,3,opt,name=metric_config,json=metricConfig,proto3" json:"metric_config,omitempty"`
	// 分布式熔断使用的集群服务名
	// 默认单集群，整个规则使用一个集群
	Cluster      *wrapperspb.StringValue                             `protobuf:"bytes,4,opt,name=cluster,proto3" json:"cluster,omitempty"`
	MultiCluster map[string]*CircuitBreakerDestination_ClusterConfig `protobuf:"bytes,5,rep,name=multi_cluster,json=multiCluster,proto3" json:"multi_cluster,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CircuitBreakerDestination) Reset() {
	*x = CircuitBreakerDestination{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerDestination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerDestination) ProtoMessage() {}

func (x *CircuitBreakerDestination) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerDestination.ProtoReflect.Descriptor instead.
func (*CircuitBreakerDestination) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_v2_proto_rawDescGZIP(), []int{2}
}

func (x *CircuitBreakerDestination) GetPolicies() []*CircuitBreakerDestination_CircuitBreakerPolicy {
	if x != nil {
		return x.Policies
	}
	return nil
}

func (x *CircuitBreakerDestination) GetRecoverConfig() *CircuitBreakerDestination_RecoverConfig {
	if x != nil {
		return x.RecoverConfig
	}
	return nil
}

func (x *CircuitBreakerDestination) GetMetricConfig() *CircuitBreakerDestination_MetricConfig {
	if x != nil {
		return x.MetricConfig
	}
	return nil
}

func (x *CircuitBreakerDestination) GetCluster() *wrapperspb.StringValue {
	if x != nil {
		return x.Cluster
	}
	return nil
}

func (x *CircuitBreakerDestination) GetMultiCluster() map[string]*CircuitBreakerDestination_ClusterConfig {
	if x != nil {
		return x.MultiCluster
	}
	return nil
}

// 如果匹配Source规则，按照Destination进行熔断，多个Source之间的关系为或
// 熔断服务匹配规则
type CircuitBreakerRule_SourceMatcher struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 主调命名空间和服务名，不填代表全匹配
	Namespace *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Service   *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
	// 被调命名空间和服务名，不填代表全匹配
	ToNamespace *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=to_namespace,json=toNamespace,proto3" json:"to_namespace,omitempty"`
	ToService   *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=to_service,json=toService,proto3" json:"to_service,omitempty"`
}

func (x *CircuitBreakerRule_SourceMatcher) Reset() {
	*x = CircuitBreakerRule_SourceMatcher{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerRule_SourceMatcher) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerRule_SourceMatcher) ProtoMessage() {}

func (x *CircuitBreakerRule_SourceMatcher) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerRule_SourceMatcher.ProtoReflect.Descriptor instead.
func (*CircuitBreakerRule_SourceMatcher) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_v2_proto_rawDescGZIP(), []int{1, 0}
}

func (x *CircuitBreakerRule_SourceMatcher) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *CircuitBreakerRule_SourceMatcher) GetService() *wrapperspb.StringValue {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *CircuitBreakerRule_SourceMatcher) GetToNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.ToNamespace
	}
	return nil
}

func (x *CircuitBreakerRule_SourceMatcher) GetToService() *wrapperspb.StringValue {
	if x != nil {
		return x.ToService
	}
	return nil
}

// 错误率熔断规则
type CircuitBreakerRule_ErrorRateConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 错误类型
	ErrorType CircuitBreakerRule_ErrorRateConfig_ErrorType `protobuf:"varint,1,opt,name=error_type,json=errorType,proto3,enum=api.v1.model.CircuitBreakerRule_ErrorRateConfig_ErrorType" json:"error_type,omitempty"`
	// 返回码黑白名单计算错误率时，相关的返回码配置
	CodesList []string `protobuf:"bytes,2,rep,name=codes_list,json=codesList,proto3" json:"codes_list,omitempty"`
	// 最大响应时间，超过该时间属于慢调用请求
	MaxSlowRt *durationpb.Duration `protobuf:"bytes,3,opt,name=max_slow_rt,json=maxSlowRt,proto3" json:"max_slow_rt,omitempty"`
	// 触发错误率熔断的最低请求阈值
	RequestThreshold *wrapperspb.UInt32Value `protobuf:"bytes,4,opt,name=request_threshold,json=requestThreshold,proto3" json:"request_threshold,omitempty"`
	// 可选。触发保持状态的错误率阈值，假如不配置，则默认不会进入Preserved状态
	ErrorRateToPreserved *wrapperspb.UInt32Value `protobuf:"bytes,5,opt,name=error_rate_to_preserved,json=errorRateToPreserved,proto3" json:"error_rate_to_preserved,omitempty"`
	// 触发熔断的错误率阈值
	ErrorRateToBreaker *wrapperspb.UInt32Value `protobuf:"bytes,6,opt,name=error_rate_to_breaker,json=errorRateToBreaker,proto3" json:"error_rate_to_breaker,omitempty"`
	// 触发恢复的错误率阈值
	ErrorRateToRecover *wrapperspb.UInt32Value `protobuf:"bytes,7,opt,name=error_rate_to_recover,json=errorRateToRecover,proto3" json:"error_rate_to_recover,omitempty"`
	// 触发错误率熔断最低失败节点阈值
	MinErrorNodeRate *wrapperspb.UInt32Value `protobuf:"bytes,8,opt,name=min_error_node_rate,json=minErrorNodeRate,proto3" json:"min_error_node_rate,omitempty"`
	// 触发连续错误熔断的阈值
	ContinuousErrorThreshold *wrapperspb.UInt32Value `protobuf:"bytes,9,opt,name=continuous_error_threshold,json=continuousErrorThreshold,proto3" json:"continuous_error_threshold,omitempty"`
	// 是否禁用熔断
	IsDisable *wrapperspb.BoolValue `protobuf:"bytes,10,opt,name=is_disable,json=isDisable,proto3" json:"is_disable,omitempty"`
}

func (x *CircuitBreakerRule_ErrorRateConfig) Reset() {
	*x = CircuitBreakerRule_ErrorRateConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerRule_ErrorRateConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerRule_ErrorRateConfig) ProtoMessage() {}

func (x *CircuitBreakerRule_ErrorRateConfig) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerRule_ErrorRateConfig.ProtoReflect.Descriptor instead.
func (*CircuitBreakerRule_ErrorRateConfig) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_v2_proto_rawDescGZIP(), []int{1, 2}
}

func (x *CircuitBreakerRule_ErrorRateConfig) GetErrorType() CircuitBreakerRule_ErrorRateConfig_ErrorType {
	if x != nil {
		return x.ErrorType
	}
	return CircuitBreakerRule_ErrorRateConfig_NETWORK_ERROR
}

func (x *CircuitBreakerRule_ErrorRateConfig) GetCodesList() []string {
	if x != nil {
		return x.CodesList
	}
	return nil
}

func (x *CircuitBreakerRule_ErrorRateConfig) GetMaxSlowRt() *durationpb.Duration {
	if x != nil {
		return x.MaxSlowRt
	}
	return nil
}

func (x *CircuitBreakerRule_ErrorRateConfig) GetRequestThreshold() *wrapperspb.UInt32Value {
	if x != nil {
		return x.RequestThreshold
	}
	return nil
}

func (x *CircuitBreakerRule_ErrorRateConfig) GetErrorRateToPreserved() *wrapperspb.UInt32Value {
	if x != nil {
		return x.ErrorRateToPreserved
	}
	return nil
}

func (x *CircuitBreakerRule_ErrorRateConfig) GetErrorRateToBreaker() *wrapperspb.UInt32Value {
	if x != nil {
		return x.ErrorRateToBreaker
	}
	return nil
}

func (x *CircuitBreakerRule_ErrorRateConfig) GetErrorRateToRecover() *wrapperspb.UInt32Value {
	if x != nil {
		return x.ErrorRateToRecover
	}
	return nil
}

func (x *CircuitBreakerRule_ErrorRateConfig) GetMinErrorNodeRate() *wrapperspb.UInt32Value {
	if x != nil {
		return x.MinErrorNodeRate
	}
	return nil
}

func (x *CircuitBreakerRule_ErrorRateConfig) GetContinuousErrorThreshold() *wrapperspb.UInt32Value {
	if x != nil {
		return x.ContinuousErrorThreshold
	}
	return nil
}

func (x *CircuitBreakerRule_ErrorRateConfig) GetIsDisable() *wrapperspb.BoolValue {
	if x != nil {
		return x.IsDisable
	}
	return nil
}

// 针对具体资源+接口的熔断策略
type CircuitBreakerDestination_CircuitBreakerPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Resource CircuitBreakerDestination_CircuitBreakerPolicy_Resource `protobuf:"varint,1,opt,name=resource,proto3,enum=api.v1.model.CircuitBreakerDestination_CircuitBreakerPolicy_Resource" json:"resource,omitempty"`
	// 可选，SUBSET标识
	Subset map[string]*MatchString                              `protobuf:"bytes,2,rep,name=subset,proto3" json:"subset,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Scope  CircuitBreakerDestination_CircuitBreakerPolicy_Scope `protobuf:"varint,3,opt,name=scope,proto3,enum=api.v1.model.CircuitBreakerDestination_CircuitBreakerPolicy_Scope" json:"scope,omitempty"`
	// 用于表示统计时匹配的接口信息，接口信息匹配则使用该规则进行统计
	// 不设置时表示所有接口
	MatchLabels map[string]*MatchString `protobuf:"bytes,4,rep,name=match_labels,json=matchLabels,proto3" json:"match_labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 可选，所匹配的接口是否作为一组接口进行统计，默认每个接口单独统计
	LablesCombine *wrapperspb.BoolValue `protobuf:"bytes,5,opt,name=lables_combine,json=lablesCombine,proto3" json:"lables_combine,omitempty"`
	// 当scope为特定接口熔断时，用于表示需要被当前策略熔断的接口
	// 可选，不填时默认使用match_labels
	BreakerLabels map[string]*MatchString `protobuf:"bytes,6,rep,name=breaker_labels,json=breakerLabels,proto3" json:"breaker_labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 错误率规则
	ErrorRateList []string `protobuf:"bytes,7,rep,name=error_rate_list,json=errorRateList,proto3" json:"error_rate_list,omitempty"`
	// 最大熔断实例比例
	MaxBreakerRate *wrapperspb.UInt32Value `protobuf:"bytes,8,opt,name=max_breaker_rate,json=maxBreakerRate,proto3" json:"max_breaker_rate,omitempty"`
}

func (x *CircuitBreakerDestination_CircuitBreakerPolicy) Reset() {
	*x = CircuitBreakerDestination_CircuitBreakerPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerDestination_CircuitBreakerPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerDestination_CircuitBreakerPolicy) ProtoMessage() {}

func (x *CircuitBreakerDestination_CircuitBreakerPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerDestination_CircuitBreakerPolicy.ProtoReflect.Descriptor instead.
func (*CircuitBreakerDestination_CircuitBreakerPolicy) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_v2_proto_rawDescGZIP(), []int{2, 0}
}

func (x *CircuitBreakerDestination_CircuitBreakerPolicy) GetResource() CircuitBreakerDestination_CircuitBreakerPolicy_Resource {
	if x != nil {
		return x.Resource
	}
	return CircuitBreakerDestination_CircuitBreakerPolicy_SUBSET_ALL_INSTANCES
}

func (x *CircuitBreakerDestination_CircuitBreakerPolicy) GetSubset() map[string]*MatchString {
	if x != nil {
		return x.Subset
	}
	return nil
}

func (x *CircuitBreakerDestination_CircuitBreakerPolicy) GetScope() CircuitBreakerDestination_CircuitBreakerPolicy_Scope {
	if x != nil {
		return x.Scope
	}
	return CircuitBreakerDestination_CircuitBreakerPolicy_SERVICE_ALL_LABELS
}

func (x *CircuitBreakerDestination_CircuitBreakerPolicy) GetMatchLabels() map[string]*MatchString {
	if x != nil {
		return x.MatchLabels
	}
	return nil
}

func (x *CircuitBreakerDestination_CircuitBreakerPolicy) GetLablesCombine() *wrapperspb.BoolValue {
	if x != nil {
		return x.LablesCombine
	}
	return nil
}

func (x *CircuitBreakerDestination_CircuitBreakerPolicy) GetBreakerLabels() map[string]*MatchString {
	if x != nil {
		return x.BreakerLabels
	}
	return nil
}

func (x *CircuitBreakerDestination_CircuitBreakerPolicy) GetErrorRateList() []string {
	if x != nil {
		return x.ErrorRateList
	}
	return nil
}

func (x *CircuitBreakerDestination_CircuitBreakerPolicy) GetMaxBreakerRate() *wrapperspb.UInt32Value {
	if x != nil {
		return x.MaxBreakerRate
	}
	return nil
}

// 熔断恢复配置
type CircuitBreakerDestination_RecoverConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 触发熔断后到半开状态之间的等待间隔
	SleepWindow *durationpb.Duration `protobuf:"bytes,1,opt,name=sleep_window,json=sleepWindow,proto3" json:"sleep_window,omitempty"`
	// 半开后，最多重试多少次恢复
	MaxRetryAfterHalfOpen *wrapperspb.UInt32Value `protobuf:"bytes,2,opt,name=max_retry_after_half_open,json=maxRetryAfterHalfOpen,proto3" json:"max_retry_after_half_open,omitempty"`
	// 半开后放量的百分比
	RequestRateAfterHalfOpen []*wrapperspb.UInt32Value `protobuf:"bytes,3,rep,name=request_rate_after_half_open,json=requestRateAfterHalfOpen,proto3" json:"request_rate_after_half_open,omitempty"`
	// 熔断器半开到关闭所必须的最少成功率
	SuccessRateToClose *wrapperspb.UInt32Value `protobuf:"bytes,4,opt,name=success_rate_to_close,json=successRateToClose,proto3" json:"success_rate_to_close,omitempty"`
	// 半开放量成功率计算最小阈值
	HalfOpenRequestThreshold *wrapperspb.UInt32Value `protobuf:"bytes,5,opt,name=half_open_request_threshold,json=halfOpenRequestThreshold,proto3" json:"half_open_request_threshold,omitempty"`
	// 半开失败后，熔断时间扩展因子
	ExpansionFactor *wrapperspb.FloatValue `protobuf:"bytes,6,opt,name=expansion_factor,json=expansionFactor,proto3" json:"expansion_factor,omitempty"`
	// 是否开启自动回切
	Enable *wrapperspb.BoolValue `protobuf:"bytes,7,opt,name=enable,proto3" json:"enable,omitempty"`
	// 触发熔断后到半开状态之间等待间隔的最大值
	MaxSleepWindow *durationpb.Duration `protobuf:"bytes,8,opt,name=max_sleep_window,json=maxSleepWindow,proto3" json:"max_sleep_window,omitempty"`
	// 熔断器半开后最大允许的请求数
	RequestCountAfterHalfOpen *wrapperspb.UInt32Value `protobuf:"bytes,9,opt,name=request_count_after_half_open,json=requestCountAfterHalfOpen,proto3" json:"request_count_after_half_open,omitempty"`
	// 熔断器半开到关闭所必须的最少成功请求数
	SuccessCountAfterHalfOpen *wrapperspb.UInt32Value `protobuf:"bytes,10,opt,name=success_count_after_half_open,json=successCountAfterHalfOpen,proto3" json:"success_count_after_half_open,omitempty"`
}

func (x *CircuitBreakerDestination_RecoverConfig) Reset() {
	*x = CircuitBreakerDestination_RecoverConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerDestination_RecoverConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerDestination_RecoverConfig) ProtoMessage() {}

func (x *CircuitBreakerDestination_RecoverConfig) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerDestination_RecoverConfig.ProtoReflect.Descriptor instead.
func (*CircuitBreakerDestination_RecoverConfig) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_v2_proto_rawDescGZIP(), []int{2, 1}
}

func (x *CircuitBreakerDestination_RecoverConfig) GetSleepWindow() *durationpb.Duration {
	if x != nil {
		return x.SleepWindow
	}
	return nil
}

func (x *CircuitBreakerDestination_RecoverConfig) GetMaxRetryAfterHalfOpen() *wrapperspb.UInt32Value {
	if x != nil {
		return x.MaxRetryAfterHalfOpen
	}
	return nil
}

func (x *CircuitBreakerDestination_RecoverConfig) GetRequestRateAfterHalfOpen() []*wrapperspb.UInt32Value {
	if x != nil {
		return x.RequestRateAfterHalfOpen
	}
	return nil
}

func (x *CircuitBreakerDestination_RecoverConfig) GetSuccessRateToClose() *wrapperspb.UInt32Value {
	if x != nil {
		return x.SuccessRateToClose
	}
	return nil
}

func (x *CircuitBreakerDestination_RecoverConfig) GetHalfOpenRequestThreshold() *wrapperspb.UInt32Value {
	if x != nil {
		return x.HalfOpenRequestThreshold
	}
	return nil
}

func (x *CircuitBreakerDestination_RecoverConfig) GetExpansionFactor() *wrapperspb.FloatValue {
	if x != nil {
		return x.ExpansionFactor
	}
	return nil
}

func (x *CircuitBreakerDestination_RecoverConfig) GetEnable() *wrapperspb.BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

func (x *CircuitBreakerDestination_RecoverConfig) GetMaxSleepWindow() *durationpb.Duration {
	if x != nil {
		return x.MaxSleepWindow
	}
	return nil
}

func (x *CircuitBreakerDestination_RecoverConfig) GetRequestCountAfterHalfOpen() *wrapperspb.UInt32Value {
	if x != nil {
		return x.RequestCountAfterHalfOpen
	}
	return nil
}

func (x *CircuitBreakerDestination_RecoverConfig) GetSuccessCountAfterHalfOpen() *wrapperspb.UInt32Value {
	if x != nil {
		return x.SuccessCountAfterHalfOpen
	}
	return nil
}

// 统计配置
type CircuitBreakerDestination_MetricConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 熔断数据度量周期
	// 所有的阈值指标按此周期进行统计
	Window *durationpb.Duration `protobuf:"bytes,1,opt,name=window,proto3" json:"window,omitempty"`
	// 数据上报周期
	ReportInterval *durationpb.Duration `protobuf:"bytes,2,opt,name=report_interval,json=reportInterval,proto3" json:"report_interval,omitempty"`
	// 熔断的决策周期，多久触发一次熔断决策
	JudgeInterval *durationpb.Duration `protobuf:"bytes,3,opt,name=judge_interval,json=judgeInterval,proto3" json:"judge_interval,omitempty"`
}

func (x *CircuitBreakerDestination_MetricConfig) Reset() {
	*x = CircuitBreakerDestination_MetricConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerDestination_MetricConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerDestination_MetricConfig) ProtoMessage() {}

func (x *CircuitBreakerDestination_MetricConfig) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerDestination_MetricConfig.ProtoReflect.Descriptor instead.
func (*CircuitBreakerDestination_MetricConfig) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_v2_proto_rawDescGZIP(), []int{2, 2}
}

func (x *CircuitBreakerDestination_MetricConfig) GetWindow() *durationpb.Duration {
	if x != nil {
		return x.Window
	}
	return nil
}

func (x *CircuitBreakerDestination_MetricConfig) GetReportInterval() *durationpb.Duration {
	if x != nil {
		return x.ReportInterval
	}
	return nil
}

func (x *CircuitBreakerDestination_MetricConfig) GetJudgeInterval() *durationpb.Duration {
	if x != nil {
		return x.JudgeInterval
	}
	return nil
}

// 如果业务选择针对不同地域分集群接入，则使用分集群配置
// 主调配置划分成多个地域，每个地域对应一个metric集群名字
// 不同地域的主调分开熔断，每个SET对应一个主地域
// SET被主地域熔断以后，需要通知其他非主地域的主调也进行对应的熔断
type CircuitBreakerDestination_ClusterConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 接入服务名，可以通过别名配合别名路由进行划分集群
	Cluster *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 该地域为主地域负责熔断的subset
	Subsets []*CircuitBreakerDestination_ClusterConfig_Subset `protobuf:"bytes,2,rep,name=subsets,proto3" json:"subsets,omitempty"`
}

func (x *CircuitBreakerDestination_ClusterConfig) Reset() {
	*x = CircuitBreakerDestination_ClusterConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerDestination_ClusterConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerDestination_ClusterConfig) ProtoMessage() {}

func (x *CircuitBreakerDestination_ClusterConfig) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerDestination_ClusterConfig.ProtoReflect.Descriptor instead.
func (*CircuitBreakerDestination_ClusterConfig) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_v2_proto_rawDescGZIP(), []int{2, 3}
}

func (x *CircuitBreakerDestination_ClusterConfig) GetCluster() *wrapperspb.StringValue {
	if x != nil {
		return x.Cluster
	}
	return nil
}

func (x *CircuitBreakerDestination_ClusterConfig) GetSubsets() []*CircuitBreakerDestination_ClusterConfig_Subset {
	if x != nil {
		return x.Subsets
	}
	return nil
}

// 标识subset
type CircuitBreakerDestination_ClusterConfig_Subset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标识subset
	Subset map[string]*MatchString `protobuf:"bytes,1,rep,name=subset,proto3" json:"subset,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CircuitBreakerDestination_ClusterConfig_Subset) Reset() {
	*x = CircuitBreakerDestination_ClusterConfig_Subset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerDestination_ClusterConfig_Subset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerDestination_ClusterConfig_Subset) ProtoMessage() {}

func (x *CircuitBreakerDestination_ClusterConfig_Subset) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_v2_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerDestination_ClusterConfig_Subset.ProtoReflect.Descriptor instead.
func (*CircuitBreakerDestination_ClusterConfig_Subset) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_v2_proto_rawDescGZIP(), []int{2, 3, 0}
}

func (x *CircuitBreakerDestination_ClusterConfig_Subset) GetSubset() map[string]*MatchString {
	if x != nil {
		return x.Subset
	}
	return nil
}

var File_v1_model_circuitbreaker_v2_proto protoreflect.FileDescriptor

var file_v1_model_circuitbreaker_v2_proto_rawDesc = []byte{
	0x0a, 0x20, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x69, 0x72, 0x63, 0x75,
	0x69, 0x74, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x76, 0x32, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x14, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa3, 0x07, 0x0a, 0x10, 0x43, 0x69, 0x72, 0x63, 0x75,
	0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x56, 0x32, 0x12, 0x2c, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x02, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x30, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x36, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x49, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x69, 0x6e, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b,
	0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x69, 0x6e, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73,
	0x12, 0x3e, 0x0a, 0x09, 0x6f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65,
	0x72, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x09, 0x6f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73,
	0x12, 0x32, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x34, 0x0a, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x38, 0x0a, 0x08, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x12, 0x3c, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x36, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x05, 0x63, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x32,
	0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x6d, 0x74, 0x69,
	0x6d, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x90, 0x0f, 0x0a,
	0x12, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52,
	0x75, 0x6c, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65,
	0x61, 0x6b, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12,
	0x41, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d,
	0x62, 0x69, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f,
	0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x6f,
	0x6d, 0x62, 0x69, 0x6e, 0x65, 0x12, 0x5d, 0x0a, 0x0f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69,
	0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65,
	0x2e, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x63, 0x6f, 0x64, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x61,
	0x74, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42,
	0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x61, 0x74, 0x65, 0x12, 0x56, 0x0a, 0x12, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x64,
	0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x44, 0x65,
	0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x67, 0x6c, 0x6f, 0x62, 0x61,
	0x6c, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x54, 0x0a, 0x11,
	0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72,
	0x65, 0x61, 0x6b, 0x65, 0x72, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x1a, 0x81, 0x02, 0x0a, 0x0d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x36, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x74, 0x6f, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x74, 0x6f,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x3b, 0x0a, 0x0a, 0x74, 0x6f, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x74, 0x6f, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x1a, 0x5c, 0x0a, 0x13, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0xc8, 0x06, 0x0a, 0x0f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x59, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63,
	0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x39, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x6c, 0x6f, 0x77, 0x5f, 0x72,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x53, 0x6c, 0x6f, 0x77, 0x52, 0x74, 0x12, 0x49, 0x0a,
	0x11, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x53, 0x0a, 0x17, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x14, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61,
	0x74, 0x65, 0x54, 0x6f, 0x50, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x12, 0x4f, 0x0a,
	0x15, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x62,
	0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x12, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x52, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x4f,
	0x0a, 0x15, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x12, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12,
	0x4b, 0x0a, 0x13, 0x6d, 0x69, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6e, 0x6f, 0x64,
	0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x6d, 0x69, 0x6e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x5a, 0x0a, 0x1a,
	0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x18,
	0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42,
	0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x69, 0x73, 0x44, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x22, 0x5a, 0x0a, 0x09, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x11, 0x0a, 0x0d, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4f, 0x4b, 0x5f, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x4c,
	0x4f, 0x57, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x03, 0x1a,
	0x6e, 0x0a, 0x0e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x46, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72,
	0x52, 0x75, 0x6c, 0x65, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xc8, 0x18, 0x0a, 0x19, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b,
	0x65, 0x72, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x58, 0x0a,
	0x08, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43,
	0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x44, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74,
	0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x08, 0x70,
	0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x12, 0x5c, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43,
	0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x44, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x59, 0x0a, 0x0d, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63,
	0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0c, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x36, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x5e, 0x0a, 0x0d, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x5f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43,
	0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x44, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x1a, 0xa3, 0x09, 0x0a, 0x14, 0x43, 0x69, 0x72,
	0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x12, 0x61, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65,
	0x72, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x69, 0x72,
	0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x60, 0x0a, 0x06, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b,
	0x65, 0x72, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x69,
	0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06,
	0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x12, 0x58, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x42, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61,
	0x6b, 0x65, 0x72, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43,
	0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x2e, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x12, 0x70, 0x0a, 0x0c, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65,
	0x61, 0x6b, 0x65, 0x72, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x12, 0x41, 0x0a, 0x0e, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x6d,
	0x62, 0x69, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f,
	0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x43, 0x6f,
	0x6d, 0x62, 0x69, 0x6e, 0x65, 0x12, 0x76, 0x0a, 0x0e, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72,
	0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72,
	0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x44, 0x65, 0x73, 0x74, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72,
	0x65, 0x61, 0x6b, 0x65, 0x72, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x42, 0x72, 0x65, 0x61,
	0x6b, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d,
	0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x26, 0x0a,
	0x0f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x10, 0x6d, 0x61, 0x78, 0x5f, 0x62, 0x72, 0x65,
	0x61, 0x6b, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x6d,
	0x61, 0x78, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65, 0x1a, 0x54, 0x0a,
	0x0b, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x59, 0x0a, 0x10, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x5b,
	0x0a, 0x12, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x52, 0x0a, 0x08, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x55, 0x42, 0x53, 0x45,
	0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x53, 0x10,
	0x00, 0x12, 0x11, 0x0a, 0x0d, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x45,
	0x4c, 0x46, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f,
	0x41, 0x4c, 0x4c, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x53, 0x10, 0x02, 0x22,
	0x6d, 0x0a, 0x05, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x45, 0x52, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x53, 0x10, 0x00,
	0x12, 0x1b, 0x0a, 0x17, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x43, 0x5f, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x53, 0x10, 0x01, 0x12, 0x14, 0x0a,
	0x10, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x4c, 0x41, 0x42, 0x45, 0x4c,
	0x53, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x43, 0x5f, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x53, 0x10, 0x03, 0x1a, 0xb2,
	0x06, 0x0a, 0x0d, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x3c, 0x0a, 0x0c, 0x73, 0x6c, 0x65, 0x65, 0x70, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0b, 0x73, 0x6c, 0x65, 0x65, 0x70, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x56,
	0x0a, 0x19, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x61, 0x66, 0x74, 0x65,
	0x72, 0x5f, 0x68, 0x61, 0x6c, 0x66, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x15, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x74, 0x72, 0x79, 0x41, 0x66, 0x74, 0x65, 0x72, 0x48, 0x61,
	0x6c, 0x66, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x5c, 0x0a, 0x1c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x68, 0x61, 0x6c,
	0x66, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x18, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x41, 0x66, 0x74, 0x65, 0x72, 0x48, 0x61, 0x6c, 0x66,
	0x4f, 0x70, 0x65, 0x6e, 0x12, 0x4f, 0x0a, 0x15, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f,
	0x72, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x12, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x61, 0x74, 0x65, 0x54, 0x6f,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x1b, 0x68, 0x61, 0x6c, 0x66, 0x5f, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x18, 0x68, 0x61, 0x6c, 0x66, 0x4f, 0x70,
	0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x12, 0x46, 0x0a, 0x10, 0x65, 0x78, 0x70, 0x61, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46,
	0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x65, 0x78, 0x70, 0x61, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x32, 0x0a, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f,
	0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x43,
	0x0a, 0x10, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x6c, 0x65, 0x65, 0x70, 0x5f, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x53, 0x6c, 0x65, 0x65, 0x70, 0x57, 0x69, 0x6e,
	0x64, 0x6f, 0x77, 0x12, 0x5e, 0x0a, 0x1d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x68, 0x61, 0x6c, 0x66, 0x5f,
	0x6f, 0x70, 0x65, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x19, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x66, 0x74, 0x65, 0x72, 0x48, 0x61, 0x6c, 0x66, 0x4f,
	0x70, 0x65, 0x6e, 0x12, 0x5e, 0x0a, 0x1d, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x68, 0x61, 0x6c, 0x66, 0x5f,
	0x6f, 0x70, 0x65, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x19, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x66, 0x74, 0x65, 0x72, 0x48, 0x61, 0x6c, 0x66, 0x4f,
	0x70, 0x65, 0x6e, 0x1a, 0xc7, 0x01, 0x0a, 0x0c, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x31, 0x0a, 0x06, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x06, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x42, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x40, 0x0a, 0x0e, 0x6a,
	0x75, 0x64, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d,
	0x6a, 0x75, 0x64, 0x67, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x1a, 0xe2, 0x02,
	0x0a, 0x0d, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x36, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x56, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x73, 0x65,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42,
	0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x52, 0x07, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x73, 0x1a,
	0xc0, 0x01, 0x0a, 0x06, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x12, 0x60, 0x0a, 0x06, 0x73, 0x75,
	0x62, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69,
	0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x1a, 0x54, 0x0a, 0x0b,
	0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x76, 0x0a, 0x11, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4b, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42,
	0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69,
	0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x3b, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v1_model_circuitbreaker_v2_proto_rawDescOnce sync.Once
	file_v1_model_circuitbreaker_v2_proto_rawDescData = file_v1_model_circuitbreaker_v2_proto_rawDesc
)

func file_v1_model_circuitbreaker_v2_proto_rawDescGZIP() []byte {
	file_v1_model_circuitbreaker_v2_proto_rawDescOnce.Do(func() {
		file_v1_model_circuitbreaker_v2_proto_rawDescData = protoimpl.X.CompressGZIP(file_v1_model_circuitbreaker_v2_proto_rawDescData)
	})
	return file_v1_model_circuitbreaker_v2_proto_rawDescData
}

var file_v1_model_circuitbreaker_v2_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_v1_model_circuitbreaker_v2_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_v1_model_circuitbreaker_v2_proto_goTypes = []interface{}{
	(CircuitBreakerRule_ErrorRateConfig_ErrorType)(0),            // 0: api.v1.model.CircuitBreakerRule.ErrorRateConfig.ErrorType
	(CircuitBreakerDestination_CircuitBreakerPolicy_Resource)(0), // 1: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.Resource
	(CircuitBreakerDestination_CircuitBreakerPolicy_Scope)(0),    // 2: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.Scope
	(*CircuitBreakerV2)(nil),                                     // 3: api.v1.model.CircuitBreakerV2
	(*CircuitBreakerRule)(nil),                                   // 4: api.v1.model.CircuitBreakerRule
	(*CircuitBreakerDestination)(nil),                            // 5: api.v1.model.CircuitBreakerDestination
	(*CircuitBreakerRule_SourceMatcher)(nil),                     // 6: api.v1.model.CircuitBreakerRule.SourceMatcher
	nil,                                                          // 7: api.v1.model.CircuitBreakerRule.CodeCategoriesEntry
	(*CircuitBreakerRule_ErrorRateConfig)(nil),                   // 8: api.v1.model.CircuitBreakerRule.ErrorRateConfig
	nil, // 9: api.v1.model.CircuitBreakerRule.ErrorRateEntry
	(*CircuitBreakerDestination_CircuitBreakerPolicy)(nil), // 10: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy
	(*CircuitBreakerDestination_RecoverConfig)(nil),        // 11: api.v1.model.CircuitBreakerDestination.RecoverConfig
	(*CircuitBreakerDestination_MetricConfig)(nil),         // 12: api.v1.model.CircuitBreakerDestination.MetricConfig
	(*CircuitBreakerDestination_ClusterConfig)(nil),        // 13: api.v1.model.CircuitBreakerDestination.ClusterConfig
	nil, // 14: api.v1.model.CircuitBreakerDestination.MultiClusterEntry
	nil, // 15: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.SubsetEntry
	nil, // 16: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.MatchLabelsEntry
	nil, // 17: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.BreakerLabelsEntry
	(*CircuitBreakerDestination_ClusterConfig_Subset)(nil), // 18: api.v1.model.CircuitBreakerDestination.ClusterConfig.Subset
	nil,                            // 19: api.v1.model.CircuitBreakerDestination.ClusterConfig.Subset.SubsetEntry
	(*wrapperspb.StringValue)(nil), // 20: google.protobuf.StringValue
	(*wrapperspb.BoolValue)(nil),   // 21: google.protobuf.BoolValue
	(*MatchString)(nil),            // 22: api.v1.model.MatchString
	(*durationpb.Duration)(nil),    // 23: google.protobuf.Duration
	(*wrapperspb.UInt32Value)(nil), // 24: google.protobuf.UInt32Value
	(*wrapperspb.FloatValue)(nil),  // 25: google.protobuf.FloatValue
}
var file_v1_model_circuitbreaker_v2_proto_depIdxs = []int32{
	20, // 0: api.v1.model.CircuitBreakerV2.id:type_name -> google.protobuf.StringValue
	20, // 1: api.v1.model.CircuitBreakerV2.version:type_name -> google.protobuf.StringValue
	20, // 2: api.v1.model.CircuitBreakerV2.name:type_name -> google.protobuf.StringValue
	20, // 3: api.v1.model.CircuitBreakerV2.namespace:type_name -> google.protobuf.StringValue
	20, // 4: api.v1.model.CircuitBreakerV2.service:type_name -> google.protobuf.StringValue
	20, // 5: api.v1.model.CircuitBreakerV2.service_namespace:type_name -> google.protobuf.StringValue
	4,  // 6: api.v1.model.CircuitBreakerV2.inbounds:type_name -> api.v1.model.CircuitBreakerRule
	4,  // 7: api.v1.model.CircuitBreakerV2.outbounds:type_name -> api.v1.model.CircuitBreakerRule
	20, // 8: api.v1.model.CircuitBreakerV2.token:type_name -> google.protobuf.StringValue
	20, // 9: api.v1.model.CircuitBreakerV2.owners:type_name -> google.protobuf.StringValue
	20, // 10: api.v1.model.CircuitBreakerV2.business:type_name -> google.protobuf.StringValue
	20, // 11: api.v1.model.CircuitBreakerV2.department:type_name -> google.protobuf.StringValue
	20, // 12: api.v1.model.CircuitBreakerV2.comment:type_name -> google.protobuf.StringValue
	20, // 13: api.v1.model.CircuitBreakerV2.ctime:type_name -> google.protobuf.StringValue
	20, // 14: api.v1.model.CircuitBreakerV2.mtime:type_name -> google.protobuf.StringValue
	20, // 15: api.v1.model.CircuitBreakerV2.revision:type_name -> google.protobuf.StringValue
	20, // 16: api.v1.model.CircuitBreakerRule.name:type_name -> google.protobuf.StringValue
	6,  // 17: api.v1.model.CircuitBreakerRule.sources:type_name -> api.v1.model.CircuitBreakerRule.SourceMatcher
	20, // 18: api.v1.model.CircuitBreakerRule.service_group:type_name -> google.protobuf.StringValue
	21, // 19: api.v1.model.CircuitBreakerRule.caller_combine:type_name -> google.protobuf.BoolValue
	7,  // 20: api.v1.model.CircuitBreakerRule.code_categories:type_name -> api.v1.model.CircuitBreakerRule.CodeCategoriesEntry
	9,  // 21: api.v1.model.CircuitBreakerRule.error_rate:type_name -> api.v1.model.CircuitBreakerRule.ErrorRateEntry
	5,  // 22: api.v1.model.CircuitBreakerRule.global_destination:type_name -> api.v1.model.CircuitBreakerDestination
	5,  // 23: api.v1.model.CircuitBreakerRule.local_destination:type_name -> api.v1.model.CircuitBreakerDestination
	10, // 24: api.v1.model.CircuitBreakerDestination.policies:type_name -> api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy
	11, // 25: api.v1.model.CircuitBreakerDestination.recover_config:type_name -> api.v1.model.CircuitBreakerDestination.RecoverConfig
	12, // 26: api.v1.model.CircuitBreakerDestination.metric_config:type_name -> api.v1.model.CircuitBreakerDestination.MetricConfig
	20, // 27: api.v1.model.CircuitBreakerDestination.cluster:type_name -> google.protobuf.StringValue
	14, // 28: api.v1.model.CircuitBreakerDestination.multi_cluster:type_name -> api.v1.model.CircuitBreakerDestination.MultiClusterEntry
	20, // 29: api.v1.model.CircuitBreakerRule.SourceMatcher.namespace:type_name -> google.protobuf.StringValue
	20, // 30: api.v1.model.CircuitBreakerRule.SourceMatcher.service:type_name -> google.protobuf.StringValue
	20, // 31: api.v1.model.CircuitBreakerRule.SourceMatcher.to_namespace:type_name -> google.protobuf.StringValue
	20, // 32: api.v1.model.CircuitBreakerRule.SourceMatcher.to_service:type_name -> google.protobuf.StringValue
	22, // 33: api.v1.model.CircuitBreakerRule.CodeCategoriesEntry.value:type_name -> api.v1.model.MatchString
	0,  // 34: api.v1.model.CircuitBreakerRule.ErrorRateConfig.error_type:type_name -> api.v1.model.CircuitBreakerRule.ErrorRateConfig.ErrorType
	23, // 35: api.v1.model.CircuitBreakerRule.ErrorRateConfig.max_slow_rt:type_name -> google.protobuf.Duration
	24, // 36: api.v1.model.CircuitBreakerRule.ErrorRateConfig.request_threshold:type_name -> google.protobuf.UInt32Value
	24, // 37: api.v1.model.CircuitBreakerRule.ErrorRateConfig.error_rate_to_preserved:type_name -> google.protobuf.UInt32Value
	24, // 38: api.v1.model.CircuitBreakerRule.ErrorRateConfig.error_rate_to_breaker:type_name -> google.protobuf.UInt32Value
	24, // 39: api.v1.model.CircuitBreakerRule.ErrorRateConfig.error_rate_to_recover:type_name -> google.protobuf.UInt32Value
	24, // 40: api.v1.model.CircuitBreakerRule.ErrorRateConfig.min_error_node_rate:type_name -> google.protobuf.UInt32Value
	24, // 41: api.v1.model.CircuitBreakerRule.ErrorRateConfig.continuous_error_threshold:type_name -> google.protobuf.UInt32Value
	21, // 42: api.v1.model.CircuitBreakerRule.ErrorRateConfig.is_disable:type_name -> google.protobuf.BoolValue
	8,  // 43: api.v1.model.CircuitBreakerRule.ErrorRateEntry.value:type_name -> api.v1.model.CircuitBreakerRule.ErrorRateConfig
	1,  // 44: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.resource:type_name -> api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.Resource
	15, // 45: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.subset:type_name -> api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.SubsetEntry
	2,  // 46: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.scope:type_name -> api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.Scope
	16, // 47: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.match_labels:type_name -> api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.MatchLabelsEntry
	21, // 48: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.lables_combine:type_name -> google.protobuf.BoolValue
	17, // 49: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.breaker_labels:type_name -> api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.BreakerLabelsEntry
	24, // 50: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.max_breaker_rate:type_name -> google.protobuf.UInt32Value
	23, // 51: api.v1.model.CircuitBreakerDestination.RecoverConfig.sleep_window:type_name -> google.protobuf.Duration
	24, // 52: api.v1.model.CircuitBreakerDestination.RecoverConfig.max_retry_after_half_open:type_name -> google.protobuf.UInt32Value
	24, // 53: api.v1.model.CircuitBreakerDestination.RecoverConfig.request_rate_after_half_open:type_name -> google.protobuf.UInt32Value
	24, // 54: api.v1.model.CircuitBreakerDestination.RecoverConfig.success_rate_to_close:type_name -> google.protobuf.UInt32Value
	24, // 55: api.v1.model.CircuitBreakerDestination.RecoverConfig.half_open_request_threshold:type_name -> google.protobuf.UInt32Value
	25, // 56: api.v1.model.CircuitBreakerDestination.RecoverConfig.expansion_factor:type_name -> google.protobuf.FloatValue
	21, // 57: api.v1.model.CircuitBreakerDestination.RecoverConfig.enable:type_name -> google.protobuf.BoolValue
	23, // 58: api.v1.model.CircuitBreakerDestination.RecoverConfig.max_sleep_window:type_name -> google.protobuf.Duration
	24, // 59: api.v1.model.CircuitBreakerDestination.RecoverConfig.request_count_after_half_open:type_name -> google.protobuf.UInt32Value
	24, // 60: api.v1.model.CircuitBreakerDestination.RecoverConfig.success_count_after_half_open:type_name -> google.protobuf.UInt32Value
	23, // 61: api.v1.model.CircuitBreakerDestination.MetricConfig.window:type_name -> google.protobuf.Duration
	23, // 62: api.v1.model.CircuitBreakerDestination.MetricConfig.report_interval:type_name -> google.protobuf.Duration
	23, // 63: api.v1.model.CircuitBreakerDestination.MetricConfig.judge_interval:type_name -> google.protobuf.Duration
	20, // 64: api.v1.model.CircuitBreakerDestination.ClusterConfig.cluster:type_name -> google.protobuf.StringValue
	18, // 65: api.v1.model.CircuitBreakerDestination.ClusterConfig.subsets:type_name -> api.v1.model.CircuitBreakerDestination.ClusterConfig.Subset
	13, // 66: api.v1.model.CircuitBreakerDestination.MultiClusterEntry.value:type_name -> api.v1.model.CircuitBreakerDestination.ClusterConfig
	22, // 67: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.SubsetEntry.value:type_name -> api.v1.model.MatchString
	22, // 68: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.MatchLabelsEntry.value:type_name -> api.v1.model.MatchString
	22, // 69: api.v1.model.CircuitBreakerDestination.CircuitBreakerPolicy.BreakerLabelsEntry.value:type_name -> api.v1.model.MatchString
	19, // 70: api.v1.model.CircuitBreakerDestination.ClusterConfig.Subset.subset:type_name -> api.v1.model.CircuitBreakerDestination.ClusterConfig.Subset.SubsetEntry
	22, // 71: api.v1.model.CircuitBreakerDestination.ClusterConfig.Subset.SubsetEntry.value:type_name -> api.v1.model.MatchString
	72, // [72:72] is the sub-list for method output_type
	72, // [72:72] is the sub-list for method input_type
	72, // [72:72] is the sub-list for extension type_name
	72, // [72:72] is the sub-list for extension extendee
	0,  // [0:72] is the sub-list for field type_name
}

func init() { file_v1_model_circuitbreaker_v2_proto_init() }
func file_v1_model_circuitbreaker_v2_proto_init() {
	if File_v1_model_circuitbreaker_v2_proto != nil {
		return
	}
	file_v1_model_model_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_v1_model_circuitbreaker_v2_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_v2_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_v2_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerDestination); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_v2_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerRule_SourceMatcher); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_v2_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerRule_ErrorRateConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_v2_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerDestination_CircuitBreakerPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_v2_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerDestination_RecoverConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_v2_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerDestination_MetricConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_v2_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerDestination_ClusterConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_v2_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerDestination_ClusterConfig_Subset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_model_circuitbreaker_v2_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v1_model_circuitbreaker_v2_proto_goTypes,
		DependencyIndexes: file_v1_model_circuitbreaker_v2_proto_depIdxs,
		EnumInfos:         file_v1_model_circuitbreaker_v2_proto_enumTypes,
		MessageInfos:      file_v1_model_circuitbreaker_v2_proto_msgTypes,
	}.Build()
	File_v1_model_circuitbreaker_v2_proto = out.File
	file_v1_model_circuitbreaker_v2_proto_rawDesc = nil
	file_v1_model_circuitbreaker_v2_proto_goTypes = nil
	file_v1_model_circuitbreaker_v2_proto_depIdxs = nil
}
