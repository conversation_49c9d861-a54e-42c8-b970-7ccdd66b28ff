// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v2/model/request.proto

package model

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// WatchTReqType 监听类型
type WatchReq_WatchTReqType int32

const (
	WatchReq_RENEW              WatchReq_WatchTReqType = 0
	WatchReq_INSTANCE           WatchReq_WatchTReqType = 1
	WatchReq_ROUTING            WatchReq_WatchTReqType = 2
	WatchReq_RATE_LIMIT         WatchReq_WatchTReqType = 3
	WatchReq_INCREMENT_INSTANCE WatchReq_WatchTReqType = 14
	WatchReq_INSTANCE_RAW       WatchReq_WatchTReqType = 15
)

// Enum value maps for WatchReq_WatchTReqType.
var (
	WatchReq_WatchTReqType_name = map[int32]string{
		0:  "RENEW",
		1:  "INSTANCE",
		2:  "ROUTING",
		3:  "RATE_LIMIT",
		14: "INCREMENT_INSTANCE",
		15: "INSTANCE_RAW",
	}
	WatchReq_WatchTReqType_value = map[string]int32{
		"RENEW":              0,
		"INSTANCE":           1,
		"ROUTING":            2,
		"RATE_LIMIT":         3,
		"INCREMENT_INSTANCE": 14,
		"INSTANCE_RAW":       15,
	}
)

func (x WatchReq_WatchTReqType) Enum() *WatchReq_WatchTReqType {
	p := new(WatchReq_WatchTReqType)
	*p = x
	return p
}

func (x WatchReq_WatchTReqType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WatchReq_WatchTReqType) Descriptor() protoreflect.EnumDescriptor {
	return file_v2_model_request_proto_enumTypes[0].Descriptor()
}

func (WatchReq_WatchTReqType) Type() protoreflect.EnumType {
	return &file_v2_model_request_proto_enumTypes[0]
}

func (x WatchReq_WatchTReqType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WatchReq_WatchTReqType.Descriptor instead.
func (WatchReq_WatchTReqType) EnumDescriptor() ([]byte, []int) {
	return file_v2_model_request_proto_rawDescGZIP(), []int{0, 0}
}

// WatchTRspType 监听类型
type WatchRsp_WatchTRspType int32

const (
	WatchRsp_RENEW              WatchRsp_WatchTRspType = 0
	WatchRsp_INSTANCE           WatchRsp_WatchTRspType = 1
	WatchRsp_ROUTING            WatchRsp_WatchTRspType = 2
	WatchRsp_RATE_LIMIT         WatchRsp_WatchTRspType = 3
	WatchRsp_INCREMENT_INSTANCE WatchRsp_WatchTRspType = 14
	WatchRsp_INSTANCE_RAW       WatchRsp_WatchTRspType = 15
)

// Enum value maps for WatchRsp_WatchTRspType.
var (
	WatchRsp_WatchTRspType_name = map[int32]string{
		0:  "RENEW",
		1:  "INSTANCE",
		2:  "ROUTING",
		3:  "RATE_LIMIT",
		14: "INCREMENT_INSTANCE",
		15: "INSTANCE_RAW",
	}
	WatchRsp_WatchTRspType_value = map[string]int32{
		"RENEW":              0,
		"INSTANCE":           1,
		"ROUTING":            2,
		"RATE_LIMIT":         3,
		"INCREMENT_INSTANCE": 14,
		"INSTANCE_RAW":       15,
	}
)

func (x WatchRsp_WatchTRspType) Enum() *WatchRsp_WatchTRspType {
	p := new(WatchRsp_WatchTRspType)
	*p = x
	return p
}

func (x WatchRsp_WatchTRspType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WatchRsp_WatchTRspType) Descriptor() protoreflect.EnumDescriptor {
	return file_v2_model_request_proto_enumTypes[1].Descriptor()
}

func (WatchRsp_WatchTRspType) Type() protoreflect.EnumType {
	return &file_v2_model_request_proto_enumTypes[1]
}

func (x WatchRsp_WatchTRspType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WatchRsp_WatchTRspType.Descriptor instead.
func (WatchRsp_WatchTRspType) EnumDescriptor() ([]byte, []int) {
	return file_v2_model_request_proto_rawDescGZIP(), []int{1, 0}
}

// WatchReq 监听请求
type WatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    WatchReq_WatchTReqType `protobuf:"varint,1,opt,name=type,proto3,enum=api.v2.model.WatchReq_WatchTReqType" json:"type,omitempty"`
	Timeout uint32                 `protobuf:"varint,2,opt,name=timeout,proto3" json:"timeout,omitempty"` // 监听秒数
	Service *Service               `protobuf:"bytes,3,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *WatchReq) Reset() {
	*x = WatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v2_model_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchReq) ProtoMessage() {}

func (x *WatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_v2_model_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchReq.ProtoReflect.Descriptor instead.
func (*WatchReq) Descriptor() ([]byte, []int) {
	return file_v2_model_request_proto_rawDescGZIP(), []int{0}
}

func (x *WatchReq) GetType() WatchReq_WatchTReqType {
	if x != nil {
		return x.Type
	}
	return WatchReq_RENEW
}

func (x *WatchReq) GetTimeout() uint32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *WatchReq) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// WatchRsp 监听返回
type WatchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 返回码
	Code uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// 返回信息
	Info string `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	// 消息类型
	Type WatchRsp_WatchTRspType `protobuf:"varint,3,opt,name=type,proto3,enum=api.v2.model.WatchRsp_WatchTRspType" json:"type,omitempty"`
	// 服务数据
	Service *Service `protobuf:"bytes,4,opt,name=service,proto3" json:"service,omitempty"`
	// 实例数据
	Instances []*Instance `protobuf:"bytes,5,rep,name=instances,proto3" json:"instances,omitempty"`
	// 路由数据
	Routing *Routing `protobuf:"bytes,6,opt,name=routing,proto3" json:"routing,omitempty"`
	// 限流数据
	RateLimit *RateLimit `protobuf:"bytes,7,opt,name=rateLimit,proto3" json:"rateLimit,omitempty"`
}

func (x *WatchRsp) Reset() {
	*x = WatchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v2_model_request_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchRsp) ProtoMessage() {}

func (x *WatchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_v2_model_request_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchRsp.ProtoReflect.Descriptor instead.
func (*WatchRsp) Descriptor() ([]byte, []int) {
	return file_v2_model_request_proto_rawDescGZIP(), []int{1}
}

func (x *WatchRsp) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *WatchRsp) GetInfo() string {
	if x != nil {
		return x.Info
	}
	return ""
}

func (x *WatchRsp) GetType() WatchRsp_WatchTRspType {
	if x != nil {
		return x.Type
	}
	return WatchRsp_RENEW
}

func (x *WatchRsp) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *WatchRsp) GetInstances() []*Instance {
	if x != nil {
		return x.Instances
	}
	return nil
}

func (x *WatchRsp) GetRouting() *Routing {
	if x != nil {
		return x.Routing
	}
	return nil
}

func (x *WatchRsp) GetRateLimit() *RateLimit {
	if x != nil {
		return x.RateLimit
	}
	return nil
}

var File_v2_model_request_proto protoreflect.FileDescriptor

var file_v2_model_request_proto_rawDesc = []byte{
	0x0a, 0x16, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x16, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x18, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x72, 0x61, 0x74, 0x65, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x80, 0x02, 0x0a, 0x08, 0x57, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x38, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x2e, 0x57, 0x61, 0x74,
	0x63, 0x68, 0x54, 0x52, 0x65, 0x71, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x2f, 0x0a, 0x07, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x6f, 0x0a, 0x0d, 0x57,
	0x61, 0x74, 0x63, 0x68, 0x54, 0x52, 0x65, 0x71, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05,
	0x52, 0x45, 0x4e, 0x45, 0x57, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x53, 0x54, 0x41,
	0x4e, 0x43, 0x45, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x4f, 0x55, 0x54, 0x49, 0x4e, 0x47,
	0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54,
	0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x4e, 0x43, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x0e, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x4e,
	0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x41, 0x57, 0x10, 0x0f, 0x22, 0xac, 0x03, 0x0a,
	0x08, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6e, 0x66,
	0x6f, 0x12, 0x38, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x57,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x54, 0x52, 0x73,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x07, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x34, 0x0a, 0x09,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x12, 0x2f, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x74,
	0x69, 0x6e, 0x67, 0x12, 0x35, 0x0a, 0x09, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52,
	0x09, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x6f, 0x0a, 0x0d, 0x57, 0x61,
	0x74, 0x63, 0x68, 0x54, 0x52, 0x73, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x52,
	0x45, 0x4e, 0x45, 0x57, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e,
	0x43, 0x45, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x4f, 0x55, 0x54, 0x49, 0x4e, 0x47, 0x10,
	0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10,
	0x03, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x4e, 0x43, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49,
	0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x0e, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x4e, 0x53,
	0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x41, 0x57, 0x10, 0x0f, 0x42, 0x35, 0x5a, 0x33, 0x67,
	0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72,
	0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v2_model_request_proto_rawDescOnce sync.Once
	file_v2_model_request_proto_rawDescData = file_v2_model_request_proto_rawDesc
)

func file_v2_model_request_proto_rawDescGZIP() []byte {
	file_v2_model_request_proto_rawDescOnce.Do(func() {
		file_v2_model_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_v2_model_request_proto_rawDescData)
	})
	return file_v2_model_request_proto_rawDescData
}

var file_v2_model_request_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_v2_model_request_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_v2_model_request_proto_goTypes = []interface{}{
	(WatchReq_WatchTReqType)(0), // 0: api.v2.model.WatchReq.WatchTReqType
	(WatchRsp_WatchTRspType)(0), // 1: api.v2.model.WatchRsp.WatchTRspType
	(*WatchReq)(nil),            // 2: api.v2.model.WatchReq
	(*WatchRsp)(nil),            // 3: api.v2.model.WatchRsp
	(*Service)(nil),             // 4: api.v2.model.Service
	(*Instance)(nil),            // 5: api.v2.model.Instance
	(*Routing)(nil),             // 6: api.v2.model.Routing
	(*RateLimit)(nil),           // 7: api.v2.model.RateLimit
}
var file_v2_model_request_proto_depIdxs = []int32{
	0, // 0: api.v2.model.WatchReq.type:type_name -> api.v2.model.WatchReq.WatchTReqType
	4, // 1: api.v2.model.WatchReq.service:type_name -> api.v2.model.Service
	1, // 2: api.v2.model.WatchRsp.type:type_name -> api.v2.model.WatchRsp.WatchTRspType
	4, // 3: api.v2.model.WatchRsp.service:type_name -> api.v2.model.Service
	5, // 4: api.v2.model.WatchRsp.instances:type_name -> api.v2.model.Instance
	6, // 5: api.v2.model.WatchRsp.routing:type_name -> api.v2.model.Routing
	7, // 6: api.v2.model.WatchRsp.rateLimit:type_name -> api.v2.model.RateLimit
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_v2_model_request_proto_init() }
func file_v2_model_request_proto_init() {
	if File_v2_model_request_proto != nil {
		return
	}
	file_v2_model_service_proto_init()
	file_v2_model_instance_proto_init()
	file_v2_model_routing_proto_init()
	file_v2_model_ratelimit_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_v2_model_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v2_model_request_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v2_model_request_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v2_model_request_proto_goTypes,
		DependencyIndexes: file_v2_model_request_proto_depIdxs,
		EnumInfos:         file_v2_model_request_proto_enumTypes,
		MessageInfos:      file_v2_model_request_proto_msgTypes,
	}.Build()
	File_v2_model_request_proto = out.File
	file_v2_model_request_proto_rawDesc = nil
	file_v2_model_request_proto_goTypes = nil
	file_v2_model_request_proto_depIdxs = nil
}
