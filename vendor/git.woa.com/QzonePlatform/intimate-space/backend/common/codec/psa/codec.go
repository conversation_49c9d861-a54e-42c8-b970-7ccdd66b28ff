// Package psa ...
package psa

import (
	"encoding/binary"
	"errors"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/transport"
	"io"
)

// init
func init() {
	codec.Register("psa", nil, DefaultClientCodec)
	transport.RegisterFramerBuilder("psa", DefaultFrameBuilder)
}

var (
	DefaultClientCodec  = &ClientCodec{}
	DefaultFrameBuilder = &FrameBuilder{}
)

// FrameBuilder 数据帧构造器
type FrameBuilder struct {
}

// New 生成一个数据帧
func (fb *FrameBuilder) New(reader io.Reader) transport.Framer {
	return &framer{
		reader: reader,
		msg:    make([]byte, 0, 4096),
	}
}

// framer 非变长qza头固定长度106字节
type framer struct {
	reader io.Reader
	head   [headLen]byte
	msg    []byte
}

// ReadFrame 解析完整的一帧qza请求
func (f *framer) ReadFrame() ([]byte, error) {
	num, err := io.ReadFull(f.reader, f.head[:])
	if err != nil {
		return nil, err
	}
	if num != headLen {
		return nil, errors.New("read frame msg head num invalid")
	}

	if magicNum := binary.BigEndian.Uint32(f.head[0:4]); magicNum != stx {
		return nil, errors.New("read msg header stx invalid")
	}

	packLen := binary.BigEndian.Uint32(f.head[8:12])
	if packLen < headLen {
		return nil, errors.New("read msg total packet len invalid")
	}

	if len(f.msg) < int(packLen) {
		f.msg = make([]byte, packLen)
	}
	copy(f.msg, f.head[:])

	num, err = io.ReadFull(f.reader, f.msg[headLen:packLen])
	if err != nil {
		return nil, err
	}
	if num+headLen != int(packLen) {
		return nil, errors.New("read frame msg body num invalid")
	}

	return f.msg[:packLen], nil
}

// ClientCodec 客户端编解码
type ClientCodec struct {
}

// Encode 客户端打包reqbody结构体到二进制数据 发到服务端
func (c *ClientCodec) Encode(msg codec.Msg, reqBody []byte) ([]byte, error) {
	var head *Head
	if msg.ClientReqHead() != nil {
		if reqHead, ok := msg.ClientReqHead().(*Head); ok {
			head = reqHead
		}
	} else {
		head = NewHead()
		msg.WithClientReqHead(head)
	}

	return head.Encode(reqBody)
}

// Decode 客户端收到服务端二进制回包数据解包到rspbody结构体
func (c *ClientCodec) Decode(msg codec.Msg, rspBuf []byte) ([]byte, error) {
	if len(rspBuf) < headLen {
		return nil, errors.New("client decode rsp len invalid")
	}

	var head *Head
	if msg.ClientRspHead() != nil {
		if rspHead, ok := msg.ClientRspHead().(*Head); ok {
			head = rspHead
		}
	} else {
		head = NewHead()
		msg.WithClientRspHead(head)
	}

	return head.Decode(rspBuf)
}
