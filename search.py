import argparse
import time
import json
from urllib.parse import urlparse,parse_qs
import urllib.parse
import urllib.request
import base64
import struct

from scripts.util import log
from http.server import BaseHTTPRequestHandler, HTTPServer
from urllib.parse import urlparse, parse_qs
from pipeline_online import search, prepare_embedder

def my_search(text, limit, uid):
    start = time.time()
    results = search(text, limit=limit, uid=uid)
    end = time.time()
    for result in results:
        photo = result[2]
        # 删除无用的字段
        photo.pop('_id', None)
        photo.pop('text_embedding', None)
        photo.pop('image_embedding', None)
    my_results = {}
    my_results['results'] = results
    my_results['cost'] = f"search cost {end - start} seconds"
    log(f"search cost {end - start} seconds")
    return my_results

class MyHTTPRequestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        # 路由
        url = urlparse(self.path)
        path = url.path

        if path == '/search':
            self.handle_search(url)
        elif path == '/cluster_list':
            self.handle_cluster_list(url)
        else:
            self.response_json(400, -360001, 'Invalid path', None)
            return
    
    def handle_search(self,url):
        query = url.query
        params = parse_qs(query)
        text = params.get('text', [''])[0]
        limit = params.get('limit', [''])[0]
        uid = params.get('uid', [''])[0]
        html = params.get('html', [''])[0]
        cluster_uuid_list = params.get('cluster_uuid_list',[''])[0]

        if url.path != '/search':
            self.response_json(400, -360001, 'Invalid path', None)
            return

        if not limit:
            limit = 10
        else:
            limit = int(limit)

        if not html:
            html = 0
        else:
            html = int(html)

        if not uid:
            self.response_json(400, -360002, 'Missing uid parameter', None)
            return
        
        if cluster_uuid_list:
            cluster_uuid_list = [uuid.strip() for uuid in cluster_uuid_list.split(',') if uuid.strip()] #空的字符串会被排除
        else:
            cluster_uuid_list = None
        
        if not text or text == '':  #todo 返回cluster_info
            if not cluster_uuid_list or len(cluster_uuid_list) == 0:
                self.search_return_ret({'results':[],'cost':'no search'},html)
            else:
                cluster_info,code,message = self.get_cluster_info(uid)
                if code != 0:
                    self.response_json(500,code,message,None)
                    return
                #如果过滤用的聚簇不存在，不加入cluster_info
                if not cluster_info or 'cluster_list' not in cluster_info:
                    self.search_return_ret({'results':[],'cost':'no search'},html) 
                    return 
                # 根据cluster_uuid_list 获取具体的 cluster
                cluster_list = cluster_info['cluster_list']
                filtered_clusters = [c for c in cluster_list if c.get('cluster_uuid') in cluster_uuid_list]

                if not filtered_clusters or len(filtered_clusters) == 0:
                    self.search_return_ret({'results':[],'cost':'no search'},html)
                    return
                
                self.search_return_ret({'results':[],'cost':'no search','cluster_info':filtered_clusters},html)
            
            return

        start = time.time()
        ret = my_search(text, limit, uid)

        #根据聚类的uuids，过滤ret，并且带上cluster_info信息
        if cluster_uuid_list is not None:
            ret = self.filter_results_by_cluster_uuids(uid,ret,cluster_uuid_list)
            if ret is None: # 在filter方法中已经发送了错误响应
                return
        
        self.search_return_ret(ret,html)
    
    def search_return_ret(self,ret,html):
        if html > 0:
            content = f"{ret['cost']}<br>"
            for result in ret['results']:
                score = result[1]
                photo = result[2]
                content += f"{score}"
                content += f"<img referrerpolicy='no-referrer' src='{photo['url']}' width='200px'>"
            # 构造响应内容
            content = content.encode('utf-8')
            self.response_data(200, 'text/html;charset=utf-8', content)
        else:
            # 转成json
            self.response_json(200, 0, 'success', ret)
    
    def filter_results_by_cluster_uuids(self,uid ,ret, cluster_uuid_list):
        # 获取cluster_info
        cluster_info,code,message = self.get_cluster_info(uid)
        if code != 0:
            self.response_json(500,code,message,None)
            return None
        
        #如果过滤用的聚簇不存在，则没有结果能通过过滤
        if not cluster_info or 'cluster_list' not in cluster_info: 
            return []

        # 根据cluster_uuid_list 获取具体的 cluster
        cluster_list = cluster_info['cluster_list']
        filtered_clusters = [c for c in cluster_list if c.get('cluster_uuid') in cluster_uuid_list]

        # 从具体的cluster中，提取其中的file_key, 从file_key中解析出photo_id,形成set，在这个set里面的才能出现在结果中
        filtered_photo_id_set = set()
        for cluster in filtered_clusters:
            face_list = cluster.get('face_list', [])
            for face in face_list:
                file_info = face.get('file_info', {})
                file_key = file_info.get('file_key')
                photo_id,_,error_message = self.parse_qzone_cloud_lloc(file_key)
                if error_message != None:
                    log(f"parse_qzone_cloud_lloc failed: {error_message}")
                    continue
                if photo_id:
                    filtered_photo_id_set.add(photo_id)
                    file_info['photo_id'] = photo_id  #保存file_key转换photo_id的结果

        # 过滤 ret['results']，在只有photo_id在filtered_photo_id_set中才能留下
        filtered_results = []
        for result in ret['results']:
            photo = result[2]
            photo_id = photo.get('photo_id')
            if photo_id in filtered_photo_id_set:
                filtered_results.append(result)

        # 返回过滤后的 ret，已经对ret的聚类结果
        ret['results'] = filtered_results
        ret['cluster_info'] = filtered_clusters
        return ret

    def handle_cluster_list(self,url):
        # 取出请求参数
        query = parse_qs(url.query)
        uid = query.get('uid',[''])[0]
        html = query.get('html',['0'])[0] #默认值为'0'
        with_face_list = query.get('with_face_list',['0'])[0] #默认值为'0'，返回的json，face_list置空


        if not uid:
            self.response_json(400,-360002,'Missing uid parameter',None)
            return
        
        html = int(html)

        cluster_info,code,message = self.get_cluster_info(uid)

        if code != 0:
            self.response_json(500,code,message,None)
        elif cluster_info is not None:
            if with_face_list == '0': #不返回face_list
                # 遍历cluster_info 把face_list置空
                self.clear_face_lists(cluster_info)
            self.response_json(200, 0,'success',cluster_info)
        else:
            self.response_json(500, -360002, 'Failed to retrieve cluster info', None)
                
    def get_cluster_info(self,uid):
        # access_url = "http://socail.aigc.access.prod.polaris:6602/trpc.zy_socail.aigc_access.AigcAccess/GetUserInfo" 
        access_url = "http://socail.aigc.access.devcloud2prod.polaris:6602/trpc.zy_socail.aigc_access.AigcAccess/GetUserInfo"
        request_data = {
            "appid": "qzone",
            "uin": uid,
            "with_cluster_info": True,
            "with_file_url": True
        }

        try:
            # 构造请求
            json_data = json.dumps(request_data).encode('utf-8')

            #构造请求头(模拟curl的行为,curl能行得通)
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'curl/8.4.0',  
                'Accept': '*/*',             
                'Content-Length': str(len(json_data))
            }

            req = urllib.request.Request(
                access_url,
                data=json_data,
                headers=headers,
                method='POST'
            )

            # 发送请求
            with urllib.request.urlopen(req) as response:
                if response.status == 200:
                    try:
                        resp_json = json.loads(response.read().decode('utf-8'))
                        # Check if response contains common error code
                        if 'common' in resp_json and resp_json['common'].get('code', 0) != 0:
                            log(f"Cluster info request failed with code: {resp_json['common']['code']}, "
                                f"message: {resp_json['common']['message']}")
                            return (None,resp_json['common']['code'],resp_json['common']['message'])
                            
                        cluster_info = resp_json.get('cluster_info')
                        if cluster_info:
                            return (cluster_info,0,"")
                        else:
                            log(f"Cluster info not found in response.")
                            return (None, -360002,"Cluster info not found in response.")
                    except json.JSONDecodeError as e:
                        log(f"Failed to parse JSON response: {e}")
                        return (None,-360002,f"Failed to parse JSON response: {str(e)}")
                else:
                    log(f"Access server returned error: {response.status}")
                    return (None, -360002, f"Cluster_info access server returned HTTP error: {response.status}")

        except Exception as e:
            log(f"Error fetching cluster info: {e}")
            return (None, -360002, f"Cluster_info request exception: {str(e)}")
    
    def parse_qzone_cloud_lloc(self,lloc_code: str) -> tuple:
        try:
            # Base64 解码
            # 反转义
            decoded_lloc = lloc_code.translate(str.maketrans({
                '*': '/',
                '!': '=',
                '.': '+'
            }))
            lloc = base64.urlsafe_b64decode(decoded_lloc + '=' * (-len(lloc_code) % 4))
        except Exception as e:
            return "", "", f"Base64 解码失败: {e}"
        # 检查编码类型是否为 '5'
        encode_type_cloud = b'5'
        if len(lloc) < 1:
            return "", "", "LLOC 数据过短，无法读取类型字节"
        if lloc[0:1] != encode_type_cloud:
            return "", "", f"编码类型不匹配，期望 {encode_type_cloud}, 实际为 {lloc[0:1]}"
        # 读取 photoID 长度（uint16，小端序）
        if len(lloc) < 3:
            return "", "", "LLOC 数据过短，无法读取 photoID 长度"
        photo_id_len = struct.unpack_from('<H', lloc, 1)[0]  # '<H' 表示小端序 uint16
        # 检查 photoID 是否完整
        if len(lloc) < 3 + photo_id_len:
            return "", "", f"photoID 数据不完整，期望 {photo_id_len} 字节，实际 {len(lloc) - 3} 字节"
        # 提取 photoID
        photo_id = lloc[3:3 + photo_id_len].decode('utf-8', errors='replace')
        # 读取 bucketID 长度（uint16，小端序）
        bucket_id_len_offset = 3 + photo_id_len
        if len(lloc) < bucket_id_len_offset + 2:
            return "", "", "LLOC 数据过短，无法读取 bucketID 长度"
        bucket_id_len = struct.unpack_from('<H', lloc, bucket_id_len_offset)[0]
        # 检查 bucketID 是否完整
        bucket_id_offset = bucket_id_len_offset + 2
        if len(lloc) < bucket_id_offset + bucket_id_len:
            return "", "", f"bucketID 数据不完整，期望 {bucket_id_len} 字节，实际 {len(lloc) - bucket_id_offset} 字节"
        # 提取 bucketID
        bucket_id = lloc[bucket_id_offset:bucket_id_offset + bucket_id_len].decode('utf-8', errors='replace')
        # 验证 bucketID 长度是否一致
        if len(bucket_id) != bucket_id_len:
            return "", "", f"bucketID 长度不一致，期望 {bucket_id_len}，实际 {len(bucket_id)}"
        return photo_id, bucket_id, None
    
    def clear_face_lists(self,cluster_info):
        if cluster_info and 'cluster_list' in cluster_info:
            for cluster in cluster_info['cluster_list']:
                if 'face_list' in cluster:
                    cluster['face_list'] = []

    def response_json(self, status_code, code, message, data):
        content_data = {}
        content_data['code'] = code
        content_data['message'] = message
        if data:
            content_data['data'] = data
        content = json.dumps(content_data).encode('utf-8')
        self.response_data(status_code, 'application/json', content)

    def response_data(self, status_code, content_type, content):
        self.send_response(status_code)
        self.send_header('Content-type', content_type)
        self.send_header('Content-length', len(content))
        self.end_headers()
        self.wfile.write(content)

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--port', type=int, default=8067, help='listen port')
    args = parser.parse_args()
    log(f"args:{args}")
    log(f"prepare embedder...")
    prepare_embedder()

    log(f"start server...")
    PORT = args.port
    server_address = ('', PORT)
    httpd = HTTPServer(server_address, MyHTTPRequestHandler)
    print(f"Server running on port {PORT}...")
    httpd.serve_forever()