// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: monitor/polaris/v1/circuitbreak.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//熔断状态变更类型
type StatusChange int32

const (
	StatusChange_Unknown          StatusChange = 0
	StatusChange_CloseToOpen      StatusChange = 1
	StatusChange_OpenToHalfOpen   StatusChange = 2
	StatusChange_HalfOpenToOpen   StatusChange = 3
	StatusChange_HalfOpenToClose  StatusChange = 4
	StatusChange_CloseToPreserved StatusChange = 5
	StatusChange_PreservedToClose StatusChange = 6
	StatusChange_PreservedToOpen  StatusChange = 7
)

// Enum value maps for StatusChange.
var (
	StatusChange_name = map[int32]string{
		0: "Unknown",
		1: "CloseToOpen",
		2: "OpenToHalfOpen",
		3: "HalfOpenToOpen",
		4: "HalfOpenToClose",
		5: "CloseToPreserved",
		6: "PreservedToClose",
		7: "PreservedToOpen",
	}
	StatusChange_value = map[string]int32{
		"Unknown":          0,
		"CloseToOpen":      1,
		"OpenToHalfOpen":   2,
		"HalfOpenToOpen":   3,
		"HalfOpenToClose":  4,
		"CloseToPreserved": 5,
		"PreservedToClose": 6,
		"PreservedToOpen":  7,
	}
)

func (x StatusChange) Enum() *StatusChange {
	p := new(StatusChange)
	*p = x
	return p
}

func (x StatusChange) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatusChange) Descriptor() protoreflect.EnumDescriptor {
	return file_monitor_polaris_v1_circuitbreak_proto_enumTypes[0].Descriptor()
}

func (StatusChange) Type() protoreflect.EnumType {
	return &file_monitor_polaris_v1_circuitbreak_proto_enumTypes[0]
}

func (x StatusChange) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StatusChange.Descriptor instead.
func (StatusChange) EnumDescriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_circuitbreak_proto_rawDescGZIP(), []int{0}
}

type RecoverAllStatus int32

const (
	RecoverAllStatus_Invalid RecoverAllStatus = 0
	//发生了全死全活
	RecoverAllStatus_Start RecoverAllStatus = 1
	//全死全活结束（由于服务实例状态的改变）
	RecoverAllStatus_End RecoverAllStatus = 2
)

// Enum value maps for RecoverAllStatus.
var (
	RecoverAllStatus_name = map[int32]string{
		0: "Invalid",
		1: "Start",
		2: "End",
	}
	RecoverAllStatus_value = map[string]int32{
		"Invalid": 0,
		"Start":   1,
		"End":     2,
	}
)

func (x RecoverAllStatus) Enum() *RecoverAllStatus {
	p := new(RecoverAllStatus)
	*p = x
	return p
}

func (x RecoverAllStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecoverAllStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_monitor_polaris_v1_circuitbreak_proto_enumTypes[1].Descriptor()
}

func (RecoverAllStatus) Type() protoreflect.EnumType {
	return &file_monitor_polaris_v1_circuitbreak_proto_enumTypes[1]
}

func (x RecoverAllStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecoverAllStatus.Descriptor instead.
func (RecoverAllStatus) EnumDescriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_circuitbreak_proto_rawDescGZIP(), []int{1}
}

//实例的一次熔断状态改变
type CircuitbreakChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//变更时间
	Time *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=time,proto3" json:"time,omitempty"`
	//变更的次序
	ChangeSeq uint32 `protobuf:"varint,2,opt,name=change_seq,json=changeSeq,proto3" json:"change_seq,omitempty"`
	//状态变更类型
	Change StatusChange `protobuf:"varint,3,opt,name=change,proto3,enum=v1.StatusChange" json:"change,omitempty"`
	//状态变更原因
	Reason string `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *CircuitbreakChange) Reset() {
	*x = CircuitbreakChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_circuitbreak_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitbreakChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitbreakChange) ProtoMessage() {}

func (x *CircuitbreakChange) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_circuitbreak_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitbreakChange.ProtoReflect.Descriptor instead.
func (*CircuitbreakChange) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_circuitbreak_proto_rawDescGZIP(), []int{0}
}

func (x *CircuitbreakChange) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *CircuitbreakChange) GetChangeSeq() uint32 {
	if x != nil {
		return x.ChangeSeq
	}
	return 0
}

func (x *CircuitbreakChange) GetChange() StatusChange {
	if x != nil {
		return x.Change
	}
	return StatusChange_Unknown
}

func (x *CircuitbreakChange) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

//实例的周期熔断变更历史
type CircuitbreakHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip      string                `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	Port    uint32                `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	VpcId   string                `protobuf:"bytes,3,opt,name=vpc_id,json=vpcId,proto3" json:"vpc_id,omitempty"`
	Changes []*CircuitbreakChange `protobuf:"bytes,4,rep,name=changes,proto3" json:"changes,omitempty"`
	Labels  string                `protobuf:"bytes,5,opt,name=labels,proto3" json:"labels,omitempty"`
	RuleId  string                `protobuf:"bytes,6,opt,name=ruleId,proto3" json:"ruleId,omitempty"`
}

func (x *CircuitbreakHistory) Reset() {
	*x = CircuitbreakHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_circuitbreak_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitbreakHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitbreakHistory) ProtoMessage() {}

func (x *CircuitbreakHistory) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_circuitbreak_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitbreakHistory.ProtoReflect.Descriptor instead.
func (*CircuitbreakHistory) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_circuitbreak_proto_rawDescGZIP(), []int{1}
}

func (x *CircuitbreakHistory) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *CircuitbreakHistory) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *CircuitbreakHistory) GetVpcId() string {
	if x != nil {
		return x.VpcId
	}
	return ""
}

func (x *CircuitbreakHistory) GetChanges() []*CircuitbreakChange {
	if x != nil {
		return x.Changes
	}
	return nil
}

func (x *CircuitbreakHistory) GetLabels() string {
	if x != nil {
		return x.Labels
	}
	return ""
}

func (x *CircuitbreakHistory) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

//针对subset的周期变更历史
type SubsetCbHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subset  string                `protobuf:"bytes,1,opt,name=subset,proto3" json:"subset,omitempty"`
	Labels  string                `protobuf:"bytes,2,opt,name=labels,proto3" json:"labels,omitempty"`
	RuleId  string                `protobuf:"bytes,3,opt,name=ruleId,proto3" json:"ruleId,omitempty"`
	Changes []*CircuitbreakChange `protobuf:"bytes,4,rep,name=changes,proto3" json:"changes,omitempty"`
}

func (x *SubsetCbHistory) Reset() {
	*x = SubsetCbHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_circuitbreak_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubsetCbHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubsetCbHistory) ProtoMessage() {}

func (x *SubsetCbHistory) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_circuitbreak_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubsetCbHistory.ProtoReflect.Descriptor instead.
func (*SubsetCbHistory) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_circuitbreak_proto_rawDescGZIP(), []int{2}
}

func (x *SubsetCbHistory) GetSubset() string {
	if x != nil {
		return x.Subset
	}
	return ""
}

func (x *SubsetCbHistory) GetLabels() string {
	if x != nil {
		return x.Labels
	}
	return ""
}

func (x *SubsetCbHistory) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

func (x *SubsetCbHistory) GetChanges() []*CircuitbreakChange {
	if x != nil {
		return x.Changes
	}
	return nil
}

//全死全活状态
type RecoverAllChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//发生全死全活时间
	Time *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=time,proto3" json:"time,omitempty"`
	//发生全死全活的实例集合的信息，如共同metadata、共同位置等
	InstanceInfo string `protobuf:"bytes,2,opt,name=instance_info,json=instanceInfo,proto3" json:"instance_info,omitempty"`
	//全死全活是发生了还是结束了
	Change RecoverAllStatus `protobuf:"varint,3,opt,name=change,proto3,enum=v1.RecoverAllStatus" json:"change,omitempty"`
	//发生全死全活原因是什么
	Reason string `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *RecoverAllChange) Reset() {
	*x = RecoverAllChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_circuitbreak_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecoverAllChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecoverAllChange) ProtoMessage() {}

func (x *RecoverAllChange) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_circuitbreak_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecoverAllChange.ProtoReflect.Descriptor instead.
func (*RecoverAllChange) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_circuitbreak_proto_rawDescGZIP(), []int{3}
}

func (x *RecoverAllChange) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *RecoverAllChange) GetInstanceInfo() string {
	if x != nil {
		return x.InstanceInfo
	}
	return ""
}

func (x *RecoverAllChange) GetChange() RecoverAllStatus {
	if x != nil {
		return x.Change
	}
	return RecoverAllStatus_Invalid
}

func (x *RecoverAllChange) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

//一个服务一个周期的熔断变化情况
type ServiceCircuitbreak struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//该条记录的唯一id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//sdk标识
	SdkToken  *SDKToken `protobuf:"bytes,2,opt,name=sdk_token,json=sdkToken,proto3" json:"sdk_token,omitempty"`
	Namespace string    `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Service   string    `protobuf:"bytes,4,opt,name=service,proto3" json:"service,omitempty"`
	//发生全死全活的情况
	RecoverAll []*RecoverAllChange `protobuf:"bytes,5,rep,name=recover_all,json=recoverAll,proto3" json:"recover_all,omitempty"`
	//实例熔断情况
	InstanceCircuitbreak []*CircuitbreakHistory `protobuf:"bytes,6,rep,name=instance_circuitbreak,json=instanceCircuitbreak,proto3" json:"instance_circuitbreak,omitempty"`
	//subset熔断情况
	SubsetCircuitbreak []*SubsetCbHistory `protobuf:"bytes,7,rep,name=subset_circuitbreak,json=subsetCircuitbreak,proto3" json:"subset_circuitbreak,omitempty"`
	Signature          *MonitorSignature  `protobuf:"bytes,8,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *ServiceCircuitbreak) Reset() {
	*x = ServiceCircuitbreak{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_circuitbreak_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceCircuitbreak) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCircuitbreak) ProtoMessage() {}

func (x *ServiceCircuitbreak) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_circuitbreak_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCircuitbreak.ProtoReflect.Descriptor instead.
func (*ServiceCircuitbreak) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_circuitbreak_proto_rawDescGZIP(), []int{4}
}

func (x *ServiceCircuitbreak) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ServiceCircuitbreak) GetSdkToken() *SDKToken {
	if x != nil {
		return x.SdkToken
	}
	return nil
}

func (x *ServiceCircuitbreak) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ServiceCircuitbreak) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *ServiceCircuitbreak) GetRecoverAll() []*RecoverAllChange {
	if x != nil {
		return x.RecoverAll
	}
	return nil
}

func (x *ServiceCircuitbreak) GetInstanceCircuitbreak() []*CircuitbreakHistory {
	if x != nil {
		return x.InstanceCircuitbreak
	}
	return nil
}

func (x *ServiceCircuitbreak) GetSubsetCircuitbreak() []*SubsetCbHistory {
	if x != nil {
		return x.SubsetCircuitbreak
	}
	return nil
}

func (x *ServiceCircuitbreak) GetSignature() *MonitorSignature {
	if x != nil {
		return x.Signature
	}
	return nil
}

var File_monitor_polaris_v1_circuitbreak_proto protoreflect.FileDescriptor

var file_monitor_polaris_v1_circuitbreak_proto_rawDesc = []byte{
	0x0a, 0x25, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x62, 0x72, 0x65, 0x61,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x76, 0x31, 0x1a, 0x21, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x64, 0x6b, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x22, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xa5, 0x01, 0x0a, 0x12, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x62,
	0x72, 0x65, 0x61, 0x6b, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x65, 0x71, 0x12, 0x28, 0x0a, 0x06, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x06, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xb2, 0x01, 0x0a, 0x13,
	0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x76, 0x70, 0x63, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x70, 0x63, 0x49, 0x64, 0x12, 0x30,
	0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x62, 0x72, 0x65, 0x61,
	0x6b, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x75, 0x6c, 0x65,
	0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64,
	0x22, 0x8b, 0x01, 0x0a, 0x0f, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x43, 0x62, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x07,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x22, 0xad,
	0x01, 0x0a, 0x10, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x06, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x87,
	0x03, 0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69,
	0x74, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x73, 0x64, 0x6b, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x44, 0x4b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x08, 0x73, 0x64, 0x6b, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x35, 0x0a, 0x0b, 0x72, 0x65, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x41, 0x6c, 0x6c,
	0x12, 0x4c, 0x0a, 0x15, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x69, 0x72,
	0x63, 0x75, 0x69, 0x74, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x62, 0x72, 0x65, 0x61,
	0x6b, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x14, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x12, 0x44,
	0x0a, 0x13, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x5f, 0x63, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74,
	0x62, 0x72, 0x65, 0x61, 0x6b, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x43, 0x62, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x52, 0x12, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x62,
	0x72, 0x65, 0x61, 0x6b, 0x12, 0x32, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x09, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2a, 0xaa, 0x01, 0x0a, 0x0c, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54,
	0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x4f, 0x70, 0x65, 0x6e, 0x54,
	0x6f, 0x48, 0x61, 0x6c, 0x66, 0x4f, 0x70, 0x65, 0x6e, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x48,
	0x61, 0x6c, 0x66, 0x4f, 0x70, 0x65, 0x6e, 0x54, 0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x10, 0x03, 0x12,
	0x13, 0x0a, 0x0f, 0x48, 0x61, 0x6c, 0x66, 0x4f, 0x70, 0x65, 0x6e, 0x54, 0x6f, 0x43, 0x6c, 0x6f,
	0x73, 0x65, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x6f, 0x50,
	0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x72,
	0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x54, 0x6f, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x10, 0x06,
	0x12, 0x13, 0x0a, 0x0f, 0x50, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x54, 0x6f, 0x4f,
	0x70, 0x65, 0x6e, 0x10, 0x07, 0x2a, 0x33, 0x0a, 0x10, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x41, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x6e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x72, 0x74, 0x10,
	0x01, 0x12, 0x07, 0x0a, 0x03, 0x45, 0x6e, 0x64, 0x10, 0x02, 0x42, 0x3f, 0x5a, 0x3d, 0x67, 0x69,
	0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_monitor_polaris_v1_circuitbreak_proto_rawDescOnce sync.Once
	file_monitor_polaris_v1_circuitbreak_proto_rawDescData = file_monitor_polaris_v1_circuitbreak_proto_rawDesc
)

func file_monitor_polaris_v1_circuitbreak_proto_rawDescGZIP() []byte {
	file_monitor_polaris_v1_circuitbreak_proto_rawDescOnce.Do(func() {
		file_monitor_polaris_v1_circuitbreak_proto_rawDescData = protoimpl.X.CompressGZIP(file_monitor_polaris_v1_circuitbreak_proto_rawDescData)
	})
	return file_monitor_polaris_v1_circuitbreak_proto_rawDescData
}

var file_monitor_polaris_v1_circuitbreak_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_monitor_polaris_v1_circuitbreak_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_monitor_polaris_v1_circuitbreak_proto_goTypes = []interface{}{
	(StatusChange)(0),             // 0: v1.StatusChange
	(RecoverAllStatus)(0),         // 1: v1.RecoverAllStatus
	(*CircuitbreakChange)(nil),    // 2: v1.CircuitbreakChange
	(*CircuitbreakHistory)(nil),   // 3: v1.CircuitbreakHistory
	(*SubsetCbHistory)(nil),       // 4: v1.SubsetCbHistory
	(*RecoverAllChange)(nil),      // 5: v1.RecoverAllChange
	(*ServiceCircuitbreak)(nil),   // 6: v1.ServiceCircuitbreak
	(*timestamppb.Timestamp)(nil), // 7: google.protobuf.Timestamp
	(*SDKToken)(nil),              // 8: v1.SDKToken
	(*MonitorSignature)(nil),      // 9: v1.MonitorSignature
}
var file_monitor_polaris_v1_circuitbreak_proto_depIdxs = []int32{
	7,  // 0: v1.CircuitbreakChange.time:type_name -> google.protobuf.Timestamp
	0,  // 1: v1.CircuitbreakChange.change:type_name -> v1.StatusChange
	2,  // 2: v1.CircuitbreakHistory.changes:type_name -> v1.CircuitbreakChange
	2,  // 3: v1.SubsetCbHistory.changes:type_name -> v1.CircuitbreakChange
	7,  // 4: v1.RecoverAllChange.time:type_name -> google.protobuf.Timestamp
	1,  // 5: v1.RecoverAllChange.change:type_name -> v1.RecoverAllStatus
	8,  // 6: v1.ServiceCircuitbreak.sdk_token:type_name -> v1.SDKToken
	5,  // 7: v1.ServiceCircuitbreak.recover_all:type_name -> v1.RecoverAllChange
	3,  // 8: v1.ServiceCircuitbreak.instance_circuitbreak:type_name -> v1.CircuitbreakHistory
	4,  // 9: v1.ServiceCircuitbreak.subset_circuitbreak:type_name -> v1.SubsetCbHistory
	9,  // 10: v1.ServiceCircuitbreak.signature:type_name -> v1.MonitorSignature
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_monitor_polaris_v1_circuitbreak_proto_init() }
func file_monitor_polaris_v1_circuitbreak_proto_init() {
	if File_monitor_polaris_v1_circuitbreak_proto != nil {
		return
	}
	file_monitor_polaris_v1_sdktoken_proto_init()
	file_monitor_polaris_v1_signature_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_monitor_polaris_v1_circuitbreak_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitbreakChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_circuitbreak_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitbreakHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_circuitbreak_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubsetCbHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_circuitbreak_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecoverAllChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_circuitbreak_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceCircuitbreak); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_monitor_polaris_v1_circuitbreak_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_monitor_polaris_v1_circuitbreak_proto_goTypes,
		DependencyIndexes: file_monitor_polaris_v1_circuitbreak_proto_depIdxs,
		EnumInfos:         file_monitor_polaris_v1_circuitbreak_proto_enumTypes,
		MessageInfos:      file_monitor_polaris_v1_circuitbreak_proto_msgTypes,
	}.Build()
	File_monitor_polaris_v1_circuitbreak_proto = out.File
	file_monitor_polaris_v1_circuitbreak_proto_rawDesc = nil
	file_monitor_polaris_v1_circuitbreak_proto_goTypes = nil
	file_monitor_polaris_v1_circuitbreak_proto_depIdxs = nil
}
