// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: monitor/polaris/v1/serviceratelimit.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LimitMode int32

const (
	//未知类型，用于兼容前面pb
	LimitMode_UnknownMode LimitMode = 0
	//全局类型，与限流server发生交互
	LimitMode_GlobalMode LimitMode = 1
	//本地类型，使用本地限流算法
	LimitMode_LocalMode LimitMode = 2
	//降级类型，因为无法连接限流server，导致必须使用本地限流算法
	LimitMode_DegradeMode LimitMode = 3
)

// Enum value maps for LimitMode.
var (
	LimitMode_name = map[int32]string{
		0: "UnknownMode",
		1: "GlobalMode",
		2: "LocalMode",
		3: "DegradeMode",
	}
	LimitMode_value = map[string]int32{
		"UnknownMode": 0,
		"GlobalMode":  1,
		"LocalMode":   2,
		"DegradeMode": 3,
	}
)

func (x LimitMode) Enum() *LimitMode {
	p := new(LimitMode)
	*p = x
	return p
}

func (x LimitMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LimitMode) Descriptor() protoreflect.EnumDescriptor {
	return file_monitor_polaris_v1_serviceratelimit_proto_enumTypes[0].Descriptor()
}

func (LimitMode) Type() protoreflect.EnumType {
	return &file_monitor_polaris_v1_serviceratelimit_proto_enumTypes[0]
}

func (x LimitMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LimitMode.Descriptor instead.
func (LimitMode) EnumDescriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_serviceratelimit_proto_rawDescGZIP(), []int{0}
}

//被限流的统计
type LimitStat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Time *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=time,proto3" json:"time,omitempty"`
	//限流次数
	PeriodTimes uint32 `protobuf:"varint,2,opt,name=period_times,json=periodTimes,proto3" json:"period_times,omitempty"`
	//限流原因，每种原因一个limitStat
	Reason string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	//限流发生时处于哪种模式
	Mode LimitMode `protobuf:"varint,4,opt,name=mode,proto3,enum=v1.LimitMode" json:"mode,omitempty"`
	//通过请求数
	Pass uint32 `protobuf:"varint,5,opt,name=pass,proto3" json:"pass,omitempty"`
	//限流周期，单位为s
	LimitDuration uint32 `protobuf:"varint,6,opt,name=limit_duration,json=limitDuration,proto3" json:"limit_duration,omitempty"`
}

func (x *LimitStat) Reset() {
	*x = LimitStat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_serviceratelimit_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitStat) ProtoMessage() {}

func (x *LimitStat) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_serviceratelimit_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitStat.ProtoReflect.Descriptor instead.
func (*LimitStat) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_serviceratelimit_proto_rawDescGZIP(), []int{0}
}

func (x *LimitStat) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *LimitStat) GetPeriodTimes() uint32 {
	if x != nil {
		return x.PeriodTimes
	}
	return 0
}

func (x *LimitStat) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *LimitStat) GetMode() LimitMode {
	if x != nil {
		return x.Mode
	}
	return LimitMode_UnknownMode
}

func (x *LimitStat) GetPass() uint32 {
	if x != nil {
		return x.Pass
	}
	return 0
}

func (x *LimitStat) GetLimitDuration() uint32 {
	if x != nil {
		return x.LimitDuration
	}
	return 0
}

//一个周期内，限流请求数量的情况
type LimitRequestsCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Time *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=time,proto3" json:"time,omitempty"`
	//总的请求次数
	TotalRequests uint32 `protobuf:"varint,2,opt,name=total_requests,json=totalRequests,proto3" json:"total_requests,omitempty"`
	//通过请求数
	PassRequests uint32 `protobuf:"varint,3,opt,name=pass_requests,json=passRequests,proto3" json:"pass_requests,omitempty"`
	//被拒请求数
	RejectRequests uint32 `protobuf:"varint,4,opt,name=reject_requests,json=rejectRequests,proto3" json:"reject_requests,omitempty"`
	//请求发生在什么模式下面
	Mode LimitMode `protobuf:"varint,5,opt,name=mode,proto3,enum=v1.LimitMode" json:"mode,omitempty"`
}

func (x *LimitRequestsCount) Reset() {
	*x = LimitRequestsCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_serviceratelimit_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitRequestsCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitRequestsCount) ProtoMessage() {}

func (x *LimitRequestsCount) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_serviceratelimit_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitRequestsCount.ProtoReflect.Descriptor instead.
func (*LimitRequestsCount) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_serviceratelimit_proto_rawDescGZIP(), []int{1}
}

func (x *LimitRequestsCount) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *LimitRequestsCount) GetTotalRequests() uint32 {
	if x != nil {
		return x.TotalRequests
	}
	return 0
}

func (x *LimitRequestsCount) GetPassRequests() uint32 {
	if x != nil {
		return x.PassRequests
	}
	return 0
}

func (x *LimitRequestsCount) GetRejectRequests() uint32 {
	if x != nil {
		return x.RejectRequests
	}
	return 0
}

func (x *LimitRequestsCount) GetMode() LimitMode {
	if x != nil {
		return x.Mode
	}
	return LimitMode_UnknownMode
}

type ThresholdChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Time         *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=time,proto3" json:"time,omitempty"`
	OldThreshold string                 `protobuf:"bytes,2,opt,name=oldThreshold,proto3" json:"oldThreshold,omitempty"`
	NewThreshold string                 `protobuf:"bytes,3,opt,name=newThreshold,proto3" json:"newThreshold,omitempty"`
	Reason       string                 `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *ThresholdChange) Reset() {
	*x = ThresholdChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_serviceratelimit_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThresholdChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThresholdChange) ProtoMessage() {}

func (x *ThresholdChange) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_serviceratelimit_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThresholdChange.ProtoReflect.Descriptor instead.
func (*ThresholdChange) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_serviceratelimit_proto_rawDescGZIP(), []int{2}
}

func (x *ThresholdChange) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *ThresholdChange) GetOldThreshold() string {
	if x != nil {
		return x.OldThreshold
	}
	return ""
}

func (x *ThresholdChange) GetNewThreshold() string {
	if x != nil {
		return x.NewThreshold
	}
	return ""
}

func (x *ThresholdChange) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

//限流记录
type RateLimitRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//该条记录的唯一id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//sdk标识
	SdkToken *SDKToken `protobuf:"bytes,2,opt,name=sdk_token,json=sdkToken,proto3" json:"sdk_token,omitempty"`
	//服务命名空间
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	//服务名
	Service string `protobuf:"bytes,4,opt,name=service,proto3" json:"service,omitempty"`
	//规则id
	RuleId string `protobuf:"bytes,5,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`
	//规则生效集群
	Subset string `protobuf:"bytes,6,opt,name=subset,proto3" json:"subset,omitempty"`
	//限流器
	RateLimiter string `protobuf:"bytes,7,opt,name=rate_limiter,json=rateLimiter,proto3" json:"rate_limiter,omitempty"`
	//限流的接口，使用不同lables代表不同接口
	Labels string `protobuf:"bytes,8,opt,name=labels,proto3" json:"labels,omitempty"`
	//周期限流次数
	LimitStats []*LimitStat `protobuf:"bytes,9,rep,name=limit_stats,json=limitStats,proto3" json:"limit_stats,omitempty"`
	//阈值变化事件
	ThresholdChanges []*ThresholdChange `protobuf:"bytes,10,rep,name=threshold_changes,json=thresholdChanges,proto3" json:"threshold_changes,omitempty"`
	//该限流规则的总请求次数和通过请求次数，在这份PB之后，客户端不要继续上报该字段
	RequestsCount *LimitRequestsCount `protobuf:"bytes,11,opt,name=requests_count,json=requestsCount,proto3" json:"requests_count,omitempty"`
	//不同模式下，该限流规则的总请求次数和通过请求次数
	RequestCounts []*LimitRequestsCount `protobuf:"bytes,12,rep,name=request_counts,json=requestCounts,proto3" json:"request_counts,omitempty"`
	Signature     *MonitorSignature     `protobuf:"bytes,13,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *RateLimitRecord) Reset() {
	*x = RateLimitRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_serviceratelimit_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimitRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimitRecord) ProtoMessage() {}

func (x *RateLimitRecord) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_serviceratelimit_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimitRecord.ProtoReflect.Descriptor instead.
func (*RateLimitRecord) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_serviceratelimit_proto_rawDescGZIP(), []int{3}
}

func (x *RateLimitRecord) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RateLimitRecord) GetSdkToken() *SDKToken {
	if x != nil {
		return x.SdkToken
	}
	return nil
}

func (x *RateLimitRecord) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *RateLimitRecord) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *RateLimitRecord) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

func (x *RateLimitRecord) GetSubset() string {
	if x != nil {
		return x.Subset
	}
	return ""
}

func (x *RateLimitRecord) GetRateLimiter() string {
	if x != nil {
		return x.RateLimiter
	}
	return ""
}

func (x *RateLimitRecord) GetLabels() string {
	if x != nil {
		return x.Labels
	}
	return ""
}

func (x *RateLimitRecord) GetLimitStats() []*LimitStat {
	if x != nil {
		return x.LimitStats
	}
	return nil
}

func (x *RateLimitRecord) GetThresholdChanges() []*ThresholdChange {
	if x != nil {
		return x.ThresholdChanges
	}
	return nil
}

func (x *RateLimitRecord) GetRequestsCount() *LimitRequestsCount {
	if x != nil {
		return x.RequestsCount
	}
	return nil
}

func (x *RateLimitRecord) GetRequestCounts() []*LimitRequestsCount {
	if x != nil {
		return x.RequestCounts
	}
	return nil
}

func (x *RateLimitRecord) GetSignature() *MonitorSignature {
	if x != nil {
		return x.Signature
	}
	return nil
}

var File_monitor_polaris_v1_serviceratelimit_proto protoreflect.FileDescriptor

var file_monitor_polaris_v1_serviceratelimit_proto_rawDesc = []byte{
	0x0a, 0x29, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x76, 0x31, 0x1a,
	0x21, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x64, 0x6b, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c,
	0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd4, 0x01, 0x0a, 0x09, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x21, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d,
	0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x70, 0x61, 0x73, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0d, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xdc,
	0x01, 0x0a, 0x12, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x70, 0x61, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x61, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x73, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x72, 0x65, 0x6a, 0x65,
	0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x04, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x22, 0xa1, 0x01,
	0x0a, 0x0f, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x6c, 0x64, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x6c, 0x64, 0x54, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x6e, 0x65, 0x77, 0x54, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x65, 0x77,
	0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x22, 0x94, 0x04, 0x0a, 0x0f, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x73, 0x64, 0x6b, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x44,
	0x4b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x08, 0x73, 0x64, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x61, 0x74,
	0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x12, 0x40, 0x0a, 0x11, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c,
	0x64, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x10, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x12, 0x32, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x09, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2a, 0x4c, 0x0a, 0x09, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c,
	0x4d, 0x6f, 0x64, 0x65, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x4d,
	0x6f, 0x64, 0x65, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x65, 0x67, 0x72, 0x61, 0x64, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x10, 0x03, 0x42, 0x3f, 0x5a, 0x3d, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c,
	0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_monitor_polaris_v1_serviceratelimit_proto_rawDescOnce sync.Once
	file_monitor_polaris_v1_serviceratelimit_proto_rawDescData = file_monitor_polaris_v1_serviceratelimit_proto_rawDesc
)

func file_monitor_polaris_v1_serviceratelimit_proto_rawDescGZIP() []byte {
	file_monitor_polaris_v1_serviceratelimit_proto_rawDescOnce.Do(func() {
		file_monitor_polaris_v1_serviceratelimit_proto_rawDescData = protoimpl.X.CompressGZIP(file_monitor_polaris_v1_serviceratelimit_proto_rawDescData)
	})
	return file_monitor_polaris_v1_serviceratelimit_proto_rawDescData
}

var file_monitor_polaris_v1_serviceratelimit_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_monitor_polaris_v1_serviceratelimit_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_monitor_polaris_v1_serviceratelimit_proto_goTypes = []interface{}{
	(LimitMode)(0),                // 0: v1.LimitMode
	(*LimitStat)(nil),             // 1: v1.LimitStat
	(*LimitRequestsCount)(nil),    // 2: v1.LimitRequestsCount
	(*ThresholdChange)(nil),       // 3: v1.ThresholdChange
	(*RateLimitRecord)(nil),       // 4: v1.RateLimitRecord
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
	(*SDKToken)(nil),              // 6: v1.SDKToken
	(*MonitorSignature)(nil),      // 7: v1.MonitorSignature
}
var file_monitor_polaris_v1_serviceratelimit_proto_depIdxs = []int32{
	5,  // 0: v1.LimitStat.time:type_name -> google.protobuf.Timestamp
	0,  // 1: v1.LimitStat.mode:type_name -> v1.LimitMode
	5,  // 2: v1.LimitRequestsCount.time:type_name -> google.protobuf.Timestamp
	0,  // 3: v1.LimitRequestsCount.mode:type_name -> v1.LimitMode
	5,  // 4: v1.ThresholdChange.time:type_name -> google.protobuf.Timestamp
	6,  // 5: v1.RateLimitRecord.sdk_token:type_name -> v1.SDKToken
	1,  // 6: v1.RateLimitRecord.limit_stats:type_name -> v1.LimitStat
	3,  // 7: v1.RateLimitRecord.threshold_changes:type_name -> v1.ThresholdChange
	2,  // 8: v1.RateLimitRecord.requests_count:type_name -> v1.LimitRequestsCount
	2,  // 9: v1.RateLimitRecord.request_counts:type_name -> v1.LimitRequestsCount
	7,  // 10: v1.RateLimitRecord.signature:type_name -> v1.MonitorSignature
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_monitor_polaris_v1_serviceratelimit_proto_init() }
func file_monitor_polaris_v1_serviceratelimit_proto_init() {
	if File_monitor_polaris_v1_serviceratelimit_proto != nil {
		return
	}
	file_monitor_polaris_v1_sdktoken_proto_init()
	file_monitor_polaris_v1_signature_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_monitor_polaris_v1_serviceratelimit_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitStat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_serviceratelimit_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitRequestsCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_serviceratelimit_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThresholdChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_serviceratelimit_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimitRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_monitor_polaris_v1_serviceratelimit_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_monitor_polaris_v1_serviceratelimit_proto_goTypes,
		DependencyIndexes: file_monitor_polaris_v1_serviceratelimit_proto_depIdxs,
		EnumInfos:         file_monitor_polaris_v1_serviceratelimit_proto_enumTypes,
		MessageInfos:      file_monitor_polaris_v1_serviceratelimit_proto_msgTypes,
	}.Build()
	File_monitor_polaris_v1_serviceratelimit_proto = out.File
	file_monitor_polaris_v1_serviceratelimit_proto_rawDesc = nil
	file_monitor_polaris_v1_serviceratelimit_proto_goTypes = nil
	file_monitor_polaris_v1_serviceratelimit_proto_depIdxs = nil
}
