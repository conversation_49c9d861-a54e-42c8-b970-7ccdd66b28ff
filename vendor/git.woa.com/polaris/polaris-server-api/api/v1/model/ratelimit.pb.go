// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v1/model/ratelimit.proto

package polarismodel_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 限流资源
type Rule_Resource int32

const (
	// 针对QPS进行限流
	Rule_QPS Rule_Resource = 0
	// 针对并发数进行限流
	Rule_CONCURRENCY Rule_Resource = 1
)

// Enum value maps for Rule_Resource.
var (
	Rule_Resource_name = map[int32]string{
		0: "QPS",
		1: "CONCURRENCY",
	}
	Rule_Resource_value = map[string]int32{
		"QPS":         0,
		"CONCURRENCY": 1,
	}
)

func (x Rule_Resource) Enum() *Rule_Resource {
	p := new(Rule_Resource)
	*p = x
	return p
}

func (x Rule_Resource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Rule_Resource) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_ratelimit_proto_enumTypes[0].Descriptor()
}

func (Rule_Resource) Type() protoreflect.EnumType {
	return &file_v1_model_ratelimit_proto_enumTypes[0]
}

func (x Rule_Resource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Rule_Resource.Descriptor instead.
func (Rule_Resource) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{1, 0}
}

// 限流类型
// global全局限流(默认)或者local单机限流
type Rule_Type int32

const (
	Rule_GLOBAL Rule_Type = 0
	Rule_LOCAL  Rule_Type = 1
)

// Enum value maps for Rule_Type.
var (
	Rule_Type_name = map[int32]string{
		0: "GLOBAL",
		1: "LOCAL",
	}
	Rule_Type_value = map[string]int32{
		"GLOBAL": 0,
		"LOCAL":  1,
	}
)

func (x Rule_Type) Enum() *Rule_Type {
	p := new(Rule_Type)
	*p = x
	return p
}

func (x Rule_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Rule_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_ratelimit_proto_enumTypes[1].Descriptor()
}

func (Rule_Type) Type() protoreflect.EnumType {
	return &file_v1_model_ratelimit_proto_enumTypes[1]
}

func (x Rule_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Rule_Type.Descriptor instead.
func (Rule_Type) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{1, 1}
}

// 限流阈值模式
type Rule_AmountMode int32

const (
	Rule_GLOBAL_TOTAL     Rule_AmountMode = 0 // 总体阈值
	Rule_SHARE_EQUALLY    Rule_AmountMode = 1 // 按单个SDK阈值配置
	Rule_INSTANCE_EQUALLY Rule_AmountMode = 2 // 按单个实例阈值配置
	Rule_INSTANCE_AVERAGE Rule_AmountMode = 3 // 实例均摊模式
)

// Enum value maps for Rule_AmountMode.
var (
	Rule_AmountMode_name = map[int32]string{
		0: "GLOBAL_TOTAL",
		1: "SHARE_EQUALLY",
		2: "INSTANCE_EQUALLY",
		3: "INSTANCE_AVERAGE",
	}
	Rule_AmountMode_value = map[string]int32{
		"GLOBAL_TOTAL":     0,
		"SHARE_EQUALLY":    1,
		"INSTANCE_EQUALLY": 2,
		"INSTANCE_AVERAGE": 3,
	}
)

func (x Rule_AmountMode) Enum() *Rule_AmountMode {
	p := new(Rule_AmountMode)
	*p = x
	return p
}

func (x Rule_AmountMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Rule_AmountMode) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_ratelimit_proto_enumTypes[2].Descriptor()
}

func (Rule_AmountMode) Type() protoreflect.EnumType {
	return &file_v1_model_ratelimit_proto_enumTypes[2]
}

func (x Rule_AmountMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Rule_AmountMode.Descriptor instead.
func (Rule_AmountMode) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{1, 2}
}

// 与限流集群连接失败时降级模式
type Rule_FailoverType int32

const (
	Rule_FAILOVER_LOCAL          Rule_FailoverType = 0 // 降级成本地阈值
	Rule_FAILOVER_PASS           Rule_FailoverType = 1 // 降级成直接通过
	Rule_FAILOVER_CLIENT_AVERAGE Rule_FailoverType = 2 // 按客户端数均摊
)

// Enum value maps for Rule_FailoverType.
var (
	Rule_FailoverType_name = map[int32]string{
		0: "FAILOVER_LOCAL",
		1: "FAILOVER_PASS",
		2: "FAILOVER_CLIENT_AVERAGE",
	}
	Rule_FailoverType_value = map[string]int32{
		"FAILOVER_LOCAL":          0,
		"FAILOVER_PASS":           1,
		"FAILOVER_CLIENT_AVERAGE": 2,
	}
)

func (x Rule_FailoverType) Enum() *Rule_FailoverType {
	p := new(Rule_FailoverType)
	*p = x
	return p
}

func (x Rule_FailoverType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Rule_FailoverType) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_ratelimit_proto_enumTypes[3].Descriptor()
}

func (Rule_FailoverType) Type() protoreflect.EnumType {
	return &file_v1_model_ratelimit_proto_enumTypes[3]
}

func (x Rule_FailoverType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Rule_FailoverType.Descriptor instead.
func (Rule_FailoverType) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{1, 3}
}

// 限流模式
type Rule_LimitMode int32

const (
	//批量抢占模式，客户端进行拉取，Server返回全量剩余配额
	Rule_BATCH_OCCUPY Rule_LimitMode = 0
	//批量分摊模式，客户端进行拉取，Server按比例进行分摊
	Rule_BATCH_SHARE Rule_LimitMode = 1
	//自适应模式，根据历史流量自动调整
	Rule_ADAPTIVE Rule_LimitMode = 2
)

// Enum value maps for Rule_LimitMode.
var (
	Rule_LimitMode_name = map[int32]string{
		0: "BATCH_OCCUPY",
		1: "BATCH_SHARE",
		2: "ADAPTIVE",
	}
	Rule_LimitMode_value = map[string]int32{
		"BATCH_OCCUPY": 0,
		"BATCH_SHARE":  1,
		"ADAPTIVE":     2,
	}
)

func (x Rule_LimitMode) Enum() *Rule_LimitMode {
	p := new(Rule_LimitMode)
	*p = x
	return p
}

func (x Rule_LimitMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Rule_LimitMode) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_ratelimit_proto_enumTypes[4].Descriptor()
}

func (Rule_LimitMode) Type() protoreflect.EnumType {
	return &file_v1_model_ratelimit_proto_enumTypes[4]
}

func (x Rule_LimitMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Rule_LimitMode.Descriptor instead.
func (Rule_LimitMode) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{1, 4}
}

// 同一服务下限流规则集合
type RateLimit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 限流规则集合
	Rules []*Rule `protobuf:"bytes,1,rep,name=rules,proto3" json:"rules,omitempty"`
	// 限流规则汇总的revision信息
	Revision *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=revision,proto3" json:"revision,omitempty"`
}

func (x *RateLimit) Reset() {
	*x = RateLimit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_ratelimit_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimit) ProtoMessage() {}

func (x *RateLimit) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_ratelimit_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimit.ProtoReflect.Descriptor instead.
func (*RateLimit) Descriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{0}
}

func (x *RateLimit) GetRules() []*Rule {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *RateLimit) GetRevision() *wrapperspb.StringValue {
	if x != nil {
		return x.Revision
	}
	return nil
}

// 单个限流规则信息
type Rule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 限流规则唯一标识
	Id *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 限流规则所属服务名
	Service *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
	// 限流规则所属命名空间
	Namespace *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 可选，SUBSET标识
	Subset map[string]*MatchString `protobuf:"bytes,4,rep,name=subset,proto3" json:"subset,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 限流规则优先级，0值最高
	Priority *wrapperspb.UInt32Value `protobuf:"bytes,5,opt,name=priority,proto3" json:"priority,omitempty"`
	Resource Rule_Resource           `protobuf:"varint,6,opt,name=resource,proto3,enum=api.v1.model.Rule_Resource" json:"resource,omitempty"`
	Type     Rule_Type               `protobuf:"varint,7,opt,name=type,proto3,enum=api.v1.model.Rule_Type" json:"type,omitempty"`
	// 业务标签集合，通过KV进行匹配，全部匹配才使用该规则
	Labels map[string]*MatchString `protobuf:"bytes,8,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 限流阈值
	// 可以有多个粒度的配置（比如同时针对秒级，分钟级，天级），匹配一个则进行限流
	// 全局限流模式下，该值为服务配额总量；单机限流模式下，该值为单个节点能处理的配额量
	Amounts []*Amount `protobuf:"bytes,9,rep,name=amounts,proto3" json:"amounts,omitempty"`
	// 限流动作，对应着客户端的插件名字
	Action *wrapperspb.StringValue `protobuf:"bytes,10,opt,name=action,proto3" json:"action,omitempty"`
	// 是否停用该限流规则，默认启用
	Disable *wrapperspb.BoolValue `protobuf:"bytes,11,opt,name=disable,proto3" json:"disable,omitempty"`
	// 限流上报方式，同时支持按固定周期上报，以及达到配额百分比后上报
	Report *Report `protobuf:"bytes,12,opt,name=report,proto3" json:"report,omitempty"`
	// 限流规则创建时间
	Ctime *wrapperspb.StringValue `protobuf:"bytes,13,opt,name=ctime,proto3" json:"ctime,omitempty"`
	// 限流规则修改时间
	Mtime *wrapperspb.StringValue `protobuf:"bytes,14,opt,name=mtime,proto3" json:"mtime,omitempty"`
	// 限流规则revision信息
	Revision *wrapperspb.StringValue `protobuf:"bytes,15,opt,name=revision,proto3" json:"revision,omitempty"`
	// 服务的TOKEN信息，仅用于控制台，discover接口不下发
	ServiceToken *wrapperspb.StringValue `protobuf:"bytes,16,opt,name=service_token,proto3" json:"service_token,omitempty"`
	// 配额调整算法
	Adjuster *AmountAdjuster `protobuf:"bytes,17,opt,name=adjuster,proto3" json:"adjuster,omitempty"`
	// 通配符是否合并计算，默认分开计数
	RegexCombine *wrapperspb.BoolValue `protobuf:"bytes,18,opt,name=regex_combine,json=regexCombine,proto3" json:"regex_combine,omitempty"`
	AmountMode   Rule_AmountMode       `protobuf:"varint,19,opt,name=amount_mode,json=amountMode,proto3,enum=api.v1.model.Rule_AmountMode" json:"amount_mode,omitempty"`
	Failover     Rule_FailoverType     `protobuf:"varint,20,opt,name=failover,proto3,enum=api.v1.model.Rule_FailoverType" json:"failover,omitempty"`
	// 分布式限流服务集群
	Cluster *RateLimitCluster `protobuf:"bytes,21,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 多级限流规则，父规则（节点）标识
	ParentId *wrapperspb.StringValue `protobuf:"bytes,22,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 限流规则名称
	Name *wrapperspb.StringValue `protobuf:"bytes,23,opt,name=name,proto3" json:"name,omitempty"`
	// 限流模式
	LimitMode Rule_LimitMode `protobuf:"varint,24,opt,name=limit_mode,json=limitMode,proto3,enum=api.v1.model.Rule_LimitMode" json:"limit_mode,omitempty"`
	// 限流模式切换阈值，目前使用的模式：ADAPTIVE
	SwitchThreshold *wrapperspb.UInt32Value `protobuf:"bytes,25,opt,name=switch_threshold,json=switchThreshold,proto3" json:"switch_threshold,omitempty"`
	// 降级配额比例因子
	FailoverFactor *wrapperspb.FloatValue `protobuf:"bytes,26,opt,name=failover_factor,json=failoverFactor,proto3" json:"failover_factor,omitempty"`
}

func (x *Rule) Reset() {
	*x = Rule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_ratelimit_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rule) ProtoMessage() {}

func (x *Rule) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_ratelimit_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rule.ProtoReflect.Descriptor instead.
func (*Rule) Descriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{1}
}

func (x *Rule) GetId() *wrapperspb.StringValue {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *Rule) GetService() *wrapperspb.StringValue {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *Rule) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *Rule) GetSubset() map[string]*MatchString {
	if x != nil {
		return x.Subset
	}
	return nil
}

func (x *Rule) GetPriority() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Priority
	}
	return nil
}

func (x *Rule) GetResource() Rule_Resource {
	if x != nil {
		return x.Resource
	}
	return Rule_QPS
}

func (x *Rule) GetType() Rule_Type {
	if x != nil {
		return x.Type
	}
	return Rule_GLOBAL
}

func (x *Rule) GetLabels() map[string]*MatchString {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Rule) GetAmounts() []*Amount {
	if x != nil {
		return x.Amounts
	}
	return nil
}

func (x *Rule) GetAction() *wrapperspb.StringValue {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *Rule) GetDisable() *wrapperspb.BoolValue {
	if x != nil {
		return x.Disable
	}
	return nil
}

func (x *Rule) GetReport() *Report {
	if x != nil {
		return x.Report
	}
	return nil
}

func (x *Rule) GetCtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Ctime
	}
	return nil
}

func (x *Rule) GetMtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Mtime
	}
	return nil
}

func (x *Rule) GetRevision() *wrapperspb.StringValue {
	if x != nil {
		return x.Revision
	}
	return nil
}

func (x *Rule) GetServiceToken() *wrapperspb.StringValue {
	if x != nil {
		return x.ServiceToken
	}
	return nil
}

func (x *Rule) GetAdjuster() *AmountAdjuster {
	if x != nil {
		return x.Adjuster
	}
	return nil
}

func (x *Rule) GetRegexCombine() *wrapperspb.BoolValue {
	if x != nil {
		return x.RegexCombine
	}
	return nil
}

func (x *Rule) GetAmountMode() Rule_AmountMode {
	if x != nil {
		return x.AmountMode
	}
	return Rule_GLOBAL_TOTAL
}

func (x *Rule) GetFailover() Rule_FailoverType {
	if x != nil {
		return x.Failover
	}
	return Rule_FAILOVER_LOCAL
}

func (x *Rule) GetCluster() *RateLimitCluster {
	if x != nil {
		return x.Cluster
	}
	return nil
}

func (x *Rule) GetParentId() *wrapperspb.StringValue {
	if x != nil {
		return x.ParentId
	}
	return nil
}

func (x *Rule) GetName() *wrapperspb.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *Rule) GetLimitMode() Rule_LimitMode {
	if x != nil {
		return x.LimitMode
	}
	return Rule_BATCH_OCCUPY
}

func (x *Rule) GetSwitchThreshold() *wrapperspb.UInt32Value {
	if x != nil {
		return x.SwitchThreshold
	}
	return nil
}

func (x *Rule) GetFailoverFactor() *wrapperspb.FloatValue {
	if x != nil {
		return x.FailoverFactor
	}
	return nil
}

// 分布式限流服务集群
type RateLimitCluster struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Service *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// 限流规则所属命名空间
	Namespace *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
}

func (x *RateLimitCluster) Reset() {
	*x = RateLimitCluster{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_ratelimit_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimitCluster) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimitCluster) ProtoMessage() {}

func (x *RateLimitCluster) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_ratelimit_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimitCluster.ProtoReflect.Descriptor instead.
func (*RateLimitCluster) Descriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{2}
}

func (x *RateLimitCluster) GetService() *wrapperspb.StringValue {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *RateLimitCluster) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

// 限流配额
type Amount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 时间周期内的最大配额数
	MaxAmount *wrapperspb.UInt32Value `protobuf:"bytes,1,opt,name=maxAmount,proto3" json:"maxAmount,omitempty"`
	// 配额生效的时间周期，必须大于等于1s
	ValidDuration *durationpb.Duration `protobuf:"bytes,2,opt,name=validDuration,proto3" json:"validDuration,omitempty"`
	// 请求统计精度
	Precision *wrapperspb.UInt32Value `protobuf:"bytes,3,opt,name=precision,proto3" json:"precision,omitempty"`
	// 可选，起始限流阈值，爬坡起始值
	StartAmount *wrapperspb.UInt32Value `protobuf:"bytes,4,opt,name=startAmount,proto3" json:"startAmount,omitempty"`
	// 可选，最小限流阈值，降低时最小值
	MinAmount *wrapperspb.UInt32Value `protobuf:"bytes,5,opt,name=minAmount,proto3" json:"minAmount,omitempty"`
}

func (x *Amount) Reset() {
	*x = Amount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_ratelimit_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Amount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Amount) ProtoMessage() {}

func (x *Amount) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_ratelimit_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Amount.ProtoReflect.Descriptor instead.
func (*Amount) Descriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{3}
}

func (x *Amount) GetMaxAmount() *wrapperspb.UInt32Value {
	if x != nil {
		return x.MaxAmount
	}
	return nil
}

func (x *Amount) GetValidDuration() *durationpb.Duration {
	if x != nil {
		return x.ValidDuration
	}
	return nil
}

func (x *Amount) GetPrecision() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Precision
	}
	return nil
}

func (x *Amount) GetStartAmount() *wrapperspb.UInt32Value {
	if x != nil {
		return x.StartAmount
	}
	return nil
}

func (x *Amount) GetMinAmount() *wrapperspb.UInt32Value {
	if x != nil {
		return x.MinAmount
	}
	return nil
}

// 限流上报方式
type Report struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 配额固定上报周期，单位毫秒
	Interval *durationpb.Duration `protobuf:"bytes,1,opt,name=interval,proto3" json:"interval,omitempty"`
	// 使用了百分之多少配额后启动一次实时上报，值范围(0,100]
	AmountPercent *wrapperspb.UInt32Value `protobuf:"bytes,2,opt,name=amountPercent,proto3" json:"amountPercent,omitempty"`
	// 配置规则是否是否开启批量上报
	EnableBatch *wrapperspb.BoolValue `protobuf:"bytes,3,opt,name=enableBatch,proto3" json:"enableBatch,omitempty"`
}

func (x *Report) Reset() {
	*x = Report{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_ratelimit_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Report) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Report) ProtoMessage() {}

func (x *Report) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_ratelimit_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Report.ProtoReflect.Descriptor instead.
func (*Report) Descriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{4}
}

func (x *Report) GetInterval() *durationpb.Duration {
	if x != nil {
		return x.Interval
	}
	return nil
}

func (x *Report) GetAmountPercent() *wrapperspb.UInt32Value {
	if x != nil {
		return x.AmountPercent
	}
	return nil
}

func (x *Report) GetEnableBatch() *wrapperspb.BoolValue {
	if x != nil {
		return x.EnableBatch
	}
	return nil
}

// 配额调整算法
type AmountAdjuster struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Climb *ClimbConfig `protobuf:"bytes,1,opt,name=climb,proto3" json:"climb,omitempty"`
}

func (x *AmountAdjuster) Reset() {
	*x = AmountAdjuster{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_ratelimit_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AmountAdjuster) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AmountAdjuster) ProtoMessage() {}

func (x *AmountAdjuster) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_ratelimit_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AmountAdjuster.ProtoReflect.Descriptor instead.
func (*AmountAdjuster) Descriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{5}
}

func (x *AmountAdjuster) GetClimb() *ClimbConfig {
	if x != nil {
		return x.Climb
	}
	return nil
}

// 限流调整算法Climb相关配置
type ClimbConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable     *wrapperspb.BoolValue        `protobuf:"bytes,1,opt,name=enable,proto3" json:"enable,omitempty"`         // 是否开启
	Metric     *ClimbConfig_MetricConfig    `protobuf:"bytes,2,opt,name=metric,proto3" json:"metric,omitempty"`         // 限流数据统计配置
	Policy     *ClimbConfig_TriggerPolicy   `protobuf:"bytes,3,opt,name=policy,proto3" json:"policy,omitempty"`         // 触发调整策略
	Throttling *ClimbConfig_ClimbThrottling `protobuf:"bytes,4,opt,name=throttling,proto3" json:"throttling,omitempty"` // 限流调整相关参数
}

func (x *ClimbConfig) Reset() {
	*x = ClimbConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_ratelimit_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClimbConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClimbConfig) ProtoMessage() {}

func (x *ClimbConfig) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_ratelimit_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClimbConfig.ProtoReflect.Descriptor instead.
func (*ClimbConfig) Descriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{6}
}

func (x *ClimbConfig) GetEnable() *wrapperspb.BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

func (x *ClimbConfig) GetMetric() *ClimbConfig_MetricConfig {
	if x != nil {
		return x.Metric
	}
	return nil
}

func (x *ClimbConfig) GetPolicy() *ClimbConfig_TriggerPolicy {
	if x != nil {
		return x.Policy
	}
	return nil
}

func (x *ClimbConfig) GetThrottling() *ClimbConfig_ClimbThrottling {
	if x != nil {
		return x.Throttling
	}
	return nil
}

// 限流数据统计配置
type ClimbConfig_MetricConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 限流数据度量周期，默认60s
	Window *durationpb.Duration `protobuf:"bytes,1,opt,name=window,proto3" json:"window,omitempty"`
	// 数据统计精度，决定数据度量的最小周期，度量滑窗的步长=window/precision
	Precision *wrapperspb.UInt32Value `protobuf:"bytes,2,opt,name=precision,proto3" json:"precision,omitempty"`
	// 上报周期，默认20s
	ReportInterval *durationpb.Duration `protobuf:"bytes,3,opt,name=reportInterval,proto3" json:"reportInterval,omitempty"`
}

func (x *ClimbConfig_MetricConfig) Reset() {
	*x = ClimbConfig_MetricConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_ratelimit_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClimbConfig_MetricConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClimbConfig_MetricConfig) ProtoMessage() {}

func (x *ClimbConfig_MetricConfig) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_ratelimit_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClimbConfig_MetricConfig.ProtoReflect.Descriptor instead.
func (*ClimbConfig_MetricConfig) Descriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ClimbConfig_MetricConfig) GetWindow() *durationpb.Duration {
	if x != nil {
		return x.Window
	}
	return nil
}

func (x *ClimbConfig_MetricConfig) GetPrecision() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Precision
	}
	return nil
}

func (x *ClimbConfig_MetricConfig) GetReportInterval() *durationpb.Duration {
	if x != nil {
		return x.ReportInterval
	}
	return nil
}

// 触发调整的策略
type ClimbConfig_TriggerPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorRate *ClimbConfig_TriggerPolicy_ErrorRate `protobuf:"bytes,1,opt,name=errorRate,proto3" json:"errorRate,omitempty"` // 按错误率阈值调整
	SlowRate  *ClimbConfig_TriggerPolicy_SlowRate  `protobuf:"bytes,2,opt,name=slowRate,proto3" json:"slowRate,omitempty"`   // 慢调用进行触发调整
}

func (x *ClimbConfig_TriggerPolicy) Reset() {
	*x = ClimbConfig_TriggerPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_ratelimit_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClimbConfig_TriggerPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClimbConfig_TriggerPolicy) ProtoMessage() {}

func (x *ClimbConfig_TriggerPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_ratelimit_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClimbConfig_TriggerPolicy.ProtoReflect.Descriptor instead.
func (*ClimbConfig_TriggerPolicy) Descriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{6, 1}
}

func (x *ClimbConfig_TriggerPolicy) GetErrorRate() *ClimbConfig_TriggerPolicy_ErrorRate {
	if x != nil {
		return x.ErrorRate
	}
	return nil
}

func (x *ClimbConfig_TriggerPolicy) GetSlowRate() *ClimbConfig_TriggerPolicy_SlowRate {
	if x != nil {
		return x.SlowRate
	}
	return nil
}

// 爬坡调整相关参数
type ClimbConfig_ClimbThrottling struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ColdBelowTuneDownRate  *wrapperspb.Int32Value `protobuf:"bytes,1,opt,name=coldBelowTuneDownRate,proto3" json:"coldBelowTuneDownRate,omitempty"`   // 冷水位以下区间的下调百分比
	ColdBelowTuneUpRate    *wrapperspb.Int32Value `protobuf:"bytes,2,opt,name=coldBelowTuneUpRate,proto3" json:"coldBelowTuneUpRate,omitempty"`       // 冷水位以下区间的上调百分比
	ColdAboveTuneDownRate  *wrapperspb.Int32Value `protobuf:"bytes,3,opt,name=coldAboveTuneDownRate,proto3" json:"coldAboveTuneDownRate,omitempty"`   // 冷水位以上区间的下调百分比
	ColdAboveTuneUpRate    *wrapperspb.Int32Value `protobuf:"bytes,4,opt,name=coldAboveTuneUpRate,proto3" json:"coldAboveTuneUpRate,omitempty"`       // 冷水位以上区间的上调百分比
	LimitThresholdToTuneUp *wrapperspb.Int32Value `protobuf:"bytes,5,opt,name=limitThresholdToTuneUp,proto3" json:"limitThresholdToTuneUp,omitempty"` // 冷水位以上，超过该百分的请求被限流后进行阈值上调
	JudgeDuration          *durationpb.Duration   `protobuf:"bytes,6,opt,name=judgeDuration,proto3" json:"judgeDuration,omitempty"`                   // 阈值调整规则的决策间隔
	TuneUpPeriod           *wrapperspb.Int32Value `protobuf:"bytes,7,opt,name=tuneUpPeriod,proto3" json:"tuneUpPeriod,omitempty"`                     // 阈值上调周期数，连续N个决策间隔都为上调，才执行上调
	TuneDownPeriod         *wrapperspb.Int32Value `protobuf:"bytes,8,opt,name=tuneDownPeriod,proto3" json:"tuneDownPeriod,omitempty"`                 // 阈值下调周期数，连续N个决策间隔都为下调，才执行下调
}

func (x *ClimbConfig_ClimbThrottling) Reset() {
	*x = ClimbConfig_ClimbThrottling{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_ratelimit_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClimbConfig_ClimbThrottling) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClimbConfig_ClimbThrottling) ProtoMessage() {}

func (x *ClimbConfig_ClimbThrottling) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_ratelimit_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClimbConfig_ClimbThrottling.ProtoReflect.Descriptor instead.
func (*ClimbConfig_ClimbThrottling) Descriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{6, 2}
}

func (x *ClimbConfig_ClimbThrottling) GetColdBelowTuneDownRate() *wrapperspb.Int32Value {
	if x != nil {
		return x.ColdBelowTuneDownRate
	}
	return nil
}

func (x *ClimbConfig_ClimbThrottling) GetColdBelowTuneUpRate() *wrapperspb.Int32Value {
	if x != nil {
		return x.ColdBelowTuneUpRate
	}
	return nil
}

func (x *ClimbConfig_ClimbThrottling) GetColdAboveTuneDownRate() *wrapperspb.Int32Value {
	if x != nil {
		return x.ColdAboveTuneDownRate
	}
	return nil
}

func (x *ClimbConfig_ClimbThrottling) GetColdAboveTuneUpRate() *wrapperspb.Int32Value {
	if x != nil {
		return x.ColdAboveTuneUpRate
	}
	return nil
}

func (x *ClimbConfig_ClimbThrottling) GetLimitThresholdToTuneUp() *wrapperspb.Int32Value {
	if x != nil {
		return x.LimitThresholdToTuneUp
	}
	return nil
}

func (x *ClimbConfig_ClimbThrottling) GetJudgeDuration() *durationpb.Duration {
	if x != nil {
		return x.JudgeDuration
	}
	return nil
}

func (x *ClimbConfig_ClimbThrottling) GetTuneUpPeriod() *wrapperspb.Int32Value {
	if x != nil {
		return x.TuneUpPeriod
	}
	return nil
}

func (x *ClimbConfig_ClimbThrottling) GetTuneDownPeriod() *wrapperspb.Int32Value {
	if x != nil {
		return x.TuneDownPeriod
	}
	return nil
}

// 错误率触发调整配置
type ClimbConfig_TriggerPolicy_ErrorRate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable                 *wrapperspb.BoolValue                                `protobuf:"bytes,1,opt,name=enable,proto3" json:"enable,omitempty"`                                 // 是否开启
	RequestVolumeThreshold *wrapperspb.UInt32Value                              `protobuf:"bytes,2,opt,name=requestVolumeThreshold,proto3" json:"requestVolumeThreshold,omitempty"` // 触发限流调整的最小的请求数
	ErrorRate              *wrapperspb.Int32Value                               `protobuf:"bytes,3,opt,name=errorRate,proto3" json:"errorRate,omitempty"`                           // 触发限流的错误率配置
	Specials               []*ClimbConfig_TriggerPolicy_ErrorRate_SpecialConfig `protobuf:"bytes,4,rep,name=specials,proto3" json:"specials,omitempty"`                             // 针对部分错误码，使用额外的错误率统计，可设置多组特殊规则
}

func (x *ClimbConfig_TriggerPolicy_ErrorRate) Reset() {
	*x = ClimbConfig_TriggerPolicy_ErrorRate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_ratelimit_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClimbConfig_TriggerPolicy_ErrorRate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClimbConfig_TriggerPolicy_ErrorRate) ProtoMessage() {}

func (x *ClimbConfig_TriggerPolicy_ErrorRate) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_ratelimit_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClimbConfig_TriggerPolicy_ErrorRate.ProtoReflect.Descriptor instead.
func (*ClimbConfig_TriggerPolicy_ErrorRate) Descriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{6, 1, 0}
}

func (x *ClimbConfig_TriggerPolicy_ErrorRate) GetEnable() *wrapperspb.BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

func (x *ClimbConfig_TriggerPolicy_ErrorRate) GetRequestVolumeThreshold() *wrapperspb.UInt32Value {
	if x != nil {
		return x.RequestVolumeThreshold
	}
	return nil
}

func (x *ClimbConfig_TriggerPolicy_ErrorRate) GetErrorRate() *wrapperspb.Int32Value {
	if x != nil {
		return x.ErrorRate
	}
	return nil
}

func (x *ClimbConfig_TriggerPolicy_ErrorRate) GetSpecials() []*ClimbConfig_TriggerPolicy_ErrorRate_SpecialConfig {
	if x != nil {
		return x.Specials
	}
	return nil
}

// 慢调用触发调整配置
type ClimbConfig_TriggerPolicy_SlowRate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable   *wrapperspb.BoolValue  `protobuf:"bytes,1,opt,name=enable,proto3" json:"enable,omitempty"`     // 是否开启
	MaxRt    *durationpb.Duration   `protobuf:"bytes,2,opt,name=maxRt,proto3" json:"maxRt,omitempty"`       // 最大响应时间，超过该响应时间属于慢调用
	SlowRate *wrapperspb.Int32Value `protobuf:"bytes,3,opt,name=slowRate,proto3" json:"slowRate,omitempty"` // 慢请求率阈值，达到该阈值进行限流
}

func (x *ClimbConfig_TriggerPolicy_SlowRate) Reset() {
	*x = ClimbConfig_TriggerPolicy_SlowRate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_ratelimit_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClimbConfig_TriggerPolicy_SlowRate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClimbConfig_TriggerPolicy_SlowRate) ProtoMessage() {}

func (x *ClimbConfig_TriggerPolicy_SlowRate) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_ratelimit_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClimbConfig_TriggerPolicy_SlowRate.ProtoReflect.Descriptor instead.
func (*ClimbConfig_TriggerPolicy_SlowRate) Descriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{6, 1, 1}
}

func (x *ClimbConfig_TriggerPolicy_SlowRate) GetEnable() *wrapperspb.BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

func (x *ClimbConfig_TriggerPolicy_SlowRate) GetMaxRt() *durationpb.Duration {
	if x != nil {
		return x.MaxRt
	}
	return nil
}

func (x *ClimbConfig_TriggerPolicy_SlowRate) GetSlowRate() *wrapperspb.Int32Value {
	if x != nil {
		return x.SlowRate
	}
	return nil
}

// 特殊错误码触发调整配置
type ClimbConfig_TriggerPolicy_ErrorRate_SpecialConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       *wrapperspb.StringValue  `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`             // 自定义错误类型
	ErrorCodes []*wrapperspb.Int64Value `protobuf:"bytes,2,rep,name=errorCodes,proto3" json:"errorCodes,omitempty"` // 特定规则针对的错误码
	ErrorRate  *wrapperspb.Int32Value   `protobuf:"bytes,3,opt,name=errorRate,proto3" json:"errorRate,omitempty"`   //特定规则错误率
}

func (x *ClimbConfig_TriggerPolicy_ErrorRate_SpecialConfig) Reset() {
	*x = ClimbConfig_TriggerPolicy_ErrorRate_SpecialConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_ratelimit_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClimbConfig_TriggerPolicy_ErrorRate_SpecialConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClimbConfig_TriggerPolicy_ErrorRate_SpecialConfig) ProtoMessage() {}

func (x *ClimbConfig_TriggerPolicy_ErrorRate_SpecialConfig) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_ratelimit_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClimbConfig_TriggerPolicy_ErrorRate_SpecialConfig.ProtoReflect.Descriptor instead.
func (*ClimbConfig_TriggerPolicy_ErrorRate_SpecialConfig) Descriptor() ([]byte, []int) {
	return file_v1_model_ratelimit_proto_rawDescGZIP(), []int{6, 1, 0, 0}
}

func (x *ClimbConfig_TriggerPolicy_ErrorRate_SpecialConfig) GetType() *wrapperspb.StringValue {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *ClimbConfig_TriggerPolicy_ErrorRate_SpecialConfig) GetErrorCodes() []*wrapperspb.Int64Value {
	if x != nil {
		return x.ErrorCodes
	}
	return nil
}

func (x *ClimbConfig_TriggerPolicy_ErrorRate_SpecialConfig) GetErrorRate() *wrapperspb.Int32Value {
	if x != nil {
		return x.ErrorRate
	}
	return nil
}

var File_v1_model_ratelimit_proto protoreflect.FileDescriptor

var file_v1_model_ratelimit_proto_rawDesc = []byte{
	0x0a, 0x18, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x72, 0x61, 0x74, 0x65, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65,
	0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6f,
	0x0a, 0x09, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x28, 0x0a, 0x05, 0x72,
	0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x05,
	0x72, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0xb5, 0x0f, 0x0a, 0x04, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x02, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3a,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x75,
	0x62, 0x73, 0x65, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x53,
	0x75, 0x62, 0x73, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x73, 0x75, 0x62, 0x73,
	0x65, 0x74, 0x12, 0x38, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x37, 0x0a, 0x08,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x75,
	0x6c, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x07, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x07, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x34, 0x0a, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x34, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x06, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x12, 0x32, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x08,
	0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x72, 0x65,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x38, 0x0a, 0x08, 0x61, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x08, 0x61, 0x64, 0x6a, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x0d, 0x72, 0x65, 0x67, 0x65, 0x78, 0x5f, 0x63, 0x6f,
	0x6d, 0x62, 0x69, 0x6e, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x72, 0x65, 0x67, 0x65, 0x78, 0x43, 0x6f,
	0x6d, 0x62, 0x69, 0x6e, 0x65, 0x12, 0x3e, 0x0a, 0x0b, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x3b, 0x0a, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x6f, 0x76, 0x65,
	0x72, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x46, 0x61, 0x69, 0x6c,
	0x6f, 0x76, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x6f, 0x76,
	0x65, 0x72, 0x12, 0x38, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x09,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0a, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x75, 0x6c,
	0x65, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x47, 0x0a, 0x10, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f,
	0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12,
	0x44, 0x0a, 0x0f, 0x66, 0x61, 0x69, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x66, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x46,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x1a, 0x54, 0x0a, 0x0b, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x54, 0x0a, 0x0b, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x24, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x07, 0x0a,
	0x03, 0x51, 0x50, 0x53, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x4f, 0x4e, 0x43, 0x55, 0x52,
	0x52, 0x45, 0x4e, 0x43, 0x59, 0x10, 0x01, 0x22, 0x1d, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0a, 0x0a, 0x06, 0x47, 0x4c, 0x4f, 0x42, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x4c,
	0x4f, 0x43, 0x41, 0x4c, 0x10, 0x01, 0x22, 0x5d, 0x0a, 0x0a, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x47, 0x4c, 0x4f, 0x42, 0x41, 0x4c, 0x5f, 0x54,
	0x4f, 0x54, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x48, 0x41, 0x52, 0x45, 0x5f,
	0x45, 0x51, 0x55, 0x41, 0x4c, 0x4c, 0x59, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x53,
	0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x4c, 0x59, 0x10, 0x02, 0x12,
	0x14, 0x0a, 0x10, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x41, 0x56, 0x45, 0x52,
	0x41, 0x47, 0x45, 0x10, 0x03, 0x22, 0x52, 0x0a, 0x0c, 0x46, 0x61, 0x69, 0x6c, 0x6f, 0x76, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x41, 0x49, 0x4c, 0x4f, 0x56, 0x45,
	0x52, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x41, 0x49,
	0x4c, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17,
	0x46, 0x41, 0x49, 0x4c, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f,
	0x41, 0x56, 0x45, 0x52, 0x41, 0x47, 0x45, 0x10, 0x02, 0x22, 0x3c, 0x0a, 0x09, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x42, 0x41, 0x54, 0x43, 0x48, 0x5f,
	0x4f, 0x43, 0x43, 0x55, 0x50, 0x59, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x41, 0x54, 0x43,
	0x48, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x45, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x44, 0x41,
	0x50, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x22, 0x86, 0x01, 0x0a, 0x10, 0x52, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x07,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x22, 0xbd, 0x02, 0x0a, 0x06, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x09, 0x6d,
	0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6d, 0x61,
	0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x0d, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x09, 0x70, 0x72, 0x65, 0x63,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x70, 0x72, 0x65, 0x63, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0xc1, 0x01, 0x0a, 0x06, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x12, 0x42, 0x0a, 0x0d, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x0b, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x22, 0x41, 0x0a, 0x0e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x05, 0x63, 0x6c, 0x69, 0x6d, 0x62, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6c, 0x69, 0x6d, 0x62, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x05, 0x63, 0x6c, 0x69, 0x6d, 0x62, 0x22, 0x8c, 0x0f, 0x0a, 0x0b, 0x43, 0x6c, 0x69, 0x6d,
	0x62, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x32, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x3e, 0x0a, 0x06, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6c, 0x69, 0x6d, 0x62,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x3f, 0x0a, 0x06, 0x70,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6c, 0x69, 0x6d, 0x62,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x52, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x49, 0x0a, 0x0a,
	0x74, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x43, 0x6c, 0x69, 0x6d, 0x62, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x6c, 0x69, 0x6d,
	0x62, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x0a, 0x74, 0x68, 0x72,
	0x6f, 0x74, 0x74, 0x6c, 0x69, 0x6e, 0x67, 0x1a, 0xc0, 0x01, 0x0a, 0x0c, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x31, 0x0a, 0x06, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x06, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x3a, 0x0a, 0x09, 0x70,
	0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x70, 0x72,
	0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x1a, 0xc5, 0x06, 0x0a, 0x0d, 0x54,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x4f, 0x0a, 0x09,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43,
	0x6c, 0x69, 0x6d, 0x62, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61,
	0x74, 0x65, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x12, 0x4c, 0x0a,
	0x08, 0x73, 0x6c, 0x6f, 0x77, 0x52, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43,
	0x6c, 0x69, 0x6d, 0x62, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x53, 0x6c, 0x6f, 0x77, 0x52, 0x61, 0x74,
	0x65, 0x52, 0x08, 0x73, 0x6c, 0x6f, 0x77, 0x52, 0x61, 0x74, 0x65, 0x1a, 0xe9, 0x03, 0x0a, 0x09,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x54, 0x0a,
	0x16, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x16, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68,
	0x6f, 0x6c, 0x64, 0x12, 0x39, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x12, 0x5b,
	0x0a, 0x08, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x43, 0x6c, 0x69, 0x6d, 0x62, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52,
	0x61, 0x74, 0x65, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x08, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x73, 0x1a, 0xb9, 0x01, 0x0a, 0x0d,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x30, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x3b, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x09,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x1a, 0xa8, 0x01, 0x0a, 0x08, 0x53, 0x6c, 0x6f, 0x77,
	0x52, 0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x6d, 0x61, 0x78, 0x52,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x05, 0x6d, 0x61, 0x78, 0x52, 0x74, 0x12, 0x37, 0x0a, 0x08, 0x73, 0x6c, 0x6f,
	0x77, 0x52, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x73, 0x6c, 0x6f, 0x77, 0x52, 0x61,
	0x74, 0x65, 0x1a, 0xf1, 0x04, 0x0a, 0x0f, 0x43, 0x6c, 0x69, 0x6d, 0x62, 0x54, 0x68, 0x72, 0x6f,
	0x74, 0x74, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x51, 0x0a, 0x15, 0x63, 0x6f, 0x6c, 0x64, 0x42, 0x65,
	0x6c, 0x6f, 0x77, 0x54, 0x75, 0x6e, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x15, 0x63, 0x6f, 0x6c, 0x64, 0x42, 0x65, 0x6c, 0x6f, 0x77, 0x54, 0x75, 0x6e,
	0x65, 0x44, 0x6f, 0x77, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x12, 0x4d, 0x0a, 0x13, 0x63, 0x6f, 0x6c,
	0x64, 0x42, 0x65, 0x6c, 0x6f, 0x77, 0x54, 0x75, 0x6e, 0x65, 0x55, 0x70, 0x52, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x13, 0x63, 0x6f, 0x6c, 0x64, 0x42, 0x65, 0x6c, 0x6f, 0x77, 0x54, 0x75,
	0x6e, 0x65, 0x55, 0x70, 0x52, 0x61, 0x74, 0x65, 0x12, 0x51, 0x0a, 0x15, 0x63, 0x6f, 0x6c, 0x64,
	0x41, 0x62, 0x6f, 0x76, 0x65, 0x54, 0x75, 0x6e, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x52, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x15, 0x63, 0x6f, 0x6c, 0x64, 0x41, 0x62, 0x6f, 0x76, 0x65, 0x54,
	0x75, 0x6e, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x12, 0x4d, 0x0a, 0x13, 0x63,
	0x6f, 0x6c, 0x64, 0x41, 0x62, 0x6f, 0x76, 0x65, 0x54, 0x75, 0x6e, 0x65, 0x55, 0x70, 0x52, 0x61,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x13, 0x63, 0x6f, 0x6c, 0x64, 0x41, 0x62, 0x6f, 0x76, 0x65,
	0x54, 0x75, 0x6e, 0x65, 0x55, 0x70, 0x52, 0x61, 0x74, 0x65, 0x12, 0x53, 0x0a, 0x16, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x54, 0x6f, 0x54, 0x75,
	0x6e, 0x65, 0x55, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x16, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x54, 0x6f, 0x54, 0x75, 0x6e, 0x65, 0x55, 0x70, 0x12,
	0x3f, 0x0a, 0x0d, 0x6a, 0x75, 0x64, 0x67, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0d, 0x6a, 0x75, 0x64, 0x67, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x3f, 0x0a, 0x0c, 0x74, 0x75, 0x6e, 0x65, 0x55, 0x70, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x0c, 0x74, 0x75, 0x6e, 0x65, 0x55, 0x70, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x12, 0x43, 0x0a, 0x0e, 0x74, 0x75, 0x6e, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x74, 0x75, 0x6e, 0x65, 0x44, 0x6f, 0x77, 0x6e,
	0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x3b, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v1_model_ratelimit_proto_rawDescOnce sync.Once
	file_v1_model_ratelimit_proto_rawDescData = file_v1_model_ratelimit_proto_rawDesc
)

func file_v1_model_ratelimit_proto_rawDescGZIP() []byte {
	file_v1_model_ratelimit_proto_rawDescOnce.Do(func() {
		file_v1_model_ratelimit_proto_rawDescData = protoimpl.X.CompressGZIP(file_v1_model_ratelimit_proto_rawDescData)
	})
	return file_v1_model_ratelimit_proto_rawDescData
}

var file_v1_model_ratelimit_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_v1_model_ratelimit_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_v1_model_ratelimit_proto_goTypes = []interface{}{
	(Rule_Resource)(0),                  // 0: api.v1.model.Rule.Resource
	(Rule_Type)(0),                      // 1: api.v1.model.Rule.Type
	(Rule_AmountMode)(0),                // 2: api.v1.model.Rule.AmountMode
	(Rule_FailoverType)(0),              // 3: api.v1.model.Rule.FailoverType
	(Rule_LimitMode)(0),                 // 4: api.v1.model.Rule.LimitMode
	(*RateLimit)(nil),                   // 5: api.v1.model.RateLimit
	(*Rule)(nil),                        // 6: api.v1.model.Rule
	(*RateLimitCluster)(nil),            // 7: api.v1.model.RateLimitCluster
	(*Amount)(nil),                      // 8: api.v1.model.Amount
	(*Report)(nil),                      // 9: api.v1.model.Report
	(*AmountAdjuster)(nil),              // 10: api.v1.model.AmountAdjuster
	(*ClimbConfig)(nil),                 // 11: api.v1.model.ClimbConfig
	nil,                                 // 12: api.v1.model.Rule.SubsetEntry
	nil,                                 // 13: api.v1.model.Rule.LabelsEntry
	(*ClimbConfig_MetricConfig)(nil),    // 14: api.v1.model.ClimbConfig.MetricConfig
	(*ClimbConfig_TriggerPolicy)(nil),   // 15: api.v1.model.ClimbConfig.TriggerPolicy
	(*ClimbConfig_ClimbThrottling)(nil), // 16: api.v1.model.ClimbConfig.ClimbThrottling
	(*ClimbConfig_TriggerPolicy_ErrorRate)(nil),               // 17: api.v1.model.ClimbConfig.TriggerPolicy.ErrorRate
	(*ClimbConfig_TriggerPolicy_SlowRate)(nil),                // 18: api.v1.model.ClimbConfig.TriggerPolicy.SlowRate
	(*ClimbConfig_TriggerPolicy_ErrorRate_SpecialConfig)(nil), // 19: api.v1.model.ClimbConfig.TriggerPolicy.ErrorRate.SpecialConfig
	(*wrapperspb.StringValue)(nil),                            // 20: google.protobuf.StringValue
	(*wrapperspb.UInt32Value)(nil),                            // 21: google.protobuf.UInt32Value
	(*wrapperspb.BoolValue)(nil),                              // 22: google.protobuf.BoolValue
	(*wrapperspb.FloatValue)(nil),                             // 23: google.protobuf.FloatValue
	(*durationpb.Duration)(nil),                               // 24: google.protobuf.Duration
	(*MatchString)(nil),                                       // 25: api.v1.model.MatchString
	(*wrapperspb.Int32Value)(nil),                             // 26: google.protobuf.Int32Value
	(*wrapperspb.Int64Value)(nil),                             // 27: google.protobuf.Int64Value
}
var file_v1_model_ratelimit_proto_depIdxs = []int32{
	6,  // 0: api.v1.model.RateLimit.rules:type_name -> api.v1.model.Rule
	20, // 1: api.v1.model.RateLimit.revision:type_name -> google.protobuf.StringValue
	20, // 2: api.v1.model.Rule.id:type_name -> google.protobuf.StringValue
	20, // 3: api.v1.model.Rule.service:type_name -> google.protobuf.StringValue
	20, // 4: api.v1.model.Rule.namespace:type_name -> google.protobuf.StringValue
	12, // 5: api.v1.model.Rule.subset:type_name -> api.v1.model.Rule.SubsetEntry
	21, // 6: api.v1.model.Rule.priority:type_name -> google.protobuf.UInt32Value
	0,  // 7: api.v1.model.Rule.resource:type_name -> api.v1.model.Rule.Resource
	1,  // 8: api.v1.model.Rule.type:type_name -> api.v1.model.Rule.Type
	13, // 9: api.v1.model.Rule.labels:type_name -> api.v1.model.Rule.LabelsEntry
	8,  // 10: api.v1.model.Rule.amounts:type_name -> api.v1.model.Amount
	20, // 11: api.v1.model.Rule.action:type_name -> google.protobuf.StringValue
	22, // 12: api.v1.model.Rule.disable:type_name -> google.protobuf.BoolValue
	9,  // 13: api.v1.model.Rule.report:type_name -> api.v1.model.Report
	20, // 14: api.v1.model.Rule.ctime:type_name -> google.protobuf.StringValue
	20, // 15: api.v1.model.Rule.mtime:type_name -> google.protobuf.StringValue
	20, // 16: api.v1.model.Rule.revision:type_name -> google.protobuf.StringValue
	20, // 17: api.v1.model.Rule.service_token:type_name -> google.protobuf.StringValue
	10, // 18: api.v1.model.Rule.adjuster:type_name -> api.v1.model.AmountAdjuster
	22, // 19: api.v1.model.Rule.regex_combine:type_name -> google.protobuf.BoolValue
	2,  // 20: api.v1.model.Rule.amount_mode:type_name -> api.v1.model.Rule.AmountMode
	3,  // 21: api.v1.model.Rule.failover:type_name -> api.v1.model.Rule.FailoverType
	7,  // 22: api.v1.model.Rule.cluster:type_name -> api.v1.model.RateLimitCluster
	20, // 23: api.v1.model.Rule.parent_id:type_name -> google.protobuf.StringValue
	20, // 24: api.v1.model.Rule.name:type_name -> google.protobuf.StringValue
	4,  // 25: api.v1.model.Rule.limit_mode:type_name -> api.v1.model.Rule.LimitMode
	21, // 26: api.v1.model.Rule.switch_threshold:type_name -> google.protobuf.UInt32Value
	23, // 27: api.v1.model.Rule.failover_factor:type_name -> google.protobuf.FloatValue
	20, // 28: api.v1.model.RateLimitCluster.service:type_name -> google.protobuf.StringValue
	20, // 29: api.v1.model.RateLimitCluster.namespace:type_name -> google.protobuf.StringValue
	21, // 30: api.v1.model.Amount.maxAmount:type_name -> google.protobuf.UInt32Value
	24, // 31: api.v1.model.Amount.validDuration:type_name -> google.protobuf.Duration
	21, // 32: api.v1.model.Amount.precision:type_name -> google.protobuf.UInt32Value
	21, // 33: api.v1.model.Amount.startAmount:type_name -> google.protobuf.UInt32Value
	21, // 34: api.v1.model.Amount.minAmount:type_name -> google.protobuf.UInt32Value
	24, // 35: api.v1.model.Report.interval:type_name -> google.protobuf.Duration
	21, // 36: api.v1.model.Report.amountPercent:type_name -> google.protobuf.UInt32Value
	22, // 37: api.v1.model.Report.enableBatch:type_name -> google.protobuf.BoolValue
	11, // 38: api.v1.model.AmountAdjuster.climb:type_name -> api.v1.model.ClimbConfig
	22, // 39: api.v1.model.ClimbConfig.enable:type_name -> google.protobuf.BoolValue
	14, // 40: api.v1.model.ClimbConfig.metric:type_name -> api.v1.model.ClimbConfig.MetricConfig
	15, // 41: api.v1.model.ClimbConfig.policy:type_name -> api.v1.model.ClimbConfig.TriggerPolicy
	16, // 42: api.v1.model.ClimbConfig.throttling:type_name -> api.v1.model.ClimbConfig.ClimbThrottling
	25, // 43: api.v1.model.Rule.SubsetEntry.value:type_name -> api.v1.model.MatchString
	25, // 44: api.v1.model.Rule.LabelsEntry.value:type_name -> api.v1.model.MatchString
	24, // 45: api.v1.model.ClimbConfig.MetricConfig.window:type_name -> google.protobuf.Duration
	21, // 46: api.v1.model.ClimbConfig.MetricConfig.precision:type_name -> google.protobuf.UInt32Value
	24, // 47: api.v1.model.ClimbConfig.MetricConfig.reportInterval:type_name -> google.protobuf.Duration
	17, // 48: api.v1.model.ClimbConfig.TriggerPolicy.errorRate:type_name -> api.v1.model.ClimbConfig.TriggerPolicy.ErrorRate
	18, // 49: api.v1.model.ClimbConfig.TriggerPolicy.slowRate:type_name -> api.v1.model.ClimbConfig.TriggerPolicy.SlowRate
	26, // 50: api.v1.model.ClimbConfig.ClimbThrottling.coldBelowTuneDownRate:type_name -> google.protobuf.Int32Value
	26, // 51: api.v1.model.ClimbConfig.ClimbThrottling.coldBelowTuneUpRate:type_name -> google.protobuf.Int32Value
	26, // 52: api.v1.model.ClimbConfig.ClimbThrottling.coldAboveTuneDownRate:type_name -> google.protobuf.Int32Value
	26, // 53: api.v1.model.ClimbConfig.ClimbThrottling.coldAboveTuneUpRate:type_name -> google.protobuf.Int32Value
	26, // 54: api.v1.model.ClimbConfig.ClimbThrottling.limitThresholdToTuneUp:type_name -> google.protobuf.Int32Value
	24, // 55: api.v1.model.ClimbConfig.ClimbThrottling.judgeDuration:type_name -> google.protobuf.Duration
	26, // 56: api.v1.model.ClimbConfig.ClimbThrottling.tuneUpPeriod:type_name -> google.protobuf.Int32Value
	26, // 57: api.v1.model.ClimbConfig.ClimbThrottling.tuneDownPeriod:type_name -> google.protobuf.Int32Value
	22, // 58: api.v1.model.ClimbConfig.TriggerPolicy.ErrorRate.enable:type_name -> google.protobuf.BoolValue
	21, // 59: api.v1.model.ClimbConfig.TriggerPolicy.ErrorRate.requestVolumeThreshold:type_name -> google.protobuf.UInt32Value
	26, // 60: api.v1.model.ClimbConfig.TriggerPolicy.ErrorRate.errorRate:type_name -> google.protobuf.Int32Value
	19, // 61: api.v1.model.ClimbConfig.TriggerPolicy.ErrorRate.specials:type_name -> api.v1.model.ClimbConfig.TriggerPolicy.ErrorRate.SpecialConfig
	22, // 62: api.v1.model.ClimbConfig.TriggerPolicy.SlowRate.enable:type_name -> google.protobuf.BoolValue
	24, // 63: api.v1.model.ClimbConfig.TriggerPolicy.SlowRate.maxRt:type_name -> google.protobuf.Duration
	26, // 64: api.v1.model.ClimbConfig.TriggerPolicy.SlowRate.slowRate:type_name -> google.protobuf.Int32Value
	20, // 65: api.v1.model.ClimbConfig.TriggerPolicy.ErrorRate.SpecialConfig.type:type_name -> google.protobuf.StringValue
	27, // 66: api.v1.model.ClimbConfig.TriggerPolicy.ErrorRate.SpecialConfig.errorCodes:type_name -> google.protobuf.Int64Value
	26, // 67: api.v1.model.ClimbConfig.TriggerPolicy.ErrorRate.SpecialConfig.errorRate:type_name -> google.protobuf.Int32Value
	68, // [68:68] is the sub-list for method output_type
	68, // [68:68] is the sub-list for method input_type
	68, // [68:68] is the sub-list for extension type_name
	68, // [68:68] is the sub-list for extension extendee
	0,  // [0:68] is the sub-list for field type_name
}

func init() { file_v1_model_ratelimit_proto_init() }
func file_v1_model_ratelimit_proto_init() {
	if File_v1_model_ratelimit_proto != nil {
		return
	}
	file_v1_model_model_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_v1_model_ratelimit_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_ratelimit_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_ratelimit_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimitCluster); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_ratelimit_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Amount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_ratelimit_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Report); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_ratelimit_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AmountAdjuster); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_ratelimit_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClimbConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_ratelimit_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClimbConfig_MetricConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_ratelimit_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClimbConfig_TriggerPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_ratelimit_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClimbConfig_ClimbThrottling); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_ratelimit_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClimbConfig_TriggerPolicy_ErrorRate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_ratelimit_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClimbConfig_TriggerPolicy_SlowRate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_ratelimit_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClimbConfig_TriggerPolicy_ErrorRate_SpecialConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_model_ratelimit_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v1_model_ratelimit_proto_goTypes,
		DependencyIndexes: file_v1_model_ratelimit_proto_depIdxs,
		EnumInfos:         file_v1_model_ratelimit_proto_enumTypes,
		MessageInfos:      file_v1_model_ratelimit_proto_msgTypes,
	}.Build()
	File_v1_model_ratelimit_proto = out.File
	file_v1_model_ratelimit_proto_rawDesc = nil
	file_v1_model_ratelimit_proto_goTypes = nil
	file_v1_model_ratelimit_proto_depIdxs = nil
}
