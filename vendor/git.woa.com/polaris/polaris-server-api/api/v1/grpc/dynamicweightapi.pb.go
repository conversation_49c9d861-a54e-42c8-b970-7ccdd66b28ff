// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v1/grpc/dynamicweightapi.proto

package grpc

import (
	context "context"
	model "git.woa.com/polaris/polaris-server-api/api/v1/model"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_v1_grpc_dynamicweightapi_proto protoreflect.FileDescriptor

var file_v1_grpc_dynamicweightapi_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x02, 0x76, 0x31, 0x1a, 0x1f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xc7, 0x01, 0x0a, 0x11, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x47, 0x52, 0x50, 0x43, 0x12, 0x59, 0x0a, 0x06, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x57, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x57, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x57,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42,
	0x34, 0x5a, 0x32, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70,
	0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_v1_grpc_dynamicweightapi_proto_goTypes = []interface{}{
	(*model.DynamicWeightReportRequest)(nil), // 0: api.v1.model.DynamicWeightReportRequest
	(*model.DynamicWeightQueryRequest)(nil),  // 1: api.v1.model.DynamicWeightQueryRequest
	(*model.DynamicWeightResponse)(nil),      // 2: api.v1.model.DynamicWeightResponse
}
var file_v1_grpc_dynamicweightapi_proto_depIdxs = []int32{
	0, // 0: v1.DynamicWeightGRPC.Report:input_type -> api.v1.model.DynamicWeightReportRequest
	1, // 1: v1.DynamicWeightGRPC.Query:input_type -> api.v1.model.DynamicWeightQueryRequest
	2, // 2: v1.DynamicWeightGRPC.Report:output_type -> api.v1.model.DynamicWeightResponse
	2, // 3: v1.DynamicWeightGRPC.Query:output_type -> api.v1.model.DynamicWeightResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_v1_grpc_dynamicweightapi_proto_init() }
func file_v1_grpc_dynamicweightapi_proto_init() {
	if File_v1_grpc_dynamicweightapi_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_grpc_dynamicweightapi_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_v1_grpc_dynamicweightapi_proto_goTypes,
		DependencyIndexes: file_v1_grpc_dynamicweightapi_proto_depIdxs,
	}.Build()
	File_v1_grpc_dynamicweightapi_proto = out.File
	file_v1_grpc_dynamicweightapi_proto_rawDesc = nil
	file_v1_grpc_dynamicweightapi_proto_goTypes = nil
	file_v1_grpc_dynamicweightapi_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// DynamicWeightGRPCClient is the client API for DynamicWeightGRPC service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DynamicWeightGRPCClient interface {
	// 数据上报
	Report(ctx context.Context, in *model.DynamicWeightReportRequest, opts ...grpc.CallOption) (*model.DynamicWeightResponse, error)
	// 查询
	Query(ctx context.Context, in *model.DynamicWeightQueryRequest, opts ...grpc.CallOption) (*model.DynamicWeightResponse, error)
}

type dynamicWeightGRPCClient struct {
	cc grpc.ClientConnInterface
}

func NewDynamicWeightGRPCClient(cc grpc.ClientConnInterface) DynamicWeightGRPCClient {
	return &dynamicWeightGRPCClient{cc}
}

func (c *dynamicWeightGRPCClient) Report(ctx context.Context, in *model.DynamicWeightReportRequest, opts ...grpc.CallOption) (*model.DynamicWeightResponse, error) {
	out := new(model.DynamicWeightResponse)
	err := c.cc.Invoke(ctx, "/v1.DynamicWeightGRPC/Report", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dynamicWeightGRPCClient) Query(ctx context.Context, in *model.DynamicWeightQueryRequest, opts ...grpc.CallOption) (*model.DynamicWeightResponse, error) {
	out := new(model.DynamicWeightResponse)
	err := c.cc.Invoke(ctx, "/v1.DynamicWeightGRPC/Query", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DynamicWeightGRPCServer is the server API for DynamicWeightGRPC service.
type DynamicWeightGRPCServer interface {
	// 数据上报
	Report(context.Context, *model.DynamicWeightReportRequest) (*model.DynamicWeightResponse, error)
	// 查询
	Query(context.Context, *model.DynamicWeightQueryRequest) (*model.DynamicWeightResponse, error)
}

// UnimplementedDynamicWeightGRPCServer can be embedded to have forward compatible implementations.
type UnimplementedDynamicWeightGRPCServer struct {
}

func (*UnimplementedDynamicWeightGRPCServer) Report(context.Context, *model.DynamicWeightReportRequest) (*model.DynamicWeightResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Report not implemented")
}
func (*UnimplementedDynamicWeightGRPCServer) Query(context.Context, *model.DynamicWeightQueryRequest) (*model.DynamicWeightResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Query not implemented")
}

func RegisterDynamicWeightGRPCServer(s *grpc.Server, srv DynamicWeightGRPCServer) {
	s.RegisterService(&_DynamicWeightGRPC_serviceDesc, srv)
}

func _DynamicWeightGRPC_Report_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(model.DynamicWeightReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DynamicWeightGRPCServer).Report(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/v1.DynamicWeightGRPC/Report",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DynamicWeightGRPCServer).Report(ctx, req.(*model.DynamicWeightReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DynamicWeightGRPC_Query_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(model.DynamicWeightQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DynamicWeightGRPCServer).Query(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/v1.DynamicWeightGRPC/Query",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DynamicWeightGRPCServer).Query(ctx, req.(*model.DynamicWeightQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DynamicWeightGRPC_serviceDesc = grpc.ServiceDesc{
	ServiceName: "v1.DynamicWeightGRPC",
	HandlerType: (*DynamicWeightGRPCServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Report",
			Handler:    _DynamicWeightGRPC_Report_Handler,
		},
		{
			MethodName: "Query",
			Handler:    _DynamicWeightGRPC_Query_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "v1/grpc/dynamicweightapi.proto",
}
