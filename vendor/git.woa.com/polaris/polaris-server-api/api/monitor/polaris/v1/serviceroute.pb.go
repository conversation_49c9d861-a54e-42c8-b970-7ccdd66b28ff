// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: monitor/polaris/v1/serviceroute.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 规则类型，现在就规则路由要区分主调规则还是被调规则
type RouteRecord_RuleType int32

const (
	RouteRecord_Unknown  RouteRecord_RuleType = 0
	RouteRecord_DestRule RouteRecord_RuleType = 1
	RouteRecord_SrcRule  RouteRecord_RuleType = 2
)

// Enum value maps for RouteRecord_RuleType.
var (
	RouteRecord_RuleType_name = map[int32]string{
		0: "Unknown",
		1: "DestRule",
		2: "SrcRule",
	}
	RouteRecord_RuleType_value = map[string]int32{
		"Unknown":  0,
		"DestRule": 1,
		"SrcRule":  2,
	}
)

func (x RouteRecord_RuleType) Enum() *RouteRecord_RuleType {
	p := new(RouteRecord_RuleType)
	*p = x
	return p
}

func (x RouteRecord_RuleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RouteRecord_RuleType) Descriptor() protoreflect.EnumDescriptor {
	return file_monitor_polaris_v1_serviceroute_proto_enumTypes[0].Descriptor()
}

func (RouteRecord_RuleType) Type() protoreflect.EnumType {
	return &file_monitor_polaris_v1_serviceroute_proto_enumTypes[0]
}

func (x RouteRecord_RuleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RouteRecord_RuleType.Descriptor instead.
func (RouteRecord_RuleType) EnumDescriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_serviceroute_proto_rawDescGZIP(), []int{1, 0}
}

//路由调用结果
type RouteResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//返回码
	RetCode string `protobuf:"bytes,1,opt,name=ret_code,json=retCode,proto3" json:"ret_code,omitempty"`
	//一个周期内的这种返回码的调用次数
	PeriodTimes uint32 `protobuf:"varint,2,opt,name=period_times,json=periodTimes,proto3" json:"period_times,omitempty"`
	//路由状态，如是否发生了就近降级，是否是全活状态
	RouteStatus string `protobuf:"bytes,3,opt,name=route_status,json=routeStatus,proto3" json:"route_status,omitempty"`
	//路由返回的cluster结果
	Cluster string `protobuf:"bytes,4,opt,name=cluster,proto3" json:"cluster,omitempty"`
}

func (x *RouteResult) Reset() {
	*x = RouteResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_serviceroute_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RouteResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteResult) ProtoMessage() {}

func (x *RouteResult) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_serviceroute_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteResult.ProtoReflect.Descriptor instead.
func (*RouteResult) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_serviceroute_proto_rawDescGZIP(), []int{0}
}

func (x *RouteResult) GetRetCode() string {
	if x != nil {
		return x.RetCode
	}
	return ""
}

func (x *RouteResult) GetPeriodTimes() uint32 {
	if x != nil {
		return x.PeriodTimes
	}
	return 0
}

func (x *RouteResult) GetRouteStatus() string {
	if x != nil {
		return x.RouteStatus
	}
	return ""
}

func (x *RouteResult) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

//路由规则调用记录
type RouteRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//使用这个规则的插件名字
	PluginName string `protobuf:"bytes,1,opt,name=plugin_name,json=pluginName,proto3" json:"plugin_name,omitempty"`
	//各种不同返回码的结果
	Results []*RouteResult `protobuf:"bytes,2,rep,name=results,proto3" json:"results,omitempty"`
	//主调服务命名空间
	SrcNamespace string `protobuf:"bytes,3,opt,name=src_namespace,json=srcNamespace,proto3" json:"src_namespace,omitempty"`
	//主调服务名
	SrcService string               `protobuf:"bytes,4,opt,name=src_service,json=srcService,proto3" json:"src_service,omitempty"`
	RuleType   RouteRecord_RuleType `protobuf:"varint,5,opt,name=rule_type,json=ruleType,proto3,enum=v1.RouteRecord_RuleType" json:"rule_type,omitempty"`
}

func (x *RouteRecord) Reset() {
	*x = RouteRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_serviceroute_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RouteRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteRecord) ProtoMessage() {}

func (x *RouteRecord) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_serviceroute_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteRecord.ProtoReflect.Descriptor instead.
func (*RouteRecord) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_serviceroute_proto_rawDescGZIP(), []int{1}
}

func (x *RouteRecord) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *RouteRecord) GetResults() []*RouteResult {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *RouteRecord) GetSrcNamespace() string {
	if x != nil {
		return x.SrcNamespace
	}
	return ""
}

func (x *RouteRecord) GetSrcService() string {
	if x != nil {
		return x.SrcService
	}
	return ""
}

func (x *RouteRecord) GetRuleType() RouteRecord_RuleType {
	if x != nil {
		return x.RuleType
	}
	return RouteRecord_Unknown
}

//一个服务不同类型路由规则调用记录
type ServiceRouteRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//该条记录的唯一id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//sdk标识
	SdkToken *SDKToken `protobuf:"bytes,2,opt,name=sdk_token,json=sdkToken,proto3" json:"sdk_token,omitempty"`
	//命名空间
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	//服务名
	Service string `protobuf:"bytes,4,opt,name=service,proto3" json:"service,omitempty"`
	//上报的时间
	Time *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=time,proto3" json:"time,omitempty"`
	//路由规则调用记录
	Records   []*RouteRecord    `protobuf:"bytes,6,rep,name=records,proto3" json:"records,omitempty"`
	Signature *MonitorSignature `protobuf:"bytes,7,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *ServiceRouteRecord) Reset() {
	*x = ServiceRouteRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_serviceroute_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceRouteRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceRouteRecord) ProtoMessage() {}

func (x *ServiceRouteRecord) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_serviceroute_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceRouteRecord.ProtoReflect.Descriptor instead.
func (*ServiceRouteRecord) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_serviceroute_proto_rawDescGZIP(), []int{2}
}

func (x *ServiceRouteRecord) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ServiceRouteRecord) GetSdkToken() *SDKToken {
	if x != nil {
		return x.SdkToken
	}
	return nil
}

func (x *ServiceRouteRecord) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ServiceRouteRecord) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *ServiceRouteRecord) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *ServiceRouteRecord) GetRecords() []*RouteRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *ServiceRouteRecord) GetSignature() *MonitorSignature {
	if x != nil {
		return x.Signature
	}
	return nil
}

var File_monitor_polaris_v1_serviceroute_proto protoreflect.FileDescriptor

var file_monitor_polaris_v1_serviceroute_proto_rawDesc = []byte{
	0x0a, 0x25, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x72, 0x6f, 0x75, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x76, 0x31, 0x1a, 0x21, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x64, 0x6b, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x22, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x88, 0x01, 0x0a, 0x0b, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x22, 0x8a,
	0x02, 0x0a, 0x0b, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x29, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x72,
	0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x73, 0x72, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x72, 0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x72, 0x63, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x35, 0x0a, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x72,
	0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x32, 0x0a, 0x08, 0x52, 0x75, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x12, 0x0c, 0x0a, 0x08, 0x44, 0x65, 0x73, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x0b,
	0x0a, 0x07, 0x53, 0x72, 0x63, 0x52, 0x75, 0x6c, 0x65, 0x10, 0x02, 0x22, 0x96, 0x02, 0x0a, 0x12,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x73, 0x64, 0x6b, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x44, 0x4b, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x08, 0x73, 0x64, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1c, 0x0a,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x12, 0x32, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x42, 0x3f, 0x5a, 0x3d, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61,
	0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_monitor_polaris_v1_serviceroute_proto_rawDescOnce sync.Once
	file_monitor_polaris_v1_serviceroute_proto_rawDescData = file_monitor_polaris_v1_serviceroute_proto_rawDesc
)

func file_monitor_polaris_v1_serviceroute_proto_rawDescGZIP() []byte {
	file_monitor_polaris_v1_serviceroute_proto_rawDescOnce.Do(func() {
		file_monitor_polaris_v1_serviceroute_proto_rawDescData = protoimpl.X.CompressGZIP(file_monitor_polaris_v1_serviceroute_proto_rawDescData)
	})
	return file_monitor_polaris_v1_serviceroute_proto_rawDescData
}

var file_monitor_polaris_v1_serviceroute_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_monitor_polaris_v1_serviceroute_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_monitor_polaris_v1_serviceroute_proto_goTypes = []interface{}{
	(RouteRecord_RuleType)(0),     // 0: v1.RouteRecord.RuleType
	(*RouteResult)(nil),           // 1: v1.RouteResult
	(*RouteRecord)(nil),           // 2: v1.RouteRecord
	(*ServiceRouteRecord)(nil),    // 3: v1.ServiceRouteRecord
	(*SDKToken)(nil),              // 4: v1.SDKToken
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
	(*MonitorSignature)(nil),      // 6: v1.MonitorSignature
}
var file_monitor_polaris_v1_serviceroute_proto_depIdxs = []int32{
	1, // 0: v1.RouteRecord.results:type_name -> v1.RouteResult
	0, // 1: v1.RouteRecord.rule_type:type_name -> v1.RouteRecord.RuleType
	4, // 2: v1.ServiceRouteRecord.sdk_token:type_name -> v1.SDKToken
	5, // 3: v1.ServiceRouteRecord.time:type_name -> google.protobuf.Timestamp
	2, // 4: v1.ServiceRouteRecord.records:type_name -> v1.RouteRecord
	6, // 5: v1.ServiceRouteRecord.signature:type_name -> v1.MonitorSignature
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_monitor_polaris_v1_serviceroute_proto_init() }
func file_monitor_polaris_v1_serviceroute_proto_init() {
	if File_monitor_polaris_v1_serviceroute_proto != nil {
		return
	}
	file_monitor_polaris_v1_sdktoken_proto_init()
	file_monitor_polaris_v1_signature_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_monitor_polaris_v1_serviceroute_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RouteResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_serviceroute_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RouteRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_serviceroute_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceRouteRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_monitor_polaris_v1_serviceroute_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_monitor_polaris_v1_serviceroute_proto_goTypes,
		DependencyIndexes: file_monitor_polaris_v1_serviceroute_proto_depIdxs,
		EnumInfos:         file_monitor_polaris_v1_serviceroute_proto_enumTypes,
		MessageInfos:      file_monitor_polaris_v1_serviceroute_proto_msgTypes,
	}.Build()
	File_monitor_polaris_v1_serviceroute_proto = out.File
	file_monitor_polaris_v1_serviceroute_proto_rawDesc = nil
	file_monitor_polaris_v1_serviceroute_proto_goTypes = nil
	file_monitor_polaris_v1_serviceroute_proto_depIdxs = nil
}
