// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.12.3
// source: SSOUserData.proto

package sso_protos

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SSO头的UserData字段
type UserData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	URLParameters        []*UserDataItem `protobuf:"bytes,1,rep,name=URLParameters" json:"URLParameters,omitempty"`                                               // OpenSDK 或者 BWM 等业务，在这里存储HTTP头部的URL参数列表
	RedirectUrl          []byte          `protobuf:"bytes,2,opt,name=redirect_url,json=redirectUrl" json:"redirect_url,omitempty"`                                // http 302 跳转URL
	ClientBuildInfo      []byte          `protobuf:"bytes,3,opt,name=ClientBuildInfo" json:"ClientBuildInfo,omitempty"`                                           // 客户端的版本信息，如Android5.0.2
	HttpExtraHeaders     []byte          `protobuf:"bytes,4,opt,name=http_extra_headers,json=httpExtraHeaders" json:"http_extra_headers,omitempty"`               // OpenSSO构造http请求头时加上这串Header(如"Cookie: 123\r\nToken: ABC\r\n")
	SecParameters        []*UserDataItem `protobuf:"bytes,5,rep,name=SecParameters" json:"SecParameters,omitempty"`                                               // 校验身份用的字段, 不透传, 请求带的要清空, 收到校验回包后再填数据
	HttpMethod           []byte          `protobuf:"bytes,6,opt,name=http_method,json=httpMethod,def=POST" json:"http_method,omitempty"`                          // 请求中允许指定HTTP method
	RespHttpExtraHeaders []byte          `protobuf:"bytes,7,opt,name=resp_http_extra_headers,json=respHttpExtraHeaders" json:"resp_http_extra_headers,omitempty"` // http响应支持HEADER透传(如"Cookie: 123\r\nToken: ABC\r\n"),长度不能超过512字节
	RespCode             *uint32         `protobuf:"varint,8,opt,name=resp_code,json=respCode" json:"resp_code,omitempty"`                                        // http响应码
	RespBodyCompressed   *bool           `protobuf:"varint,9,opt,name=resp_body_compressed,json=respBodyCompressed,def=0" json:"resp_body_compressed,omitempty"`  // http响应body压缩
	EnterpriseId         *uint64         `protobuf:"varint,10,opt,name=enterprise_id,json=enterpriseId" json:"enterprise_id,omitempty"`                           // 透传企业ID
	NeedRealCliip        *bool           `protobuf:"varint,11,opt,name=need_real_cliip,json=needRealCliip,def=0" json:"need_real_cliip,omitempty"`                // client->server->opensso opensso trans clientip or serverip,default trans serverip
	ClientIpv4Hint       *uint32         `protobuf:"varint,13,opt,name=client_ipv4_hint,json=clientIpv4Hint" json:"client_ipv4_hint,omitempty"`                   // 调度系统使用 客户端通过IPv6连接时带上的IPv4网关地址 主机序
	SsoVipv6             []byte          `protobuf:"bytes,14,opt,name=sso_vipv6,json=ssoVipv6" json:"sso_vipv6,omitempty"`                                        // 调度系统使用 客户端的sso接入点的IPv6地址 16字节
	QqHead               *QQHead         `protobuf:"bytes,15,opt,name=qq_head,json=qqHead" json:"qq_head,omitempty"`                                              // 使用统一的结构体传递模块间透传字段
	TestRouteId          *uint32         `protobuf:"varint,16,opt,name=test_route_id,json=testRouteId" json:"test_route_id,omitempty"`                            // 测试环境路由规则id
	BytesQimei           []byte          `protobuf:"bytes,17,opt,name=bytes_qimei,json=bytesQimei" json:"bytes_qimei,omitempty"`                                  // qimei
	SsoUdpVipv4          *uint32         `protobuf:"varint,18,opt,name=sso_udp_vipv4,json=ssoUdpVipv4" json:"sso_udp_vipv4,omitempty"`                            // udp请求sso接入点的IPv4地址
}

// Default values for UserData fields.
const (
	Default_UserData_RespBodyCompressed = bool(false)
	Default_UserData_NeedRealCliip      = bool(false)
)

// Default values for UserData fields.
var (
	Default_UserData_HttpMethod = []byte("POST")
)

func (x *UserData) Reset() {
	*x = UserData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSOUserData_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserData) ProtoMessage() {}

func (x *UserData) ProtoReflect() protoreflect.Message {
	mi := &file_SSOUserData_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserData.ProtoReflect.Descriptor instead.
func (*UserData) Descriptor() ([]byte, []int) {
	return file_SSOUserData_proto_rawDescGZIP(), []int{0}
}

func (x *UserData) GetURLParameters() []*UserDataItem {
	if x != nil {
		return x.URLParameters
	}
	return nil
}

func (x *UserData) GetRedirectUrl() []byte {
	if x != nil {
		return x.RedirectUrl
	}
	return nil
}

func (x *UserData) GetClientBuildInfo() []byte {
	if x != nil {
		return x.ClientBuildInfo
	}
	return nil
}

func (x *UserData) GetHttpExtraHeaders() []byte {
	if x != nil {
		return x.HttpExtraHeaders
	}
	return nil
}

func (x *UserData) GetSecParameters() []*UserDataItem {
	if x != nil {
		return x.SecParameters
	}
	return nil
}

func (x *UserData) GetHttpMethod() []byte {
	if x != nil && x.HttpMethod != nil {
		return x.HttpMethod
	}
	return append([]byte(nil), Default_UserData_HttpMethod...)
}

func (x *UserData) GetRespHttpExtraHeaders() []byte {
	if x != nil {
		return x.RespHttpExtraHeaders
	}
	return nil
}

func (x *UserData) GetRespCode() uint32 {
	if x != nil && x.RespCode != nil {
		return *x.RespCode
	}
	return 0
}

func (x *UserData) GetRespBodyCompressed() bool {
	if x != nil && x.RespBodyCompressed != nil {
		return *x.RespBodyCompressed
	}
	return Default_UserData_RespBodyCompressed
}

func (x *UserData) GetEnterpriseId() uint64 {
	if x != nil && x.EnterpriseId != nil {
		return *x.EnterpriseId
	}
	return 0
}

func (x *UserData) GetNeedRealCliip() bool {
	if x != nil && x.NeedRealCliip != nil {
		return *x.NeedRealCliip
	}
	return Default_UserData_NeedRealCliip
}

func (x *UserData) GetClientIpv4Hint() uint32 {
	if x != nil && x.ClientIpv4Hint != nil {
		return *x.ClientIpv4Hint
	}
	return 0
}

func (x *UserData) GetSsoVipv6() []byte {
	if x != nil {
		return x.SsoVipv6
	}
	return nil
}

func (x *UserData) GetQqHead() *QQHead {
	if x != nil {
		return x.QqHead
	}
	return nil
}

func (x *UserData) GetTestRouteId() uint32 {
	if x != nil && x.TestRouteId != nil {
		return *x.TestRouteId
	}
	return 0
}

func (x *UserData) GetBytesQimei() []byte {
	if x != nil {
		return x.BytesQimei
	}
	return nil
}

func (x *UserData) GetSsoUdpVipv4() uint32 {
	if x != nil && x.SsoUdpVipv4 != nil {
		return *x.SsoUdpVipv4
	}
	return 0
}

type UserDataItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   []byte `protobuf:"bytes,1,req,name=key" json:"key,omitempty"`
	Value []byte `protobuf:"bytes,2,req,name=value" json:"value,omitempty"`
}

func (x *UserDataItem) Reset() {
	*x = UserDataItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSOUserData_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserDataItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserDataItem) ProtoMessage() {}

func (x *UserDataItem) ProtoReflect() protoreflect.Message {
	mi := &file_SSOUserData_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserDataItem.ProtoReflect.Descriptor instead.
func (*UserDataItem) Descriptor() ([]byte, []int) {
	return file_SSOUserData_proto_rawDescGZIP(), []int{1}
}

func (x *UserDataItem) GetKey() []byte {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *UserDataItem) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

var File_SSOUserData_proto protoreflect.FileDescriptor

var file_SSOUserData_proto_rawDesc = []byte{
	0x0a, 0x11, 0x53, 0x53, 0x4f, 0x55, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x15, 0x54, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x51, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x53, 0x53, 0x4f, 0x1a, 0x0c, 0x51, 0x51, 0x48, 0x65,
	0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8e, 0x06, 0x0a, 0x08, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x49, 0x0a, 0x0d, 0x55, 0x52, 0x4c, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x54,
	0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x51, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x53, 0x53, 0x4f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x0d, 0x55, 0x52, 0x4c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a, 0x0f, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x75, 0x69,
	0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a,
	0x12, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x10, 0x68, 0x74, 0x74, 0x70, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x49, 0x0a, 0x0d, 0x53,
	0x65, 0x63, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x54, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x51, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x53, 0x53, 0x4f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0d, 0x53, 0x65, 0x63, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x25, 0x0a, 0x0b, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x3a, 0x04, 0x50, 0x4f, 0x53,
	0x54, 0x52, 0x0a, 0x68, 0x74, 0x74, 0x70, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x35, 0x0a,
	0x17, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x14,
	0x72, 0x65, 0x73, 0x70, 0x48, 0x74, 0x74, 0x70, 0x45, 0x78, 0x74, 0x72, 0x61, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x37, 0x0a, 0x14, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x3a,
	0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x12, 0x72, 0x65, 0x73, 0x70, 0x42, 0x6f, 0x64, 0x79,
	0x43, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12,
	0x2d, 0x0a, 0x0f, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x63, 0x6c, 0x69,
	0x69, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52,
	0x0d, 0x6e, 0x65, 0x65, 0x64, 0x52, 0x65, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x69, 0x70, 0x12, 0x28,
	0x0a, 0x10, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x76, 0x34, 0x5f, 0x68, 0x69,
	0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x49, 0x70, 0x76, 0x34, 0x48, 0x69, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x73, 0x6f, 0x5f,
	0x76, 0x69, 0x70, 0x76, 0x36, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x73, 0x73, 0x6f,
	0x56, 0x69, 0x70, 0x76, 0x36, 0x12, 0x39, 0x0a, 0x07, 0x71, 0x71, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x54, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74,
	0x2e, 0x51, 0x51, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x51, 0x51, 0x48, 0x65, 0x61, 0x64, 0x52, 0x06, 0x71, 0x71, 0x48, 0x65, 0x61, 0x64,
	0x12, 0x22, 0x0a, 0x0d, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x74, 0x65, 0x73, 0x74, 0x52, 0x6f, 0x75,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x71, 0x69,
	0x6d, 0x65, 0x69, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x62, 0x79, 0x74, 0x65, 0x73,
	0x51, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x73, 0x6f, 0x5f, 0x75, 0x64, 0x70,
	0x5f, 0x76, 0x69, 0x70, 0x76, 0x34, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73, 0x73,
	0x6f, 0x55, 0x64, 0x70, 0x56, 0x69, 0x70, 0x76, 0x34, 0x22, 0x36, 0x0a, 0x0c, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x6f, 0x61,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x50, 0x43, 0x47, 0x2d, 0x4d, 0x51, 0x51, 0x2d, 0x51, 0x51, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2d, 0x44, 0x65, 0x76, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x32,
	0x2f, 0x53, 0x53, 0x4f, 0x2d, 0x50, 0x72, 0x6f, 0x6a, 0x2f, 0x73, 0x73, 0x6f, 0x5f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x73,
}

var (
	file_SSOUserData_proto_rawDescOnce sync.Once
	file_SSOUserData_proto_rawDescData = file_SSOUserData_proto_rawDesc
)

func file_SSOUserData_proto_rawDescGZIP() []byte {
	file_SSOUserData_proto_rawDescOnce.Do(func() {
		file_SSOUserData_proto_rawDescData = protoimpl.X.CompressGZIP(file_SSOUserData_proto_rawDescData)
	})
	return file_SSOUserData_proto_rawDescData
}

var file_SSOUserData_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_SSOUserData_proto_goTypes = []interface{}{
	(*UserData)(nil),     // 0: Tencent.QQService.SSO.UserData
	(*UserDataItem)(nil), // 1: Tencent.QQService.SSO.UserDataItem
	(*QQHead)(nil),       // 2: Tencent.QQService.Common.QQHead
}
var file_SSOUserData_proto_depIdxs = []int32{
	1, // 0: Tencent.QQService.SSO.UserData.URLParameters:type_name -> Tencent.QQService.SSO.UserDataItem
	1, // 1: Tencent.QQService.SSO.UserData.SecParameters:type_name -> Tencent.QQService.SSO.UserDataItem
	2, // 2: Tencent.QQService.SSO.UserData.qq_head:type_name -> Tencent.QQService.Common.QQHead
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_SSOUserData_proto_init() }
func file_SSOUserData_proto_init() {
	if File_SSOUserData_proto != nil {
		return
	}
	file_QQHead_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_SSOUserData_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSOUserData_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserDataItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_SSOUserData_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_SSOUserData_proto_goTypes,
		DependencyIndexes: file_SSOUserData_proto_depIdxs,
		MessageInfos:      file_SSOUserData_proto_msgTypes,
	}.Build()
	File_SSOUserData_proto = out.File
	file_SSOUserData_proto_rawDesc = nil
	file_SSOUserData_proto_goTypes = nil
	file_SSOUserData_proto_depIdxs = nil
}
