// Package SLICE_UPLOAD comment
// Code generated by trpc4tars 2.0. DO NOT EDIT.
// source: slice_upload.jce
package SLICE_UPLOAD

import (
	"fmt"

	"git.woa.com/jce/jce"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = fmt.Errorf
var _ = jce.Marshal

type UPLOAD_ENTRANCE int32

const (
	UPLOAD_ENTRANCE_PhotoPlus                = 11
	UPLOAD_ENTRANCE_PhotoAlbumList           = 12
	UPLOAD_ENTRANCE_PhotoList                = 13
	UPLOAD_ENTRANCE_PhotoOther               = 14
	UPLOAD_ENTRANCE_PhotoBanner              = 15
	UPLOAD_ENTRANCE_PhotoReOne               = 16
	UPLOAD_ENTRANCE_PhotoReTwo               = 17
	UPLOAD_ENTRANCE_PhotoReThree             = 18
	UPLOAD_ENTRANCE_AlbumPageGuide           = 19
	UPLOAD_ENTRANCE_ShuoshuoPlus             = 21
	UPLOAD_ENTRANCE_ShuoshuoList             = 22
	UPLOAD_ENTRANCE_ShuoshuoOther            = 23
	UPLOAD_ENTRANCE_ShuoshuoFeeds            = 24
	UPLOAD_ENTRANCE_ShuoshuoPluscamera       = 25
	UPLOAD_ENTRANCE_ShuoshuoFeedspic         = 26
	UPLOAD_ENTRANCE_ShuoshuoFeedsBubble      = 27
	UPLOAD_ENTRANCE_ShuoshuoFeedsReOne       = 28
	UPLOAD_ENTRANCE_ShuoshuoFeedsReTwo       = 29
	UPLOAD_ENTRANCE_ShuoshuoFeedsReThree     = 30
	UPLOAD_ENTRANCE_BlogPhoto                = 31
	UPLOAD_ENTRANCE_LiuyanPhoto              = 32
	UPLOAD_ENTRANCE_PinglunPhoto             = 33
	UPLOAD_ENTRANCE_Activealbum              = 34
	UPLOAD_ENTRANCE_ShuoshuoThirdParty       = 45
	UPLOAD_ENTRANCE_LoveSpace                = 101
	UPLOAD_ENTRANCE_PhotoComposeVideoPlus    = 125
	UPLOAD_ENTRANCE_TencentXcxUpload         = 131
	UPLOAD_ENTRANCE_Plus2VideoRecordFile     = 200
	UPLOAD_ENTRANCE_Plus2VideoLocalFile      = 201
	UPLOAD_ENTRANCE_AlbumPageRecordFile      = 202
	UPLOAD_ENTRANCE_AlbumPageLocalFile       = 203
	UPLOAD_ENTRANCE_MyVideoPageRecordFile    = 204
	UPLOAD_ENTRANCE_MyVideoPageLocalFile     = 205
	UPLOAD_ENTRANCE_Aio2Qzone                = 206
	UPLOAD_ENTRANCE_ThirdPartySdk            = 207
	UPLOAD_ENTRANCE_VideoRecommandRecordFile = 208
	UPLOAD_ENTRANCE_VideoRecommandLocalFile  = 209
	UPLOAD_ENTRANCE_Cover2VideoRecordFile    = 210
	UPLOAD_ENTRANCE_Cover2VideoLocalFile     = 211
	UPLOAD_ENTRANCE_QqShotAndShare           = 212
	UPLOAD_ENTRANCE_WeishiMusic              = 213
	UPLOAD_ENTRANCE_WeishiZhuti              = 214
	UPLOAD_ENTRANCE_Wangzhe2album            = 999
)

type NetType int32

const (
	NetType_NET_UNKOWN   = 0
	NetType_NET_WIFI     = 1
	NetType_NET_2G       = 2
	NetType_NET_3G       = 3
	NetType_NET_4G       = 4
	NetType_NET_ETHERNET = 5
)

type UploadModel int32

const (
	UploadModel_MODEL_NORMAL     = 0
	UploadModel_MODEL_PRE_UPLOAD = 1
)

type CheckType int32

const (
	CheckType_TYPE_MD5  = 0
	CheckType_TYPE_SHA1 = 1
	CheckType_TYPE_NONE = 2
)

type Flag int32

const (
	Flag_FLAG_SUCC                           = 0
	Flag_FLAG_FILECOMPLETE                   = 1
	Flag_FLAG_FILENEEDCOMMIT                 = 2
	Flag_FLAG_ERROR                          = 10
	Flag_FLAG_RETRY_CURRENT                  = 11
	Flag_FLAG_RETRY_CTRL_SESSION             = 12
	Flag_FLAG_RETRY_CTRL_NOSESSION           = 13
	Flag_FLAG_RETRY_RECONNECT_CTRL_NOSESSION = 14
)

type Cmd int32

const (
	Cmd_CMD_UNKNOWN = 0
	Cmd_CMD_CONTROL = 1
	Cmd_CMD_UPLOAD  = 2
	Cmd_CMD_COMMIT  = 3
)

type AUTH_TYPE int32

const (
	AUTH_TYPE_LOGIN_TYPE_ENC_A2                = 1
	AUTH_TYPE_LOGIN_TYPE_A2                    = 2
	AUTH_TYPE_LOGIN_TYPE_SKEY                  = 3
	AUTH_TYPE_LOGIN_TYPE_PSKEY                 = 4
	AUTH_TYPE_LOGIN_TYPE_OPENID                = 5
	AUTH_TYPE_LOGIN_TYPE_THEMEALBUM_XCX_TICKET = 6
	AUTH_TYPE_LOGIN_TYPE_BIZ                   = 7
	AUTH_TYPE_LOGIN_TYPE_OPENID_OTHER          = 8
	AUTH_TYPE_LOGIN_TYPE_OPENID_WECHAT         = 9
)

type DUMP_BUSSINESS_ID int32

const (
	DUMP_BUSSINESS_ID_DUMP_BUSSINESS_DEFAULT = 0
	DUMP_BUSSINESS_ID_DUMP_BUSSINESS_WEISHI  = 1
	DUMP_BUSSINESS_ID_DUMP_BUSSINESS_MAX     = 2
)

//const as define in tars file
const (
	Appid_photo               string = "pic_qzone"
	Appid_video               string = "video_qzone"
	Appid_weiyun              string = "weiyun"
	Appid_shuoshuo            string = "commit_shuoshuo"
	Appid_shaka_photo         string = "shaka_photo"
	Appid_shaka_video         string = "shaka_video"
	Appid_touchuan            string = "touchuan"
	Appid_ups                 string = "ups"
	Appid_upp                 string = "upp"
	Appid_c2cvideo            string = "c2cvideo"
	Appid_mobilelog           string = "mobilelog"
	Appid_touchuan_outer      string = "touchuan_outer"
	Appid_xcx_photo           string = "pic_xcx"
	Appid_xcx_video           string = "video_xcx"
	Appid_qun                 string = "qun"
	Appid_xcx_themealbum      string = "themealbumxcx"
	Appid_other_video         string = "other_video"
	Appid_openid_photo        string = "openid_photo"
	Appid_openid_video        string = "openid_video"
	Appid_weishi_sdk_photo    string = "weishisdk_photo"
	Appid_weishi_sdk_video    string = "weishisdk_video"
	Appid_dengpao_photo       string = "dengpao_photo"
	Appid_dengpao_video       string = "dengpao_video"
	Appid_kantu_wx_photo      string = "pic_kantu_wx"
	Appid_secret_minico_photo string = "pic_secret_minico"
	Appid_qq_story_video      string = "qq_story_video"
	Appid_kantu_xcx_wx_photo  string = "pic_kantu_xcx_wx"
	Appid_kantu_xcx_wx_video  string = "video_kantu_xcx_wx"
	Appid_video_qun           string = "video_qun"
	Appid_Q_friend_photo      string = "Q_friend_photo"
	Appid_ptu_openid_video    string = "ptu_openid_video"
	Appid_ptu_wx_video        string = "ptu_wx_video"
)

// StEnvironment struct implement
type StEnvironment struct {
	Qua         string          `json:"qua"`
	Device      string          `json:"device"`
	Net         NetType         `json:"net"`
	Operators   string          `json:"operators"`
	Client_ip   uint32          `json:"client_ip"`
	Refer       string          `json:"refer"`
	Entrance    UPLOAD_ENTRANCE `json:"entrance"`
	Source      int32           `json:"source"`
	DeviceInfo  string          `json:"deviceInfo"`
	Client_ipv6 string          `json:"client_ipv6"`
}

func (st *StEnvironment) MakesureNotNil() {
}
func (st *StEnvironment) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StEnvironment) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_string(&st.Qua, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Device, 2, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32((*int32)(&st.Net), 3, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Operators, 4, true)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Client_ip, 5, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Refer, 6, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32((*int32)(&st.Entrance), 7, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Source, 8, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.DeviceInfo, 9, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Client_ipv6, 10, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StEnvironment) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StEnvironment, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StEnvironment) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_string(st.Qua, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Device, 2)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(st.Net), 3)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Operators, 4)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Client_ip, 5)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Refer, 6)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(st.Entrance), 7)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Source, 8)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.DeviceInfo, 9)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Client_ipv6, 10)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StEnvironment) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StEnvironment) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StEnvironment) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StEnvironment) WupTypeName() string {
	return "SLICE_UPLOAD.stEnvironment"
}

// StResult struct implement
type StResult struct {
	Ret  int32  `json:"ret"`
	Flag Flag   `json:"flag"`
	Msg  string `json:"msg"`
}

func (st *StResult) MakesureNotNil() {
}
func (st *StResult) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StResult) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_int32(&st.Ret, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32((*int32)(&st.Flag), 2, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Msg, 3, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StResult) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StResult, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StResult) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_int32(st.Ret, 1)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(st.Flag), 2)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Msg, 3)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StResult) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StResult) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StResult) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StResult) WupTypeName() string {
	return "SLICE_UPLOAD.stResult"
}

// StSession struct implement
type StSession struct {
	Sid             string `json:"sid"`
	Process_ip      uint32 `json:"process_ip"`
	Process_port    int16  `json:"process_port"`
	DumpBussinessID int32  `json:"dumpBussinessID"`
}

func (st *StSession) MakesureNotNil() {
}
func (st *StSession) ResetDefault() {
	st.MakesureNotNil()
	st.DumpBussinessID = 0
}

// ReadFrom reads  from _is and put into struct.
func (st *StSession) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_string(&st.Sid, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Process_ip, 2, true)
	if err != nil {
		return err
	}

	err = _is.Read_int16(&st.Process_port, 3, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.DumpBussinessID, 4, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StSession) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StSession, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StSession) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_string(st.Sid, 1)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Process_ip, 2)
	if err != nil {
		return err
	}

	err = _os.Write_int16(st.Process_port, 3)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.DumpBussinessID, 4)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StSession) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StSession) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StSession) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StSession) WupTypeName() string {
	return "SLICE_UPLOAD.stSession"
}

// AuthToken struct implement
type AuthToken struct {
	Type    AUTH_TYPE `json:"type"`
	Data    []int8    `json:"data"`
	Ext_key []int8    `json:"ext_key"`
	Appid   int32     `json:"appid"`
}

func (st *AuthToken) MakesureNotNil() {
}
func (st *AuthToken) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *AuthToken) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_int32((*int32)(&st.Type), 0, true)
	if err != nil {
		return err
	}

	err, have, ty = _is.SkipToNoCheck(1, true)
	if err != nil {
		return err
	}

	if ty == jce.LIST {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.Data = make([]int8, length)
		for i0, e0 := int32(0), length; i0 < e0; i0++ {

			err = _is.Read_int8(&st.Data[i0], 0, false)
			if err != nil {
				return err
			}

		}
	} else if ty == jce.SIMPLE_LIST {

		err, _ = _is.SkipTo(jce.BYTE, 0, true)
		if err != nil {
			return err
		}

		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		err = _is.Read_slice_int8(&st.Data, length, true)
		if err != nil {
			return err
		}

	} else {
		err = fmt.Errorf("require vector, but not")
		if err != nil {
			return err
		}

	}

	err, have, ty = _is.SkipToNoCheck(2, true)
	if err != nil {
		return err
	}

	if ty == jce.LIST {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.Ext_key = make([]int8, length)
		for i1, e1 := int32(0), length; i1 < e1; i1++ {

			err = _is.Read_int8(&st.Ext_key[i1], 0, false)
			if err != nil {
				return err
			}

		}
	} else if ty == jce.SIMPLE_LIST {

		err, _ = _is.SkipTo(jce.BYTE, 0, true)
		if err != nil {
			return err
		}

		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		err = _is.Read_slice_int8(&st.Ext_key, length, true)
		if err != nil {
			return err
		}

	} else {
		err = fmt.Errorf("require vector, but not")
		if err != nil {
			return err
		}

	}

	err = _is.Read_int32(&st.Appid, 3, true)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *AuthToken) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require AuthToken, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *AuthToken) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_int32(int32(st.Type), 0)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.SIMPLE_LIST, 1)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.BYTE, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Data)), 0)
	if err != nil {
		return err
	}

	err = _os.Write_slice_int8(st.Data)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.SIMPLE_LIST, 2)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.BYTE, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Ext_key)), 0)
	if err != nil {
		return err
	}

	err = _os.Write_slice_int8(st.Ext_key)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Appid, 3)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *AuthToken) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *AuthToken) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *AuthToken) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *AuthToken) WupTypeName() string {
	return "SLICE_UPLOAD.AuthToken"
}

// StOffset struct implement
type StOffset struct {
	Begin int64 `json:"begin"`
	End   int64 `json:"end"`
}

func (st *StOffset) MakesureNotNil() {
}
func (st *StOffset) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *StOffset) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_int64(&st.Begin, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_int64(&st.End, 2, true)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *StOffset) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require StOffset, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *StOffset) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_int64(st.Begin, 1)
	if err != nil {
		return err
	}

	err = _os.Write_int64(st.End, 2)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *StOffset) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *StOffset) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *StOffset) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *StOffset) WupTypeName() string {
	return "SLICE_UPLOAD.stOffset"
}

// DumpBussinessReq struct implement
type DumpBussinessReq struct {
	Biz_req []int8 `json:"biz_req"`
	IMEI    string `json:"IMEI"`
}

func (st *DumpBussinessReq) MakesureNotNil() {
}
func (st *DumpBussinessReq) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *DumpBussinessReq) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have, ty = _is.SkipToNoCheck(0, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.Biz_req = make([]int8, length)
			for i0, e0 := int32(0), length; i0 < e0; i0++ {

				err = _is.Read_int8(&st.Biz_req[i0], 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {

			err, _ = _is.SkipTo(jce.BYTE, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_slice_int8(&st.Biz_req, length, true)
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	err = _is.Read_string(&st.IMEI, 1, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *DumpBussinessReq) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require DumpBussinessReq, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *DumpBussinessReq) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.SIMPLE_LIST, 0)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.BYTE, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Biz_req)), 0)
	if err != nil {
		return err
	}

	err = _os.Write_slice_int8(st.Biz_req)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.IMEI, 1)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *DumpBussinessReq) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *DumpBussinessReq) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *DumpBussinessReq) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *DumpBussinessReq) WupTypeName() string {
	return "SLICE_UPLOAD.DumpBussinessReq"
}

// DumpBussinessRsp struct implement
type DumpBussinessRsp struct {
	Biz_rsp []int8   `json:"biz_rsp"`
	Result  StResult `json:"result"`
}

func (st *DumpBussinessRsp) MakesureNotNil() {
}
func (st *DumpBussinessRsp) ResetDefault() {
	st.MakesureNotNil()
	st.Result.ResetDefault()
}

// ReadFrom reads  from _is and put into struct.
func (st *DumpBussinessRsp) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have, ty = _is.SkipToNoCheck(0, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.Biz_rsp = make([]int8, length)
			for i0, e0 := int32(0), length; i0 < e0; i0++ {

				err = _is.Read_int8(&st.Biz_rsp[i0], 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {

			err, _ = _is.SkipTo(jce.BYTE, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_slice_int8(&st.Biz_rsp, length, true)
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	err = st.Result.ReadBlock(_is, 1, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *DumpBussinessRsp) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require DumpBussinessRsp, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *DumpBussinessRsp) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.SIMPLE_LIST, 0)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.BYTE, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Biz_rsp)), 0)
	if err != nil {
		return err
	}

	err = _os.Write_slice_int8(st.Biz_rsp)
	if err != nil {
		return err
	}

	err = st.Result.WriteBlock(_os, 1)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *DumpBussinessRsp) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *DumpBussinessRsp) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *DumpBussinessRsp) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *DumpBussinessRsp) WupTypeName() string {
	return "SLICE_UPLOAD.DumpBussinessRsp"
}

// FileControlReq struct implement
type FileControlReq struct {
	Uin              string                     `json:"uin"`
	Token            AuthToken                  `json:"token"`
	Appid            string                     `json:"appid"`
	Checksum         string                     `json:"checksum"`
	Check_type       CheckType                  `json:"check_type"`
	File_len         int64                      `json:"file_len"`
	Env              StEnvironment              `json:"env"`
	Model            UploadModel                `json:"model"`
	Biz_req          []int8                     `json:"biz_req"`
	Session          string                     `json:"session"`
	Need_ip_redirect bool                       `json:"need_ip_redirect"`
	Asy_upload       int32                      `json:"asy_upload"`
	DumpReq          map[int32]DumpBussinessReq `json:"dumpReq"`
	Slice_size       int64                      `json:"slice_size"`
	Extend_info      map[string]string          `json:"extend_info"`
}

func (st *FileControlReq) MakesureNotNil() {
}
func (st *FileControlReq) ResetDefault() {
	st.MakesureNotNil()
	st.Token.ResetDefault()
	st.Env.ResetDefault()
	st.Need_ip_redirect = false
	st.Asy_upload = 0
}

// ReadFrom reads  from _is and put into struct.
func (st *FileControlReq) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_string(&st.Uin, 0, true)
	if err != nil {
		return err
	}

	err = st.Token.ReadBlock(_is, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Appid, 2, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Checksum, 3, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32((*int32)(&st.Check_type), 4, true)
	if err != nil {
		return err
	}

	err = _is.Read_int64(&st.File_len, 5, true)
	if err != nil {
		return err
	}

	err = st.Env.ReadBlock(_is, 6, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32((*int32)(&st.Model), 7, false)
	if err != nil {
		return err
	}

	err, have, ty = _is.SkipToNoCheck(8, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.Biz_req = make([]int8, length)
			for i0, e0 := int32(0), length; i0 < e0; i0++ {

				err = _is.Read_int8(&st.Biz_req[i0], 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {

			err, _ = _is.SkipTo(jce.BYTE, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_slice_int8(&st.Biz_req, length, true)
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	err = _is.Read_string(&st.Session, 9, false)
	if err != nil {
		return err
	}

	err = _is.Read_bool(&st.Need_ip_redirect, 10, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Asy_upload, 11, false)
	if err != nil {
		return err
	}

	err, have = _is.SkipTo(jce.MAP, 12, false)
	if err != nil {
		return err
	}

	if have {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.DumpReq = make(map[int32]DumpBussinessReq)
		for i1, e1 := int32(0), length; i1 < e1; i1++ {
			var k1 int32
			var v1 DumpBussinessReq

			err = _is.Read_int32(&k1, 0, false)
			if err != nil {
				return err
			}

			err = v1.ReadBlock(_is, 1, false)
			if err != nil {
				return err
			}

			st.DumpReq[k1] = v1
		}
	}

	err = _is.Read_int64(&st.Slice_size, 13, false)
	if err != nil {
		return err
	}

	err, have = _is.SkipTo(jce.MAP, 14, false)
	if err != nil {
		return err
	}

	if have {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.Extend_info = make(map[string]string)
		for i2, e2 := int32(0), length; i2 < e2; i2++ {
			var k2 string
			var v2 string

			err = _is.Read_string(&k2, 0, false)
			if err != nil {
				return err
			}

			err = _is.Read_string(&v2, 1, false)
			if err != nil {
				return err
			}

			st.Extend_info[k2] = v2
		}
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *FileControlReq) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require FileControlReq, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *FileControlReq) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_string(st.Uin, 0)
	if err != nil {
		return err
	}

	err = st.Token.WriteBlock(_os, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Appid, 2)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Checksum, 3)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(st.Check_type), 4)
	if err != nil {
		return err
	}

	err = _os.Write_int64(st.File_len, 5)
	if err != nil {
		return err
	}

	err = st.Env.WriteBlock(_os, 6)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(st.Model), 7)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.SIMPLE_LIST, 8)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.BYTE, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Biz_req)), 0)
	if err != nil {
		return err
	}

	err = _os.Write_slice_int8(st.Biz_req)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Session, 9)
	if err != nil {
		return err
	}

	err = _os.Write_bool(st.Need_ip_redirect, 10)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Asy_upload, 11)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.MAP, 12)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.DumpReq)), 0)
	if err != nil {
		return err
	}

	for k3, v3 := range st.DumpReq {

		err = _os.Write_int32(k3, 0)
		if err != nil {
			return err
		}

		err = v3.WriteBlock(_os, 1)
		if err != nil {
			return err
		}

	}

	err = _os.Write_int64(st.Slice_size, 13)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.MAP, 14)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Extend_info)), 0)
	if err != nil {
		return err
	}

	for k4, v4 := range st.Extend_info {

		err = _os.Write_string(k4, 0)
		if err != nil {
			return err
		}

		err = _os.Write_string(v4, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *FileControlReq) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *FileControlReq) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *FileControlReq) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *FileControlReq) WupTypeName() string {
	return "SLICE_UPLOAD.FileControlReq"
}

// FileControlRsp struct implement
type FileControlRsp struct {
	Result      StResult                   `json:"result"`
	Session     string                     `json:"session"`
	Offset      int64                      `json:"offset"`
	Slice_size  int64                      `json:"slice_size"`
	Biz_rsp     []int8                     `json:"biz_rsp"`
	Offset_list []StOffset                 `json:"offset_list"`
	Redirect_ip string                     `json:"redirect_ip"`
	Thread_num  int32                      `json:"thread_num"`
	DumpRsp     map[int32]DumpBussinessRsp `json:"dumpRsp"`
}

func (st *FileControlRsp) MakesureNotNil() {
}
func (st *FileControlRsp) ResetDefault() {
	st.MakesureNotNil()
	st.Result.ResetDefault()
	st.Thread_num = 1
}

// ReadFrom reads  from _is and put into struct.
func (st *FileControlRsp) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = st.Result.ReadBlock(_is, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Session, 2, false)
	if err != nil {
		return err
	}

	err = _is.Read_int64(&st.Offset, 3, false)
	if err != nil {
		return err
	}

	err = _is.Read_int64(&st.Slice_size, 4, false)
	if err != nil {
		return err
	}

	err, have, ty = _is.SkipToNoCheck(5, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.Biz_rsp = make([]int8, length)
			for i0, e0 := int32(0), length; i0 < e0; i0++ {

				err = _is.Read_int8(&st.Biz_rsp[i0], 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {

			err, _ = _is.SkipTo(jce.BYTE, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_slice_int8(&st.Biz_rsp, length, true)
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	err, have, ty = _is.SkipToNoCheck(6, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.Offset_list = make([]StOffset, length)
			for i1, e1 := int32(0), length; i1 < e1; i1++ {

				err = st.Offset_list[i1].ReadBlock(_is, 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {
			err = fmt.Errorf("not support simple_list type")
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	err = _is.Read_string(&st.Redirect_ip, 7, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Thread_num, 8, false)
	if err != nil {
		return err
	}

	err, have = _is.SkipTo(jce.MAP, 9, false)
	if err != nil {
		return err
	}

	if have {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.DumpRsp = make(map[int32]DumpBussinessRsp)
		for i2, e2 := int32(0), length; i2 < e2; i2++ {
			var k2 int32
			var v2 DumpBussinessRsp

			err = _is.Read_int32(&k2, 0, false)
			if err != nil {
				return err
			}

			err = v2.ReadBlock(_is, 1, false)
			if err != nil {
				return err
			}

			st.DumpRsp[k2] = v2
		}
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *FileControlRsp) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require FileControlRsp, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *FileControlRsp) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = st.Result.WriteBlock(_os, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Session, 2)
	if err != nil {
		return err
	}

	err = _os.Write_int64(st.Offset, 3)
	if err != nil {
		return err
	}

	err = _os.Write_int64(st.Slice_size, 4)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.SIMPLE_LIST, 5)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.BYTE, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Biz_rsp)), 0)
	if err != nil {
		return err
	}

	err = _os.Write_slice_int8(st.Biz_rsp)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.LIST, 6)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Offset_list)), 0)
	if err != nil {
		return err
	}

	for _, v := range st.Offset_list {

		err = v.WriteBlock(_os, 0)
		if err != nil {
			return err
		}

	}

	err = _os.Write_string(st.Redirect_ip, 7)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Thread_num, 8)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.MAP, 9)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.DumpRsp)), 0)
	if err != nil {
		return err
	}

	for k3, v3 := range st.DumpRsp {

		err = _os.Write_int32(k3, 0)
		if err != nil {
			return err
		}

		err = v3.WriteBlock(_os, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *FileControlRsp) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *FileControlRsp) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *FileControlRsp) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *FileControlRsp) WupTypeName() string {
	return "SLICE_UPLOAD.FileControlRsp"
}

// FileUploadReq struct implement
type FileUploadReq struct {
	Uin        string    `json:"uin"`
	Appid      string    `json:"appid"`
	Session    string    `json:"session"`
	Offset     int64     `json:"offset"`
	Data       []int8    `json:"data"`
	Checksum   string    `json:"checksum"`
	Check_type CheckType `json:"check_type"`
	Send_time  int64     `json:"send_time"`
}

func (st *FileUploadReq) MakesureNotNil() {
}
func (st *FileUploadReq) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *FileUploadReq) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_string(&st.Uin, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Appid, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Session, 2, true)
	if err != nil {
		return err
	}

	err = _is.Read_int64(&st.Offset, 3, true)
	if err != nil {
		return err
	}

	err, have, ty = _is.SkipToNoCheck(4, true)
	if err != nil {
		return err
	}

	if ty == jce.LIST {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.Data = make([]int8, length)
		for i0, e0 := int32(0), length; i0 < e0; i0++ {

			err = _is.Read_int8(&st.Data[i0], 0, false)
			if err != nil {
				return err
			}

		}
	} else if ty == jce.SIMPLE_LIST {

		err, _ = _is.SkipTo(jce.BYTE, 0, true)
		if err != nil {
			return err
		}

		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		err = _is.Read_slice_int8(&st.Data, length, true)
		if err != nil {
			return err
		}

	} else {
		err = fmt.Errorf("require vector, but not")
		if err != nil {
			return err
		}

	}

	err = _is.Read_string(&st.Checksum, 5, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32((*int32)(&st.Check_type), 6, false)
	if err != nil {
		return err
	}

	err = _is.Read_int64(&st.Send_time, 7, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *FileUploadReq) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require FileUploadReq, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *FileUploadReq) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_string(st.Uin, 0)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Appid, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Session, 2)
	if err != nil {
		return err
	}

	err = _os.Write_int64(st.Offset, 3)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.SIMPLE_LIST, 4)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.BYTE, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Data)), 0)
	if err != nil {
		return err
	}

	err = _os.Write_slice_int8(st.Data)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Checksum, 5)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(st.Check_type), 6)
	if err != nil {
		return err
	}

	err = _os.Write_int64(st.Send_time, 7)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *FileUploadReq) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *FileUploadReq) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *FileUploadReq) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *FileUploadReq) WupTypeName() string {
	return "SLICE_UPLOAD.FileUploadReq"
}

// FileUploadRsp struct implement
type FileUploadRsp struct {
	Result        StResult                   `json:"result"`
	Session       string                     `json:"session"`
	Offset        int64                      `json:"offset"`
	Biz_rsp       []int8                     `json:"biz_rsp"`
	Receive_time  int64                      `json:"receive_time"`
	Response_time int64                      `json:"response_time"`
	DumpRsp       map[int32]DumpBussinessRsp `json:"dumpRsp"`
}

func (st *FileUploadRsp) MakesureNotNil() {
}
func (st *FileUploadRsp) ResetDefault() {
	st.MakesureNotNil()
	st.Result.ResetDefault()
}

// ReadFrom reads  from _is and put into struct.
func (st *FileUploadRsp) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = st.Result.ReadBlock(_is, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Session, 2, false)
	if err != nil {
		return err
	}

	err = _is.Read_int64(&st.Offset, 3, false)
	if err != nil {
		return err
	}

	err, have, ty = _is.SkipToNoCheck(4, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.Biz_rsp = make([]int8, length)
			for i0, e0 := int32(0), length; i0 < e0; i0++ {

				err = _is.Read_int8(&st.Biz_rsp[i0], 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {

			err, _ = _is.SkipTo(jce.BYTE, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_slice_int8(&st.Biz_rsp, length, true)
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	err = _is.Read_int64(&st.Receive_time, 5, false)
	if err != nil {
		return err
	}

	err = _is.Read_int64(&st.Response_time, 6, false)
	if err != nil {
		return err
	}

	err, have = _is.SkipTo(jce.MAP, 7, false)
	if err != nil {
		return err
	}

	if have {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.DumpRsp = make(map[int32]DumpBussinessRsp)
		for i1, e1 := int32(0), length; i1 < e1; i1++ {
			var k1 int32
			var v1 DumpBussinessRsp

			err = _is.Read_int32(&k1, 0, false)
			if err != nil {
				return err
			}

			err = v1.ReadBlock(_is, 1, false)
			if err != nil {
				return err
			}

			st.DumpRsp[k1] = v1
		}
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *FileUploadRsp) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require FileUploadRsp, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *FileUploadRsp) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = st.Result.WriteBlock(_os, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Session, 2)
	if err != nil {
		return err
	}

	err = _os.Write_int64(st.Offset, 3)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.SIMPLE_LIST, 4)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.BYTE, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Biz_rsp)), 0)
	if err != nil {
		return err
	}

	err = _os.Write_slice_int8(st.Biz_rsp)
	if err != nil {
		return err
	}

	err = _os.Write_int64(st.Receive_time, 5)
	if err != nil {
		return err
	}

	err = _os.Write_int64(st.Response_time, 6)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.MAP, 7)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.DumpRsp)), 0)
	if err != nil {
		return err
	}

	for k2, v2 := range st.DumpRsp {

		err = _os.Write_int32(k2, 0)
		if err != nil {
			return err
		}

		err = v2.WriteBlock(_os, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *FileUploadRsp) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *FileUploadRsp) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *FileUploadRsp) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *FileUploadRsp) WupTypeName() string {
	return "SLICE_UPLOAD.FileUploadRsp"
}

// FileBatchControlReq struct implement
type FileBatchControlReq struct {
	Control_req map[string]FileControlReq `json:"control_req"`
}

func (st *FileBatchControlReq) MakesureNotNil() {
}
func (st *FileBatchControlReq) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *FileBatchControlReq) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have = _is.SkipTo(jce.MAP, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&length, 0, true)
	if err != nil {
		return err
	}

	st.Control_req = make(map[string]FileControlReq)
	for i0, e0 := int32(0), length; i0 < e0; i0++ {
		var k0 string
		var v0 FileControlReq

		err = _is.Read_string(&k0, 0, false)
		if err != nil {
			return err
		}

		err = v0.ReadBlock(_is, 1, false)
		if err != nil {
			return err
		}

		st.Control_req[k0] = v0
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *FileBatchControlReq) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require FileBatchControlReq, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *FileBatchControlReq) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.MAP, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Control_req)), 0)
	if err != nil {
		return err
	}

	for k1, v1 := range st.Control_req {

		err = _os.Write_string(k1, 0)
		if err != nil {
			return err
		}

		err = v1.WriteBlock(_os, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *FileBatchControlReq) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *FileBatchControlReq) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *FileBatchControlReq) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *FileBatchControlReq) WupTypeName() string {
	return "SLICE_UPLOAD.FileBatchControlReq"
}

// FileBatchControlRsp struct implement
type FileBatchControlRsp struct {
	Control_rsp map[string]FileControlRsp `json:"control_rsp"`
}

func (st *FileBatchControlRsp) MakesureNotNil() {
}
func (st *FileBatchControlRsp) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *FileBatchControlRsp) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have = _is.SkipTo(jce.MAP, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&length, 0, true)
	if err != nil {
		return err
	}

	st.Control_rsp = make(map[string]FileControlRsp)
	for i0, e0 := int32(0), length; i0 < e0; i0++ {
		var k0 string
		var v0 FileControlRsp

		err = _is.Read_string(&k0, 0, false)
		if err != nil {
			return err
		}

		err = v0.ReadBlock(_is, 1, false)
		if err != nil {
			return err
		}

		st.Control_rsp[k0] = v0
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *FileBatchControlRsp) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require FileBatchControlRsp, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *FileBatchControlRsp) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.MAP, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Control_rsp)), 0)
	if err != nil {
		return err
	}

	for k1, v1 := range st.Control_rsp {

		err = _os.Write_string(k1, 0)
		if err != nil {
			return err
		}

		err = v1.WriteBlock(_os, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *FileBatchControlRsp) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *FileBatchControlRsp) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *FileBatchControlRsp) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *FileBatchControlRsp) WupTypeName() string {
	return "SLICE_UPLOAD.FileBatchControlRsp"
}

// FileCommitReq struct implement
type FileCommitReq struct {
	Uin     string                     `json:"uin"`
	Session string                     `json:"session"`
	Biz_req []int8                     `json:"biz_req"`
	Appid   string                     `json:"appid"`
	DumpReq map[int32]DumpBussinessReq `json:"dumpReq"`
}

func (st *FileCommitReq) MakesureNotNil() {
}
func (st *FileCommitReq) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *FileCommitReq) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_string(&st.Uin, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Session, 1, true)
	if err != nil {
		return err
	}

	err, have, ty = _is.SkipToNoCheck(2, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.Biz_req = make([]int8, length)
			for i0, e0 := int32(0), length; i0 < e0; i0++ {

				err = _is.Read_int8(&st.Biz_req[i0], 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {

			err, _ = _is.SkipTo(jce.BYTE, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_slice_int8(&st.Biz_req, length, true)
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	err = _is.Read_string(&st.Appid, 3, false)
	if err != nil {
		return err
	}

	err, have = _is.SkipTo(jce.MAP, 4, false)
	if err != nil {
		return err
	}

	if have {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.DumpReq = make(map[int32]DumpBussinessReq)
		for i1, e1 := int32(0), length; i1 < e1; i1++ {
			var k1 int32
			var v1 DumpBussinessReq

			err = _is.Read_int32(&k1, 0, false)
			if err != nil {
				return err
			}

			err = v1.ReadBlock(_is, 1, false)
			if err != nil {
				return err
			}

			st.DumpReq[k1] = v1
		}
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *FileCommitReq) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require FileCommitReq, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *FileCommitReq) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_string(st.Uin, 0)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Session, 1)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.SIMPLE_LIST, 2)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.BYTE, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Biz_req)), 0)
	if err != nil {
		return err
	}

	err = _os.Write_slice_int8(st.Biz_req)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Appid, 3)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.MAP, 4)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.DumpReq)), 0)
	if err != nil {
		return err
	}

	for k2, v2 := range st.DumpReq {

		err = _os.Write_int32(k2, 0)
		if err != nil {
			return err
		}

		err = v2.WriteBlock(_os, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *FileCommitReq) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *FileCommitReq) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *FileCommitReq) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *FileCommitReq) WupTypeName() string {
	return "SLICE_UPLOAD.FileCommitReq"
}

// FileCommitRsp struct implement
type FileCommitRsp struct {
	Result  StResult                   `json:"result"`
	Session string                     `json:"session"`
	Biz_rsp []int8                     `json:"biz_rsp"`
	DumpRsp map[int32]DumpBussinessRsp `json:"dumpRsp"`
}

func (st *FileCommitRsp) MakesureNotNil() {
}
func (st *FileCommitRsp) ResetDefault() {
	st.MakesureNotNil()
	st.Result.ResetDefault()
}

// ReadFrom reads  from _is and put into struct.
func (st *FileCommitRsp) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = st.Result.ReadBlock(_is, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Session, 2, false)
	if err != nil {
		return err
	}

	err, have, ty = _is.SkipToNoCheck(3, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.Biz_rsp = make([]int8, length)
			for i0, e0 := int32(0), length; i0 < e0; i0++ {

				err = _is.Read_int8(&st.Biz_rsp[i0], 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {

			err, _ = _is.SkipTo(jce.BYTE, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_slice_int8(&st.Biz_rsp, length, true)
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	err, have = _is.SkipTo(jce.MAP, 4, false)
	if err != nil {
		return err
	}

	if have {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.DumpRsp = make(map[int32]DumpBussinessRsp)
		for i1, e1 := int32(0), length; i1 < e1; i1++ {
			var k1 int32
			var v1 DumpBussinessRsp

			err = _is.Read_int32(&k1, 0, false)
			if err != nil {
				return err
			}

			err = v1.ReadBlock(_is, 1, false)
			if err != nil {
				return err
			}

			st.DumpRsp[k1] = v1
		}
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *FileCommitRsp) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require FileCommitRsp, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *FileCommitRsp) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = st.Result.WriteBlock(_os, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Session, 2)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.SIMPLE_LIST, 3)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.BYTE, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Biz_rsp)), 0)
	if err != nil {
		return err
	}

	err = _os.Write_slice_int8(st.Biz_rsp)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.MAP, 4)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.DumpRsp)), 0)
	if err != nil {
		return err
	}

	for k2, v2 := range st.DumpRsp {

		err = _os.Write_int32(k2, 0)
		if err != nil {
			return err
		}

		err = v2.WriteBlock(_os, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *FileCommitRsp) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *FileCommitRsp) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *FileCommitRsp) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *FileCommitRsp) WupTypeName() string {
	return "SLICE_UPLOAD.FileCommitRsp"
}

// FileBatchCommitReq struct implement
type FileBatchCommitReq struct {
	Commit_req map[string]FileCommitReq `json:"commit_req"`
}

func (st *FileBatchCommitReq) MakesureNotNil() {
}
func (st *FileBatchCommitReq) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *FileBatchCommitReq) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have = _is.SkipTo(jce.MAP, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&length, 0, true)
	if err != nil {
		return err
	}

	st.Commit_req = make(map[string]FileCommitReq)
	for i0, e0 := int32(0), length; i0 < e0; i0++ {
		var k0 string
		var v0 FileCommitReq

		err = _is.Read_string(&k0, 0, false)
		if err != nil {
			return err
		}

		err = v0.ReadBlock(_is, 1, false)
		if err != nil {
			return err
		}

		st.Commit_req[k0] = v0
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *FileBatchCommitReq) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require FileBatchCommitReq, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *FileBatchCommitReq) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.MAP, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Commit_req)), 0)
	if err != nil {
		return err
	}

	for k1, v1 := range st.Commit_req {

		err = _os.Write_string(k1, 0)
		if err != nil {
			return err
		}

		err = v1.WriteBlock(_os, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *FileBatchCommitReq) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *FileBatchCommitReq) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *FileBatchCommitReq) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *FileBatchCommitReq) WupTypeName() string {
	return "SLICE_UPLOAD.FileBatchCommitReq"
}

// FileBatchCommitRsp struct implement
type FileBatchCommitRsp struct {
	Commit_rsp map[string]FileCommitRsp `json:"commit_rsp"`
}

func (st *FileBatchCommitRsp) MakesureNotNil() {
}
func (st *FileBatchCommitRsp) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *FileBatchCommitRsp) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have = _is.SkipTo(jce.MAP, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&length, 0, true)
	if err != nil {
		return err
	}

	st.Commit_rsp = make(map[string]FileCommitRsp)
	for i0, e0 := int32(0), length; i0 < e0; i0++ {
		var k0 string
		var v0 FileCommitRsp

		err = _is.Read_string(&k0, 0, false)
		if err != nil {
			return err
		}

		err = v0.ReadBlock(_is, 1, false)
		if err != nil {
			return err
		}

		st.Commit_rsp[k0] = v0
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *FileBatchCommitRsp) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require FileBatchCommitRsp, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *FileBatchCommitRsp) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.MAP, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Commit_rsp)), 0)
	if err != nil {
		return err
	}

	for k1, v1 := range st.Commit_rsp {

		err = _os.Write_string(k1, 0)
		if err != nil {
			return err
		}

		err = v1.WriteBlock(_os, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *FileBatchCommitRsp) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *FileBatchCommitRsp) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *FileBatchCommitRsp) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *FileBatchCommitRsp) WupTypeName() string {
	return "SLICE_UPLOAD.FileBatchCommitRsp"
}
