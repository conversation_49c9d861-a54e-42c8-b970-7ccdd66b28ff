// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v1/model/circuitbreaker.proto

package polarismodel_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 需要进行熔断的资源
// 支持SUBSET（子集群），以及INSTANCE（单个实例），默认为SUBSET
type DestinationSet_Resource int32

const (
	// 针对QPS进行限流
	DestinationSet_SUBSET DestinationSet_Resource = 0
	// 针对并发数进行限流
	DestinationSet_INSTANCE DestinationSet_Resource = 1
)

// Enum value maps for DestinationSet_Resource.
var (
	DestinationSet_Resource_name = map[int32]string{
		0: "SUBSET",
		1: "INSTANCE",
	}
	DestinationSet_Resource_value = map[string]int32{
		"SUBSET":   0,
		"INSTANCE": 1,
	}
)

func (x DestinationSet_Resource) Enum() *DestinationSet_Resource {
	p := new(DestinationSet_Resource)
	*p = x
	return p
}

func (x DestinationSet_Resource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DestinationSet_Resource) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_circuitbreaker_proto_enumTypes[0].Descriptor()
}

func (DestinationSet_Resource) Type() protoreflect.EnumType {
	return &file_v1_model_circuitbreaker_proto_enumTypes[0]
}

func (x DestinationSet_Resource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DestinationSet_Resource.Descriptor instead.
func (DestinationSet_Resource) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_proto_rawDescGZIP(), []int{4, 0}
}

// 熔断决策类型，支持GLOBAL（分布式决策）以及LOCAL(本地决策），默认GLOBAL
// 当指定为GLOBAL时，则会定期上报统计数据并根据汇总数据进行熔断决策
type DestinationSet_Type int32

const (
	DestinationSet_GLOBAL DestinationSet_Type = 0
	DestinationSet_LOCAL  DestinationSet_Type = 1
)

// Enum value maps for DestinationSet_Type.
var (
	DestinationSet_Type_name = map[int32]string{
		0: "GLOBAL",
		1: "LOCAL",
	}
	DestinationSet_Type_value = map[string]int32{
		"GLOBAL": 0,
		"LOCAL":  1,
	}
)

func (x DestinationSet_Type) Enum() *DestinationSet_Type {
	p := new(DestinationSet_Type)
	*p = x
	return p
}

func (x DestinationSet_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DestinationSet_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_circuitbreaker_proto_enumTypes[1].Descriptor()
}

func (DestinationSet_Type) Type() protoreflect.EnumType {
	return &file_v1_model_circuitbreaker_proto_enumTypes[1]
}

func (x DestinationSet_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DestinationSet_Type.Descriptor instead.
func (DestinationSet_Type) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_proto_rawDescGZIP(), []int{4, 1}
}

//熔断范围，针对所有labels的熔断，还是针对单个熔断
type DestinationSet_Scope int32

const (
	//触发熔断条件，熔断所有的labels接口
	DestinationSet_ALL DestinationSet_Scope = 0
	//触发熔断条件，只熔断当前labels接口
	DestinationSet_LABELS DestinationSet_Scope = 1
)

// Enum value maps for DestinationSet_Scope.
var (
	DestinationSet_Scope_name = map[int32]string{
		0: "ALL",
		1: "LABELS",
	}
	DestinationSet_Scope_value = map[string]int32{
		"ALL":    0,
		"LABELS": 1,
	}
)

func (x DestinationSet_Scope) Enum() *DestinationSet_Scope {
	p := new(DestinationSet_Scope)
	*p = x
	return p
}

func (x DestinationSet_Scope) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DestinationSet_Scope) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_circuitbreaker_proto_enumTypes[2].Descriptor()
}

func (DestinationSet_Scope) Type() protoreflect.EnumType {
	return &file_v1_model_circuitbreaker_proto_enumTypes[2]
}

func (x DestinationSet_Scope) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DestinationSet_Scope.Descriptor instead.
func (DestinationSet_Scope) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_proto_rawDescGZIP(), []int{4, 2}
}

//单个熔断规则定义
type CircuitBreaker struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 规则版本
	Version *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// 规则名
	Name *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 规则命名空间
	Namespace *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 规则所属服务
	Service          *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=service,proto3" json:"service,omitempty"`
	ServiceNamespace *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=service_namespace,json=serviceNamespace,proto3" json:"service_namespace,omitempty"`
	//熔断规则可以分为被调规则和主调规则
	//被调规则针对所有的指定主调生效，假如不指定则对所有的主调生效
	//主调规则为当前主调方的规则，假如不指定则针对所有被调生效
	Inbounds  []*CbRule               `protobuf:"bytes,7,rep,name=inbounds,proto3" json:"inbounds,omitempty"`
	Outbounds []*CbRule               `protobuf:"bytes,8,rep,name=outbounds,proto3" json:"outbounds,omitempty"`
	Token     *wrapperspb.StringValue `protobuf:"bytes,9,opt,name=token,proto3" json:"token,omitempty"`
	Owners    *wrapperspb.StringValue `protobuf:"bytes,10,opt,name=owners,proto3" json:"owners,omitempty"`
	// 业务
	Business *wrapperspb.StringValue `protobuf:"bytes,11,opt,name=business,proto3" json:"business,omitempty"`
	// 部门
	Department *wrapperspb.StringValue `protobuf:"bytes,12,opt,name=department,proto3" json:"department,omitempty"`
	// 规则描述
	Comment  *wrapperspb.StringValue `protobuf:"bytes,13,opt,name=comment,proto3" json:"comment,omitempty"`
	Ctime    *wrapperspb.StringValue `protobuf:"bytes,14,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Mtime    *wrapperspb.StringValue `protobuf:"bytes,15,opt,name=mtime,proto3" json:"mtime,omitempty"`
	Revision *wrapperspb.StringValue `protobuf:"bytes,16,opt,name=revision,proto3" json:"revision,omitempty"`
}

func (x *CircuitBreaker) Reset() {
	*x = CircuitBreaker{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreaker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreaker) ProtoMessage() {}

func (x *CircuitBreaker) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreaker.ProtoReflect.Descriptor instead.
func (*CircuitBreaker) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_proto_rawDescGZIP(), []int{0}
}

func (x *CircuitBreaker) GetId() *wrapperspb.StringValue {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *CircuitBreaker) GetVersion() *wrapperspb.StringValue {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *CircuitBreaker) GetName() *wrapperspb.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *CircuitBreaker) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *CircuitBreaker) GetService() *wrapperspb.StringValue {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *CircuitBreaker) GetServiceNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.ServiceNamespace
	}
	return nil
}

func (x *CircuitBreaker) GetInbounds() []*CbRule {
	if x != nil {
		return x.Inbounds
	}
	return nil
}

func (x *CircuitBreaker) GetOutbounds() []*CbRule {
	if x != nil {
		return x.Outbounds
	}
	return nil
}

func (x *CircuitBreaker) GetToken() *wrapperspb.StringValue {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *CircuitBreaker) GetOwners() *wrapperspb.StringValue {
	if x != nil {
		return x.Owners
	}
	return nil
}

func (x *CircuitBreaker) GetBusiness() *wrapperspb.StringValue {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *CircuitBreaker) GetDepartment() *wrapperspb.StringValue {
	if x != nil {
		return x.Department
	}
	return nil
}

func (x *CircuitBreaker) GetComment() *wrapperspb.StringValue {
	if x != nil {
		return x.Comment
	}
	return nil
}

func (x *CircuitBreaker) GetCtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Ctime
	}
	return nil
}

func (x *CircuitBreaker) GetMtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Mtime
	}
	return nil
}

func (x *CircuitBreaker) GetRevision() *wrapperspb.StringValue {
	if x != nil {
		return x.Revision
	}
	return nil
}

// 主调匹配规则
type SourceMatcher struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 主调命名空间以及服务名，可以为*，代表全匹配
	Service   *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	Namespace *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 可选，主调业务标签，用于匹配是否使用该熔断规则，可放置用户的接口信息等
	Labels map[string]*MatchString `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SourceMatcher) Reset() {
	*x = SourceMatcher{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceMatcher) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceMatcher) ProtoMessage() {}

func (x *SourceMatcher) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceMatcher.ProtoReflect.Descriptor instead.
func (*SourceMatcher) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_proto_rawDescGZIP(), []int{1}
}

func (x *SourceMatcher) GetService() *wrapperspb.StringValue {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *SourceMatcher) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *SourceMatcher) GetLabels() map[string]*MatchString {
	if x != nil {
		return x.Labels
	}
	return nil
}

// 熔断恢复配置
type RecoverConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 触发熔断后到半开状态之间的等待间隔
	SleepWindow *durationpb.Duration `protobuf:"bytes,1,opt,name=sleepWindow,proto3" json:"sleepWindow,omitempty"`
	// 半开后，最多重试多少次恢复
	MaxRetryAfterHalfOpen *wrapperspb.UInt32Value `protobuf:"bytes,2,opt,name=maxRetryAfterHalfOpen,proto3" json:"maxRetryAfterHalfOpen,omitempty"`
	// 半开后放量的百分比
	RequestRateAfterHalfOpen []*wrapperspb.UInt32Value `protobuf:"bytes,3,rep,name=requestRateAfterHalfOpen,proto3" json:"requestRateAfterHalfOpen,omitempty"`
	// 熔断器半开到关闭所必须的最少成功率
	SuccessRateToClose *wrapperspb.UInt32Value `protobuf:"bytes,4,opt,name=successRateToClose,proto3" json:"successRateToClose,omitempty"`
}

func (x *RecoverConfig) Reset() {
	*x = RecoverConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecoverConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecoverConfig) ProtoMessage() {}

func (x *RecoverConfig) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecoverConfig.ProtoReflect.Descriptor instead.
func (*RecoverConfig) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_proto_rawDescGZIP(), []int{2}
}

func (x *RecoverConfig) GetSleepWindow() *durationpb.Duration {
	if x != nil {
		return x.SleepWindow
	}
	return nil
}

func (x *RecoverConfig) GetMaxRetryAfterHalfOpen() *wrapperspb.UInt32Value {
	if x != nil {
		return x.MaxRetryAfterHalfOpen
	}
	return nil
}

func (x *RecoverConfig) GetRequestRateAfterHalfOpen() []*wrapperspb.UInt32Value {
	if x != nil {
		return x.RequestRateAfterHalfOpen
	}
	return nil
}

func (x *RecoverConfig) GetSuccessRateToClose() *wrapperspb.UInt32Value {
	if x != nil {
		return x.SuccessRateToClose
	}
	return nil
}

// 熔断策略
type CbPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorRate *CbPolicy_ErrRateConfig  `protobuf:"bytes,1,opt,name=errorRate,proto3" json:"errorRate,omitempty"`
	SlowRate  *CbPolicy_SlowRateConfig `protobuf:"bytes,2,opt,name=slowRate,proto3" json:"slowRate,omitempty"`
	// 熔断的决策周期，多久触发一次熔断决策
	JudgeDuration *durationpb.Duration `protobuf:"bytes,3,opt,name=judgeDuration,proto3" json:"judgeDuration,omitempty"`
	//最大熔断比例，超过多少比例后不会继续熔断
	MaxEjectionPercent *wrapperspb.UInt32Value `protobuf:"bytes,4,opt,name=maxEjectionPercent,proto3" json:"maxEjectionPercent,omitempty"`
}

func (x *CbPolicy) Reset() {
	*x = CbPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CbPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CbPolicy) ProtoMessage() {}

func (x *CbPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CbPolicy.ProtoReflect.Descriptor instead.
func (*CbPolicy) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_proto_rawDescGZIP(), []int{3}
}

func (x *CbPolicy) GetErrorRate() *CbPolicy_ErrRateConfig {
	if x != nil {
		return x.ErrorRate
	}
	return nil
}

func (x *CbPolicy) GetSlowRate() *CbPolicy_SlowRateConfig {
	if x != nil {
		return x.SlowRate
	}
	return nil
}

func (x *CbPolicy) GetJudgeDuration() *durationpb.Duration {
	if x != nil {
		return x.JudgeDuration
	}
	return nil
}

func (x *CbPolicy) GetMaxEjectionPercent() *wrapperspb.UInt32Value {
	if x != nil {
		return x.MaxEjectionPercent
	}
	return nil
}

// 目标set的规则
type DestinationSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 被调命名空间以及服务名，可以为*，代表全匹配
	Service   *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	Namespace *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 可选，SUBSET标识
	Metadata map[string]*MatchString `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Resource DestinationSet_Resource `protobuf:"varint,4,opt,name=resource,proto3,enum=api.v1.model.DestinationSet_Resource" json:"resource,omitempty"`
	Type     DestinationSet_Type     `protobuf:"varint,5,opt,name=type,proto3,enum=api.v1.model.DestinationSet_Type" json:"type,omitempty"`
	Scope    DestinationSet_Scope    `protobuf:"varint,6,opt,name=scope,proto3,enum=api.v1.model.DestinationSet_Scope" json:"scope,omitempty"`
	// 熔断数据度量周期
	// 所有的阈值指标按此周期进行统计
	MetricWindow *durationpb.Duration `protobuf:"bytes,7,opt,name=metricWindow,proto3" json:"metricWindow,omitempty"`
	// 熔断数据统计精度，决定数据度量的最小周期
	// 度量滑窗的步长=window/precision
	MetricPrecision *wrapperspb.UInt32Value `protobuf:"bytes,8,opt,name=metricPrecision,proto3" json:"metricPrecision,omitempty"`
	// 数据上报周期
	UpdateInterval *durationpb.Duration `protobuf:"bytes,9,opt,name=updateInterval,proto3" json:"updateInterval,omitempty"`
	// 触发熔断后恢复配置
	Recover *RecoverConfig `protobuf:"bytes,10,opt,name=recover,proto3" json:"recover,omitempty"`
	// 熔断策略
	Policy *CbPolicy `protobuf:"bytes,11,opt,name=policy,proto3" json:"policy,omitempty"`
}

func (x *DestinationSet) Reset() {
	*x = DestinationSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DestinationSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DestinationSet) ProtoMessage() {}

func (x *DestinationSet) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DestinationSet.ProtoReflect.Descriptor instead.
func (*DestinationSet) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_proto_rawDescGZIP(), []int{4}
}

func (x *DestinationSet) GetService() *wrapperspb.StringValue {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *DestinationSet) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *DestinationSet) GetMetadata() map[string]*MatchString {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *DestinationSet) GetResource() DestinationSet_Resource {
	if x != nil {
		return x.Resource
	}
	return DestinationSet_SUBSET
}

func (x *DestinationSet) GetType() DestinationSet_Type {
	if x != nil {
		return x.Type
	}
	return DestinationSet_GLOBAL
}

func (x *DestinationSet) GetScope() DestinationSet_Scope {
	if x != nil {
		return x.Scope
	}
	return DestinationSet_ALL
}

func (x *DestinationSet) GetMetricWindow() *durationpb.Duration {
	if x != nil {
		return x.MetricWindow
	}
	return nil
}

func (x *DestinationSet) GetMetricPrecision() *wrapperspb.UInt32Value {
	if x != nil {
		return x.MetricPrecision
	}
	return nil
}

func (x *DestinationSet) GetUpdateInterval() *durationpb.Duration {
	if x != nil {
		return x.UpdateInterval
	}
	return nil
}

func (x *DestinationSet) GetRecover() *RecoverConfig {
	if x != nil {
		return x.Recover
	}
	return nil
}

func (x *DestinationSet) GetPolicy() *CbPolicy {
	if x != nil {
		return x.Policy
	}
	return nil
}

// 具体熔断规则
type CbRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 如果匹配Source规则，按照Destination进行熔断
	// 多个Source之间的关系为或
	Sources      []*SourceMatcher  `protobuf:"bytes,1,rep,name=sources,proto3" json:"sources,omitempty"`
	Destinations []*DestinationSet `protobuf:"bytes,2,rep,name=destinations,proto3" json:"destinations,omitempty"`
}

func (x *CbRule) Reset() {
	*x = CbRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CbRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CbRule) ProtoMessage() {}

func (x *CbRule) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CbRule.ProtoReflect.Descriptor instead.
func (*CbRule) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_proto_rawDescGZIP(), []int{5}
}

func (x *CbRule) GetSources() []*SourceMatcher {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *CbRule) GetDestinations() []*DestinationSet {
	if x != nil {
		return x.Destinations
	}
	return nil
}

// 错误率熔断配置
type CbPolicy_ErrRateConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//是否启用错误率配置
	Enable *wrapperspb.BoolValue `protobuf:"bytes,1,opt,name=enable,proto3" json:"enable,omitempty"`
	// 触发错误率熔断的最低请求阈值
	RequestVolumeThreshold *wrapperspb.UInt32Value `protobuf:"bytes,2,opt,name=requestVolumeThreshold,proto3" json:"requestVolumeThreshold,omitempty"`
	// 可选。触发保持状态的错误率阈值，假如不配置，则默认不会进入Preserved状态
	ErrorRateToPreserved *wrapperspb.UInt32Value `protobuf:"bytes,3,opt,name=errorRateToPreserved,proto3" json:"errorRateToPreserved,omitempty"`
	// 触发熔断的错误率阈值
	ErrorRateToOpen *wrapperspb.UInt32Value                 `protobuf:"bytes,4,opt,name=errorRateToOpen,proto3" json:"errorRateToOpen,omitempty"`
	Specials        []*CbPolicy_ErrRateConfig_SpecialConfig `protobuf:"bytes,5,rep,name=specials,proto3" json:"specials,omitempty"`
}

func (x *CbPolicy_ErrRateConfig) Reset() {
	*x = CbPolicy_ErrRateConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CbPolicy_ErrRateConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CbPolicy_ErrRateConfig) ProtoMessage() {}

func (x *CbPolicy_ErrRateConfig) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CbPolicy_ErrRateConfig.ProtoReflect.Descriptor instead.
func (*CbPolicy_ErrRateConfig) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_proto_rawDescGZIP(), []int{3, 0}
}

func (x *CbPolicy_ErrRateConfig) GetEnable() *wrapperspb.BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

func (x *CbPolicy_ErrRateConfig) GetRequestVolumeThreshold() *wrapperspb.UInt32Value {
	if x != nil {
		return x.RequestVolumeThreshold
	}
	return nil
}

func (x *CbPolicy_ErrRateConfig) GetErrorRateToPreserved() *wrapperspb.UInt32Value {
	if x != nil {
		return x.ErrorRateToPreserved
	}
	return nil
}

func (x *CbPolicy_ErrRateConfig) GetErrorRateToOpen() *wrapperspb.UInt32Value {
	if x != nil {
		return x.ErrorRateToOpen
	}
	return nil
}

func (x *CbPolicy_ErrRateConfig) GetSpecials() []*CbPolicy_ErrRateConfig_SpecialConfig {
	if x != nil {
		return x.Specials
	}
	return nil
}

// 慢调用率熔断策略配置
type CbPolicy_SlowRateConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否启用慢调用率配置
	Enable *wrapperspb.BoolValue `protobuf:"bytes,1,opt,name=enable,proto3" json:"enable,omitempty"`
	// 最大响应时间，超过该时间属于慢调用请求
	MaxRt *durationpb.Duration `protobuf:"bytes,2,opt,name=maxRt,proto3" json:"maxRt,omitempty"`
	// 可选。触发保持状态的超时率阈值，假如不配置，则默认不会进入Preserved状态
	SlowRateToPreserved *wrapperspb.UInt32Value `protobuf:"bytes,3,opt,name=slowRateToPreserved,proto3" json:"slowRateToPreserved,omitempty"`
	// 触发熔断的超时率阈值
	SlowRateToOpen *wrapperspb.UInt32Value `protobuf:"bytes,4,opt,name=slowRateToOpen,proto3" json:"slowRateToOpen,omitempty"`
}

func (x *CbPolicy_SlowRateConfig) Reset() {
	*x = CbPolicy_SlowRateConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CbPolicy_SlowRateConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CbPolicy_SlowRateConfig) ProtoMessage() {}

func (x *CbPolicy_SlowRateConfig) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CbPolicy_SlowRateConfig.ProtoReflect.Descriptor instead.
func (*CbPolicy_SlowRateConfig) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_proto_rawDescGZIP(), []int{3, 1}
}

func (x *CbPolicy_SlowRateConfig) GetEnable() *wrapperspb.BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

func (x *CbPolicy_SlowRateConfig) GetMaxRt() *durationpb.Duration {
	if x != nil {
		return x.MaxRt
	}
	return nil
}

func (x *CbPolicy_SlowRateConfig) GetSlowRateToPreserved() *wrapperspb.UInt32Value {
	if x != nil {
		return x.SlowRateToPreserved
	}
	return nil
}

func (x *CbPolicy_SlowRateConfig) GetSlowRateToOpen() *wrapperspb.UInt32Value {
	if x != nil {
		return x.SlowRateToOpen
	}
	return nil
}

//错误码相关特定配置
type CbPolicy_ErrRateConfig_SpecialConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 熔断关心的错误类型，用户可以自己定义
	Type                 *wrapperspb.StringValue  `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	ErrorCodes           []*wrapperspb.Int64Value `protobuf:"bytes,2,rep,name=errorCodes,proto3" json:"errorCodes,omitempty"`
	ErrorRateToPreserved *wrapperspb.UInt32Value  `protobuf:"bytes,3,opt,name=errorRateToPreserved,proto3" json:"errorRateToPreserved,omitempty"`
	ErrorRateToOpen      *wrapperspb.UInt32Value  `protobuf:"bytes,4,opt,name=errorRateToOpen,proto3" json:"errorRateToOpen,omitempty"`
}

func (x *CbPolicy_ErrRateConfig_SpecialConfig) Reset() {
	*x = CbPolicy_ErrRateConfig_SpecialConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_circuitbreaker_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CbPolicy_ErrRateConfig_SpecialConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CbPolicy_ErrRateConfig_SpecialConfig) ProtoMessage() {}

func (x *CbPolicy_ErrRateConfig_SpecialConfig) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_circuitbreaker_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CbPolicy_ErrRateConfig_SpecialConfig.ProtoReflect.Descriptor instead.
func (*CbPolicy_ErrRateConfig_SpecialConfig) Descriptor() ([]byte, []int) {
	return file_v1_model_circuitbreaker_proto_rawDescGZIP(), []int{3, 0, 0}
}

func (x *CbPolicy_ErrRateConfig_SpecialConfig) GetType() *wrapperspb.StringValue {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *CbPolicy_ErrRateConfig_SpecialConfig) GetErrorCodes() []*wrapperspb.Int64Value {
	if x != nil {
		return x.ErrorCodes
	}
	return nil
}

func (x *CbPolicy_ErrRateConfig_SpecialConfig) GetErrorRateToPreserved() *wrapperspb.UInt32Value {
	if x != nil {
		return x.ErrorRateToPreserved
	}
	return nil
}

func (x *CbPolicy_ErrRateConfig_SpecialConfig) GetErrorRateToOpen() *wrapperspb.UInt32Value {
	if x != nil {
		return x.ErrorRateToOpen
	}
	return nil
}

var File_v1_model_circuitbreaker_proto protoreflect.FileDescriptor

var file_v1_model_circuitbreaker_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x69, 0x72, 0x63, 0x75,
	0x69, 0x74, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x1e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77,
	0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x76,
	0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x89, 0x07, 0x0a, 0x0e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42,
	0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3a,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x36, 0x0a, 0x07, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x49, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x30, 0x0a,
	0x08, 0x69, 0x6e, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43,
	0x62, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x69, 0x6e, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x12,
	0x32, 0x0a, 0x09, 0x6f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x43, 0x62, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x09, 0x6f, 0x75, 0x74, 0x62, 0x6f, 0x75,
	0x6e, 0x64, 0x73, 0x12, 0x32, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x34, 0x0a, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x38, 0x0a,
	0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x3c, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x36, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x32, 0x0a,
	0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x32, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05,
	0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0x9a, 0x02, 0x0a, 0x0d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x12, 0x36, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x54, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc8, 0x02, 0x0a,
	0x0d, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3b,
	0x0a, 0x0b, 0x73, 0x6c, 0x65, 0x65, 0x70, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b,
	0x73, 0x6c, 0x65, 0x65, 0x70, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x52, 0x0a, 0x15, 0x6d,
	0x61, 0x78, 0x52, 0x65, 0x74, 0x72, 0x79, 0x41, 0x66, 0x74, 0x65, 0x72, 0x48, 0x61, 0x6c, 0x66,
	0x4f, 0x70, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x15, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x74,
	0x72, 0x79, 0x41, 0x66, 0x74, 0x65, 0x72, 0x48, 0x61, 0x6c, 0x66, 0x4f, 0x70, 0x65, 0x6e, 0x12,
	0x58, 0x0a, 0x18, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x41, 0x66,
	0x74, 0x65, 0x72, 0x48, 0x61, 0x6c, 0x66, 0x4f, 0x70, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x18, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x41, 0x66, 0x74, 0x65,
	0x72, 0x48, 0x61, 0x6c, 0x66, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x4c, 0x0a, 0x12, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x52, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x12, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x61, 0x74, 0x65,
	0x54, 0x6f, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x22, 0xcf, 0x09, 0x0a, 0x08, 0x43, 0x62, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x12, 0x42, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x62, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e,
	0x45, 0x72, 0x72, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x12, 0x41, 0x0a, 0x08, 0x73, 0x6c, 0x6f, 0x77,
	0x52, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x62, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x2e, 0x53, 0x6c, 0x6f, 0x77, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x08, 0x73, 0x6c, 0x6f, 0x77, 0x52, 0x61, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x6a,
	0x75, 0x64, 0x67, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x6a,
	0x75, 0x64, 0x67, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4c, 0x0a, 0x12,
	0x6d, 0x61, 0x78, 0x45, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x12, 0x6d, 0x61, 0x78, 0x45, 0x6a, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x1a, 0x9e, 0x05, 0x0a, 0x0d, 0x45,
	0x72, 0x72, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x32, 0x0a, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42,
	0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x54, 0x0a, 0x16, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x16,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x50, 0x0a, 0x14, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52,
	0x61, 0x74, 0x65, 0x54, 0x6f, 0x50, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x14, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x50,
	0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x12, 0x46, 0x0a, 0x0f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x52, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x4f, 0x70, 0x65, 0x6e,
	0x12, 0x4e, 0x0a, 0x08, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x43, 0x62, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x45, 0x72, 0x72, 0x52, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x08, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x73,
	0x1a, 0x98, 0x02, 0x0a, 0x0d, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x73, 0x12, 0x50, 0x0a, 0x14, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x54, 0x6f,
	0x50, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x14, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x50, 0x72, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x64, 0x12, 0x46, 0x0a, 0x0f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65,
	0x54, 0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x52, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x1a, 0x8b, 0x02, 0x0a, 0x0e,
	0x53, 0x6c, 0x6f, 0x77, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x32,
	0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x6d, 0x61, 0x78, 0x52, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x6d, 0x61,
	0x78, 0x52, 0x74, 0x12, 0x4e, 0x0a, 0x13, 0x73, 0x6c, 0x6f, 0x77, 0x52, 0x61, 0x74, 0x65, 0x54,
	0x6f, 0x50, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x13,
	0x73, 0x6c, 0x6f, 0x77, 0x52, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x50, 0x72, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x64, 0x12, 0x44, 0x0a, 0x0e, 0x73, 0x6c, 0x6f, 0x77, 0x52, 0x61, 0x74, 0x65, 0x54,
	0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x73, 0x6c, 0x6f, 0x77, 0x52,
	0x61, 0x74, 0x65, 0x54, 0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x22, 0xec, 0x06, 0x0a, 0x0e, 0x44, 0x65,
	0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x12, 0x36, 0x0a, 0x07,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x46, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x41, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x35, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x38, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x2e,
	0x53, 0x63, 0x6f, 0x70, 0x65, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x0c,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x46, 0x0a, 0x0f, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x50, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x0f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x50, 0x72, 0x65, 0x63, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x35, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x2e, 0x0a,
	0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x62, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x1a, 0x56, 0x0a,
	0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x24, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x55, 0x42, 0x53, 0x45, 0x54, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x01, 0x22, 0x1d, 0x0a, 0x04, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x47, 0x4c, 0x4f, 0x42, 0x41, 0x4c, 0x10, 0x00, 0x12,
	0x09, 0x0a, 0x05, 0x4c, 0x4f, 0x43, 0x41, 0x4c, 0x10, 0x01, 0x22, 0x1c, 0x0a, 0x05, 0x53, 0x63,
	0x6f, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06,
	0x4c, 0x41, 0x42, 0x45, 0x4c, 0x53, 0x10, 0x01, 0x22, 0x81, 0x01, 0x0a, 0x06, 0x43, 0x62, 0x52,
	0x75, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x52, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x0c, 0x64, 0x65,
	0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x52, 0x0c,
	0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x45, 0x5a, 0x43,
	0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61,
	0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x3b, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v1_model_circuitbreaker_proto_rawDescOnce sync.Once
	file_v1_model_circuitbreaker_proto_rawDescData = file_v1_model_circuitbreaker_proto_rawDesc
)

func file_v1_model_circuitbreaker_proto_rawDescGZIP() []byte {
	file_v1_model_circuitbreaker_proto_rawDescOnce.Do(func() {
		file_v1_model_circuitbreaker_proto_rawDescData = protoimpl.X.CompressGZIP(file_v1_model_circuitbreaker_proto_rawDescData)
	})
	return file_v1_model_circuitbreaker_proto_rawDescData
}

var file_v1_model_circuitbreaker_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_v1_model_circuitbreaker_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_v1_model_circuitbreaker_proto_goTypes = []interface{}{
	(DestinationSet_Resource)(0),                 // 0: api.v1.model.DestinationSet.Resource
	(DestinationSet_Type)(0),                     // 1: api.v1.model.DestinationSet.Type
	(DestinationSet_Scope)(0),                    // 2: api.v1.model.DestinationSet.Scope
	(*CircuitBreaker)(nil),                       // 3: api.v1.model.CircuitBreaker
	(*SourceMatcher)(nil),                        // 4: api.v1.model.SourceMatcher
	(*RecoverConfig)(nil),                        // 5: api.v1.model.RecoverConfig
	(*CbPolicy)(nil),                             // 6: api.v1.model.CbPolicy
	(*DestinationSet)(nil),                       // 7: api.v1.model.DestinationSet
	(*CbRule)(nil),                               // 8: api.v1.model.CbRule
	nil,                                          // 9: api.v1.model.SourceMatcher.LabelsEntry
	(*CbPolicy_ErrRateConfig)(nil),               // 10: api.v1.model.CbPolicy.ErrRateConfig
	(*CbPolicy_SlowRateConfig)(nil),              // 11: api.v1.model.CbPolicy.SlowRateConfig
	(*CbPolicy_ErrRateConfig_SpecialConfig)(nil), // 12: api.v1.model.CbPolicy.ErrRateConfig.SpecialConfig
	nil,                            // 13: api.v1.model.DestinationSet.MetadataEntry
	(*wrapperspb.StringValue)(nil), // 14: google.protobuf.StringValue
	(*durationpb.Duration)(nil),    // 15: google.protobuf.Duration
	(*wrapperspb.UInt32Value)(nil), // 16: google.protobuf.UInt32Value
	(*MatchString)(nil),            // 17: api.v1.model.MatchString
	(*wrapperspb.BoolValue)(nil),   // 18: google.protobuf.BoolValue
	(*wrapperspb.Int64Value)(nil),  // 19: google.protobuf.Int64Value
}
var file_v1_model_circuitbreaker_proto_depIdxs = []int32{
	14, // 0: api.v1.model.CircuitBreaker.id:type_name -> google.protobuf.StringValue
	14, // 1: api.v1.model.CircuitBreaker.version:type_name -> google.protobuf.StringValue
	14, // 2: api.v1.model.CircuitBreaker.name:type_name -> google.protobuf.StringValue
	14, // 3: api.v1.model.CircuitBreaker.namespace:type_name -> google.protobuf.StringValue
	14, // 4: api.v1.model.CircuitBreaker.service:type_name -> google.protobuf.StringValue
	14, // 5: api.v1.model.CircuitBreaker.service_namespace:type_name -> google.protobuf.StringValue
	8,  // 6: api.v1.model.CircuitBreaker.inbounds:type_name -> api.v1.model.CbRule
	8,  // 7: api.v1.model.CircuitBreaker.outbounds:type_name -> api.v1.model.CbRule
	14, // 8: api.v1.model.CircuitBreaker.token:type_name -> google.protobuf.StringValue
	14, // 9: api.v1.model.CircuitBreaker.owners:type_name -> google.protobuf.StringValue
	14, // 10: api.v1.model.CircuitBreaker.business:type_name -> google.protobuf.StringValue
	14, // 11: api.v1.model.CircuitBreaker.department:type_name -> google.protobuf.StringValue
	14, // 12: api.v1.model.CircuitBreaker.comment:type_name -> google.protobuf.StringValue
	14, // 13: api.v1.model.CircuitBreaker.ctime:type_name -> google.protobuf.StringValue
	14, // 14: api.v1.model.CircuitBreaker.mtime:type_name -> google.protobuf.StringValue
	14, // 15: api.v1.model.CircuitBreaker.revision:type_name -> google.protobuf.StringValue
	14, // 16: api.v1.model.SourceMatcher.service:type_name -> google.protobuf.StringValue
	14, // 17: api.v1.model.SourceMatcher.namespace:type_name -> google.protobuf.StringValue
	9,  // 18: api.v1.model.SourceMatcher.labels:type_name -> api.v1.model.SourceMatcher.LabelsEntry
	15, // 19: api.v1.model.RecoverConfig.sleepWindow:type_name -> google.protobuf.Duration
	16, // 20: api.v1.model.RecoverConfig.maxRetryAfterHalfOpen:type_name -> google.protobuf.UInt32Value
	16, // 21: api.v1.model.RecoverConfig.requestRateAfterHalfOpen:type_name -> google.protobuf.UInt32Value
	16, // 22: api.v1.model.RecoverConfig.successRateToClose:type_name -> google.protobuf.UInt32Value
	10, // 23: api.v1.model.CbPolicy.errorRate:type_name -> api.v1.model.CbPolicy.ErrRateConfig
	11, // 24: api.v1.model.CbPolicy.slowRate:type_name -> api.v1.model.CbPolicy.SlowRateConfig
	15, // 25: api.v1.model.CbPolicy.judgeDuration:type_name -> google.protobuf.Duration
	16, // 26: api.v1.model.CbPolicy.maxEjectionPercent:type_name -> google.protobuf.UInt32Value
	14, // 27: api.v1.model.DestinationSet.service:type_name -> google.protobuf.StringValue
	14, // 28: api.v1.model.DestinationSet.namespace:type_name -> google.protobuf.StringValue
	13, // 29: api.v1.model.DestinationSet.metadata:type_name -> api.v1.model.DestinationSet.MetadataEntry
	0,  // 30: api.v1.model.DestinationSet.resource:type_name -> api.v1.model.DestinationSet.Resource
	1,  // 31: api.v1.model.DestinationSet.type:type_name -> api.v1.model.DestinationSet.Type
	2,  // 32: api.v1.model.DestinationSet.scope:type_name -> api.v1.model.DestinationSet.Scope
	15, // 33: api.v1.model.DestinationSet.metricWindow:type_name -> google.protobuf.Duration
	16, // 34: api.v1.model.DestinationSet.metricPrecision:type_name -> google.protobuf.UInt32Value
	15, // 35: api.v1.model.DestinationSet.updateInterval:type_name -> google.protobuf.Duration
	5,  // 36: api.v1.model.DestinationSet.recover:type_name -> api.v1.model.RecoverConfig
	6,  // 37: api.v1.model.DestinationSet.policy:type_name -> api.v1.model.CbPolicy
	4,  // 38: api.v1.model.CbRule.sources:type_name -> api.v1.model.SourceMatcher
	7,  // 39: api.v1.model.CbRule.destinations:type_name -> api.v1.model.DestinationSet
	17, // 40: api.v1.model.SourceMatcher.LabelsEntry.value:type_name -> api.v1.model.MatchString
	18, // 41: api.v1.model.CbPolicy.ErrRateConfig.enable:type_name -> google.protobuf.BoolValue
	16, // 42: api.v1.model.CbPolicy.ErrRateConfig.requestVolumeThreshold:type_name -> google.protobuf.UInt32Value
	16, // 43: api.v1.model.CbPolicy.ErrRateConfig.errorRateToPreserved:type_name -> google.protobuf.UInt32Value
	16, // 44: api.v1.model.CbPolicy.ErrRateConfig.errorRateToOpen:type_name -> google.protobuf.UInt32Value
	12, // 45: api.v1.model.CbPolicy.ErrRateConfig.specials:type_name -> api.v1.model.CbPolicy.ErrRateConfig.SpecialConfig
	18, // 46: api.v1.model.CbPolicy.SlowRateConfig.enable:type_name -> google.protobuf.BoolValue
	15, // 47: api.v1.model.CbPolicy.SlowRateConfig.maxRt:type_name -> google.protobuf.Duration
	16, // 48: api.v1.model.CbPolicy.SlowRateConfig.slowRateToPreserved:type_name -> google.protobuf.UInt32Value
	16, // 49: api.v1.model.CbPolicy.SlowRateConfig.slowRateToOpen:type_name -> google.protobuf.UInt32Value
	14, // 50: api.v1.model.CbPolicy.ErrRateConfig.SpecialConfig.type:type_name -> google.protobuf.StringValue
	19, // 51: api.v1.model.CbPolicy.ErrRateConfig.SpecialConfig.errorCodes:type_name -> google.protobuf.Int64Value
	16, // 52: api.v1.model.CbPolicy.ErrRateConfig.SpecialConfig.errorRateToPreserved:type_name -> google.protobuf.UInt32Value
	16, // 53: api.v1.model.CbPolicy.ErrRateConfig.SpecialConfig.errorRateToOpen:type_name -> google.protobuf.UInt32Value
	17, // 54: api.v1.model.DestinationSet.MetadataEntry.value:type_name -> api.v1.model.MatchString
	55, // [55:55] is the sub-list for method output_type
	55, // [55:55] is the sub-list for method input_type
	55, // [55:55] is the sub-list for extension type_name
	55, // [55:55] is the sub-list for extension extendee
	0,  // [0:55] is the sub-list for field type_name
}

func init() { file_v1_model_circuitbreaker_proto_init() }
func file_v1_model_circuitbreaker_proto_init() {
	if File_v1_model_circuitbreaker_proto != nil {
		return
	}
	file_v1_model_model_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_v1_model_circuitbreaker_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreaker); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SourceMatcher); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecoverConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CbPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DestinationSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CbRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CbPolicy_ErrRateConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CbPolicy_SlowRateConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_circuitbreaker_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CbPolicy_ErrRateConfig_SpecialConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_model_circuitbreaker_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v1_model_circuitbreaker_proto_goTypes,
		DependencyIndexes: file_v1_model_circuitbreaker_proto_depIdxs,
		EnumInfos:         file_v1_model_circuitbreaker_proto_enumTypes,
		MessageInfos:      file_v1_model_circuitbreaker_proto_msgTypes,
	}.Build()
	File_v1_model_circuitbreaker_proto = out.File
	file_v1_model_circuitbreaker_proto_rawDesc = nil
	file_v1_model_circuitbreaker_proto_goTypes = nil
	file_v1_model_circuitbreaker_proto_depIdxs = nil
}
