// Code generated by protoc-gen-go-vtproto. DO NOT EDIT.
// protoc-gen-go-vtproto version: v0.5.0
// source: QQHead.proto

package sso_protos

import (
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	io "io"
	bits "math/bits"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

func (m *QQHead) MarshalVT() (dAtA []byte, err error) {
	if m == nil {
		return nil, nil
	}
	size := m.SizeVT()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBufferVT(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QQHead) MarshalToVT(dAtA []byte) (int, error) {
	size := m.SizeVT()
	return m.MarshalToSizedBufferVT(dAtA[:size])
}

func (m *QQHead) MarshalToSizedBufferVT(dAtA []byte) (int, error) {
	if m == nil {
		return 0, nil
	}
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.unknownFields != nil {
		i -= len(m.unknownFields)
		copy(dAtA[i:], m.unknownFields)
	}
	if m.BytesDummyBytes != nil {
		i -= len(m.BytesDummyBytes)
		copy(dAtA[i:], m.BytesDummyBytes)
		i = encodeVarint(dAtA, i, uint64(len(m.BytesDummyBytes)))
		i--
		dAtA[i] = 0x3e
		i--
		dAtA[i] = 0xba
	}
	if m.Uint32CmdCount != nil {
		i = encodeVarint(dAtA, i, uint64(*m.Uint32CmdCount))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xd8
	}
	if m.BytesCmd != nil {
		i -= len(m.BytesCmd)
		copy(dAtA[i:], m.BytesCmd)
		i = encodeVarint(dAtA, i, uint64(len(m.BytesCmd)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xd2
	}
	if m.BytesOpenid != nil {
		i -= len(m.BytesOpenid)
		copy(dAtA[i:], m.BytesOpenid)
		i = encodeVarint(dAtA, i, uint64(len(m.BytesOpenid)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xca
	}
	if m.MessageType != nil {
		i = encodeVarint(dAtA, i, uint64(*m.MessageType))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xc0
	}
	if m.Uint32NtCoreVersion != nil {
		i = encodeVarint(dAtA, i, uint64(*m.Uint32NtCoreVersion))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xb8
	}
	if m.SecureInfo != nil {
		size, err := m.SecureInfo.MarshalToSizedBufferVT(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarint(dAtA, i, uint64(size))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xb2
	}
	if m.Uint32InvalidAppid != nil {
		i = encodeVarint(dAtA, i, uint64(*m.Uint32InvalidAppid))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa8
	}
	if m.Uint32LoginType != nil {
		i = encodeVarint(dAtA, i, uint64(*m.Uint32LoginType))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa0
	}
	if m.BytesLoginSig != nil {
		i -= len(m.BytesLoginSig)
		copy(dAtA[i:], m.BytesLoginSig)
		i = encodeVarint(dAtA, i, uint64(len(m.BytesLoginSig)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x9a
	}
	if m.BoolIsNtProduct != nil {
		i--
		if *m.BoolIsNtProduct {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x90
	}
	if m.BytesTraceParent != nil {
		i -= len(m.BytesTraceParent)
		copy(dAtA[i:], m.BytesTraceParent)
		i = encodeVarint(dAtA, i, uint64(len(m.BytesTraceParent)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x8a
	}
	if m.BytesUid != nil {
		i -= len(m.BytesUid)
		copy(dAtA[i:], m.BytesUid)
		i = encodeVarint(dAtA, i, uint64(len(m.BytesUid)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	if m.BytesRouteEnv != nil {
		i -= len(m.BytesRouteEnv)
		copy(dAtA[i:], m.BytesRouteEnv)
		i = encodeVarint(dAtA, i, uint64(len(m.BytesRouteEnv)))
		i--
		dAtA[i] = 0x7a
	}
	if m.BytesClientSubPlat != nil {
		i -= len(m.BytesClientSubPlat)
		copy(dAtA[i:], m.BytesClientSubPlat)
		i = encodeVarint(dAtA, i, uint64(len(m.BytesClientSubPlat)))
		i--
		dAtA[i] = 0x72
	}
	if m.Uint32ClientPlatId != nil {
		i = encodeVarint(dAtA, i, uint64(*m.Uint32ClientPlatId))
		i--
		dAtA[i] = 0x68
	}
	if m.BytesQimei != nil {
		i -= len(m.BytesQimei)
		copy(dAtA[i:], m.BytesQimei)
		i = encodeVarint(dAtA, i, uint64(len(m.BytesQimei)))
		i--
		dAtA[i] = 0x62
	}
	if m.BytesClientGuid != nil {
		i -= len(m.BytesClientGuid)
		copy(dAtA[i:], m.BytesClientGuid)
		i = encodeVarint(dAtA, i, uint64(len(m.BytesClientGuid)))
		i--
		dAtA[i] = 0x5a
	}
	if m.BytesParentId != nil {
		i -= len(m.BytesParentId)
		copy(dAtA[i:], m.BytesParentId)
		i = encodeVarint(dAtA, i, uint64(len(m.BytesParentId)))
		i--
		dAtA[i] = 0x52
	}
	if m.BytesTraceId != nil {
		i -= len(m.BytesTraceId)
		copy(dAtA[i:], m.BytesTraceId)
		i = encodeVarint(dAtA, i, uint64(len(m.BytesTraceId)))
		i--
		dAtA[i] = 0x42
	}
	if m.Uint32LocaleId != nil {
		i = encodeVarint(dAtA, i, uint64(*m.Uint32LocaleId))
		i--
		dAtA[i] = 0x38
	}
	if m.Uint32RouteId != nil {
		i = encodeVarint(dAtA, i, uint64(*m.Uint32RouteId))
		i--
		dAtA[i] = 0x30
	}
	if m.Uint32ProductId != nil {
		i = encodeVarint(dAtA, i, uint64(*m.Uint32ProductId))
		i--
		dAtA[i] = 0x20
	}
	if m.BytesClientVersion != nil {
		i -= len(m.BytesClientVersion)
		copy(dAtA[i:], m.BytesClientVersion)
		i = encodeVarint(dAtA, i, uint64(len(m.BytesClientVersion)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Uint32ClientPlat != nil {
		i = encodeVarint(dAtA, i, uint64(*m.Uint32ClientPlat))
		i--
		dAtA[i] = 0x10
	}
	if m.BytesClientIp != nil {
		i -= len(m.BytesClientIp)
		copy(dAtA[i:], m.BytesClientIp)
		i = encodeVarint(dAtA, i, uint64(len(m.BytesClientIp)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LoginSig) MarshalVT() (dAtA []byte, err error) {
	if m == nil {
		return nil, nil
	}
	size := m.SizeVT()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBufferVT(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LoginSig) MarshalToVT(dAtA []byte) (int, error) {
	size := m.SizeVT()
	return m.MarshalToSizedBufferVT(dAtA[:size])
}

func (m *LoginSig) MarshalToSizedBufferVT(dAtA []byte) (int, error) {
	if m == nil {
		return 0, nil
	}
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.unknownFields != nil {
		i -= len(m.unknownFields)
		copy(dAtA[i:], m.unknownFields)
	}
	if m.Uint32Appid != nil {
		i = encodeVarint(dAtA, i, uint64(*m.Uint32Appid))
		i--
		dAtA[i] = 0x18
	}
	if m.BytesSig != nil {
		i -= len(m.BytesSig)
		copy(dAtA[i:], m.BytesSig)
		i = encodeVarint(dAtA, i, uint64(len(m.BytesSig)))
		i--
		dAtA[i] = 0x12
	}
	if m.Uint32Type != nil {
		i = encodeVarint(dAtA, i, uint64(*m.Uint32Type))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SecureInfo) MarshalVT() (dAtA []byte, err error) {
	if m == nil {
		return nil, nil
	}
	size := m.SizeVT()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBufferVT(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SecureInfo) MarshalToVT(dAtA []byte) (int, error) {
	size := m.SizeVT()
	return m.MarshalToSizedBufferVT(dAtA[:size])
}

func (m *SecureInfo) MarshalToSizedBufferVT(dAtA []byte) (int, error) {
	if m == nil {
		return 0, nil
	}
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.unknownFields != nil {
		i -= len(m.unknownFields)
		copy(dAtA[i:], m.unknownFields)
	}
	if m.SecMd5 != nil {
		i -= len(m.SecMd5)
		copy(dAtA[i:], m.SecMd5)
		i = encodeVarint(dAtA, i, uint64(len(m.SecMd5)))
		i--
		dAtA[i] = 0x22
	}
	if m.SecExtra != nil {
		i -= len(m.SecExtra)
		copy(dAtA[i:], m.SecExtra)
		i = encodeVarint(dAtA, i, uint64(len(m.SecExtra)))
		i--
		dAtA[i] = 0x1a
	}
	if m.SecDeviceToken != nil {
		i -= len(m.SecDeviceToken)
		copy(dAtA[i:], m.SecDeviceToken)
		i = encodeVarint(dAtA, i, uint64(len(m.SecDeviceToken)))
		i--
		dAtA[i] = 0x12
	}
	if m.SecSig != nil {
		i -= len(m.SecSig)
		copy(dAtA[i:], m.SecSig)
		i = encodeVarint(dAtA, i, uint64(len(m.SecSig)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarint(dAtA []byte, offset int, v uint64) int {
	offset -= sov(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *QQHead) SizeVT() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BytesClientIp != nil {
		l = len(m.BytesClientIp)
		n += 1 + l + sov(uint64(l))
	}
	if m.Uint32ClientPlat != nil {
		n += 1 + sov(uint64(*m.Uint32ClientPlat))
	}
	if m.BytesClientVersion != nil {
		l = len(m.BytesClientVersion)
		n += 1 + l + sov(uint64(l))
	}
	if m.Uint32ProductId != nil {
		n += 1 + sov(uint64(*m.Uint32ProductId))
	}
	if m.Uint32RouteId != nil {
		n += 1 + sov(uint64(*m.Uint32RouteId))
	}
	if m.Uint32LocaleId != nil {
		n += 1 + sov(uint64(*m.Uint32LocaleId))
	}
	if m.BytesTraceId != nil {
		l = len(m.BytesTraceId)
		n += 1 + l + sov(uint64(l))
	}
	if m.BytesParentId != nil {
		l = len(m.BytesParentId)
		n += 1 + l + sov(uint64(l))
	}
	if m.BytesClientGuid != nil {
		l = len(m.BytesClientGuid)
		n += 1 + l + sov(uint64(l))
	}
	if m.BytesQimei != nil {
		l = len(m.BytesQimei)
		n += 1 + l + sov(uint64(l))
	}
	if m.Uint32ClientPlatId != nil {
		n += 1 + sov(uint64(*m.Uint32ClientPlatId))
	}
	if m.BytesClientSubPlat != nil {
		l = len(m.BytesClientSubPlat)
		n += 1 + l + sov(uint64(l))
	}
	if m.BytesRouteEnv != nil {
		l = len(m.BytesRouteEnv)
		n += 1 + l + sov(uint64(l))
	}
	if m.BytesUid != nil {
		l = len(m.BytesUid)
		n += 2 + l + sov(uint64(l))
	}
	if m.BytesTraceParent != nil {
		l = len(m.BytesTraceParent)
		n += 2 + l + sov(uint64(l))
	}
	if m.BoolIsNtProduct != nil {
		n += 3
	}
	if m.BytesLoginSig != nil {
		l = len(m.BytesLoginSig)
		n += 2 + l + sov(uint64(l))
	}
	if m.Uint32LoginType != nil {
		n += 2 + sov(uint64(*m.Uint32LoginType))
	}
	if m.Uint32InvalidAppid != nil {
		n += 2 + sov(uint64(*m.Uint32InvalidAppid))
	}
	if m.SecureInfo != nil {
		l = m.SecureInfo.SizeVT()
		n += 2 + l + sov(uint64(l))
	}
	if m.Uint32NtCoreVersion != nil {
		n += 2 + sov(uint64(*m.Uint32NtCoreVersion))
	}
	if m.MessageType != nil {
		n += 2 + sov(uint64(*m.MessageType))
	}
	if m.BytesOpenid != nil {
		l = len(m.BytesOpenid)
		n += 2 + l + sov(uint64(l))
	}
	if m.BytesCmd != nil {
		l = len(m.BytesCmd)
		n += 2 + l + sov(uint64(l))
	}
	if m.Uint32CmdCount != nil {
		n += 2 + sov(uint64(*m.Uint32CmdCount))
	}
	if m.BytesDummyBytes != nil {
		l = len(m.BytesDummyBytes)
		n += 2 + l + sov(uint64(l))
	}
	n += len(m.unknownFields)
	return n
}

func (m *LoginSig) SizeVT() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Uint32Type != nil {
		n += 1 + sov(uint64(*m.Uint32Type))
	}
	if m.BytesSig != nil {
		l = len(m.BytesSig)
		n += 1 + l + sov(uint64(l))
	}
	if m.Uint32Appid != nil {
		n += 1 + sov(uint64(*m.Uint32Appid))
	}
	n += len(m.unknownFields)
	return n
}

func (m *SecureInfo) SizeVT() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.SecSig != nil {
		l = len(m.SecSig)
		n += 1 + l + sov(uint64(l))
	}
	if m.SecDeviceToken != nil {
		l = len(m.SecDeviceToken)
		n += 1 + l + sov(uint64(l))
	}
	if m.SecExtra != nil {
		l = len(m.SecExtra)
		n += 1 + l + sov(uint64(l))
	}
	if m.SecMd5 != nil {
		l = len(m.SecMd5)
		n += 1 + l + sov(uint64(l))
	}
	n += len(m.unknownFields)
	return n
}

func sov(x uint64) (n int) {
	return (bits.Len64(x|1) + 6) / 7
}
func soz(x uint64) (n int) {
	return sov(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *QQHead) UnmarshalVT(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: QQHead: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: QQHead: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesClientIp", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BytesClientIp = append(m.BytesClientIp[:0], dAtA[iNdEx:postIndex]...)
			if m.BytesClientIp == nil {
				m.BytesClientIp = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uint32ClientPlat", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Uint32ClientPlat = &v
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesClientVersion", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BytesClientVersion = append(m.BytesClientVersion[:0], dAtA[iNdEx:postIndex]...)
			if m.BytesClientVersion == nil {
				m.BytesClientVersion = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uint32ProductId", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Uint32ProductId = &v
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uint32RouteId", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Uint32RouteId = &v
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uint32LocaleId", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Uint32LocaleId = &v
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesTraceId", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BytesTraceId = append(m.BytesTraceId[:0], dAtA[iNdEx:postIndex]...)
			if m.BytesTraceId == nil {
				m.BytesTraceId = []byte{}
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesParentId", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BytesParentId = append(m.BytesParentId[:0], dAtA[iNdEx:postIndex]...)
			if m.BytesParentId == nil {
				m.BytesParentId = []byte{}
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesClientGuid", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BytesClientGuid = append(m.BytesClientGuid[:0], dAtA[iNdEx:postIndex]...)
			if m.BytesClientGuid == nil {
				m.BytesClientGuid = []byte{}
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesQimei", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BytesQimei = append(m.BytesQimei[:0], dAtA[iNdEx:postIndex]...)
			if m.BytesQimei == nil {
				m.BytesQimei = []byte{}
			}
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uint32ClientPlatId", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Uint32ClientPlatId = &v
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesClientSubPlat", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BytesClientSubPlat = append(m.BytesClientSubPlat[:0], dAtA[iNdEx:postIndex]...)
			if m.BytesClientSubPlat == nil {
				m.BytesClientSubPlat = []byte{}
			}
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesRouteEnv", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BytesRouteEnv = append(m.BytesRouteEnv[:0], dAtA[iNdEx:postIndex]...)
			if m.BytesRouteEnv == nil {
				m.BytesRouteEnv = []byte{}
			}
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesUid", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BytesUid = append(m.BytesUid[:0], dAtA[iNdEx:postIndex]...)
			if m.BytesUid == nil {
				m.BytesUid = []byte{}
			}
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesTraceParent", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BytesTraceParent = append(m.BytesTraceParent[:0], dAtA[iNdEx:postIndex]...)
			if m.BytesTraceParent == nil {
				m.BytesTraceParent = []byte{}
			}
			iNdEx = postIndex
		case 18:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BoolIsNtProduct", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.BoolIsNtProduct = &b
		case 19:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesLoginSig", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BytesLoginSig = append(m.BytesLoginSig[:0], dAtA[iNdEx:postIndex]...)
			if m.BytesLoginSig == nil {
				m.BytesLoginSig = []byte{}
			}
			iNdEx = postIndex
		case 20:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uint32LoginType", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Uint32LoginType = &v
		case 21:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uint32InvalidAppid", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Uint32InvalidAppid = &v
		case 22:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SecureInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.SecureInfo == nil {
				m.SecureInfo = &SecureInfo{}
			}
			if err := m.SecureInfo.UnmarshalVT(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 23:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uint32NtCoreVersion", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Uint32NtCoreVersion = &v
		case 24:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MessageType", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.MessageType = &v
		case 25:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesOpenid", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BytesOpenid = append(m.BytesOpenid[:0], dAtA[iNdEx:postIndex]...)
			if m.BytesOpenid == nil {
				m.BytesOpenid = []byte{}
			}
			iNdEx = postIndex
		case 26:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesCmd", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BytesCmd = append(m.BytesCmd[:0], dAtA[iNdEx:postIndex]...)
			if m.BytesCmd == nil {
				m.BytesCmd = []byte{}
			}
			iNdEx = postIndex
		case 27:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uint32CmdCount", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Uint32CmdCount = &v
		case 999:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesDummyBytes", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BytesDummyBytes = append(m.BytesDummyBytes[:0], dAtA[iNdEx:postIndex]...)
			if m.BytesDummyBytes == nil {
				m.BytesDummyBytes = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skip(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLength
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			if (fieldNum >= 1000) && (fieldNum < 2000) {
				err = proto.UnmarshalOptions{AllowPartial: true}.Unmarshal(dAtA[iNdEx:iNdEx+skippy], m)
				if err != nil {
					return err
				}
				iNdEx += skippy
			} else {
				m.unknownFields = append(m.unknownFields, dAtA[iNdEx:iNdEx+skippy]...)
				iNdEx += skippy
			}
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LoginSig) UnmarshalVT(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LoginSig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LoginSig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uint32Type", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Uint32Type = &v
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesSig", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BytesSig = append(m.BytesSig[:0], dAtA[iNdEx:postIndex]...)
			if m.BytesSig == nil {
				m.BytesSig = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uint32Appid", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Uint32Appid = &v
		default:
			iNdEx = preIndex
			skippy, err := skip(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLength
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.unknownFields = append(m.unknownFields, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SecureInfo) UnmarshalVT(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SecureInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SecureInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SecSig", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SecSig = append(m.SecSig[:0], dAtA[iNdEx:postIndex]...)
			if m.SecSig == nil {
				m.SecSig = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SecDeviceToken", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SecDeviceToken = append(m.SecDeviceToken[:0], dAtA[iNdEx:postIndex]...)
			if m.SecDeviceToken == nil {
				m.SecDeviceToken = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SecExtra", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SecExtra = append(m.SecExtra[:0], dAtA[iNdEx:postIndex]...)
			if m.SecExtra == nil {
				m.SecExtra = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SecMd5", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLength
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLength
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SecMd5 = append(m.SecMd5[:0], dAtA[iNdEx:postIndex]...)
			if m.SecMd5 == nil {
				m.SecMd5 = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skip(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLength
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.unknownFields = append(m.unknownFields, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}

func skip(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflow
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflow
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflow
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLength
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroup
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLength
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLength        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflow          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroup = fmt.Errorf("proto: unexpected end of group")
)
