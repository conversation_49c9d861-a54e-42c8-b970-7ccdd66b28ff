// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v1/model/routing.proto

package polarismodel_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 降级动作类型
type Route_DegradeAction int32

const (
	Route_RECOVER_ALL           Route_DegradeAction = 0 // 根据健康度全死全活
	Route_PART_TO_UNHEALTHY     Route_DegradeAction = 1 // 根据健康度将部分请求降级到不健康节点
	Route_TO_NEXT_PRIORITY      Route_DegradeAction = 2 // 全部请求降级到下一个优先级
	Route_PART_TO_NEXT_PRIORITY Route_DegradeAction = 3 // 根据健康度部分降级到下一个优先级
)

// Enum value maps for Route_DegradeAction.
var (
	Route_DegradeAction_name = map[int32]string{
		0: "RECOVER_ALL",
		1: "PART_TO_UNHEALTHY",
		2: "TO_NEXT_PRIORITY",
		3: "PART_TO_NEXT_PRIORITY",
	}
	Route_DegradeAction_value = map[string]int32{
		"RECOVER_ALL":           0,
		"PART_TO_UNHEALTHY":     1,
		"TO_NEXT_PRIORITY":      2,
		"PART_TO_NEXT_PRIORITY": 3,
	}
)

func (x Route_DegradeAction) Enum() *Route_DegradeAction {
	p := new(Route_DegradeAction)
	*p = x
	return p
}

func (x Route_DegradeAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Route_DegradeAction) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_routing_proto_enumTypes[0].Descriptor()
}

func (Route_DegradeAction) Type() protoreflect.EnumType {
	return &file_v1_model_routing_proto_enumTypes[0]
}

func (x Route_DegradeAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Route_DegradeAction.Descriptor instead.
func (Route_DegradeAction) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_routing_proto_rawDescGZIP(), []int{1, 0}
}

type Routing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 规则所属服务以及命名空间
	Service   *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	Namespace *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 每个服务可以配置多条入站或者出站规则
	// 对于每个请求，从上到下依次匹配，若命中则终止
	Inbounds     []*Route                `protobuf:"bytes,3,rep,name=inbounds,proto3" json:"inbounds,omitempty"`
	Outbounds    []*Route                `protobuf:"bytes,4,rep,name=outbounds,proto3" json:"outbounds,omitempty"`
	Ctime        *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Mtime        *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=mtime,proto3" json:"mtime,omitempty"`
	Revision     *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=revision,proto3" json:"revision,omitempty"`
	ServiceToken *wrapperspb.StringValue `protobuf:"bytes,8,opt,name=service_token,proto3" json:"service_token,omitempty"`
	// 就近路由配置
	NearbyInbounds  []*NearyRoute `protobuf:"bytes,9,rep,name=nearby_inbounds,json=nearbyInbounds,proto3" json:"nearby_inbounds,omitempty"`
	NearbyOutbounds []*NearyRoute `protobuf:"bytes,10,rep,name=nearby_outbounds,json=nearbyOutbounds,proto3" json:"nearby_outbounds,omitempty"`
}

func (x *Routing) Reset() {
	*x = Routing{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_routing_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Routing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Routing) ProtoMessage() {}

func (x *Routing) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_routing_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Routing.ProtoReflect.Descriptor instead.
func (*Routing) Descriptor() ([]byte, []int) {
	return file_v1_model_routing_proto_rawDescGZIP(), []int{0}
}

func (x *Routing) GetService() *wrapperspb.StringValue {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *Routing) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *Routing) GetInbounds() []*Route {
	if x != nil {
		return x.Inbounds
	}
	return nil
}

func (x *Routing) GetOutbounds() []*Route {
	if x != nil {
		return x.Outbounds
	}
	return nil
}

func (x *Routing) GetCtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Ctime
	}
	return nil
}

func (x *Routing) GetMtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Mtime
	}
	return nil
}

func (x *Routing) GetRevision() *wrapperspb.StringValue {
	if x != nil {
		return x.Revision
	}
	return nil
}

func (x *Routing) GetServiceToken() *wrapperspb.StringValue {
	if x != nil {
		return x.ServiceToken
	}
	return nil
}

func (x *Routing) GetNearbyInbounds() []*NearyRoute {
	if x != nil {
		return x.NearbyInbounds
	}
	return nil
}

func (x *Routing) GetNearbyOutbounds() []*NearyRoute {
	if x != nil {
		return x.NearbyOutbounds
	}
	return nil
}

type Route struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 如果匹配Source规则，按照Destination路由
	// 多个Source之间的关系为或
	Sources      []*Source      `protobuf:"bytes,1,rep,name=sources,proto3" json:"sources,omitempty"`
	Destinations []*Destination `protobuf:"bytes,2,rep,name=destinations,proto3" json:"destinations,omitempty"`
	// 健康阈值[0,100]，默认为0，无健康实例时全死全活或降级
	HealthThreshold *wrapperspb.UInt32Value `protobuf:"bytes,3,opt,name=health_threshold,json=healthThreshold,proto3" json:"health_threshold,omitempty"`
	DegradeAction   Route_DegradeAction     `protobuf:"varint,4,opt,name=degrade_action,json=degradeAction,proto3,enum=api.v1.model.Route_DegradeAction" json:"degrade_action,omitempty"`
	// 是否停用该路由规则，默认启用
	Disable *wrapperspb.BoolValue `protobuf:"bytes,5,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *Route) Reset() {
	*x = Route{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_routing_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Route) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Route) ProtoMessage() {}

func (x *Route) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_routing_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Route.ProtoReflect.Descriptor instead.
func (*Route) Descriptor() ([]byte, []int) {
	return file_v1_model_routing_proto_rawDescGZIP(), []int{1}
}

func (x *Route) GetSources() []*Source {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *Route) GetDestinations() []*Destination {
	if x != nil {
		return x.Destinations
	}
	return nil
}

func (x *Route) GetHealthThreshold() *wrapperspb.UInt32Value {
	if x != nil {
		return x.HealthThreshold
	}
	return nil
}

func (x *Route) GetDegradeAction() Route_DegradeAction {
	if x != nil {
		return x.DegradeAction
	}
	return Route_RECOVER_ALL
}

func (x *Route) GetDisable() *wrapperspb.BoolValue {
	if x != nil {
		return x.Disable
	}
	return nil
}

type Source struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 主调方服务以及命名空间
	Service   *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	Namespace *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 被调方服务以及命名空间,可以是真实服务名,也可以是服务别名
	ToService   *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=to_service,proto3" json:"to_service,omitempty"`
	ToNamespace *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=to_namespace,proto3" json:"to_namespace,omitempty"`
	// 主调方服务实例标签或者请求标签
	// value支持正则匹配
	Metadata map[string]*MatchString `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Source) Reset() {
	*x = Source{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_routing_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Source) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source) ProtoMessage() {}

func (x *Source) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_routing_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source.ProtoReflect.Descriptor instead.
func (*Source) Descriptor() ([]byte, []int) {
	return file_v1_model_routing_proto_rawDescGZIP(), []int{2}
}

func (x *Source) GetService() *wrapperspb.StringValue {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *Source) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *Source) GetToService() *wrapperspb.StringValue {
	if x != nil {
		return x.ToService
	}
	return nil
}

func (x *Source) GetToNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.ToNamespace
	}
	return nil
}

func (x *Source) GetMetadata() map[string]*MatchString {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type Destination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 被调方服务以及命名空间
	Service   *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	Namespace *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 被调方服务实例标签
	// value支持正则匹配
	Metadata map[string]*MatchString `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 根据服务名和服务实例metadata筛选符合条件的服务实例子集
	// 服务实例子集可以设置优先级和权重
	// 优先级：整型，范围[0, 9]，最高优先级为0
	// 权重：整型
	// 先按优先级路由，如果存在高优先级，不会使用低优先级
	// 如果存在优先级相同的子集，再按权重分配
	// 优先级和权重可以都不设置/设置一个/设置两个
	// 如果部分设置优先级，部分没有设置，认为没有设置的优先级最低
	// 如果部分设置权重，部分没有设置，认为没有设置的权重为0
	// 如果全部没有设置权重，认为权重相同
	Priority *wrapperspb.UInt32Value `protobuf:"bytes,4,opt,name=priority,proto3" json:"priority,omitempty"`
	Weight   *wrapperspb.UInt32Value `protobuf:"bytes,5,opt,name=weight,proto3" json:"weight,omitempty"`
	// 将请求转发到代理服务
	Transfer *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=transfer,proto3" json:"transfer,omitempty"`
	//是否对该set执行隔离，隔离后，不会再分配流量
	Isolate *wrapperspb.BoolValue `protobuf:"bytes,7,opt,name=isolate,proto3" json:"isolate,omitempty"`
	// 匹配取反，默认false
	InvertMatch *wrapperspb.BoolValue `protobuf:"bytes,8,opt,name=invert_match,json=invertMatch,proto3" json:"invert_match,omitempty"`
}

func (x *Destination) Reset() {
	*x = Destination{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_routing_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Destination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Destination) ProtoMessage() {}

func (x *Destination) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_routing_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Destination.ProtoReflect.Descriptor instead.
func (*Destination) Descriptor() ([]byte, []int) {
	return file_v1_model_routing_proto_rawDescGZIP(), []int{3}
}

func (x *Destination) GetService() *wrapperspb.StringValue {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *Destination) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *Destination) GetMetadata() map[string]*MatchString {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Destination) GetPriority() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Priority
	}
	return nil
}

func (x *Destination) GetWeight() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Weight
	}
	return nil
}

func (x *Destination) GetTransfer() *wrapperspb.StringValue {
	if x != nil {
		return x.Transfer
	}
	return nil
}

func (x *Destination) GetIsolate() *wrapperspb.BoolValue {
	if x != nil {
		return x.Isolate
	}
	return nil
}

func (x *Destination) GetInvertMatch() *wrapperspb.BoolValue {
	if x != nil {
		return x.InvertMatch
	}
	return nil
}

var File_v1_model_routing_proto protoreflect.FileDescriptor

var file_v1_model_routing_proto_rawDesc = []byte{
	0x0a, 0x16, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x72, 0x6f, 0x75, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x76, 0x31,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6e, 0x65, 0x61, 0x72, 0x62, 0x79, 0x5f, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcf, 0x04, 0x0a, 0x07, 0x52, 0x6f,
	0x75, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x36, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3a, 0x0a,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x2f, 0x0a, 0x08, 0x69, 0x6e, 0x62,
	0x6f, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65,
	0x52, 0x08, 0x69, 0x6e, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x31, 0x0a, 0x09, 0x6f, 0x75,
	0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x6f, 0x75,
	0x74, 0x65, 0x52, 0x09, 0x6f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x32, 0x0a,
	0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x32, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05,
	0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x42, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x41, 0x0a, 0x0f, 0x6e, 0x65, 0x61, 0x72, 0x62, 0x79, 0x5f, 0x69, 0x6e,
	0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x65, 0x61, 0x72,
	0x79, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x0e, 0x6e, 0x65, 0x61, 0x72, 0x62, 0x79, 0x49, 0x6e,
	0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x43, 0x0a, 0x10, 0x6e, 0x65, 0x61, 0x72, 0x62, 0x79,
	0x5f, 0x6f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x4e, 0x65, 0x61, 0x72, 0x79, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x0f, 0x6e, 0x65, 0x61, 0x72,
	0x62, 0x79, 0x4f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x22, 0xa9, 0x03, 0x0a, 0x05,
	0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x07, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x65, 0x73, 0x74, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x47, 0x0a, 0x10, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x74,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x68, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x48, 0x0a,
	0x0e, 0x64, 0x65, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x2e, 0x44, 0x65, 0x67, 0x72, 0x61,
	0x64, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x64, 0x65, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x68, 0x0a,
	0x0d, 0x44, 0x65, 0x67, 0x72, 0x61, 0x64, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0f,
	0x0a, 0x0b, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12,
	0x15, 0x0a, 0x11, 0x50, 0x41, 0x52, 0x54, 0x5f, 0x54, 0x4f, 0x5f, 0x55, 0x4e, 0x48, 0x45, 0x41,
	0x4c, 0x54, 0x48, 0x59, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x4f, 0x5f, 0x4e, 0x45, 0x58,
	0x54, 0x5f, 0x50, 0x52, 0x49, 0x4f, 0x52, 0x49, 0x54, 0x59, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15,
	0x50, 0x41, 0x52, 0x54, 0x5f, 0x54, 0x4f, 0x5f, 0x4e, 0x45, 0x58, 0x54, 0x5f, 0x50, 0x52, 0x49,
	0x4f, 0x52, 0x49, 0x54, 0x59, 0x10, 0x03, 0x22, 0x94, 0x03, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x36, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x3c, 0x0a, 0x0a, 0x74, 0x6f, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x74, 0x6f, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x40, 0x0a, 0x0c, 0x74, 0x6f, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x74, 0x6f, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x56, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbd,
	0x04, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36,
	0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x12, 0x43, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x38, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x12, 0x34, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x38, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x12, 0x34, 0x0a, 0x07, 0x69, 0x73, 0x6f, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07,
	0x69, 0x73, 0x6f, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x69, 0x6e, 0x76, 0x65, 0x72,
	0x74, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x69, 0x6e, 0x76, 0x65, 0x72,
	0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x1a, 0x56, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x45,
	0x5a, 0x43, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x3b, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v1_model_routing_proto_rawDescOnce sync.Once
	file_v1_model_routing_proto_rawDescData = file_v1_model_routing_proto_rawDesc
)

func file_v1_model_routing_proto_rawDescGZIP() []byte {
	file_v1_model_routing_proto_rawDescOnce.Do(func() {
		file_v1_model_routing_proto_rawDescData = protoimpl.X.CompressGZIP(file_v1_model_routing_proto_rawDescData)
	})
	return file_v1_model_routing_proto_rawDescData
}

var file_v1_model_routing_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_v1_model_routing_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_v1_model_routing_proto_goTypes = []interface{}{
	(Route_DegradeAction)(0),       // 0: api.v1.model.Route.DegradeAction
	(*Routing)(nil),                // 1: api.v1.model.Routing
	(*Route)(nil),                  // 2: api.v1.model.Route
	(*Source)(nil),                 // 3: api.v1.model.Source
	(*Destination)(nil),            // 4: api.v1.model.Destination
	nil,                            // 5: api.v1.model.Source.MetadataEntry
	nil,                            // 6: api.v1.model.Destination.MetadataEntry
	(*wrapperspb.StringValue)(nil), // 7: google.protobuf.StringValue
	(*NearyRoute)(nil),             // 8: api.v1.model.NearyRoute
	(*wrapperspb.UInt32Value)(nil), // 9: google.protobuf.UInt32Value
	(*wrapperspb.BoolValue)(nil),   // 10: google.protobuf.BoolValue
	(*MatchString)(nil),            // 11: api.v1.model.MatchString
}
var file_v1_model_routing_proto_depIdxs = []int32{
	7,  // 0: api.v1.model.Routing.service:type_name -> google.protobuf.StringValue
	7,  // 1: api.v1.model.Routing.namespace:type_name -> google.protobuf.StringValue
	2,  // 2: api.v1.model.Routing.inbounds:type_name -> api.v1.model.Route
	2,  // 3: api.v1.model.Routing.outbounds:type_name -> api.v1.model.Route
	7,  // 4: api.v1.model.Routing.ctime:type_name -> google.protobuf.StringValue
	7,  // 5: api.v1.model.Routing.mtime:type_name -> google.protobuf.StringValue
	7,  // 6: api.v1.model.Routing.revision:type_name -> google.protobuf.StringValue
	7,  // 7: api.v1.model.Routing.service_token:type_name -> google.protobuf.StringValue
	8,  // 8: api.v1.model.Routing.nearby_inbounds:type_name -> api.v1.model.NearyRoute
	8,  // 9: api.v1.model.Routing.nearby_outbounds:type_name -> api.v1.model.NearyRoute
	3,  // 10: api.v1.model.Route.sources:type_name -> api.v1.model.Source
	4,  // 11: api.v1.model.Route.destinations:type_name -> api.v1.model.Destination
	9,  // 12: api.v1.model.Route.health_threshold:type_name -> google.protobuf.UInt32Value
	0,  // 13: api.v1.model.Route.degrade_action:type_name -> api.v1.model.Route.DegradeAction
	10, // 14: api.v1.model.Route.disable:type_name -> google.protobuf.BoolValue
	7,  // 15: api.v1.model.Source.service:type_name -> google.protobuf.StringValue
	7,  // 16: api.v1.model.Source.namespace:type_name -> google.protobuf.StringValue
	7,  // 17: api.v1.model.Source.to_service:type_name -> google.protobuf.StringValue
	7,  // 18: api.v1.model.Source.to_namespace:type_name -> google.protobuf.StringValue
	5,  // 19: api.v1.model.Source.metadata:type_name -> api.v1.model.Source.MetadataEntry
	7,  // 20: api.v1.model.Destination.service:type_name -> google.protobuf.StringValue
	7,  // 21: api.v1.model.Destination.namespace:type_name -> google.protobuf.StringValue
	6,  // 22: api.v1.model.Destination.metadata:type_name -> api.v1.model.Destination.MetadataEntry
	9,  // 23: api.v1.model.Destination.priority:type_name -> google.protobuf.UInt32Value
	9,  // 24: api.v1.model.Destination.weight:type_name -> google.protobuf.UInt32Value
	7,  // 25: api.v1.model.Destination.transfer:type_name -> google.protobuf.StringValue
	10, // 26: api.v1.model.Destination.isolate:type_name -> google.protobuf.BoolValue
	10, // 27: api.v1.model.Destination.invert_match:type_name -> google.protobuf.BoolValue
	11, // 28: api.v1.model.Source.MetadataEntry.value:type_name -> api.v1.model.MatchString
	11, // 29: api.v1.model.Destination.MetadataEntry.value:type_name -> api.v1.model.MatchString
	30, // [30:30] is the sub-list for method output_type
	30, // [30:30] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_v1_model_routing_proto_init() }
func file_v1_model_routing_proto_init() {
	if File_v1_model_routing_proto != nil {
		return
	}
	file_v1_model_model_proto_init()
	file_v1_model_nearby_route_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_v1_model_routing_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Routing); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_routing_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Route); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_routing_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Source); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_routing_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Destination); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_model_routing_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v1_model_routing_proto_goTypes,
		DependencyIndexes: file_v1_model_routing_proto_depIdxs,
		EnumInfos:         file_v1_model_routing_proto_enumTypes,
		MessageInfos:      file_v1_model_routing_proto_msgTypes,
	}.Build()
	File_v1_model_routing_proto = out.File
	file_v1_model_routing_proto_rawDesc = nil
	file_v1_model_routing_proto_goTypes = nil
	file_v1_model_routing_proto_depIdxs = nil
}
