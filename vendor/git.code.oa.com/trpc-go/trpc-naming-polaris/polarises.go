package naming

import (
	"errors"
	"fmt"
	"gopkg.in/yaml.v3"

	"git.code.oa.com/polaris/polaris-go/api"
	"git.code.oa.com/trpc-go/trpc-go/plugin"
)

func init() {
	// polarises is a selector for multiple polaris configurations.
	plugin.Register("polarises", &SelectorPolarisesFactory{})
}

// PolarisesConfig is the configuration for polarises.
type PolarisesConfig map[string]*Config

// UnmarshalYAML is the customized unmarshal function to ensure the default value of the config.
func (c *PolarisesConfig) UnmarshalYAML(value *yaml.Node) error {
	type plain PolarisesConfig
	if err := value.Decode((*plain)(c)); err != nil {
		return err
	}
	// Set default configuration.
	for _, cfg := range *c {
		setDefaultConfig(cfg)
	}
	// There is and only one default is allowed.
	defaultCount := 0
	for _, cfg := range *c {
		if cfg != nil && cfg.Default != nil && *cfg.Default {
			defaultCount++
		}
	}
	if defaultCount != 1 {
		return errors.New("only one default configuration is allowed")
	}
	return nil
}

// SelectorPolarisesFactory implements the name service plugin for trpc.
type SelectorPolarisesFactory struct {
	sdkCtx         map[string]api.SDKContext
	decorateConfig func(PolarisesConfig) PolarisesConfig
}

// Type plugin type.
func (f *SelectorPolarisesFactory) Type() string {
	return "selector"
}

// Setup initialization.
func (f *SelectorPolarisesFactory) Setup(_ string, dec plugin.Decoder) error {
	if dec == nil {
		return errors.New("selector clusters config decoder empty")
	}
	f.sdkCtx = make(map[string]api.SDKContext)
	conf := PolarisesConfig{}
	if err := dec.Decode(&conf); err != nil {
		return err
	}
	if f.decorateConfig != nil {
		conf = f.decorateConfig(conf)
	}

	for name, cfg := range conf {
		name, cfg := name, cfg
		if cfg == nil {
			return fmt.Errorf("selector cluster %s config is nil", name)
		}
		if cfg.Name == "" {
			cfg.Name = name
		}
		sdkCtx, err := setupWithConfig(cfg)
		if err != nil {
			return fmt.Errorf("setup with config %s error: %w", name, err)
		}
		f.sdkCtx[name] = sdkCtx
	}
	return nil
}

// FlexDependsOn makes sure that register is initialized after selector,
// which may set some global status of SDK, such as log directories.
func (f *SelectorPolarisesFactory) FlexDependsOn() []string {
	return []string{"log-default"}
}

// PolarisesConfigDecorator is a decorator to modify the configuration right after
// unmarshaling and before the actual setting up.
//
// Typical usage:
//
//	import (
//	    naming "git.code.oa.com/trpc-go/trpc-naming-polaris"
//	    "git.code.oa.com/trpc-go/trpc-go/plugin"
//	)
//
//	func main() {
//	    pluginType, pluginName := "selector", "polarises"
//	    s := plugin.Get(pluginType, pluginName)
//	    cd, ok := s.(naming.PolarisesConfigDecorator)
//	    if !ok {
//	        log.Fatal("naming polaris selector factory should implement ConfigDecorator interface")
//	    }
//	    cd.WithDecorateConfig(func(polarisesConfig naming.PolarisesConfig) naming.PolarisesConfig {
//	        for name, cfg := range polarisesConfig {
//	        	pc := api.NewConfiguration()
//	        	pc.GetGlobal().GetAPI().SetTimeout(time.Second)
//	        	pc.GetGlobal().GetAPI().SetTimeout(time.Second)
//	        	pc.GetProvider().GetRateLimit().GetRateLimitCluster().SetService("polaris.metric.v2.test")
//	        	// Modify pc to provide any configuration you like.
//	        	// ...
//				cfg.PolarisConfig = pc
//	        	polarisesConfig[name] = cfg
//	        }
//	        return polarisesConfig
//	    })
//	    trpc.NewServer()
//	    // ...
//	}
type PolarisesConfigDecorator interface {
	WithDecorateConfig(func(PolarisesConfig) PolarisesConfig)
}

// WithDecorateConfig sets the configuration decorator for naming polaris clusters.
func (f *SelectorPolarisesFactory) WithDecorateConfig(decorate func(PolarisesConfig) PolarisesConfig) {
	f.decorateConfig = decorate
}

// GetSDKCtx returns the stored sdk context.
func (f *SelectorPolarisesFactory) GetSDKCtx(name string) (api.SDKContext, bool) {
	sdkCtx, ok := f.sdkCtx[name]
	return sdkCtx, ok
}
