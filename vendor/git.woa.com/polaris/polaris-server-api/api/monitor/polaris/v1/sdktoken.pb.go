// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: monitor/polaris/v1/sdktoken.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SDK的唯一标识
type SDKToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip      string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	Pid     uint32 `protobuf:"varint,2,opt,name=pid,proto3" json:"pid,omitempty"`
	Uid     string `protobuf:"bytes,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Client  string `protobuf:"bytes,4,opt,name=client,proto3" json:"client,omitempty"`
	Version string `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`
	//该sdkContext所属进程运行的容器pod
	PodName string `protobuf:"bytes,6,opt,name=pod_name,json=podName,proto3" json:"pod_name,omitempty"`
	//该sdkContext所属进程所属容器或者机器的hostname
	HostName string `protobuf:"bytes,7,opt,name=host_name,json=hostName,proto3" json:"host_name,omitempty"`
}

func (x *SDKToken) Reset() {
	*x = SDKToken{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdktoken_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SDKToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKToken) ProtoMessage() {}

func (x *SDKToken) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdktoken_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKToken.ProtoReflect.Descriptor instead.
func (*SDKToken) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdktoken_proto_rawDescGZIP(), []int{0}
}

func (x *SDKToken) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *SDKToken) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *SDKToken) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SDKToken) GetClient() string {
	if x != nil {
		return x.Client
	}
	return ""
}

func (x *SDKToken) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *SDKToken) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

func (x *SDKToken) GetHostName() string {
	if x != nil {
		return x.HostName
	}
	return ""
}

var File_monitor_polaris_v1_sdktoken_proto protoreflect.FileDescriptor

var file_monitor_polaris_v1_sdktoken_proto_rawDesc = []byte{
	0x0a, 0x21, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x64, 0x6b, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x02, 0x76, 0x31, 0x22, 0xa8, 0x01, 0x0a, 0x08, 0x53, 0x44, 0x4b, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6f,
	0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x42, 0x3f, 0x5a, 0x3d, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_monitor_polaris_v1_sdktoken_proto_rawDescOnce sync.Once
	file_monitor_polaris_v1_sdktoken_proto_rawDescData = file_monitor_polaris_v1_sdktoken_proto_rawDesc
)

func file_monitor_polaris_v1_sdktoken_proto_rawDescGZIP() []byte {
	file_monitor_polaris_v1_sdktoken_proto_rawDescOnce.Do(func() {
		file_monitor_polaris_v1_sdktoken_proto_rawDescData = protoimpl.X.CompressGZIP(file_monitor_polaris_v1_sdktoken_proto_rawDescData)
	})
	return file_monitor_polaris_v1_sdktoken_proto_rawDescData
}

var file_monitor_polaris_v1_sdktoken_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_monitor_polaris_v1_sdktoken_proto_goTypes = []interface{}{
	(*SDKToken)(nil), // 0: v1.SDKToken
}
var file_monitor_polaris_v1_sdktoken_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_monitor_polaris_v1_sdktoken_proto_init() }
func file_monitor_polaris_v1_sdktoken_proto_init() {
	if File_monitor_polaris_v1_sdktoken_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_monitor_polaris_v1_sdktoken_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SDKToken); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_monitor_polaris_v1_sdktoken_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_monitor_polaris_v1_sdktoken_proto_goTypes,
		DependencyIndexes: file_monitor_polaris_v1_sdktoken_proto_depIdxs,
		MessageInfos:      file_monitor_polaris_v1_sdktoken_proto_msgTypes,
	}.Build()
	File_monitor_polaris_v1_sdktoken_proto = out.File
	file_monitor_polaris_v1_sdktoken_proto_rawDesc = nil
	file_monitor_polaris_v1_sdktoken_proto_goTypes = nil
	file_monitor_polaris_v1_sdktoken_proto_depIdxs = nil
}
