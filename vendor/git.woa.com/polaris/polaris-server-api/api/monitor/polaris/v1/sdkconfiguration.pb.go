// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: monitor/polaris/v1/sdkconfiguration.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SDK的配置信息
type SDKConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token          *SDKToken              `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	Config         string                 `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
	TakeEffectTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=take_effect_time,json=takeEffectTime,proto3" json:"take_effect_time,omitempty"`
	Location       string                 `protobuf:"bytes,4,opt,name=location,proto3" json:"location,omitempty"`
	Version        string                 `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`
	Client         string                 `protobuf:"bytes,6,opt,name=client,proto3" json:"client,omitempty"`
	Plugins        string                 `protobuf:"bytes,7,opt,name=plugins,proto3" json:"plugins,omitempty"`
	InitFinishTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=init_finish_time,json=initFinishTime,proto3" json:"init_finish_time,omitempty"`
	ReportTime     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=report_time,json=reportTime,proto3" json:"report_time,omitempty"`
	Signature      *MonitorSignature      `protobuf:"bytes,10,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *SDKConfig) Reset() {
	*x = SDKConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkconfiguration_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SDKConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKConfig) ProtoMessage() {}

func (x *SDKConfig) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkconfiguration_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKConfig.ProtoReflect.Descriptor instead.
func (*SDKConfig) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkconfiguration_proto_rawDescGZIP(), []int{0}
}

func (x *SDKConfig) GetToken() *SDKToken {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *SDKConfig) GetConfig() string {
	if x != nil {
		return x.Config
	}
	return ""
}

func (x *SDKConfig) GetTakeEffectTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TakeEffectTime
	}
	return nil
}

func (x *SDKConfig) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *SDKConfig) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *SDKConfig) GetClient() string {
	if x != nil {
		return x.Client
	}
	return ""
}

func (x *SDKConfig) GetPlugins() string {
	if x != nil {
		return x.Plugins
	}
	return ""
}

func (x *SDKConfig) GetInitFinishTime() *timestamppb.Timestamp {
	if x != nil {
		return x.InitFinishTime
	}
	return nil
}

func (x *SDKConfig) GetReportTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ReportTime
	}
	return nil
}

func (x *SDKConfig) GetSignature() *MonitorSignature {
	if x != nil {
		return x.Signature
	}
	return nil
}

var File_monitor_polaris_v1_sdkconfiguration_proto protoreflect.FileDescriptor

var file_monitor_polaris_v1_sdkconfiguration_proto_rawDesc = []byte{
	0x0a, 0x29, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x64, 0x6b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x76, 0x31, 0x1a,
	0x21, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x64, 0x6b, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c,
	0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xac, 0x03, 0x0a, 0x09, 0x53, 0x44, 0x4b, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x22, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x44, 0x4b, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x44, 0x0a, 0x10, 0x74, 0x61, 0x6b, 0x65, 0x5f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x74, 0x61, 0x6b, 0x65, 0x45, 0x66, 0x66,
	0x65, 0x63, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x12,
	0x44, 0x0a, 0x10, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x69, 0x6e, 0x69, 0x74, 0x46, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x32, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x09, 0x73, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x42, 0x3f, 0x5a, 0x3d, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c,
	0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_monitor_polaris_v1_sdkconfiguration_proto_rawDescOnce sync.Once
	file_monitor_polaris_v1_sdkconfiguration_proto_rawDescData = file_monitor_polaris_v1_sdkconfiguration_proto_rawDesc
)

func file_monitor_polaris_v1_sdkconfiguration_proto_rawDescGZIP() []byte {
	file_monitor_polaris_v1_sdkconfiguration_proto_rawDescOnce.Do(func() {
		file_monitor_polaris_v1_sdkconfiguration_proto_rawDescData = protoimpl.X.CompressGZIP(file_monitor_polaris_v1_sdkconfiguration_proto_rawDescData)
	})
	return file_monitor_polaris_v1_sdkconfiguration_proto_rawDescData
}

var file_monitor_polaris_v1_sdkconfiguration_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_monitor_polaris_v1_sdkconfiguration_proto_goTypes = []interface{}{
	(*SDKConfig)(nil),             // 0: v1.SDKConfig
	(*SDKToken)(nil),              // 1: v1.SDKToken
	(*timestamppb.Timestamp)(nil), // 2: google.protobuf.Timestamp
	(*MonitorSignature)(nil),      // 3: v1.MonitorSignature
}
var file_monitor_polaris_v1_sdkconfiguration_proto_depIdxs = []int32{
	1, // 0: v1.SDKConfig.token:type_name -> v1.SDKToken
	2, // 1: v1.SDKConfig.take_effect_time:type_name -> google.protobuf.Timestamp
	2, // 2: v1.SDKConfig.init_finish_time:type_name -> google.protobuf.Timestamp
	2, // 3: v1.SDKConfig.report_time:type_name -> google.protobuf.Timestamp
	3, // 4: v1.SDKConfig.signature:type_name -> v1.MonitorSignature
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_monitor_polaris_v1_sdkconfiguration_proto_init() }
func file_monitor_polaris_v1_sdkconfiguration_proto_init() {
	if File_monitor_polaris_v1_sdkconfiguration_proto != nil {
		return
	}
	file_monitor_polaris_v1_sdktoken_proto_init()
	file_monitor_polaris_v1_signature_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_monitor_polaris_v1_sdkconfiguration_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SDKConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_monitor_polaris_v1_sdkconfiguration_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_monitor_polaris_v1_sdkconfiguration_proto_goTypes,
		DependencyIndexes: file_monitor_polaris_v1_sdkconfiguration_proto_depIdxs,
		MessageInfos:      file_monitor_polaris_v1_sdkconfiguration_proto_msgTypes,
	}.Build()
	File_monitor_polaris_v1_sdkconfiguration_proto = out.File
	file_monitor_polaris_v1_sdkconfiguration_proto_rawDesc = nil
	file_monitor_polaris_v1_sdkconfiguration_proto_goTypes = nil
	file_monitor_polaris_v1_sdkconfiguration_proto_depIdxs = nil
}
