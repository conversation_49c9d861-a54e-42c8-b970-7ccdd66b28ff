// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v1/model/request.proto

package polarismodel_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DiscoverRequest_DiscoverRequestType int32

const (
	DiscoverRequest_UNKNOWN            DiscoverRequest_DiscoverRequestType = 0
	DiscoverRequest_INSTANCE           DiscoverRequest_DiscoverRequestType = 1
	DiscoverRequest_CLUSTER            DiscoverRequest_DiscoverRequestType = 2
	DiscoverRequest_ROUTING            DiscoverRequest_DiscoverRequestType = 3
	DiscoverRequest_RATE_LIMIT         DiscoverRequest_DiscoverRequestType = 4
	DiscoverRequest_CIRCUIT_BREAKER    DiscoverRequest_DiscoverRequestType = 5
	DiscoverRequest_SERVICES           DiscoverRequest_DiscoverRequestType = 6
	DiscoverRequest_MESH               DiscoverRequest_DiscoverRequestType = 7
	DiscoverRequest_MESH_CONFIG        DiscoverRequest_DiscoverRequestType = 8
	DiscoverRequest_FLUX_DBREFRESH     DiscoverRequest_DiscoverRequestType = 9
	DiscoverRequest_FLUX_SDK           DiscoverRequest_DiscoverRequestType = 10
	DiscoverRequest_FLUX_SERVER        DiscoverRequest_DiscoverRequestType = 11
	DiscoverRequest_CIRCUIT_BREAKER_V2 DiscoverRequest_DiscoverRequestType = 13
	DiscoverRequest_INSTANCE_RAW       DiscoverRequest_DiscoverRequestType = 14
)

// Enum value maps for DiscoverRequest_DiscoverRequestType.
var (
	DiscoverRequest_DiscoverRequestType_name = map[int32]string{
		0:  "UNKNOWN",
		1:  "INSTANCE",
		2:  "CLUSTER",
		3:  "ROUTING",
		4:  "RATE_LIMIT",
		5:  "CIRCUIT_BREAKER",
		6:  "SERVICES",
		7:  "MESH",
		8:  "MESH_CONFIG",
		9:  "FLUX_DBREFRESH",
		10: "FLUX_SDK",
		11: "FLUX_SERVER",
		13: "CIRCUIT_BREAKER_V2",
		14: "INSTANCE_RAW",
	}
	DiscoverRequest_DiscoverRequestType_value = map[string]int32{
		"UNKNOWN":            0,
		"INSTANCE":           1,
		"CLUSTER":            2,
		"ROUTING":            3,
		"RATE_LIMIT":         4,
		"CIRCUIT_BREAKER":    5,
		"SERVICES":           6,
		"MESH":               7,
		"MESH_CONFIG":        8,
		"FLUX_DBREFRESH":     9,
		"FLUX_SDK":           10,
		"FLUX_SERVER":        11,
		"CIRCUIT_BREAKER_V2": 13,
		"INSTANCE_RAW":       14,
	}
)

func (x DiscoverRequest_DiscoverRequestType) Enum() *DiscoverRequest_DiscoverRequestType {
	p := new(DiscoverRequest_DiscoverRequestType)
	*p = x
	return p
}

func (x DiscoverRequest_DiscoverRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DiscoverRequest_DiscoverRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_request_proto_enumTypes[0].Descriptor()
}

func (DiscoverRequest_DiscoverRequestType) Type() protoreflect.EnumType {
	return &file_v1_model_request_proto_enumTypes[0]
}

func (x DiscoverRequest_DiscoverRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DiscoverRequest_DiscoverRequestType.Descriptor instead.
func (DiscoverRequest_DiscoverRequestType) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_request_proto_rawDescGZIP(), []int{0, 0}
}

// WatchRequestType 监听类型
type WatchRequest_WatchRequestType int32

const (
	WatchRequest_UNKNOWN            WatchRequest_WatchRequestType = 0
	WatchRequest_INSTANCE           WatchRequest_WatchRequestType = 1
	WatchRequest_CLUSTER            WatchRequest_WatchRequestType = 2
	WatchRequest_ROUTING            WatchRequest_WatchRequestType = 3
	WatchRequest_RATE_LIMIT         WatchRequest_WatchRequestType = 4
	WatchRequest_CIRCUIT_BREAKER    WatchRequest_WatchRequestType = 5
	WatchRequest_SERVICES           WatchRequest_WatchRequestType = 6
	WatchRequest_MESH               WatchRequest_WatchRequestType = 7
	WatchRequest_MESH_CONFIG        WatchRequest_WatchRequestType = 8
	WatchRequest_FLUX_DBREFRESH     WatchRequest_WatchRequestType = 9
	WatchRequest_FLUX_SDK           WatchRequest_WatchRequestType = 10
	WatchRequest_FLUX_SERVER        WatchRequest_WatchRequestType = 11
	WatchRequest_CIRCUIT_BREAKER_V2 WatchRequest_WatchRequestType = 13
	WatchRequest_INSTANCE_RAW       WatchRequest_WatchRequestType = 14
)

// Enum value maps for WatchRequest_WatchRequestType.
var (
	WatchRequest_WatchRequestType_name = map[int32]string{
		0:  "UNKNOWN",
		1:  "INSTANCE",
		2:  "CLUSTER",
		3:  "ROUTING",
		4:  "RATE_LIMIT",
		5:  "CIRCUIT_BREAKER",
		6:  "SERVICES",
		7:  "MESH",
		8:  "MESH_CONFIG",
		9:  "FLUX_DBREFRESH",
		10: "FLUX_SDK",
		11: "FLUX_SERVER",
		13: "CIRCUIT_BREAKER_V2",
		14: "INSTANCE_RAW",
	}
	WatchRequest_WatchRequestType_value = map[string]int32{
		"UNKNOWN":            0,
		"INSTANCE":           1,
		"CLUSTER":            2,
		"ROUTING":            3,
		"RATE_LIMIT":         4,
		"CIRCUIT_BREAKER":    5,
		"SERVICES":           6,
		"MESH":               7,
		"MESH_CONFIG":        8,
		"FLUX_DBREFRESH":     9,
		"FLUX_SDK":           10,
		"FLUX_SERVER":        11,
		"CIRCUIT_BREAKER_V2": 13,
		"INSTANCE_RAW":       14,
	}
)

func (x WatchRequest_WatchRequestType) Enum() *WatchRequest_WatchRequestType {
	p := new(WatchRequest_WatchRequestType)
	*p = x
	return p
}

func (x WatchRequest_WatchRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WatchRequest_WatchRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_request_proto_enumTypes[1].Descriptor()
}

func (WatchRequest_WatchRequestType) Type() protoreflect.EnumType {
	return &file_v1_model_request_proto_enumTypes[1]
}

func (x WatchRequest_WatchRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WatchRequest_WatchRequestType.Descriptor instead.
func (WatchRequest_WatchRequestType) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_request_proto_rawDescGZIP(), []int{1, 0}
}

type DiscoverRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       DiscoverRequest_DiscoverRequestType       `protobuf:"varint,1,opt,name=type,proto3,enum=api.v1.model.DiscoverRequest_DiscoverRequestType" json:"type,omitempty"`
	Service    *Service                                  `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
	Mesh       *Mesh                                     `protobuf:"bytes,3,opt,name=mesh,proto3" json:"mesh,omitempty"`
	MeshConfig *MeshConfig                               `protobuf:"bytes,4,opt,name=meshConfig,proto3" json:"meshConfig,omitempty"`
	Signature  *DiscoverRequest_DiscoverRequestSignature `protobuf:"bytes,5,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *DiscoverRequest) Reset() {
	*x = DiscoverRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiscoverRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscoverRequest) ProtoMessage() {}

func (x *DiscoverRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscoverRequest.ProtoReflect.Descriptor instead.
func (*DiscoverRequest) Descriptor() ([]byte, []int) {
	return file_v1_model_request_proto_rawDescGZIP(), []int{0}
}

func (x *DiscoverRequest) GetType() DiscoverRequest_DiscoverRequestType {
	if x != nil {
		return x.Type
	}
	return DiscoverRequest_UNKNOWN
}

func (x *DiscoverRequest) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *DiscoverRequest) GetMesh() *Mesh {
	if x != nil {
		return x.Mesh
	}
	return nil
}

func (x *DiscoverRequest) GetMeshConfig() *MeshConfig {
	if x != nil {
		return x.MeshConfig
	}
	return nil
}

func (x *DiscoverRequest) GetSignature() *DiscoverRequest_DiscoverRequestSignature {
	if x != nil {
		return x.Signature
	}
	return nil
}

// WatchReq 监听请求
type WatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    WatchRequest_WatchRequestType `protobuf:"varint,1,opt,name=type,proto3,enum=api.v1.model.WatchRequest_WatchRequestType" json:"type,omitempty"`
	Timeout uint32                        `protobuf:"varint,2,opt,name=timeout,proto3" json:"timeout,omitempty"` // 监听秒数
	Service *Service                      `protobuf:"bytes,3,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *WatchRequest) Reset() {
	*x = WatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_request_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchRequest) ProtoMessage() {}

func (x *WatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_request_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchRequest.ProtoReflect.Descriptor instead.
func (*WatchRequest) Descriptor() ([]byte, []int) {
	return file_v1_model_request_proto_rawDescGZIP(), []int{1}
}

func (x *WatchRequest) GetType() WatchRequest_WatchRequestType {
	if x != nil {
		return x.Type
	}
	return WatchRequest_UNKNOWN
}

func (x *WatchRequest) GetTimeout() uint32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *WatchRequest) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

type DiscoverRequest_DiscoverRequestSignature struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string `protobuf:"bytes,1,opt,name=app_id,proto3" json:"app_id,omitempty"`        // 唯一标识
	Signature string `protobuf:"bytes,2,opt,name=signature,proto3" json:"signature,omitempty"`  // 签名
	Timestamp int64  `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 当前时间戳
}

func (x *DiscoverRequest_DiscoverRequestSignature) Reset() {
	*x = DiscoverRequest_DiscoverRequestSignature{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_request_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiscoverRequest_DiscoverRequestSignature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscoverRequest_DiscoverRequestSignature) ProtoMessage() {}

func (x *DiscoverRequest_DiscoverRequestSignature) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_request_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscoverRequest_DiscoverRequestSignature.ProtoReflect.Descriptor instead.
func (*DiscoverRequest_DiscoverRequestSignature) Descriptor() ([]byte, []int) {
	return file_v1_model_request_proto_rawDescGZIP(), []int{0, 0}
}

func (x *DiscoverRequest_DiscoverRequestSignature) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *DiscoverRequest_DiscoverRequestSignature) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *DiscoverRequest_DiscoverRequestSignature) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

var File_v1_model_request_proto protoreflect.FileDescriptor

var file_v1_model_request_proto_rawDesc = []byte{
	0x0a, 0x16, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x16, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13,
	0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6d, 0x65, 0x73, 0x68, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xa9, 0x05, 0x0a, 0x0f, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x45, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2f,
	0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x26, 0x0a, 0x04, 0x6d, 0x65, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x73,
	0x68, 0x52, 0x04, 0x6d, 0x65, 0x73, 0x68, 0x12, 0x38, 0x0a, 0x0a, 0x6d, 0x65, 0x73, 0x68, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x73, 0x68, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x6d, 0x65, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x54, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x09, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x1a, 0x6e, 0x0a, 0x18, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0xf5, 0x01, 0x0a, 0x13, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x4c,
	0x55, 0x53, 0x54, 0x45, 0x52, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x4f, 0x55, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x49, 0x4d,
	0x49, 0x54, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x49, 0x52, 0x43, 0x55, 0x49, 0x54, 0x5f,
	0x42, 0x52, 0x45, 0x41, 0x4b, 0x45, 0x52, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x45, 0x52,
	0x56, 0x49, 0x43, 0x45, 0x53, 0x10, 0x06, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x45, 0x53, 0x48, 0x10,
	0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x45, 0x53, 0x48, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47,
	0x10, 0x08, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x4c, 0x55, 0x58, 0x5f, 0x44, 0x42, 0x52, 0x45, 0x46,
	0x52, 0x45, 0x53, 0x48, 0x10, 0x09, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x4c, 0x55, 0x58, 0x5f, 0x53,
	0x44, 0x4b, 0x10, 0x0a, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4c, 0x55, 0x58, 0x5f, 0x53, 0x45, 0x52,
	0x56, 0x45, 0x52, 0x10, 0x0b, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x49, 0x52, 0x43, 0x55, 0x49, 0x54,
	0x5f, 0x42, 0x52, 0x45, 0x41, 0x4b, 0x45, 0x52, 0x5f, 0x56, 0x32, 0x10, 0x0d, 0x12, 0x10, 0x0a,
	0x0c, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x41, 0x57, 0x10, 0x0e, 0x22,
	0x8f, 0x03, 0x0a, 0x0c, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x57, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x2f, 0x0a, 0x07, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0xf2, 0x01, 0x0a,
	0x10, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07,
	0x43, 0x4c, 0x55, 0x53, 0x54, 0x45, 0x52, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x4f, 0x55,
	0x54, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4c,
	0x49, 0x4d, 0x49, 0x54, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x49, 0x52, 0x43, 0x55, 0x49,
	0x54, 0x5f, 0x42, 0x52, 0x45, 0x41, 0x4b, 0x45, 0x52, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x53,
	0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x53, 0x10, 0x06, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x45, 0x53,
	0x48, 0x10, 0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x45, 0x53, 0x48, 0x5f, 0x43, 0x4f, 0x4e, 0x46,
	0x49, 0x47, 0x10, 0x08, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x4c, 0x55, 0x58, 0x5f, 0x44, 0x42, 0x52,
	0x45, 0x46, 0x52, 0x45, 0x53, 0x48, 0x10, 0x09, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x4c, 0x55, 0x58,
	0x5f, 0x53, 0x44, 0x4b, 0x10, 0x0a, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4c, 0x55, 0x58, 0x5f, 0x53,
	0x45, 0x52, 0x56, 0x45, 0x52, 0x10, 0x0b, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x49, 0x52, 0x43, 0x55,
	0x49, 0x54, 0x5f, 0x42, 0x52, 0x45, 0x41, 0x4b, 0x45, 0x52, 0x5f, 0x56, 0x32, 0x10, 0x0d, 0x12,
	0x10, 0x0a, 0x0c, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x41, 0x57, 0x10,
	0x0e, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73,
	0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x3b, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v1_model_request_proto_rawDescOnce sync.Once
	file_v1_model_request_proto_rawDescData = file_v1_model_request_proto_rawDesc
)

func file_v1_model_request_proto_rawDescGZIP() []byte {
	file_v1_model_request_proto_rawDescOnce.Do(func() {
		file_v1_model_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_v1_model_request_proto_rawDescData)
	})
	return file_v1_model_request_proto_rawDescData
}

var file_v1_model_request_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_v1_model_request_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_v1_model_request_proto_goTypes = []interface{}{
	(DiscoverRequest_DiscoverRequestType)(0),         // 0: api.v1.model.DiscoverRequest.DiscoverRequestType
	(WatchRequest_WatchRequestType)(0),               // 1: api.v1.model.WatchRequest.WatchRequestType
	(*DiscoverRequest)(nil),                          // 2: api.v1.model.DiscoverRequest
	(*WatchRequest)(nil),                             // 3: api.v1.model.WatchRequest
	(*DiscoverRequest_DiscoverRequestSignature)(nil), // 4: api.v1.model.DiscoverRequest.DiscoverRequestSignature
	(*Service)(nil),                                  // 5: api.v1.model.Service
	(*Mesh)(nil),                                     // 6: api.v1.model.Mesh
	(*MeshConfig)(nil),                               // 7: api.v1.model.MeshConfig
}
var file_v1_model_request_proto_depIdxs = []int32{
	0, // 0: api.v1.model.DiscoverRequest.type:type_name -> api.v1.model.DiscoverRequest.DiscoverRequestType
	5, // 1: api.v1.model.DiscoverRequest.service:type_name -> api.v1.model.Service
	6, // 2: api.v1.model.DiscoverRequest.mesh:type_name -> api.v1.model.Mesh
	7, // 3: api.v1.model.DiscoverRequest.meshConfig:type_name -> api.v1.model.MeshConfig
	4, // 4: api.v1.model.DiscoverRequest.signature:type_name -> api.v1.model.DiscoverRequest.DiscoverRequestSignature
	1, // 5: api.v1.model.WatchRequest.type:type_name -> api.v1.model.WatchRequest.WatchRequestType
	5, // 6: api.v1.model.WatchRequest.service:type_name -> api.v1.model.Service
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_v1_model_request_proto_init() }
func file_v1_model_request_proto_init() {
	if File_v1_model_request_proto != nil {
		return
	}
	file_v1_model_service_proto_init()
	file_v1_model_mesh_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_v1_model_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiscoverRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_request_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_request_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiscoverRequest_DiscoverRequestSignature); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_model_request_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v1_model_request_proto_goTypes,
		DependencyIndexes: file_v1_model_request_proto_depIdxs,
		EnumInfos:         file_v1_model_request_proto_enumTypes,
		MessageInfos:      file_v1_model_request_proto_msgTypes,
	}.Build()
	File_v1_model_request_proto = out.File
	file_v1_model_request_proto_rawDesc = nil
	file_v1_model_request_proto_goTypes = nil
	file_v1_model_request_proto_depIdxs = nil
}
