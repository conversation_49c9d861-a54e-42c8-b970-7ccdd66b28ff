syntax = "proto3";

package trpc.zy_socail.qq_photos;

option go_package = "git.woa.com/trpcprotocol/zy_socail/qq_photos";

//定义服务接口
service QzonePhotoTool{
    rpc RawFixTool(RawFixToolRequest) returns (RawFixToolReply);
}
//请求参数
message RawFixToolRequest{
    repeated uint64 id = 1;
}
//响应参数
message RawFixToolReply{
    repeated PhotoErrorInfo errorList = 1;
}

message PhotoErrorInfo{
    string uin = 1;
    string albumId = 2;
    string lloc = 3;
}