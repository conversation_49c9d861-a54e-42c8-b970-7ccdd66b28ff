package psa

import (
	"encoding/base64"
	"encoding/binary"
	"encoding/hex"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	storephotosdk "git.woa.com/trpcprotocol/qzone/storage_photo"
	"github.com/forgoer/openssl"
	"google.golang.org/protobuf/proto"
)

const (
	magicNum = 0x12344321
	key      = "466b828e33312602d0fe33b0ee72e1c3"
	version  = 1
)

// PhotoInfo 图片上传成功后相关的参数
type PhotoInfo struct {
	PhotoID  string
	BucketID string
}

// PhotoURLType 图片链接类型
type PhotoURLType int8

const (
	RAW        PhotoURLType = 1
	BIG        PhotoURLType = 2
	SMALL      PhotoURLType = 3
	BIG_LOGO   PhotoURLType = 4
	SMALL_LOGO PhotoURLType = 5
)

var urlTypeToSuffix = map[PhotoURLType]string{
	RAW:        "o",
	BIG:        "b",
	SMALL:      "a",
	BIG_LOGO:   "b",
	SMALL_LOGO: "a",
}

func getPhotoUrlSpecSuffix(t PhotoURLType) string {
	if s, ok := urlTypeToSuffix[t]; ok {
		return s
	}
	return "b"
}

// GenCloudURLFromLloc 拼接图片链接
func GenCloudURLFromLloc(uin string, lloccode string,
	uploadTime uint32, albumID string, photoUrlType PhotoURLType) string {
	return genURLFromLloc(uin, lloccode, uploadTime, albumID,
		func(photoInfo *PhotoInfo, albumID, encryptedURLInfo string) string {
			return fmt.Sprintf("https://%s.photo.store.qq.com/psc?/%s/%s/%s", photoInfo.BucketID, albumID,
				encryptedURLInfo, getPhotoUrlSpecSuffix(photoUrlType))
		})
}

func genURLFromLloc(uin string, lloccode string, uploadTime uint32, albumID string,
	domainFunc func(photoInfo *PhotoInfo, albumID, encryptedURLInfo string) string) string {
	photoInfo, err := GenPhotoInfo(lloccode)
	if err != nil {
		log.Errorf("GenPhotoInfo has err:%v", err)
		return ""
	}
	info := newCloudURLGen(uin, photoInfo.PhotoID, photoInfo.BucketID, uploadTime)
	encodedURLInfo := encodeCloudURLInfo(info)
	encryptedURLInfo := encrypt(encodedURLInfo)
	return domainFunc(&photoInfo, albumID, encryptedURLInfo)
}

// GenPhotoInfo 从lloccode中解析出图片信息
func GenPhotoInfo(lloccode string) (PhotoInfo, error) {
	photoInfo := PhotoInfo{}
	if len(lloccode) <= 5 {
		return photoInfo, errors.New("lloccode is empty")
	}
	// 替换字符
	lloccode = unescape(lloccode)
	// 解码
	originStr, err := base64.URLEncoding.DecodeString(lloccode)
	if err != nil {
		return photoInfo, errors.New("Base64 decode lloccode has err")
	}
	// 第一部分，编码类型，现在都是5
	// codeType := originStr[:1]))
	// 第二部分，photoID
	photoIDLen := binary.LittleEndian.Uint16(originStr[1:3])
	originLen := uint16(len(originStr))
	if photoIDLen > originLen {
		return photoInfo, fmt.Errorf("photo uuid format error")
	}
	photoInfo.PhotoID = string(originStr[3 : 3+photoIDLen])
	// 第三部分，bucketID
	if photoIDLen+2 > originLen {
		return photoInfo, errors.New("Base64 decode lloccode has err")
	}
	startIndex := 3 + photoIDLen + 2
	bucketIDLen := binary.LittleEndian.Uint16(originStr[3+photoIDLen : 3+photoIDLen+2])
	if startIndex+bucketIDLen > originLen {
		return photoInfo, errors.New("Base64 decode lloccode has err")
	}
	photoInfo.BucketID = string(originStr[startIndex : startIndex+bucketIDLen])
	return photoInfo, nil
}

// unescape ...
func unescape(input string) string {
	res := strings.ReplaceAll(input, "*", "/")
	res = strings.ReplaceAll(res, "!", "=")
	res = strings.ReplaceAll(res, ".", "+")
	return res
}

func newCloudURLGen(uin, photoID, bucketID string, uploadTime uint32) *storephotosdk.CloudUrlGen {
	return &storephotosdk.CloudUrlGen{
		MagicNum:   proto.Uint32(magicNum),
		Version:    proto.Uint32(version),
		PhotoId:    []byte(photoID),
		BucketId:   []byte(bucketID),
		Uin:        []byte(uin),
		UploadTime: proto.Uint32(uploadTime),
	}
}

// encodeCloudURLInfo ...
func encodeCloudURLInfo(info *storephotosdk.CloudUrlGen) []byte {
	buf, _ := proto.Marshal(info)
	buffer := make([]byte, 2, 2+len(buf))
	binary.LittleEndian.PutUint16(buffer[:2], uint16(len(buf)))
	buffer = append(buffer, buf...)
	return buffer
}

// encrypt ...
func encrypt(plainText []byte) string {
	k, _ := hex.DecodeString(key)
	cipherText, _ := openssl.AesCBCEncrypt(plainText, k, make([]byte, 16), openssl.PKCS5_PADDING)
	base64Text := base64.StdEncoding.EncodeToString(cipherText)
	return escape(base64Text)
}

// escape ...
func escape(input string) string {
	res := strings.ReplaceAll(input, "/", "*")
	res = strings.ReplaceAll(res, "=", "!")
	res = strings.ReplaceAll(res, "+", ".")
	return res
}

func GetWeightAndHeightFromURL(src string) (int32, int32) {
	w5, h5, _ := getPicFromURL(src)
	return int32(w5), int32(h5)
}

func getPicFromURL(src string) (int32, int32, int32) {
	u, err := url.Parse(src)
	if err != nil {
		return 0, 0, 0
	}
	v, err := url.ParseQuery(u.RawQuery)
	if err != nil {
		return 0, 0, 0
	}
	h5, _ := strconv.ParseInt(v.Get("h5"), 10, 32)
	w5, _ := strconv.ParseInt(v.Get("w5"), 10, 32)
	t, _ := strconv.ParseInt(v.Get("t"), 10, 32)
	return int32(w5), int32(h5), int32(t)
}

func GetPicInfoFromURL(src string) (int32, int32, int32) {
	return getPicFromURL(src)
}

// GenerateVideoUrl http://photovideo.photo.qq.com/1075_0b5372efyeyaaial3vexfnsth7qelsjaawca.f0.mp4
func GenerateVideoUrl(vid string) string {
	return fmt.Sprintf("http://photovideo.photo.qq.com/%s.f0.mp4", vid)
}