// Package PHOTO comment
// Code generated by trpc4tars 2.0. DO NOT EDIT.
// source: photo_reserve.jce
package PHOTO

import (
	"fmt"

	"git.woa.com/jce/jce"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = fmt.Errorf
var _ = jce.Marshal

type AlbumReserveID int32

const (
	AlbumReserveID_EAlbumReserveTimeEvent                  = 1
	AlbumReserveID_EAlbumReserveTravel                     = 2
	AlbumReserveID_EAlbumReserveCollege                    = 3
	AlbumReserveID_EAlbumReserveIndividual                 = 4
	AlbumReserveID_EAlbumReserveShare                      = 5
	AlbumReserveID_EAlbumReserveLovers                     = 6
	AlbumReserveID_EAlbumReserveTypeRecommend              = 7
	AlbumReserveID_EAlbumReserveNormalCalendar             = 8
	AlbumReserveID_EAlbumReserveAlbumSortType              = 9
	AlbumReserveID_EAlbumReserveNormalCalendarForShoottime = 10
	AlbumReserveID_EAlbumReserveKantuCoverInfo             = 11
)

type EventAction int32

const (
	EventAction_KAddEvent = 1
	EventAction_KModEvent = 2
	EventAction_KDelEvent = 3
)

type CalendarUpdateType int32

const (
	CalendarUpdateType_CUT_INTI    = 0
	CalendarUpdateType_CUT_INVALID = 1
	CalendarUpdateType_CUT_REBUILD = 2
	CalendarUpdateType_CUT_DEL     = 3
	CalendarUpdateType_CUT_INSERT  = 4
	CalendarUpdateType_CUT_UPLOAD  = 5
)

type Enum_groupid_status int32

const (
	Enum_groupid_status_KGroupidNormal     = 0
	Enum_groupid_status_KGroupidUserDelete = 1
)

type Enum_groupid_confirm_status int32

const (
	Enum_groupid_confirm_status_KGroupidConfirmDefault = 0
	Enum_groupid_confirm_status_KGroupidConfirmUin     = 1
	Enum_groupid_confirm_status_KGroupidConfirmLabel   = 2
)

type Enum_face_source int32

const (
	Enum_face_source_KFaceSourceRunData = 0
	Enum_face_source_KFaceSourceUpload  = 1
)

type Enum_cluster_version int32

const (
	Enum_cluster_version_KClusterVersionDefault  = 0
	Enum_cluster_version_KClusterVersion20170306 = 1
)

type Enum_label_version int32

const (
	Enum_label_version_KLabelVersionDefault  = 0
	Enum_label_version_KLabelVersion20170306 = 1
	Enum_label_version_KLabelVersion13       = 2
	Enum_label_version_KLabelVersion198      = 3
)

type Enum_label_status int32

const (
	Enum_label_status_KLabelStatusNormal     = 0
	Enum_label_status_KLabelStatusUserDelete = 1
)

type Enum_photo_tag_type int32

const (
	Enum_photo_tag_type_KPhotoTagTypeDefault = 0
	Enum_photo_tag_type_KPhotoTagTypeFace    = 1
	Enum_photo_tag_type_KPhotoTagTypePoi     = 2
	Enum_photo_tag_type_KPhotoTagTypeLabel   = 3
)

// AlbumReserve struct implement
type AlbumReserve struct {
	Map_reserve map[AlbumReserveID]string `json:"map_reserve"`
}

func (st *AlbumReserve) MakesureNotNil() {
}
func (st *AlbumReserve) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *AlbumReserve) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have = _is.SkipTo(jce.MAP, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&length, 0, true)
	if err != nil {
		return err
	}

	st.Map_reserve = make(map[AlbumReserveID]string)
	for i0, e0 := int32(0), length; i0 < e0; i0++ {
		var k0 AlbumReserveID
		var v0 string

		err = _is.Read_int32((*int32)(&k0), 0, false)
		if err != nil {
			return err
		}

		err = _is.Read_string(&v0, 1, false)
		if err != nil {
			return err
		}

		st.Map_reserve[k0] = v0
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *AlbumReserve) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require AlbumReserve, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *AlbumReserve) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.MAP, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Map_reserve)), 0)
	if err != nil {
		return err
	}

	for k1, v1 := range st.Map_reserve {

		err = _os.Write_int32(int32(k1), 0)
		if err != nil {
			return err
		}

		err = _os.Write_string(v1, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *AlbumReserve) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *AlbumReserve) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *AlbumReserve) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *AlbumReserve) WupTypeName() string {
	return "PHOTO.AlbumReserve"
}

// CalendarItem struct implement
type CalendarItem struct {
	BeginTM  int64 `json:"beginTM"`
	EndTM    int64 `json:"endTM"`
	PhotoNum int32 `json:"photoNum"`
}

func (st *CalendarItem) MakesureNotNil() {
}
func (st *CalendarItem) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *CalendarItem) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_int64(&st.BeginTM, 0, false)
	if err != nil {
		return err
	}

	err = _is.Read_int64(&st.EndTM, 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.PhotoNum, 2, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *CalendarItem) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require CalendarItem, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *CalendarItem) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_int64(st.BeginTM, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int64(st.EndTM, 1)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.PhotoNum, 2)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *CalendarItem) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *CalendarItem) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *CalendarItem) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *CalendarItem) WupTypeName() string {
	return "PHOTO.CalendarItem"
}

// NormalCalendar struct implement
type NormalCalendar struct {
	CacheVersion  int32              `json:"cacheVersion"`
	LastUpdateTM  int64              `json:"lastUpdateTM"`
	UpdateCounter int32              `json:"updateCounter"`
	UpdateType    CalendarUpdateType `json:"updateType"`
	PhotoTotalNum int32              `json:"photoTotalNum"`
	Calendar      []CalendarItem     `json:"calendar"`
}

func (st *NormalCalendar) MakesureNotNil() {
}
func (st *NormalCalendar) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *NormalCalendar) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_int32(&st.CacheVersion, 0, false)
	if err != nil {
		return err
	}

	err = _is.Read_int64(&st.LastUpdateTM, 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.UpdateCounter, 2, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32((*int32)(&st.UpdateType), 3, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.PhotoTotalNum, 4, false)
	if err != nil {
		return err
	}

	err, have, ty = _is.SkipToNoCheck(5, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.Calendar = make([]CalendarItem, length)
			for i0, e0 := int32(0), length; i0 < e0; i0++ {

				err = st.Calendar[i0].ReadBlock(_is, 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {
			err = fmt.Errorf("not support simple_list type")
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *NormalCalendar) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require NormalCalendar, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *NormalCalendar) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_int32(st.CacheVersion, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int64(st.LastUpdateTM, 1)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.UpdateCounter, 2)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(st.UpdateType), 3)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.PhotoTotalNum, 4)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.LIST, 5)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Calendar)), 0)
	if err != nil {
		return err
	}

	for _, v := range st.Calendar {

		err = v.WriteBlock(_os, 0)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *NormalCalendar) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *NormalCalendar) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *NormalCalendar) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *NormalCalendar) WupTypeName() string {
	return "PHOTO.NormalCalendar"
}

// AlbumTypeRecommend struct implement
type AlbumTypeRecommend struct {
	Has_recommend  bool   `json:"has_recommend"`
	Recommend_type int32  `json:"recommend_type"`
	Timestamp      uint32 `json:"timestamp"`
}

func (st *AlbumTypeRecommend) MakesureNotNil() {
}
func (st *AlbumTypeRecommend) ResetDefault() {
	st.MakesureNotNil()
	st.Has_recommend = false
	st.Recommend_type = 0
	st.Timestamp = 0
}

// ReadFrom reads  from _is and put into struct.
func (st *AlbumTypeRecommend) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_bool(&st.Has_recommend, 0, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Recommend_type, 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Timestamp, 2, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *AlbumTypeRecommend) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require AlbumTypeRecommend, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *AlbumTypeRecommend) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_bool(st.Has_recommend, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Recommend_type, 1)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Timestamp, 2)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *AlbumTypeRecommend) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *AlbumTypeRecommend) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *AlbumTypeRecommend) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *AlbumTypeRecommend) WupTypeName() string {
	return "PHOTO.AlbumTypeRecommend"
}

// TimeEvent struct implement
type TimeEvent struct {
	Time       int64  `json:"time"`
	Type       uint32 `json:"type"`
	Content    string `json:"content"`
	Copywriter string `json:"copywriter"`
}

func (st *TimeEvent) MakesureNotNil() {
}
func (st *TimeEvent) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *TimeEvent) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_int64(&st.Time, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Type, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Content, 2, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Copywriter, 3, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *TimeEvent) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require TimeEvent, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *TimeEvent) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_int64(st.Time, 0)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Type, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Content, 2)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Copywriter, 3)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *TimeEvent) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *TimeEvent) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *TimeEvent) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *TimeEvent) WupTypeName() string {
	return "PHOTO.TimeEvent"
}

// TimeEventOp struct implement
type TimeEventOp struct {
	Events TimeEvent   `json:"events"`
	Action EventAction `json:"action"`
}

func (st *TimeEventOp) MakesureNotNil() {
}
func (st *TimeEventOp) ResetDefault() {
	st.MakesureNotNil()
	st.Events.ResetDefault()
}

// ReadFrom reads  from _is and put into struct.
func (st *TimeEventOp) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = st.Events.ReadBlock(_is, 0, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32((*int32)(&st.Action), 1, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *TimeEventOp) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require TimeEventOp, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *TimeEventOp) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = st.Events.WriteBlock(_os, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(st.Action), 1)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *TimeEventOp) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *TimeEventOp) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *TimeEventOp) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *TimeEventOp) WupTypeName() string {
	return "PHOTO.TimeEventOp"
}

// TimeEventList struct implement
type TimeEventList struct {
	Time_event_list             []TimeEvent `json:"time_event_list"`
	Birth_time                  uint32      `json:"birth_time"`
	NickName                    string      `json:"nickName"`
	Weight                      int32       `json:"weight"`
	Sexual                      int32       `json:"sexual"`
	Fixed_event_content_charset bool        `json:"fixed_event_content_charset"`
}

func (st *TimeEventList) MakesureNotNil() {
}
func (st *TimeEventList) ResetDefault() {
	st.MakesureNotNil()
	st.Fixed_event_content_charset = false
}

// ReadFrom reads  from _is and put into struct.
func (st *TimeEventList) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have, ty = _is.SkipToNoCheck(0, true)
	if err != nil {
		return err
	}

	if ty == jce.LIST {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.Time_event_list = make([]TimeEvent, length)
		for i0, e0 := int32(0), length; i0 < e0; i0++ {

			err = st.Time_event_list[i0].ReadBlock(_is, 0, false)
			if err != nil {
				return err
			}

		}
	} else if ty == jce.SIMPLE_LIST {
		err = fmt.Errorf("not support simple_list type")
		if err != nil {
			return err
		}

	} else {
		err = fmt.Errorf("require vector, but not")
		if err != nil {
			return err
		}

	}

	err = _is.Read_uint32(&st.Birth_time, 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.NickName, 2, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Weight, 3, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Sexual, 4, false)
	if err != nil {
		return err
	}

	err = _is.Read_bool(&st.Fixed_event_content_charset, 5, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *TimeEventList) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require TimeEventList, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *TimeEventList) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.LIST, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Time_event_list)), 0)
	if err != nil {
		return err
	}

	for _, v := range st.Time_event_list {

		err = v.WriteBlock(_os, 0)
		if err != nil {
			return err
		}

	}

	err = _os.Write_uint32(st.Birth_time, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.NickName, 2)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Weight, 3)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Sexual, 4)
	if err != nil {
		return err
	}

	err = _os.Write_bool(st.Fixed_event_content_charset, 5)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *TimeEventList) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *TimeEventList) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *TimeEventList) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *TimeEventList) WupTypeName() string {
	return "PHOTO.TimeEventList"
}

// LoversTimeEventList struct implement
type LoversTimeEventList struct {
	Time_event_list             []TimeEvent `json:"time_event_list"`
	Lover_time                  uint32      `json:"lover_time"`
	Fixed_event_content_charset bool        `json:"fixed_event_content_charset"`
}

func (st *LoversTimeEventList) MakesureNotNil() {
}
func (st *LoversTimeEventList) ResetDefault() {
	st.MakesureNotNil()
	st.Fixed_event_content_charset = false
}

// ReadFrom reads  from _is and put into struct.
func (st *LoversTimeEventList) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have, ty = _is.SkipToNoCheck(0, true)
	if err != nil {
		return err
	}

	if ty == jce.LIST {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.Time_event_list = make([]TimeEvent, length)
		for i0, e0 := int32(0), length; i0 < e0; i0++ {

			err = st.Time_event_list[i0].ReadBlock(_is, 0, false)
			if err != nil {
				return err
			}

		}
	} else if ty == jce.SIMPLE_LIST {
		err = fmt.Errorf("not support simple_list type")
		if err != nil {
			return err
		}

	} else {
		err = fmt.Errorf("require vector, but not")
		if err != nil {
			return err
		}

	}

	err = _is.Read_uint32(&st.Lover_time, 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_bool(&st.Fixed_event_content_charset, 2, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *LoversTimeEventList) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require LoversTimeEventList, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *LoversTimeEventList) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.LIST, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Time_event_list)), 0)
	if err != nil {
		return err
	}

	for _, v := range st.Time_event_list {

		err = v.WriteBlock(_os, 0)
		if err != nil {
			return err
		}

	}

	err = _os.Write_uint32(st.Lover_time, 1)
	if err != nil {
		return err
	}

	err = _os.Write_bool(st.Fixed_event_content_charset, 2)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *LoversTimeEventList) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *LoversTimeEventList) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *LoversTimeEventList) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *LoversTimeEventList) WupTypeName() string {
	return "PHOTO.LoversTimeEventList"
}

// CollegeTimeEvent struct implement
type CollegeTimeEvent struct {
	Time    int64  `json:"time"`
	Content string `json:"content"`
}

func (st *CollegeTimeEvent) MakesureNotNil() {
}
func (st *CollegeTimeEvent) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *CollegeTimeEvent) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_int64(&st.Time, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Content, 1, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *CollegeTimeEvent) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require CollegeTimeEvent, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *CollegeTimeEvent) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_int64(st.Time, 0)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Content, 1)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *CollegeTimeEvent) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *CollegeTimeEvent) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *CollegeTimeEvent) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *CollegeTimeEvent) WupTypeName() string {
	return "PHOTO.CollegeTimeEvent"
}

// CollegeRecordCardInfo struct implement
type CollegeRecordCardInfo struct {
	Degree_type     uint32 `json:"degree_type"`
	Enroll_time     uint32 `json:"enroll_time"`
	Graduation_time uint32 `json:"graduation_time"`
	College         string `json:"college"`
	College_major   string `json:"college_major"`
}

func (st *CollegeRecordCardInfo) MakesureNotNil() {
}
func (st *CollegeRecordCardInfo) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *CollegeRecordCardInfo) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_uint32(&st.Degree_type, 0, false)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Enroll_time, 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Graduation_time, 2, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.College, 3, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.College_major, 4, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *CollegeRecordCardInfo) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require CollegeRecordCardInfo, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *CollegeRecordCardInfo) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_uint32(st.Degree_type, 0)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Enroll_time, 1)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Graduation_time, 2)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.College, 3)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.College_major, 4)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *CollegeRecordCardInfo) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *CollegeRecordCardInfo) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *CollegeRecordCardInfo) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *CollegeRecordCardInfo) WupTypeName() string {
	return "PHOTO.CollegeRecordCardInfo"
}

// CollegeTimeEventList struct implement
type CollegeTimeEventList struct {
	College_time_event_list []CollegeTimeEvent    `json:"college_time_event_list"`
	Record_card             CollegeRecordCardInfo `json:"record_card"`
}

func (st *CollegeTimeEventList) MakesureNotNil() {
}
func (st *CollegeTimeEventList) ResetDefault() {
	st.MakesureNotNil()
	st.Record_card.ResetDefault()
}

// ReadFrom reads  from _is and put into struct.
func (st *CollegeTimeEventList) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have, ty = _is.SkipToNoCheck(0, true)
	if err != nil {
		return err
	}

	if ty == jce.LIST {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.College_time_event_list = make([]CollegeTimeEvent, length)
		for i0, e0 := int32(0), length; i0 < e0; i0++ {

			err = st.College_time_event_list[i0].ReadBlock(_is, 0, false)
			if err != nil {
				return err
			}

		}
	} else if ty == jce.SIMPLE_LIST {
		err = fmt.Errorf("not support simple_list type")
		if err != nil {
			return err
		}

	} else {
		err = fmt.Errorf("require vector, but not")
		if err != nil {
			return err
		}

	}

	err = st.Record_card.ReadBlock(_is, 1, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *CollegeTimeEventList) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require CollegeTimeEventList, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *CollegeTimeEventList) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.LIST, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.College_time_event_list)), 0)
	if err != nil {
		return err
	}

	for _, v := range st.College_time_event_list {

		err = v.WriteBlock(_os, 0)
		if err != nil {
			return err
		}

	}

	err = st.Record_card.WriteBlock(_os, 1)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *CollegeTimeEventList) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *CollegeTimeEventList) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *CollegeTimeEventList) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *CollegeTimeEventList) WupTypeName() string {
	return "PHOTO.CollegeTimeEventList"
}

// MaterialFile struct implement
type MaterialFile struct {
	IFileId   int32  `json:"iFileId"`
	StrName   string `json:"strName"`
	StrUrl    string `json:"strUrl"`
	StrMd5    string `json:"strMd5"`
	ISize     int32  `json:"iSize"`
	IFileType int32  `json:"iFileType"`
	IWidth    int32  `json:"iWidth"`
	IHeight   int32  `json:"iHeight"`
}

func (st *MaterialFile) MakesureNotNil() {
}
func (st *MaterialFile) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *MaterialFile) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_int32(&st.IFileId, 0, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.StrName, 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.StrUrl, 2, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.StrMd5, 3, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.ISize, 4, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.IFileType, 5, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.IWidth, 6, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.IHeight, 7, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *MaterialFile) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require MaterialFile, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *MaterialFile) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_int32(st.IFileId, 0)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.StrName, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.StrUrl, 2)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.StrMd5, 3)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.ISize, 4)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.IFileType, 5)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.IWidth, 6)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.IHeight, 7)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *MaterialFile) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *MaterialFile) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *MaterialFile) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *MaterialFile) WupTypeName() string {
	return "PHOTO.MaterialFile"
}

// MaterialItem struct implement
type MaterialItem struct {
	IItemId         int32             `json:"iItemId"`
	ITypeId         int32             `json:"iTypeId"`
	IItemType       int32             `json:"iItemType"`
	StrItemName     string            `json:"strItemName"`
	IExpireTime     int32             `json:"iExpireTime"`
	VecFile         []MaterialFile    `json:"vecFile"`
	StrItemSummary  string            `json:"strItemSummary"`
	StrDescription  string            `json:"strDescription"`
	StThumb         MaterialFile      `json:"stThumb"`
	StBanner        MaterialFile      `json:"stBanner"`
	UiSettleTime    uint32            `json:"uiSettleTime"`
	StrTraceInfo    string            `json:"strTraceInfo"`
	StrDesignerInfo string            `json:"strDesignerInfo"`
	StrExtFields    []int8            `json:"strExtFields"`
	MapExtInfo      map[string]string `json:"mapExtInfo"`
}

func (st *MaterialItem) MakesureNotNil() {
}
func (st *MaterialItem) ResetDefault() {
	st.MakesureNotNil()
	st.StThumb.ResetDefault()
	st.StBanner.ResetDefault()
}

// ReadFrom reads  from _is and put into struct.
func (st *MaterialItem) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_int32(&st.IItemId, 0, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.ITypeId, 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.IItemType, 2, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.StrItemName, 3, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.IExpireTime, 4, false)
	if err != nil {
		return err
	}

	err, have, ty = _is.SkipToNoCheck(5, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.VecFile = make([]MaterialFile, length)
			for i0, e0 := int32(0), length; i0 < e0; i0++ {

				err = st.VecFile[i0].ReadBlock(_is, 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {
			err = fmt.Errorf("not support simple_list type")
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	err = _is.Read_string(&st.StrItemSummary, 6, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.StrDescription, 7, false)
	if err != nil {
		return err
	}

	err = st.StThumb.ReadBlock(_is, 8, false)
	if err != nil {
		return err
	}

	err = st.StBanner.ReadBlock(_is, 9, false)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.UiSettleTime, 10, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.StrTraceInfo, 11, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.StrDesignerInfo, 12, false)
	if err != nil {
		return err
	}

	err, have, ty = _is.SkipToNoCheck(13, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.StrExtFields = make([]int8, length)
			for i1, e1 := int32(0), length; i1 < e1; i1++ {

				err = _is.Read_int8(&st.StrExtFields[i1], 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {

			err, _ = _is.SkipTo(jce.BYTE, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			err = _is.Read_slice_int8(&st.StrExtFields, length, true)
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	err, have = _is.SkipTo(jce.MAP, 14, false)
	if err != nil {
		return err
	}

	if have {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.MapExtInfo = make(map[string]string)
		for i2, e2 := int32(0), length; i2 < e2; i2++ {
			var k2 string
			var v2 string

			err = _is.Read_string(&k2, 0, false)
			if err != nil {
				return err
			}

			err = _is.Read_string(&v2, 1, false)
			if err != nil {
				return err
			}

			st.MapExtInfo[k2] = v2
		}
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *MaterialItem) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require MaterialItem, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *MaterialItem) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_int32(st.IItemId, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.ITypeId, 1)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.IItemType, 2)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.StrItemName, 3)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.IExpireTime, 4)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.LIST, 5)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.VecFile)), 0)
	if err != nil {
		return err
	}

	for _, v := range st.VecFile {

		err = v.WriteBlock(_os, 0)
		if err != nil {
			return err
		}

	}

	err = _os.Write_string(st.StrItemSummary, 6)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.StrDescription, 7)
	if err != nil {
		return err
	}

	err = st.StThumb.WriteBlock(_os, 8)
	if err != nil {
		return err
	}

	err = st.StBanner.WriteBlock(_os, 9)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.UiSettleTime, 10)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.StrTraceInfo, 11)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.StrDesignerInfo, 12)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.SIMPLE_LIST, 13)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.BYTE, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.StrExtFields)), 0)
	if err != nil {
		return err
	}

	err = _os.Write_slice_int8(st.StrExtFields)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.MAP, 14)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.MapExtInfo)), 0)
	if err != nil {
		return err
	}

	for k3, v3 := range st.MapExtInfo {

		err = _os.Write_string(k3, 0)
		if err != nil {
			return err
		}

		err = _os.Write_string(v3, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *MaterialItem) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *MaterialItem) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *MaterialItem) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *MaterialItem) WupTypeName() string {
	return "PHOTO.MaterialItem"
}

// IndividualList struct implement
type IndividualList struct {
	IsIndividual bool         `json:"isIndividual"`
	Material     MaterialItem `json:"material"`
}

func (st *IndividualList) MakesureNotNil() {
}
func (st *IndividualList) ResetDefault() {
	st.MakesureNotNil()
	st.IsIndividual = false
	st.Material.ResetDefault()
}

// ReadFrom reads  from _is and put into struct.
func (st *IndividualList) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_bool(&st.IsIndividual, 0, false)
	if err != nil {
		return err
	}

	err = st.Material.ReadBlock(_is, 1, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *IndividualList) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require IndividualList, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *IndividualList) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_bool(st.IsIndividual, 0)
	if err != nil {
		return err
	}

	err = st.Material.WriteBlock(_os, 1)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *IndividualList) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *IndividualList) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *IndividualList) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *IndividualList) WupTypeName() string {
	return "PHOTO.IndividualList"
}

// Face_poi struct implement
type Face_poi struct {
	X int32 `json:"x"`
	Y int32 `json:"y"`
	W int32 `json:"w"`
	H int32 `json:"h"`
}

func (st *Face_poi) MakesureNotNil() {
}
func (st *Face_poi) ResetDefault() {
	st.MakesureNotNil()
	st.X = 0
	st.Y = 0
	st.W = 0
	st.H = 0
}

// ReadFrom reads  from _is and put into struct.
func (st *Face_poi) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_int32(&st.X, 0, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Y, 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.W, 2, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.H, 3, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *Face_poi) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require Face_poi, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *Face_poi) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_int32(st.X, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Y, 1)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.W, 2)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.H, 3)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *Face_poi) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *Face_poi) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *Face_poi) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *Face_poi) WupTypeName() string {
	return "PHOTO.face_poi"
}

// Uin_face_op struct implement
type Uin_face_op struct {
	Face_uin       uint32                      `json:"face_uin"`
	Status         Enum_groupid_status         `json:"status"`
	User_label     string                      `json:"user_label"`
	Confirm_status Enum_groupid_confirm_status `json:"confirm_status"`
}

func (st *Uin_face_op) MakesureNotNil() {
}
func (st *Uin_face_op) ResetDefault() {
	st.MakesureNotNil()
	st.Face_uin = 0
}

// ReadFrom reads  from _is and put into struct.
func (st *Uin_face_op) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_uint32(&st.Face_uin, 0, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32((*int32)(&st.Status), 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.User_label, 2, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32((*int32)(&st.Confirm_status), 3, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *Uin_face_op) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require Uin_face_op, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *Uin_face_op) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_uint32(st.Face_uin, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(st.Status), 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.User_label, 2)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(st.Confirm_status), 3)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *Uin_face_op) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *Uin_face_op) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *Uin_face_op) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *Uin_face_op) WupTypeName() string {
	return "PHOTO.uin_face_op"
}

// Groupid_face struct implement
type Groupid_face struct {
	Groupid           string                      `json:"groupid"`
	Facepoi           Face_poi                    `json:"facepoi"`
	Has_identify      bool                        `json:"has_identify"`
	Face_uin          uint32                      `json:"face_uin"`
	Status            Enum_groupid_status         `json:"status"`
	User_label        string                      `json:"user_label"`
	Confirm_status    Enum_groupid_confirm_status `json:"confirm_status"`
	Last_confirm_time uint32                      `json:"last_confirm_time"`
	Uin_face_ops      map[uint32]Uin_face_op      `json:"uin_face_ops"`
	Source            Enum_face_source            `json:"source"`
}

func (st *Groupid_face) MakesureNotNil() {
}
func (st *Groupid_face) ResetDefault() {
	st.MakesureNotNil()
	st.Facepoi.ResetDefault()
	st.Has_identify = false
	st.Face_uin = 0
}

// ReadFrom reads  from _is and put into struct.
func (st *Groupid_face) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_string(&st.Groupid, 0, false)
	if err != nil {
		return err
	}

	err = st.Facepoi.ReadBlock(_is, 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_bool(&st.Has_identify, 2, false)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Face_uin, 3, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32((*int32)(&st.Status), 4, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.User_label, 5, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32((*int32)(&st.Confirm_status), 6, false)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Last_confirm_time, 7, false)
	if err != nil {
		return err
	}

	err, have = _is.SkipTo(jce.MAP, 8, false)
	if err != nil {
		return err
	}

	if have {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.Uin_face_ops = make(map[uint32]Uin_face_op)
		for i0, e0 := int32(0), length; i0 < e0; i0++ {
			var k0 uint32
			var v0 Uin_face_op

			err = _is.Read_uint32(&k0, 0, false)
			if err != nil {
				return err
			}

			err = v0.ReadBlock(_is, 1, false)
			if err != nil {
				return err
			}

			st.Uin_face_ops[k0] = v0
		}
	}

	err = _is.Read_int32((*int32)(&st.Source), 9, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *Groupid_face) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require Groupid_face, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *Groupid_face) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_string(st.Groupid, 0)
	if err != nil {
		return err
	}

	err = st.Facepoi.WriteBlock(_os, 1)
	if err != nil {
		return err
	}

	err = _os.Write_bool(st.Has_identify, 2)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Face_uin, 3)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(st.Status), 4)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.User_label, 5)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(st.Confirm_status), 6)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Last_confirm_time, 7)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.MAP, 8)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Uin_face_ops)), 0)
	if err != nil {
		return err
	}

	for k1, v1 := range st.Uin_face_ops {

		err = _os.Write_uint32(k1, 0)
		if err != nil {
			return err
		}

		err = v1.WriteBlock(_os, 1)
		if err != nil {
			return err
		}

	}

	err = _os.Write_int32(int32(st.Source), 9)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *Groupid_face) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *Groupid_face) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *Groupid_face) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *Groupid_face) WupTypeName() string {
	return "PHOTO.groupid_face"
}

// Pic_cluster_info struct implement
type Pic_cluster_info struct {
	Has_face       bool                 `json:"has_face"`
	Is_cluster     bool                 `json:"is_cluster"`
	Groupid_faces  []Groupid_face       `json:"groupid_faces"`
	Version        Enum_cluster_version `json:"version"`
	Is_face_detect bool                 `json:"is_face_detect"`
}

func (st *Pic_cluster_info) MakesureNotNil() {
}
func (st *Pic_cluster_info) ResetDefault() {
	st.MakesureNotNil()
	st.Has_face = false
	st.Is_cluster = false
	st.Is_face_detect = false
}

// ReadFrom reads  from _is and put into struct.
func (st *Pic_cluster_info) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_bool(&st.Has_face, 0, false)
	if err != nil {
		return err
	}

	err = _is.Read_bool(&st.Is_cluster, 1, false)
	if err != nil {
		return err
	}

	err, have, ty = _is.SkipToNoCheck(2, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.Groupid_faces = make([]Groupid_face, length)
			for i0, e0 := int32(0), length; i0 < e0; i0++ {

				err = st.Groupid_faces[i0].ReadBlock(_is, 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {
			err = fmt.Errorf("not support simple_list type")
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	err = _is.Read_int32((*int32)(&st.Version), 3, false)
	if err != nil {
		return err
	}

	err = _is.Read_bool(&st.Is_face_detect, 4, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *Pic_cluster_info) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require Pic_cluster_info, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *Pic_cluster_info) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_bool(st.Has_face, 0)
	if err != nil {
		return err
	}

	err = _os.Write_bool(st.Is_cluster, 1)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.LIST, 2)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Groupid_faces)), 0)
	if err != nil {
		return err
	}

	for _, v := range st.Groupid_faces {

		err = v.WriteBlock(_os, 0)
		if err != nil {
			return err
		}

	}

	err = _os.Write_int32(int32(st.Version), 3)
	if err != nil {
		return err
	}

	err = _os.Write_bool(st.Is_face_detect, 4)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *Pic_cluster_info) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *Pic_cluster_info) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *Pic_cluster_info) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *Pic_cluster_info) WupTypeName() string {
	return "PHOTO.pic_cluster_info"
}

// Uin_label_op struct implement
type Uin_label_op struct {
	Status Enum_label_status `json:"status"`
}

func (st *Uin_label_op) MakesureNotNil() {
}
func (st *Uin_label_op) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *Uin_label_op) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_int32((*int32)(&st.Status), 1, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *Uin_label_op) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require Uin_label_op, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *Uin_label_op) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_int32(int32(st.Status), 1)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *Uin_label_op) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *Uin_label_op) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *Uin_label_op) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *Uin_label_op) WupTypeName() string {
	return "PHOTO.uin_label_op"
}

// Label_info struct implement
type Label_info struct {
	Label         uint32                  `json:"label"`
	Status        Enum_label_status       `json:"status"`
	Uin_label_ops map[uint32]Uin_label_op `json:"uin_label_ops"`
}

func (st *Label_info) MakesureNotNil() {
}
func (st *Label_info) ResetDefault() {
	st.MakesureNotNil()
	st.Label = 0
}

// ReadFrom reads  from _is and put into struct.
func (st *Label_info) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_uint32(&st.Label, 0, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32((*int32)(&st.Status), 1, false)
	if err != nil {
		return err
	}

	err, have = _is.SkipTo(jce.MAP, 2, false)
	if err != nil {
		return err
	}

	if have {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.Uin_label_ops = make(map[uint32]Uin_label_op)
		for i0, e0 := int32(0), length; i0 < e0; i0++ {
			var k0 uint32
			var v0 Uin_label_op

			err = _is.Read_uint32(&k0, 0, false)
			if err != nil {
				return err
			}

			err = v0.ReadBlock(_is, 1, false)
			if err != nil {
				return err
			}

			st.Uin_label_ops[k0] = v0
		}
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *Label_info) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require Label_info, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *Label_info) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_uint32(st.Label, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(st.Status), 1)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.MAP, 2)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Uin_label_ops)), 0)
	if err != nil {
		return err
	}

	for k1, v1 := range st.Uin_label_ops {

		err = _os.Write_uint32(k1, 0)
		if err != nil {
			return err
		}

		err = v1.WriteBlock(_os, 1)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *Label_info) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *Label_info) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *Label_info) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *Label_info) WupTypeName() string {
	return "PHOTO.label_info"
}

// Pic_label_info struct implement
type Pic_label_info struct {
	Is_labeled  bool               `json:"is_labeled"`
	Labels      []uint32           `json:"labels"`
	Version     Enum_label_version `json:"version"`
	Label_infos []Label_info       `json:"label_infos"`
}

func (st *Pic_label_info) MakesureNotNil() {
}
func (st *Pic_label_info) ResetDefault() {
	st.MakesureNotNil()
	st.Is_labeled = false
}

// ReadFrom reads  from _is and put into struct.
func (st *Pic_label_info) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_bool(&st.Is_labeled, 0, false)
	if err != nil {
		return err
	}

	err, have, ty = _is.SkipToNoCheck(1, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.Labels = make([]uint32, length)
			for i0, e0 := int32(0), length; i0 < e0; i0++ {

				err = _is.Read_uint32(&st.Labels[i0], 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {
			err = fmt.Errorf("not support simple_list type")
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	err = _is.Read_int32((*int32)(&st.Version), 2, false)
	if err != nil {
		return err
	}

	err, have, ty = _is.SkipToNoCheck(3, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.Label_infos = make([]Label_info, length)
			for i1, e1 := int32(0), length; i1 < e1; i1++ {

				err = st.Label_infos[i1].ReadBlock(_is, 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {
			err = fmt.Errorf("not support simple_list type")
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *Pic_label_info) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require Pic_label_info, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *Pic_label_info) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_bool(st.Is_labeled, 0)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.LIST, 1)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Labels)), 0)
	if err != nil {
		return err
	}

	for _, v := range st.Labels {

		err = _os.Write_uint32(v, 0)
		if err != nil {
			return err
		}

	}

	err = _os.Write_int32(int32(st.Version), 2)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.LIST, 3)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Label_infos)), 0)
	if err != nil {
		return err
	}

	for _, v := range st.Label_infos {

		err = v.WriteBlock(_os, 0)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *Pic_label_info) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *Pic_label_info) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *Pic_label_info) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *Pic_label_info) WupTypeName() string {
	return "PHOTO.pic_label_info"
}

// Photo_tag_cover_ele struct implement
type Photo_tag_cover_ele struct {
	Cover_urls  []string            `json:"cover_urls"`
	Ele_count   uint32              `json:"ele_count"`
	Type        Enum_photo_tag_type `json:"type"`
	Update_time uint32              `json:"update_time"`
}

func (st *Photo_tag_cover_ele) MakesureNotNil() {
}
func (st *Photo_tag_cover_ele) ResetDefault() {
	st.MakesureNotNil()
	st.Ele_count = 0
	st.Update_time = 0
}

// ReadFrom reads  from _is and put into struct.
func (st *Photo_tag_cover_ele) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have, ty = _is.SkipToNoCheck(0, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.Cover_urls = make([]string, length)
			for i0, e0 := int32(0), length; i0 < e0; i0++ {

				err = _is.Read_string(&st.Cover_urls[i0], 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {
			err = fmt.Errorf("not support simple_list type")
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	err = _is.Read_uint32(&st.Ele_count, 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32((*int32)(&st.Type), 2, false)
	if err != nil {
		return err
	}

	err = _is.Read_uint32(&st.Update_time, 3, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *Photo_tag_cover_ele) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require Photo_tag_cover_ele, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *Photo_tag_cover_ele) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.LIST, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Cover_urls)), 0)
	if err != nil {
		return err
	}

	for _, v := range st.Cover_urls {

		err = _os.Write_string(v, 0)
		if err != nil {
			return err
		}

	}

	err = _os.Write_uint32(st.Ele_count, 1)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(st.Type), 2)
	if err != nil {
		return err
	}

	err = _os.Write_uint32(st.Update_time, 3)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *Photo_tag_cover_ele) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *Photo_tag_cover_ele) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *Photo_tag_cover_ele) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *Photo_tag_cover_ele) WupTypeName() string {
	return "PHOTO.photo_tag_cover_ele"
}

// Photo_tag_cover struct implement
type Photo_tag_cover struct {
	Covers []Photo_tag_cover_ele `json:"covers"`
}

func (st *Photo_tag_cover) MakesureNotNil() {
}
func (st *Photo_tag_cover) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *Photo_tag_cover) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err, have, ty = _is.SkipToNoCheck(0, false)
	if err != nil {
		return err
	}

	if have {
		if ty == jce.LIST {
			err = _is.Read_int32(&length, 0, true)
			if err != nil {
				return err
			}

			st.Covers = make([]Photo_tag_cover_ele, length)
			for i0, e0 := int32(0), length; i0 < e0; i0++ {

				err = st.Covers[i0].ReadBlock(_is, 0, false)
				if err != nil {
					return err
				}

			}
		} else if ty == jce.SIMPLE_LIST {
			err = fmt.Errorf("not support simple_list type")
			if err != nil {
				return err
			}

		} else {
			err = fmt.Errorf("require vector, but not")
			if err != nil {
				return err
			}

		}
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *Photo_tag_cover) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require Photo_tag_cover, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *Photo_tag_cover) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.WriteHead(jce.LIST, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Covers)), 0)
	if err != nil {
		return err
	}

	for _, v := range st.Covers {

		err = v.WriteBlock(_os, 0)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *Photo_tag_cover) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *Photo_tag_cover) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *Photo_tag_cover) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *Photo_tag_cover) WupTypeName() string {
	return "PHOTO.photo_tag_cover"
}

// AlbumReserveAlbumSortType struct implement
type AlbumReserveAlbumSortType struct {
	Is_set_sort_type bool  `json:"is_set_sort_type"`
	Sort_type        int32 `json:"sort_type"`
}

func (st *AlbumReserveAlbumSortType) MakesureNotNil() {
}
func (st *AlbumReserveAlbumSortType) ResetDefault() {
	st.MakesureNotNil()
	st.Is_set_sort_type = false
	st.Sort_type = 0
}

// ReadFrom reads  from _is and put into struct.
func (st *AlbumReserveAlbumSortType) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_bool(&st.Is_set_sort_type, 0, false)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Sort_type, 1, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *AlbumReserveAlbumSortType) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require AlbumReserveAlbumSortType, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *AlbumReserveAlbumSortType) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_bool(st.Is_set_sort_type, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Sort_type, 1)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *AlbumReserveAlbumSortType) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *AlbumReserveAlbumSortType) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *AlbumReserveAlbumSortType) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *AlbumReserveAlbumSortType) WupTypeName() string {
	return "PHOTO.AlbumReserveAlbumSortType"
}

// Kantu_cover_info struct implement
type Kantu_cover_info struct {
	Cover_title         string `json:"cover_title"`
	Cover_subtitile     string `json:"cover_subtitile"`
	Cover_sign          string `json:"cover_sign"`
	Back_cover_title    string `json:"back_cover_title"`
	Back_cover_subtitle string `json:"back_cover_subtitle"`
}

func (st *Kantu_cover_info) MakesureNotNil() {
}
func (st *Kantu_cover_info) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *Kantu_cover_info) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_string(&st.Cover_title, 0, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Cover_subtitile, 1, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Cover_sign, 2, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Back_cover_title, 3, false)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Back_cover_subtitle, 4, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *Kantu_cover_info) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require Kantu_cover_info, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *Kantu_cover_info) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_string(st.Cover_title, 0)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Cover_subtitile, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Cover_sign, 2)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Back_cover_title, 3)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Back_cover_subtitle, 4)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *Kantu_cover_info) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *Kantu_cover_info) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *Kantu_cover_info) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *Kantu_cover_info) WupTypeName() string {
	return "PHOTO.kantu_cover_info"
}
