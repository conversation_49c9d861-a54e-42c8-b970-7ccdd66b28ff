// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: monitor/polaris/v1/request.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//api接口调用
type APIResultType int32

const (
	//未知原因，更新pb后的sdk不得上报这种类型，用于兼容旧版本sdk
	APIResultType_UnknownType APIResultType = 0
	//用户调用成功
	APIResultType_Success APIResultType = 1
	//由于用户的原因，如参数错误导致的调用错误
	APIResultType_UserFail APIResultType = 2
	//由于系统原因（sdk或者server）导致用户调用失败
	APIResultType_PolarisFail APIResultType = 3
)

// Enum value maps for APIResultType.
var (
	APIResultType_name = map[int32]string{
		0: "UnknownType",
		1: "Success",
		2: "UserFail",
		3: "PolarisFail",
	}
	APIResultType_value = map[string]int32{
		"UnknownType": 0,
		"Success":     1,
		"UserFail":    2,
		"PolarisFail": 3,
	}
)

func (x APIResultType) Enum() *APIResultType {
	p := new(APIResultType)
	*p = x
	return p
}

func (x APIResultType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (APIResultType) Descriptor() protoreflect.EnumDescriptor {
	return file_monitor_polaris_v1_request_proto_enumTypes[0].Descriptor()
}

func (APIResultType) Type() protoreflect.EnumType {
	return &file_monitor_polaris_v1_request_proto_enumTypes[0]
}

func (x APIResultType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use APIResultType.Descriptor instead.
func (APIResultType) EnumDescriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_request_proto_rawDescGZIP(), []int{0}
}

// polaris server上报的统计数据
type ServerStatistics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`       // 唯一标识
	Key       *ServerStatisticsKey    `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`     // 维度
	Value     *Indicator              `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"` // 指标
	Signature *MonitorSignature       `protobuf:"bytes,4,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *ServerStatistics) Reset() {
	*x = ServerStatistics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerStatistics) ProtoMessage() {}

func (x *ServerStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerStatistics.ProtoReflect.Descriptor instead.
func (*ServerStatistics) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_request_proto_rawDescGZIP(), []int{0}
}

func (x *ServerStatistics) GetId() *wrapperspb.StringValue {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *ServerStatistics) GetKey() *ServerStatisticsKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *ServerStatistics) GetValue() *Indicator {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *ServerStatistics) GetSignature() *MonitorSignature {
	if x != nil {
		return x.Signature
	}
	return nil
}

type ServerStatisticsKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerHost *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=server_host,json=serverHost,proto3" json:"server_host,omitempty"` // server地址
	Resource   *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=resource,proto3" json:"resource,omitempty"`                       // server处理的资源类型，分为Namespace、Service和Instance
	Operation  *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=operation,proto3" json:"operation,omitempty"`                     // server对于资源进行的操作，分为创建、查询、修改和删除
	Code       *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`                               // server对于资源操作的返回码
	Success    *wrapperspb.BoolValue   `protobuf:"bytes,5,opt,name=success,proto3" json:"success,omitempty"`                         // server对于资源操作是否成功
	DelayRange *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=delay_range,json=delayRange,proto3" json:"delay_range,omitempty"` // 操作延迟范围，如[3ms, 10ms)
}

func (x *ServerStatisticsKey) Reset() {
	*x = ServerStatisticsKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_request_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerStatisticsKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerStatisticsKey) ProtoMessage() {}

func (x *ServerStatisticsKey) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_request_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerStatisticsKey.ProtoReflect.Descriptor instead.
func (*ServerStatisticsKey) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_request_proto_rawDescGZIP(), []int{1}
}

func (x *ServerStatisticsKey) GetServerHost() *wrapperspb.StringValue {
	if x != nil {
		return x.ServerHost
	}
	return nil
}

func (x *ServerStatisticsKey) GetResource() *wrapperspb.StringValue {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *ServerStatisticsKey) GetOperation() *wrapperspb.StringValue {
	if x != nil {
		return x.Operation
	}
	return nil
}

func (x *ServerStatisticsKey) GetCode() *wrapperspb.StringValue {
	if x != nil {
		return x.Code
	}
	return nil
}

func (x *ServerStatisticsKey) GetSuccess() *wrapperspb.BoolValue {
	if x != nil {
		return x.Success
	}
	return nil
}

func (x *ServerStatisticsKey) GetDelayRange() *wrapperspb.StringValue {
	if x != nil {
		return x.DelayRange
	}
	return nil
}

// SDK上报的API调用统计
type SDKAPIStatistics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`       // 唯一标识
	Key       *SDKAPIStatisticsKey    `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`     // 维度
	Value     *Indicator              `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"` // 指标
	Signature *MonitorSignature       `protobuf:"bytes,4,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *SDKAPIStatistics) Reset() {
	*x = SDKAPIStatistics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_request_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SDKAPIStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKAPIStatistics) ProtoMessage() {}

func (x *SDKAPIStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_request_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKAPIStatistics.ProtoReflect.Descriptor instead.
func (*SDKAPIStatistics) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_request_proto_rawDescGZIP(), []int{2}
}

func (x *SDKAPIStatistics) GetId() *wrapperspb.StringValue {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *SDKAPIStatistics) GetKey() *SDKAPIStatisticsKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *SDKAPIStatistics) GetValue() *Indicator {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *SDKAPIStatistics) GetSignature() *MonitorSignature {
	if x != nil {
		return x.Signature
	}
	return nil
}

type SDKAPIStatisticsKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientHost    *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=client_host,json=clientHost,proto3" json:"client_host,omitempty"`          // SDK的ip
	SdkApi        *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=sdk_api,json=sdkApi,proto3" json:"sdk_api,omitempty"`                      // 被调用的api
	ResCode       *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=res_code,json=resCode,proto3" json:"res_code,omitempty"`                   // 调用结果码，如果有错误，那么就是具体的错误码，否则为0，表示成功
	Success       *wrapperspb.BoolValue   `protobuf:"bytes,4,opt,name=success,proto3" json:"success,omitempty"`                                  // API调用是否成功
	DelayRange    *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=delay_range,json=delayRange,proto3" json:"delay_range,omitempty"`          // 操作延迟范围，如[3ms, 10ms)
	ClientVersion *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"` // 版本号
	ClientType    *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`          // polaris-go/polaris-cpp/polaris-nodejs/polaris-sidecar
	Result        APIResultType           `protobuf:"varint,8,opt,name=result,proto3,enum=v1.APIResultType" json:"result,omitempty"`             //api接口调用的返回类型
	Uid           string                  `protobuf:"bytes,9,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *SDKAPIStatisticsKey) Reset() {
	*x = SDKAPIStatisticsKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_request_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SDKAPIStatisticsKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKAPIStatisticsKey) ProtoMessage() {}

func (x *SDKAPIStatisticsKey) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_request_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKAPIStatisticsKey.ProtoReflect.Descriptor instead.
func (*SDKAPIStatisticsKey) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_request_proto_rawDescGZIP(), []int{3}
}

func (x *SDKAPIStatisticsKey) GetClientHost() *wrapperspb.StringValue {
	if x != nil {
		return x.ClientHost
	}
	return nil
}

func (x *SDKAPIStatisticsKey) GetSdkApi() *wrapperspb.StringValue {
	if x != nil {
		return x.SdkApi
	}
	return nil
}

func (x *SDKAPIStatisticsKey) GetResCode() *wrapperspb.StringValue {
	if x != nil {
		return x.ResCode
	}
	return nil
}

func (x *SDKAPIStatisticsKey) GetSuccess() *wrapperspb.BoolValue {
	if x != nil {
		return x.Success
	}
	return nil
}

func (x *SDKAPIStatisticsKey) GetDelayRange() *wrapperspb.StringValue {
	if x != nil {
		return x.DelayRange
	}
	return nil
}

func (x *SDKAPIStatisticsKey) GetClientVersion() *wrapperspb.StringValue {
	if x != nil {
		return x.ClientVersion
	}
	return nil
}

func (x *SDKAPIStatisticsKey) GetClientType() *wrapperspb.StringValue {
	if x != nil {
		return x.ClientType
	}
	return nil
}

func (x *SDKAPIStatisticsKey) GetResult() APIResultType {
	if x != nil {
		return x.Result
	}
	return APIResultType_UnknownType
}

func (x *SDKAPIStatisticsKey) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

// server和sdk的上报指标
type Indicator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalRequestPerMinute *wrapperspb.UInt32Value `protobuf:"bytes,1,opt,name=total_request_per_minute,json=totalRequestPerMinute,proto3" json:"total_request_per_minute,omitempty"` //总请求数
}

func (x *Indicator) Reset() {
	*x = Indicator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_request_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Indicator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Indicator) ProtoMessage() {}

func (x *Indicator) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_request_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Indicator.ProtoReflect.Descriptor instead.
func (*Indicator) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_request_proto_rawDescGZIP(), []int{4}
}

func (x *Indicator) GetTotalRequestPerMinute() *wrapperspb.UInt32Value {
	if x != nil {
		return x.TotalRequestPerMinute
	}
	return nil
}

// SDK上报的服务调用统计
type ServiceStatistics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                             // 唯一标识
	Key       *ServiceStatisticsKey   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`                           // 维度
	Value     *ServiceIndicator       `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`                       // 指标
	SdkToken  *SDKToken               `protobuf:"bytes,4,opt,name=sdk_token,json=sdkToken,proto3" json:"sdk_token,omitempty"` // sdk标识
	Signature *MonitorSignature       `protobuf:"bytes,5,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *ServiceStatistics) Reset() {
	*x = ServiceStatistics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_request_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceStatistics) ProtoMessage() {}

func (x *ServiceStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_request_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceStatistics.ProtoReflect.Descriptor instead.
func (*ServiceStatistics) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_request_proto_rawDescGZIP(), []int{5}
}

func (x *ServiceStatistics) GetId() *wrapperspb.StringValue {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *ServiceStatistics) GetKey() *ServiceStatisticsKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *ServiceStatistics) GetValue() *ServiceIndicator {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *ServiceStatistics) GetSdkToken() *SDKToken {
	if x != nil {
		return x.SdkToken
	}
	return nil
}

func (x *ServiceStatistics) GetSignature() *MonitorSignature {
	if x != nil {
		return x.Signature
	}
	return nil
}

// 服务调用的上报维度
type ServiceStatisticsKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CallerHost   *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=caller_host,json=callerHost,proto3" json:"caller_host,omitempty"`       // 该服务调用者的IP
	Namespace    *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`                           // 命名空间
	Service      *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=service,proto3" json:"service,omitempty"`                               // 服务名
	InstanceHost *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=instance_host,json=instanceHost,proto3" json:"instance_host,omitempty"` //具体服务实例ip
	Success      *wrapperspb.BoolValue   `protobuf:"bytes,5,opt,name=success,proto3" json:"success,omitempty"`                               // 服务调用是否成功
	// Types that are assignable to ResCode:
	//	*ServiceStatisticsKey_ResCodeInt32
	//	*ServiceStatisticsKey_ResCodeString
	ResCode isServiceStatisticsKey_ResCode `protobuf_oneof:"res_code"`
}

func (x *ServiceStatisticsKey) Reset() {
	*x = ServiceStatisticsKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_request_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceStatisticsKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceStatisticsKey) ProtoMessage() {}

func (x *ServiceStatisticsKey) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_request_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceStatisticsKey.ProtoReflect.Descriptor instead.
func (*ServiceStatisticsKey) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_request_proto_rawDescGZIP(), []int{6}
}

func (x *ServiceStatisticsKey) GetCallerHost() *wrapperspb.StringValue {
	if x != nil {
		return x.CallerHost
	}
	return nil
}

func (x *ServiceStatisticsKey) GetNamespace() *wrapperspb.StringValue {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *ServiceStatisticsKey) GetService() *wrapperspb.StringValue {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *ServiceStatisticsKey) GetInstanceHost() *wrapperspb.StringValue {
	if x != nil {
		return x.InstanceHost
	}
	return nil
}

func (x *ServiceStatisticsKey) GetSuccess() *wrapperspb.BoolValue {
	if x != nil {
		return x.Success
	}
	return nil
}

func (m *ServiceStatisticsKey) GetResCode() isServiceStatisticsKey_ResCode {
	if m != nil {
		return m.ResCode
	}
	return nil
}

func (x *ServiceStatisticsKey) GetResCodeInt32() int32 {
	if x, ok := x.GetResCode().(*ServiceStatisticsKey_ResCodeInt32); ok {
		return x.ResCodeInt32
	}
	return 0
}

func (x *ServiceStatisticsKey) GetResCodeString() *wrapperspb.StringValue {
	if x, ok := x.GetResCode().(*ServiceStatisticsKey_ResCodeString); ok {
		return x.ResCodeString
	}
	return nil
}

type isServiceStatisticsKey_ResCode interface {
	isServiceStatisticsKey_ResCode()
}

type ServiceStatisticsKey_ResCodeInt32 struct {
	ResCodeInt32 int32 `protobuf:"varint,6,opt,name=res_code_int32,json=resCodeInt32,proto3,oneof"`
}

type ServiceStatisticsKey_ResCodeString struct {
	ResCodeString *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=res_code_string,json=resCodeString,proto3,oneof"`
}

func (*ServiceStatisticsKey_ResCodeInt32) isServiceStatisticsKey_ResCode() {}

func (*ServiceStatisticsKey_ResCodeString) isServiceStatisticsKey_ResCode() {}

// 服务调用的上报指标
type ServiceIndicator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalRequestPerMinute *wrapperspb.UInt32Value `protobuf:"bytes,1,opt,name=total_request_per_minute,json=totalRequestPerMinute,proto3" json:"total_request_per_minute,omitempty"` // 总请求数
	TotalDelayPerMinute   *wrapperspb.UInt64Value `protobuf:"bytes,2,opt,name=total_delay_per_minute,json=totalDelayPerMinute,proto3" json:"total_delay_per_minute,omitempty"`       // 总延迟
}

func (x *ServiceIndicator) Reset() {
	*x = ServiceIndicator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_request_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceIndicator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceIndicator) ProtoMessage() {}

func (x *ServiceIndicator) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_request_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceIndicator.ProtoReflect.Descriptor instead.
func (*ServiceIndicator) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_request_proto_rawDescGZIP(), []int{7}
}

func (x *ServiceIndicator) GetTotalRequestPerMinute() *wrapperspb.UInt32Value {
	if x != nil {
		return x.TotalRequestPerMinute
	}
	return nil
}

func (x *ServiceIndicator) GetTotalDelayPerMinute() *wrapperspb.UInt64Value {
	if x != nil {
		return x.TotalDelayPerMinute
	}
	return nil
}

type BatchServiceStatistics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Statistics []*ServiceStatistics `protobuf:"bytes,1,rep,name=statistics,proto3" json:"statistics,omitempty"`
}

func (x *BatchServiceStatistics) Reset() {
	*x = BatchServiceStatistics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_request_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchServiceStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchServiceStatistics) ProtoMessage() {}

func (x *BatchServiceStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_request_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchServiceStatistics.ProtoReflect.Descriptor instead.
func (*BatchServiceStatistics) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_request_proto_rawDescGZIP(), []int{8}
}

func (x *BatchServiceStatistics) GetStatistics() []*ServiceStatistics {
	if x != nil {
		return x.Statistics
	}
	return nil
}

var File_monitor_polaris_v1_request_proto protoreflect.FileDescriptor

var file_monitor_polaris_v1_request_proto_rawDesc = []byte{
	0x0a, 0x20, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x02, 0x76, 0x31, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x64, 0x6b, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc4, 0x01,
	0x0a, 0x10, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x12, 0x2c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x29, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x73, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x23, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x32, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x22, 0xf1, 0x02, 0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x3d, 0x0a, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x08, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x30, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x3d, 0x0a, 0x0b, 0x64, 0x65, 0x6c,
	0x61, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x64, 0x65,
	0x6c, 0x61, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x22, 0xc4, 0x01, 0x0a, 0x10, 0x53, 0x44, 0x4b,
	0x41, 0x50, 0x49, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x2c, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x44,
	0x4b, 0x41, 0x50, 0x49, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x4b, 0x65,
	0x79, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x23, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x6f, 0x72, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x32, 0x0a, 0x09, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22,
	0xfa, 0x03, 0x0a, 0x13, 0x53, 0x44, 0x4b, 0x41, 0x50, 0x49, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x3d, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x07, 0x73, 0x64, 0x6b, 0x5f, 0x61, 0x70,
	0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x73, 0x64, 0x6b, 0x41, 0x70, 0x69, 0x12, 0x37, 0x0a,
	0x08, 0x72, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x72,
	0x65, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x3d, 0x0a, 0x0b,
	0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0a, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x3d, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x29, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x11, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x50, 0x49, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x62, 0x0a, 0x09,
	0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x55, 0x0a, 0x18, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x6d,
	0x69, 0x6e, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x15, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65,
	0x22, 0xf8, 0x01, 0x0a, 0x11, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x2c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x29, 0x0a, 0x09,
	0x73, 0x64, 0x6b, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0c, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x44, 0x4b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x08, 0x73,
	0x64, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x32, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22, 0xbe, 0x03, 0x0a, 0x14,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x73, 0x4b, 0x65, 0x79, 0x12, 0x3d, 0x0a, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x68,
	0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x48,
	0x6f, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x36, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x26, 0x0a, 0x0e, 0x72, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x43,
	0x6f, 0x64, 0x65, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48,
	0x00, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x42, 0x0a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xbc, 0x01, 0x0a,
	0x10, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x55, 0x0a, 0x18, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x15, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50,
	0x65, 0x72, 0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x12, 0x51, 0x0a, 0x16, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x69, 0x6e, 0x75,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x65, 0x6c,
	0x61, 0x79, 0x50, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x22, 0x4f, 0x0a, 0x16, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x35, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2a, 0x4c, 0x0a, 0x0d,
	0x41, 0x50, 0x49, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a,
	0x0b, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x55,
	0x73, 0x65, 0x72, 0x46, 0x61, 0x69, 0x6c, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x6f, 0x6c,
	0x61, 0x72, 0x69, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x10, 0x03, 0x42, 0x3f, 0x5a, 0x3d, 0x67, 0x69,
	0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_monitor_polaris_v1_request_proto_rawDescOnce sync.Once
	file_monitor_polaris_v1_request_proto_rawDescData = file_monitor_polaris_v1_request_proto_rawDesc
)

func file_monitor_polaris_v1_request_proto_rawDescGZIP() []byte {
	file_monitor_polaris_v1_request_proto_rawDescOnce.Do(func() {
		file_monitor_polaris_v1_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_monitor_polaris_v1_request_proto_rawDescData)
	})
	return file_monitor_polaris_v1_request_proto_rawDescData
}

var file_monitor_polaris_v1_request_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_monitor_polaris_v1_request_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_monitor_polaris_v1_request_proto_goTypes = []interface{}{
	(APIResultType)(0),             // 0: v1.APIResultType
	(*ServerStatistics)(nil),       // 1: v1.ServerStatistics
	(*ServerStatisticsKey)(nil),    // 2: v1.ServerStatisticsKey
	(*SDKAPIStatistics)(nil),       // 3: v1.SDKAPIStatistics
	(*SDKAPIStatisticsKey)(nil),    // 4: v1.SDKAPIStatisticsKey
	(*Indicator)(nil),              // 5: v1.Indicator
	(*ServiceStatistics)(nil),      // 6: v1.ServiceStatistics
	(*ServiceStatisticsKey)(nil),   // 7: v1.ServiceStatisticsKey
	(*ServiceIndicator)(nil),       // 8: v1.ServiceIndicator
	(*BatchServiceStatistics)(nil), // 9: v1.BatchServiceStatistics
	(*wrapperspb.StringValue)(nil), // 10: google.protobuf.StringValue
	(*MonitorSignature)(nil),       // 11: v1.MonitorSignature
	(*wrapperspb.BoolValue)(nil),   // 12: google.protobuf.BoolValue
	(*wrapperspb.UInt32Value)(nil), // 13: google.protobuf.UInt32Value
	(*SDKToken)(nil),               // 14: v1.SDKToken
	(*wrapperspb.UInt64Value)(nil), // 15: google.protobuf.UInt64Value
}
var file_monitor_polaris_v1_request_proto_depIdxs = []int32{
	10, // 0: v1.ServerStatistics.id:type_name -> google.protobuf.StringValue
	2,  // 1: v1.ServerStatistics.key:type_name -> v1.ServerStatisticsKey
	5,  // 2: v1.ServerStatistics.value:type_name -> v1.Indicator
	11, // 3: v1.ServerStatistics.signature:type_name -> v1.MonitorSignature
	10, // 4: v1.ServerStatisticsKey.server_host:type_name -> google.protobuf.StringValue
	10, // 5: v1.ServerStatisticsKey.resource:type_name -> google.protobuf.StringValue
	10, // 6: v1.ServerStatisticsKey.operation:type_name -> google.protobuf.StringValue
	10, // 7: v1.ServerStatisticsKey.code:type_name -> google.protobuf.StringValue
	12, // 8: v1.ServerStatisticsKey.success:type_name -> google.protobuf.BoolValue
	10, // 9: v1.ServerStatisticsKey.delay_range:type_name -> google.protobuf.StringValue
	10, // 10: v1.SDKAPIStatistics.id:type_name -> google.protobuf.StringValue
	4,  // 11: v1.SDKAPIStatistics.key:type_name -> v1.SDKAPIStatisticsKey
	5,  // 12: v1.SDKAPIStatistics.value:type_name -> v1.Indicator
	11, // 13: v1.SDKAPIStatistics.signature:type_name -> v1.MonitorSignature
	10, // 14: v1.SDKAPIStatisticsKey.client_host:type_name -> google.protobuf.StringValue
	10, // 15: v1.SDKAPIStatisticsKey.sdk_api:type_name -> google.protobuf.StringValue
	10, // 16: v1.SDKAPIStatisticsKey.res_code:type_name -> google.protobuf.StringValue
	12, // 17: v1.SDKAPIStatisticsKey.success:type_name -> google.protobuf.BoolValue
	10, // 18: v1.SDKAPIStatisticsKey.delay_range:type_name -> google.protobuf.StringValue
	10, // 19: v1.SDKAPIStatisticsKey.client_version:type_name -> google.protobuf.StringValue
	10, // 20: v1.SDKAPIStatisticsKey.client_type:type_name -> google.protobuf.StringValue
	0,  // 21: v1.SDKAPIStatisticsKey.result:type_name -> v1.APIResultType
	13, // 22: v1.Indicator.total_request_per_minute:type_name -> google.protobuf.UInt32Value
	10, // 23: v1.ServiceStatistics.id:type_name -> google.protobuf.StringValue
	7,  // 24: v1.ServiceStatistics.key:type_name -> v1.ServiceStatisticsKey
	8,  // 25: v1.ServiceStatistics.value:type_name -> v1.ServiceIndicator
	14, // 26: v1.ServiceStatistics.sdk_token:type_name -> v1.SDKToken
	11, // 27: v1.ServiceStatistics.signature:type_name -> v1.MonitorSignature
	10, // 28: v1.ServiceStatisticsKey.caller_host:type_name -> google.protobuf.StringValue
	10, // 29: v1.ServiceStatisticsKey.namespace:type_name -> google.protobuf.StringValue
	10, // 30: v1.ServiceStatisticsKey.service:type_name -> google.protobuf.StringValue
	10, // 31: v1.ServiceStatisticsKey.instance_host:type_name -> google.protobuf.StringValue
	12, // 32: v1.ServiceStatisticsKey.success:type_name -> google.protobuf.BoolValue
	10, // 33: v1.ServiceStatisticsKey.res_code_string:type_name -> google.protobuf.StringValue
	13, // 34: v1.ServiceIndicator.total_request_per_minute:type_name -> google.protobuf.UInt32Value
	15, // 35: v1.ServiceIndicator.total_delay_per_minute:type_name -> google.protobuf.UInt64Value
	6,  // 36: v1.BatchServiceStatistics.statistics:type_name -> v1.ServiceStatistics
	37, // [37:37] is the sub-list for method output_type
	37, // [37:37] is the sub-list for method input_type
	37, // [37:37] is the sub-list for extension type_name
	37, // [37:37] is the sub-list for extension extendee
	0,  // [0:37] is the sub-list for field type_name
}

func init() { file_monitor_polaris_v1_request_proto_init() }
func file_monitor_polaris_v1_request_proto_init() {
	if File_monitor_polaris_v1_request_proto != nil {
		return
	}
	file_monitor_polaris_v1_sdktoken_proto_init()
	file_monitor_polaris_v1_signature_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_monitor_polaris_v1_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerStatistics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_request_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerStatisticsKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_request_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SDKAPIStatistics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_request_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SDKAPIStatisticsKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_request_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Indicator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_request_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceStatistics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_request_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceStatisticsKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_request_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceIndicator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_request_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchServiceStatistics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_monitor_polaris_v1_request_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*ServiceStatisticsKey_ResCodeInt32)(nil),
		(*ServiceStatisticsKey_ResCodeString)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_monitor_polaris_v1_request_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_monitor_polaris_v1_request_proto_goTypes,
		DependencyIndexes: file_monitor_polaris_v1_request_proto_depIdxs,
		EnumInfos:         file_monitor_polaris_v1_request_proto_enumTypes,
		MessageInfos:      file_monitor_polaris_v1_request_proto_msgTypes,
	}.Build()
	File_monitor_polaris_v1_request_proto = out.File
	file_monitor_polaris_v1_request_proto_rawDesc = nil
	file_monitor_polaris_v1_request_proto_goTypes = nil
	file_monitor_polaris_v1_request_proto_depIdxs = nil
}
