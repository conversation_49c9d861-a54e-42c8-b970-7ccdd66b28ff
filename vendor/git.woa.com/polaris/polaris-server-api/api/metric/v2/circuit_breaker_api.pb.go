// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: metric/v2/circuit_breaker_api.proto

package v2

import (
	context "context"
	model "git.woa.com/polaris/polaris-server-api/api/v1/model"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 熔断范围
type CircuitBreakerResponse_StateData_Scope int32

const (
	CircuitBreakerResponse_StateData_SERVICE CircuitBreakerResponse_StateData_Scope = 0
	CircuitBreakerResponse_StateData_GROUP   CircuitBreakerResponse_StateData_Scope = 1
)

// Enum value maps for CircuitBreakerResponse_StateData_Scope.
var (
	CircuitBreakerResponse_StateData_Scope_name = map[int32]string{
		0: "SERVICE",
		1: "GROUP",
	}
	CircuitBreakerResponse_StateData_Scope_value = map[string]int32{
		"SERVICE": 0,
		"GROUP":   1,
	}
)

func (x CircuitBreakerResponse_StateData_Scope) Enum() *CircuitBreakerResponse_StateData_Scope {
	p := new(CircuitBreakerResponse_StateData_Scope)
	*p = x
	return p
}

func (x CircuitBreakerResponse_StateData_Scope) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CircuitBreakerResponse_StateData_Scope) Descriptor() protoreflect.EnumDescriptor {
	return file_metric_v2_circuit_breaker_api_proto_enumTypes[0].Descriptor()
}

func (CircuitBreakerResponse_StateData_Scope) Type() protoreflect.EnumType {
	return &file_metric_v2_circuit_breaker_api_proto_enumTypes[0]
}

func (x CircuitBreakerResponse_StateData_Scope) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CircuitBreakerResponse_StateData_Scope.Descriptor instead.
func (CircuitBreakerResponse_StateData_Scope) EnumDescriptor() ([]byte, []int) {
	return file_metric_v2_circuit_breaker_api_proto_rawDescGZIP(), []int{5, 0, 0}
}

// 熔断状态
type CircuitBreakerResponse_StateData_State int32

const (
	CircuitBreakerResponse_StateData_CLOSE     CircuitBreakerResponse_StateData_State = 0
	CircuitBreakerResponse_StateData_OPEN      CircuitBreakerResponse_StateData_State = 1
	CircuitBreakerResponse_StateData_HALF_OPEN CircuitBreakerResponse_StateData_State = 2
	CircuitBreakerResponse_StateData_PRESERVED CircuitBreakerResponse_StateData_State = 3
)

// Enum value maps for CircuitBreakerResponse_StateData_State.
var (
	CircuitBreakerResponse_StateData_State_name = map[int32]string{
		0: "CLOSE",
		1: "OPEN",
		2: "HALF_OPEN",
		3: "PRESERVED",
	}
	CircuitBreakerResponse_StateData_State_value = map[string]int32{
		"CLOSE":     0,
		"OPEN":      1,
		"HALF_OPEN": 2,
		"PRESERVED": 3,
	}
)

func (x CircuitBreakerResponse_StateData_State) Enum() *CircuitBreakerResponse_StateData_State {
	p := new(CircuitBreakerResponse_StateData_State)
	*p = x
	return p
}

func (x CircuitBreakerResponse_StateData_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CircuitBreakerResponse_StateData_State) Descriptor() protoreflect.EnumDescriptor {
	return file_metric_v2_circuit_breaker_api_proto_enumTypes[1].Descriptor()
}

func (CircuitBreakerResponse_StateData_State) Type() protoreflect.EnumType {
	return &file_metric_v2_circuit_breaker_api_proto_enumTypes[1]
}

func (x CircuitBreakerResponse_StateData_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CircuitBreakerResponse_StateData_State.Descriptor instead.
func (CircuitBreakerResponse_StateData_State) EnumDescriptor() ([]byte, []int) {
	return file_metric_v2_circuit_breaker_api_proto_rawDescGZIP(), []int{5, 0, 1}
}

// 熔断规则信息
type CircuitBreakerRuleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 被调命名空间
	Namespace string `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 被调服务名
	Service string `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
	// 是否主调的规则
	IsSrcRule bool `protobuf:"varint,3,opt,name=is_src_rule,json=isSrcRule,proto3" json:"is_src_rule,omitempty"`
	// 源服务命名空间，SDK匹配到主调的规则时使用
	SrcNamespace string `protobuf:"bytes,4,opt,name=src_namespace,json=srcNamespace,proto3" json:"src_namespace,omitempty"`
	// 源服务服务名，SDK匹配到主调的规则时使用
	SrcService string `protobuf:"bytes,5,opt,name=src_service,json=srcService,proto3" json:"src_service,omitempty"`
	// 服务下熔断子规则对应的名字
	RuleName string `protobuf:"bytes,6,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
}

func (x *CircuitBreakerRuleInfo) Reset() {
	*x = CircuitBreakerRuleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerRuleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerRuleInfo) ProtoMessage() {}

func (x *CircuitBreakerRuleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerRuleInfo.ProtoReflect.Descriptor instead.
func (*CircuitBreakerRuleInfo) Descriptor() ([]byte, []int) {
	return file_metric_v2_circuit_breaker_api_proto_rawDescGZIP(), []int{0}
}

func (x *CircuitBreakerRuleInfo) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *CircuitBreakerRuleInfo) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *CircuitBreakerRuleInfo) GetIsSrcRule() bool {
	if x != nil {
		return x.IsSrcRule
	}
	return false
}

func (x *CircuitBreakerRuleInfo) GetSrcNamespace() string {
	if x != nil {
		return x.SrcNamespace
	}
	return ""
}

func (x *CircuitBreakerRuleInfo) GetSrcService() string {
	if x != nil {
		return x.SrcService
	}
	return ""
}

func (x *CircuitBreakerRuleInfo) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

// 服务调用统计，包含服务本次上报的所有数据
type ServiceRpcCallStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 熔断规则信息
	RuleInfo *CircuitBreakerRuleInfo            `protobuf:"bytes,1,opt,name=rule_info,json=ruleInfo,proto3" json:"rule_info,omitempty"`
	CallStat []*ServiceRpcCallStats_RpcCallStat `protobuf:"bytes,2,rep,name=call_stat,json=callStat,proto3" json:"call_stat,omitempty"`
}

func (x *ServiceRpcCallStats) Reset() {
	*x = ServiceRpcCallStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceRpcCallStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceRpcCallStats) ProtoMessage() {}

func (x *ServiceRpcCallStats) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceRpcCallStats.ProtoReflect.Descriptor instead.
func (*ServiceRpcCallStats) Descriptor() ([]byte, []int) {
	return file_metric_v2_circuit_breaker_api_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceRpcCallStats) GetRuleInfo() *CircuitBreakerRuleInfo {
	if x != nil {
		return x.RuleInfo
	}
	return nil
}

func (x *ServiceRpcCallStats) GetCallStat() []*ServiceRpcCallStats_RpcCallStat {
	if x != nil {
		return x.CallStat
	}
	return nil
}

// 周期统计上报请求
type HealthDataReportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//消息ID，用于和应答相对应
	MsgId uint32 `protobuf:"varint,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	// 主调集群标识
	CallerSet string `protobuf:"bytes,2,opt,name=caller_set,json=callerSet,proto3" json:"caller_set,omitempty"`
	// 服务统计数据
	RpcCallStats []*ServiceRpcCallStats `protobuf:"bytes,3,rep,name=rpc_call_stats,json=rpcCallStats,proto3" json:"rpc_call_stats,omitempty"`
}

func (x *HealthDataReportRequest) Reset() {
	*x = HealthDataReportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthDataReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthDataReportRequest) ProtoMessage() {}

func (x *HealthDataReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthDataReportRequest.ProtoReflect.Descriptor instead.
func (*HealthDataReportRequest) Descriptor() ([]byte, []int) {
	return file_metric_v2_circuit_breaker_api_proto_rawDescGZIP(), []int{2}
}

func (x *HealthDataReportRequest) GetMsgId() uint32 {
	if x != nil {
		return x.MsgId
	}
	return 0
}

func (x *HealthDataReportRequest) GetCallerSet() string {
	if x != nil {
		return x.CallerSet
	}
	return ""
}

func (x *HealthDataReportRequest) GetRpcCallStats() []*ServiceRpcCallStats {
	if x != nil {
		return x.RpcCallStats
	}
	return nil
}

// 统计上报应答
type HealthDataReportResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 消息ID，用于和应答相对应
	MsgId uint32 `protobuf:"varint,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	// 返回码
	Code uint32 `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	// 返回码详情
	Info string `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *HealthDataReportResponse) Reset() {
	*x = HealthDataReportResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthDataReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthDataReportResponse) ProtoMessage() {}

func (x *HealthDataReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthDataReportResponse.ProtoReflect.Descriptor instead.
func (*HealthDataReportResponse) Descriptor() ([]byte, []int) {
	return file_metric_v2_circuit_breaker_api_proto_rawDescGZIP(), []int{3}
}

func (x *HealthDataReportResponse) GetMsgId() uint32 {
	if x != nil {
		return x.MsgId
	}
	return 0
}

func (x *HealthDataReportResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HealthDataReportResponse) GetInfo() string {
	if x != nil {
		return x.Info
	}
	return ""
}

// 熔断状态查询请求
type CircuitBreakerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 消息ID，用于和应答相对应
	MsgId           uint32                                       `protobuf:"varint,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	ServiceRequests []*CircuitBreakerRequest_ServiceRequest      `protobuf:"bytes,2,rep,name=service_requests,json=serviceRequests,proto3" json:"service_requests,omitempty"`
	GroupRequests   []*CircuitBreakerRequest_ServiceGroupRequest `protobuf:"bytes,3,rep,name=group_requests,json=groupRequests,proto3" json:"group_requests,omitempty"`
}

func (x *CircuitBreakerRequest) Reset() {
	*x = CircuitBreakerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerRequest) ProtoMessage() {}

func (x *CircuitBreakerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerRequest.ProtoReflect.Descriptor instead.
func (*CircuitBreakerRequest) Descriptor() ([]byte, []int) {
	return file_metric_v2_circuit_breaker_api_proto_rawDescGZIP(), []int{4}
}

func (x *CircuitBreakerRequest) GetMsgId() uint32 {
	if x != nil {
		return x.MsgId
	}
	return 0
}

func (x *CircuitBreakerRequest) GetServiceRequests() []*CircuitBreakerRequest_ServiceRequest {
	if x != nil {
		return x.ServiceRequests
	}
	return nil
}

func (x *CircuitBreakerRequest) GetGroupRequests() []*CircuitBreakerRequest_ServiceGroupRequest {
	if x != nil {
		return x.GroupRequests
	}
	return nil
}

//统计查询请求
type CircuitBreakerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 消息ID，用于和应答相对应
	MsgId uint32 `protobuf:"varint,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	// 返回码
	Code uint32 `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	// 返回码详情
	Info          string                                 `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	ServiceStates []*CircuitBreakerResponse_ServiceState `protobuf:"bytes,4,rep,name=service_states,json=serviceStates,proto3" json:"service_states,omitempty"`
	// 服务分组熔断数据
	GroupStates []*CircuitBreakerResponse_ServiceGroupState `protobuf:"bytes,5,rep,name=group_states,json=groupStates,proto3" json:"group_states,omitempty"`
}

func (x *CircuitBreakerResponse) Reset() {
	*x = CircuitBreakerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerResponse) ProtoMessage() {}

func (x *CircuitBreakerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerResponse.ProtoReflect.Descriptor instead.
func (*CircuitBreakerResponse) Descriptor() ([]byte, []int) {
	return file_metric_v2_circuit_breaker_api_proto_rawDescGZIP(), []int{5}
}

func (x *CircuitBreakerResponse) GetMsgId() uint32 {
	if x != nil {
		return x.MsgId
	}
	return 0
}

func (x *CircuitBreakerResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CircuitBreakerResponse) GetInfo() string {
	if x != nil {
		return x.Info
	}
	return ""
}

func (x *CircuitBreakerResponse) GetServiceStates() []*CircuitBreakerResponse_ServiceState {
	if x != nil {
		return x.ServiceStates
	}
	return nil
}

func (x *CircuitBreakerResponse) GetGroupStates() []*CircuitBreakerResponse_ServiceGroupState {
	if x != nil {
		return x.GroupStates
	}
	return nil
}

// RPC调用结果统计
type ServiceRpcCallStats_RpcCallStat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 可选，服务子集群
	Subset map[string]string `protobuf:"bytes,1,rep,name=subset,proto3" json:"subset,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 标签是否合并计算，如果未合并计算，则labels中的value为业务配置的正则
	LablesCombine bool `protobuf:"varint,2,opt,name=lables_combine,json=lablesCombine,proto3" json:"lables_combine,omitempty"`
	// 可选，业务标签信息
	Labels map[string]string `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 可选，服务实例
	Instance string `protobuf:"bytes,4,opt,name=instance,proto3" json:"instance,omitempty"`
	// 上次上报到本次上报中间总请求数
	Total uint32 `protobuf:"varint,5,opt,name=total,proto3" json:"total,omitempty"`
	// 各种类型错误统计到的错误数
	ErrStat map[string]uint32 `protobuf:"bytes,6,rep,name=err_stat,json=errStat,proto3" json:"err_stat,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// 该时间戳为当前统计数据的起始时间，单位s
	// 客户端使用时间对齐协议会服务器对齐该时间
	StartTimestamp uint64 `protobuf:"varint,7,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
}

func (x *ServiceRpcCallStats_RpcCallStat) Reset() {
	*x = ServiceRpcCallStats_RpcCallStat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceRpcCallStats_RpcCallStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceRpcCallStats_RpcCallStat) ProtoMessage() {}

func (x *ServiceRpcCallStats_RpcCallStat) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceRpcCallStats_RpcCallStat.ProtoReflect.Descriptor instead.
func (*ServiceRpcCallStats_RpcCallStat) Descriptor() ([]byte, []int) {
	return file_metric_v2_circuit_breaker_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ServiceRpcCallStats_RpcCallStat) GetSubset() map[string]string {
	if x != nil {
		return x.Subset
	}
	return nil
}

func (x *ServiceRpcCallStats_RpcCallStat) GetLablesCombine() bool {
	if x != nil {
		return x.LablesCombine
	}
	return false
}

func (x *ServiceRpcCallStats_RpcCallStat) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ServiceRpcCallStats_RpcCallStat) GetInstance() string {
	if x != nil {
		return x.Instance
	}
	return ""
}

func (x *ServiceRpcCallStats_RpcCallStat) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ServiceRpcCallStats_RpcCallStat) GetErrStat() map[string]uint32 {
	if x != nil {
		return x.ErrStat
	}
	return nil
}

func (x *ServiceRpcCallStats_RpcCallStat) GetStartTimestamp() uint64 {
	if x != nil {
		return x.StartTimestamp
	}
	return 0
}

// 服务熔断数据请求
type CircuitBreakerRequest_ServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 熔断规则信息
	RuleInfo *CircuitBreakerRuleInfo `protobuf:"bytes,1,opt,name=rule_info,json=ruleInfo,proto3" json:"rule_info,omitempty"`
	// SDK缓存熔断数据revision
	// 服务器检查revison，如果未变化，则不用返回具体的熔断数据
	Revision string `protobuf:"bytes,2,opt,name=revision,proto3" json:"revision,omitempty"`
}

func (x *CircuitBreakerRequest_ServiceRequest) Reset() {
	*x = CircuitBreakerRequest_ServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerRequest_ServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerRequest_ServiceRequest) ProtoMessage() {}

func (x *CircuitBreakerRequest_ServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerRequest_ServiceRequest.ProtoReflect.Descriptor instead.
func (*CircuitBreakerRequest_ServiceRequest) Descriptor() ([]byte, []int) {
	return file_metric_v2_circuit_breaker_api_proto_rawDescGZIP(), []int{4, 0}
}

func (x *CircuitBreakerRequest_ServiceRequest) GetRuleInfo() *CircuitBreakerRuleInfo {
	if x != nil {
		return x.RuleInfo
	}
	return nil
}

func (x *CircuitBreakerRequest_ServiceRequest) GetRevision() string {
	if x != nil {
		return x.Revision
	}
	return ""
}

// 服务分组熔断数据请求
type CircuitBreakerRequest_ServiceGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 服务分组
	ServiceGroup string `protobuf:"bytes,1,opt,name=service_group,json=serviceGroup,proto3" json:"service_group,omitempty"`
	// SDK缓存熔断数据revision
	// 服务器检查revison，如果未变化，则不用返回具体的熔断数据
	Revision string `protobuf:"bytes,2,opt,name=revision,proto3" json:"revision,omitempty"`
}

func (x *CircuitBreakerRequest_ServiceGroupRequest) Reset() {
	*x = CircuitBreakerRequest_ServiceGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerRequest_ServiceGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerRequest_ServiceGroupRequest) ProtoMessage() {}

func (x *CircuitBreakerRequest_ServiceGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerRequest_ServiceGroupRequest.ProtoReflect.Descriptor instead.
func (*CircuitBreakerRequest_ServiceGroupRequest) Descriptor() ([]byte, []int) {
	return file_metric_v2_circuit_breaker_api_proto_rawDescGZIP(), []int{4, 1}
}

func (x *CircuitBreakerRequest_ServiceGroupRequest) GetServiceGroup() string {
	if x != nil {
		return x.ServiceGroup
	}
	return ""
}

func (x *CircuitBreakerRequest_ServiceGroupRequest) GetRevision() string {
	if x != nil {
		return x.Revision
	}
	return ""
}

// 熔断数据
type CircuitBreakerResponse_StateData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 可选，服务子集群
	Subset map[string]string `protobuf:"bytes,1,rep,name=subset,proto3" json:"subset,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 可选，业务标签信息
	// 对应熔断规则中的breaker_labels
	Labels map[string]*model.MatchString `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 可选，服务实例
	Instance string                                 `protobuf:"bytes,3,opt,name=instance,proto3" json:"instance,omitempty"`
	Scope    CircuitBreakerResponse_StateData_Scope `protobuf:"varint,4,opt,name=scope,proto3,enum=polaris.metric.v2.CircuitBreakerResponse_StateData_Scope" json:"scope,omitempty"`
	State    CircuitBreakerResponse_StateData_State `protobuf:"varint,5,opt,name=state,proto3,enum=polaris.metric.v2.CircuitBreakerResponse_StateData_State" json:"state,omitempty"`
	// 半开放量权重
	RemainWeight int32 `protobuf:"varint,6,opt,name=remain_weight,json=remainWeight,proto3" json:"remain_weight,omitempty"`
}

func (x *CircuitBreakerResponse_StateData) Reset() {
	*x = CircuitBreakerResponse_StateData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerResponse_StateData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerResponse_StateData) ProtoMessage() {}

func (x *CircuitBreakerResponse_StateData) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerResponse_StateData.ProtoReflect.Descriptor instead.
func (*CircuitBreakerResponse_StateData) Descriptor() ([]byte, []int) {
	return file_metric_v2_circuit_breaker_api_proto_rawDescGZIP(), []int{5, 0}
}

func (x *CircuitBreakerResponse_StateData) GetSubset() map[string]string {
	if x != nil {
		return x.Subset
	}
	return nil
}

func (x *CircuitBreakerResponse_StateData) GetLabels() map[string]*model.MatchString {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *CircuitBreakerResponse_StateData) GetInstance() string {
	if x != nil {
		return x.Instance
	}
	return ""
}

func (x *CircuitBreakerResponse_StateData) GetScope() CircuitBreakerResponse_StateData_Scope {
	if x != nil {
		return x.Scope
	}
	return CircuitBreakerResponse_StateData_SERVICE
}

func (x *CircuitBreakerResponse_StateData) GetState() CircuitBreakerResponse_StateData_State {
	if x != nil {
		return x.State
	}
	return CircuitBreakerResponse_StateData_CLOSE
}

func (x *CircuitBreakerResponse_StateData) GetRemainWeight() int32 {
	if x != nil {
		return x.RemainWeight
	}
	return 0
}

// 服务熔断数据
type CircuitBreakerResponse_ServiceState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 服务熔断规则信息
	RuleInfo *CircuitBreakerRuleInfo `protobuf:"bytes,1,opt,name=rule_info,json=ruleInfo,proto3" json:"rule_info,omitempty"`
	// 熔断数据revision
	Revision string `protobuf:"bytes,2,opt,name=revision,proto3" json:"revision,omitempty"`
	// 具体熔断数据
	States []*CircuitBreakerResponse_StateData `protobuf:"bytes,3,rep,name=states,proto3" json:"states,omitempty"`
}

func (x *CircuitBreakerResponse_ServiceState) Reset() {
	*x = CircuitBreakerResponse_ServiceState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerResponse_ServiceState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerResponse_ServiceState) ProtoMessage() {}

func (x *CircuitBreakerResponse_ServiceState) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerResponse_ServiceState.ProtoReflect.Descriptor instead.
func (*CircuitBreakerResponse_ServiceState) Descriptor() ([]byte, []int) {
	return file_metric_v2_circuit_breaker_api_proto_rawDescGZIP(), []int{5, 1}
}

func (x *CircuitBreakerResponse_ServiceState) GetRuleInfo() *CircuitBreakerRuleInfo {
	if x != nil {
		return x.RuleInfo
	}
	return nil
}

func (x *CircuitBreakerResponse_ServiceState) GetRevision() string {
	if x != nil {
		return x.Revision
	}
	return ""
}

func (x *CircuitBreakerResponse_ServiceState) GetStates() []*CircuitBreakerResponse_StateData {
	if x != nil {
		return x.States
	}
	return nil
}

// 服务分组熔断数据
type CircuitBreakerResponse_ServiceGroupState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 熔断分组
	ServiceGroup string `protobuf:"bytes,1,opt,name=service_group,json=serviceGroup,proto3" json:"service_group,omitempty"`
	// 熔断数据revision
	Revision string `protobuf:"bytes,2,opt,name=revision,proto3" json:"revision,omitempty"`
	// 具体熔断数据
	States []*CircuitBreakerResponse_StateData `protobuf:"bytes,3,rep,name=states,proto3" json:"states,omitempty"`
}

func (x *CircuitBreakerResponse_ServiceGroupState) Reset() {
	*x = CircuitBreakerResponse_ServiceGroupState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircuitBreakerResponse_ServiceGroupState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerResponse_ServiceGroupState) ProtoMessage() {}

func (x *CircuitBreakerResponse_ServiceGroupState) ProtoReflect() protoreflect.Message {
	mi := &file_metric_v2_circuit_breaker_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerResponse_ServiceGroupState.ProtoReflect.Descriptor instead.
func (*CircuitBreakerResponse_ServiceGroupState) Descriptor() ([]byte, []int) {
	return file_metric_v2_circuit_breaker_api_proto_rawDescGZIP(), []int{5, 2}
}

func (x *CircuitBreakerResponse_ServiceGroupState) GetServiceGroup() string {
	if x != nil {
		return x.ServiceGroup
	}
	return ""
}

func (x *CircuitBreakerResponse_ServiceGroupState) GetRevision() string {
	if x != nil {
		return x.Revision
	}
	return ""
}

func (x *CircuitBreakerResponse_ServiceGroupState) GetStates() []*CircuitBreakerResponse_StateData {
	if x != nil {
		return x.States
	}
	return nil
}

var File_metric_v2_circuit_breaker_api_proto protoreflect.FileDescriptor

var file_metric_v2_circuit_breaker_api_proto_rawDesc = []byte{
	0x0a, 0x23, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x69, 0x72, 0x63,
	0x75, 0x69, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x1a, 0x14, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd3,
	0x01, 0x0a, 0x16, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65,
	0x72, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x73, 0x72, 0x63, 0x5f, 0x72, 0x75, 0x6c, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x53, 0x72, 0x63, 0x52, 0x75, 0x6c,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x72, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x72, 0x63, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x72, 0x63, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x72, 0x63,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0xfe, 0x05, 0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x70, 0x63, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x46, 0x0a, 0x09,
	0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x2e, 0x76, 0x32, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b,
	0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4f, 0x0a, 0x09, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x70, 0x63, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x2e,
	0x52, 0x70, 0x63, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x52, 0x08, 0x63, 0x61, 0x6c,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x1a, 0xcd, 0x04, 0x0a, 0x0b, 0x52, 0x70, 0x63, 0x43, 0x61, 0x6c,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x12, 0x56, 0x0a, 0x06, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x70, 0x63, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x2e, 0x52, 0x70,
	0x63, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x12, 0x25, 0x0a,
	0x0e, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x43, 0x6f, 0x6d,
	0x62, 0x69, 0x6e, 0x65, 0x12, 0x56, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x70, 0x63, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x2e, 0x52, 0x70, 0x63,
	0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x5a,
	0x0a, 0x08, 0x65, 0x72, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3f, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x70, 0x63, 0x43,
	0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x2e, 0x52, 0x70, 0x63, 0x43, 0x61, 0x6c, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x2e, 0x45, 0x72, 0x72, 0x53, 0x74, 0x61, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x07, 0x65, 0x72, 0x72, 0x53, 0x74, 0x61, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x1a, 0x39, 0x0a, 0x0b, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x39,
	0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3a, 0x0a, 0x0c, 0x45, 0x72, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x9d, 0x01, 0x0a, 0x17, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x15, 0x0a, 0x06, 0x6d, 0x73, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x6c, 0x6c,
	0x65, 0x72, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x61,
	0x6c, 0x6c, 0x65, 0x72, 0x53, 0x65, 0x74, 0x12, 0x4c, 0x0a, 0x0e, 0x72, 0x70, 0x63, 0x5f, 0x63,
	0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x70, 0x63, 0x43, 0x61,
	0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x0c, 0x72, 0x70, 0x63, 0x43, 0x61, 0x6c, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x73, 0x22, 0x59, 0x0a, 0x18, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x15, 0x0a, 0x06, 0x6d, 0x73, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f,
	0x22, 0xc5, 0x03, 0x0a, 0x15, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61,
	0x6b, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6d, 0x73,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49,
	0x64, 0x12, 0x62, 0x0a, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e,
	0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x63, 0x0a, 0x0e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0d, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x1a, 0x74, 0x0a, 0x0e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x09,
	0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x2e, 0x76, 0x32, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b,
	0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x1a, 0x56, 0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1a, 0x0a, 0x08,
	0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x8d, 0x0a, 0x0a, 0x16, 0x43, 0x69, 0x72,
	0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x6d, 0x73, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6e,
	0x66, 0x6f, 0x12, 0x5d, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x6f, 0x6c,
	0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x43,
	0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x73, 0x12, 0x5e, 0x0a, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x69, 0x72, 0x63,
	0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x73, 0x1a, 0x8e, 0x05, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x57, 0x0a, 0x06, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3f, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x2e, 0x76, 0x32, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x06, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x12, 0x57, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72,
	0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x69, 0x72,
	0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x4f, 0x0a,
	0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x70,
	0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32,
	0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x2e, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x4f,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x57, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x54, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x1f, 0x0a, 0x05, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x0b,
	0x0a, 0x07, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x47,
	0x52, 0x4f, 0x55, 0x50, 0x10, 0x01, 0x22, 0x3a, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x09, 0x0a, 0x05, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4f, 0x50,
	0x45, 0x4e, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x48, 0x41, 0x4c, 0x46, 0x5f, 0x4f, 0x50, 0x45,
	0x4e, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x52, 0x45, 0x53, 0x45, 0x52, 0x56, 0x45, 0x44,
	0x10, 0x03, 0x1a, 0xbf, 0x01, 0x0a, 0x0c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x46, 0x0a, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73,
	0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75,
	0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x72,
	0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x4b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x69, 0x72, 0x63,
	0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x73, 0x1a, 0xa1, 0x01, 0x0a, 0x11, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x4b, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e,
	0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x32, 0xd9, 0x01, 0x0a, 0x12, 0x43, 0x69, 0x72,
	0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x47, 0x52, 0x50, 0x43, 0x12,
	0x63, 0x0a, 0x06, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x2a, 0x2e, 0x70, 0x6f, 0x6c, 0x61,
	0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x48, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x5e, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x28, 0x2e,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x69, 0x72, 0x63,
	0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x42, 0x36, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61,
	0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2f, 0x76, 0x32, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_metric_v2_circuit_breaker_api_proto_rawDescOnce sync.Once
	file_metric_v2_circuit_breaker_api_proto_rawDescData = file_metric_v2_circuit_breaker_api_proto_rawDesc
)

func file_metric_v2_circuit_breaker_api_proto_rawDescGZIP() []byte {
	file_metric_v2_circuit_breaker_api_proto_rawDescOnce.Do(func() {
		file_metric_v2_circuit_breaker_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_metric_v2_circuit_breaker_api_proto_rawDescData)
	})
	return file_metric_v2_circuit_breaker_api_proto_rawDescData
}

var file_metric_v2_circuit_breaker_api_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_metric_v2_circuit_breaker_api_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_metric_v2_circuit_breaker_api_proto_goTypes = []interface{}{
	(CircuitBreakerResponse_StateData_Scope)(0),  // 0: polaris.metric.v2.CircuitBreakerResponse.StateData.Scope
	(CircuitBreakerResponse_StateData_State)(0),  // 1: polaris.metric.v2.CircuitBreakerResponse.StateData.State
	(*CircuitBreakerRuleInfo)(nil),               // 2: polaris.metric.v2.CircuitBreakerRuleInfo
	(*ServiceRpcCallStats)(nil),                  // 3: polaris.metric.v2.ServiceRpcCallStats
	(*HealthDataReportRequest)(nil),              // 4: polaris.metric.v2.HealthDataReportRequest
	(*HealthDataReportResponse)(nil),             // 5: polaris.metric.v2.HealthDataReportResponse
	(*CircuitBreakerRequest)(nil),                // 6: polaris.metric.v2.CircuitBreakerRequest
	(*CircuitBreakerResponse)(nil),               // 7: polaris.metric.v2.CircuitBreakerResponse
	(*ServiceRpcCallStats_RpcCallStat)(nil),      // 8: polaris.metric.v2.ServiceRpcCallStats.RpcCallStat
	nil,                                          // 9: polaris.metric.v2.ServiceRpcCallStats.RpcCallStat.SubsetEntry
	nil,                                          // 10: polaris.metric.v2.ServiceRpcCallStats.RpcCallStat.LabelsEntry
	nil,                                          // 11: polaris.metric.v2.ServiceRpcCallStats.RpcCallStat.ErrStatEntry
	(*CircuitBreakerRequest_ServiceRequest)(nil), // 12: polaris.metric.v2.CircuitBreakerRequest.ServiceRequest
	(*CircuitBreakerRequest_ServiceGroupRequest)(nil), // 13: polaris.metric.v2.CircuitBreakerRequest.ServiceGroupRequest
	(*CircuitBreakerResponse_StateData)(nil),          // 14: polaris.metric.v2.CircuitBreakerResponse.StateData
	(*CircuitBreakerResponse_ServiceState)(nil),       // 15: polaris.metric.v2.CircuitBreakerResponse.ServiceState
	(*CircuitBreakerResponse_ServiceGroupState)(nil),  // 16: polaris.metric.v2.CircuitBreakerResponse.ServiceGroupState
	nil,                       // 17: polaris.metric.v2.CircuitBreakerResponse.StateData.SubsetEntry
	nil,                       // 18: polaris.metric.v2.CircuitBreakerResponse.StateData.LabelsEntry
	(*model.MatchString)(nil), // 19: api.v1.model.MatchString
}
var file_metric_v2_circuit_breaker_api_proto_depIdxs = []int32{
	2,  // 0: polaris.metric.v2.ServiceRpcCallStats.rule_info:type_name -> polaris.metric.v2.CircuitBreakerRuleInfo
	8,  // 1: polaris.metric.v2.ServiceRpcCallStats.call_stat:type_name -> polaris.metric.v2.ServiceRpcCallStats.RpcCallStat
	3,  // 2: polaris.metric.v2.HealthDataReportRequest.rpc_call_stats:type_name -> polaris.metric.v2.ServiceRpcCallStats
	12, // 3: polaris.metric.v2.CircuitBreakerRequest.service_requests:type_name -> polaris.metric.v2.CircuitBreakerRequest.ServiceRequest
	13, // 4: polaris.metric.v2.CircuitBreakerRequest.group_requests:type_name -> polaris.metric.v2.CircuitBreakerRequest.ServiceGroupRequest
	15, // 5: polaris.metric.v2.CircuitBreakerResponse.service_states:type_name -> polaris.metric.v2.CircuitBreakerResponse.ServiceState
	16, // 6: polaris.metric.v2.CircuitBreakerResponse.group_states:type_name -> polaris.metric.v2.CircuitBreakerResponse.ServiceGroupState
	9,  // 7: polaris.metric.v2.ServiceRpcCallStats.RpcCallStat.subset:type_name -> polaris.metric.v2.ServiceRpcCallStats.RpcCallStat.SubsetEntry
	10, // 8: polaris.metric.v2.ServiceRpcCallStats.RpcCallStat.labels:type_name -> polaris.metric.v2.ServiceRpcCallStats.RpcCallStat.LabelsEntry
	11, // 9: polaris.metric.v2.ServiceRpcCallStats.RpcCallStat.err_stat:type_name -> polaris.metric.v2.ServiceRpcCallStats.RpcCallStat.ErrStatEntry
	2,  // 10: polaris.metric.v2.CircuitBreakerRequest.ServiceRequest.rule_info:type_name -> polaris.metric.v2.CircuitBreakerRuleInfo
	17, // 11: polaris.metric.v2.CircuitBreakerResponse.StateData.subset:type_name -> polaris.metric.v2.CircuitBreakerResponse.StateData.SubsetEntry
	18, // 12: polaris.metric.v2.CircuitBreakerResponse.StateData.labels:type_name -> polaris.metric.v2.CircuitBreakerResponse.StateData.LabelsEntry
	0,  // 13: polaris.metric.v2.CircuitBreakerResponse.StateData.scope:type_name -> polaris.metric.v2.CircuitBreakerResponse.StateData.Scope
	1,  // 14: polaris.metric.v2.CircuitBreakerResponse.StateData.state:type_name -> polaris.metric.v2.CircuitBreakerResponse.StateData.State
	2,  // 15: polaris.metric.v2.CircuitBreakerResponse.ServiceState.rule_info:type_name -> polaris.metric.v2.CircuitBreakerRuleInfo
	14, // 16: polaris.metric.v2.CircuitBreakerResponse.ServiceState.states:type_name -> polaris.metric.v2.CircuitBreakerResponse.StateData
	14, // 17: polaris.metric.v2.CircuitBreakerResponse.ServiceGroupState.states:type_name -> polaris.metric.v2.CircuitBreakerResponse.StateData
	19, // 18: polaris.metric.v2.CircuitBreakerResponse.StateData.LabelsEntry.value:type_name -> api.v1.model.MatchString
	4,  // 19: polaris.metric.v2.CircuitBreakerGRPC.Report:input_type -> polaris.metric.v2.HealthDataReportRequest
	6,  // 20: polaris.metric.v2.CircuitBreakerGRPC.Query:input_type -> polaris.metric.v2.CircuitBreakerRequest
	5,  // 21: polaris.metric.v2.CircuitBreakerGRPC.Report:output_type -> polaris.metric.v2.HealthDataReportResponse
	7,  // 22: polaris.metric.v2.CircuitBreakerGRPC.Query:output_type -> polaris.metric.v2.CircuitBreakerResponse
	21, // [21:23] is the sub-list for method output_type
	19, // [19:21] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_metric_v2_circuit_breaker_api_proto_init() }
func file_metric_v2_circuit_breaker_api_proto_init() {
	if File_metric_v2_circuit_breaker_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_metric_v2_circuit_breaker_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerRuleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_circuit_breaker_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceRpcCallStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_circuit_breaker_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthDataReportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_circuit_breaker_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthDataReportResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_circuit_breaker_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_circuit_breaker_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_circuit_breaker_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceRpcCallStats_RpcCallStat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_circuit_breaker_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerRequest_ServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_circuit_breaker_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerRequest_ServiceGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_circuit_breaker_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerResponse_StateData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_circuit_breaker_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerResponse_ServiceState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metric_v2_circuit_breaker_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircuitBreakerResponse_ServiceGroupState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_metric_v2_circuit_breaker_api_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_metric_v2_circuit_breaker_api_proto_goTypes,
		DependencyIndexes: file_metric_v2_circuit_breaker_api_proto_depIdxs,
		EnumInfos:         file_metric_v2_circuit_breaker_api_proto_enumTypes,
		MessageInfos:      file_metric_v2_circuit_breaker_api_proto_msgTypes,
	}.Build()
	File_metric_v2_circuit_breaker_api_proto = out.File
	file_metric_v2_circuit_breaker_api_proto_rawDesc = nil
	file_metric_v2_circuit_breaker_api_proto_goTypes = nil
	file_metric_v2_circuit_breaker_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// CircuitBreakerGRPCClient is the client API for CircuitBreakerGRPC service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CircuitBreakerGRPCClient interface {
	// 上报服务统计数据
	Report(ctx context.Context, in *HealthDataReportRequest, opts ...grpc.CallOption) (*HealthDataReportResponse, error)
	// 查询服务熔断信息
	Query(ctx context.Context, in *CircuitBreakerRequest, opts ...grpc.CallOption) (*CircuitBreakerResponse, error)
}

type circuitBreakerGRPCClient struct {
	cc grpc.ClientConnInterface
}

func NewCircuitBreakerGRPCClient(cc grpc.ClientConnInterface) CircuitBreakerGRPCClient {
	return &circuitBreakerGRPCClient{cc}
}

func (c *circuitBreakerGRPCClient) Report(ctx context.Context, in *HealthDataReportRequest, opts ...grpc.CallOption) (*HealthDataReportResponse, error) {
	out := new(HealthDataReportResponse)
	err := c.cc.Invoke(ctx, "/polaris.metric.v2.CircuitBreakerGRPC/Report", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *circuitBreakerGRPCClient) Query(ctx context.Context, in *CircuitBreakerRequest, opts ...grpc.CallOption) (*CircuitBreakerResponse, error) {
	out := new(CircuitBreakerResponse)
	err := c.cc.Invoke(ctx, "/polaris.metric.v2.CircuitBreakerGRPC/Query", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CircuitBreakerGRPCServer is the server API for CircuitBreakerGRPC service.
type CircuitBreakerGRPCServer interface {
	// 上报服务统计数据
	Report(context.Context, *HealthDataReportRequest) (*HealthDataReportResponse, error)
	// 查询服务熔断信息
	Query(context.Context, *CircuitBreakerRequest) (*CircuitBreakerResponse, error)
}

// UnimplementedCircuitBreakerGRPCServer can be embedded to have forward compatible implementations.
type UnimplementedCircuitBreakerGRPCServer struct {
}

func (*UnimplementedCircuitBreakerGRPCServer) Report(context.Context, *HealthDataReportRequest) (*HealthDataReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Report not implemented")
}
func (*UnimplementedCircuitBreakerGRPCServer) Query(context.Context, *CircuitBreakerRequest) (*CircuitBreakerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Query not implemented")
}

func RegisterCircuitBreakerGRPCServer(s *grpc.Server, srv CircuitBreakerGRPCServer) {
	s.RegisterService(&_CircuitBreakerGRPC_serviceDesc, srv)
}

func _CircuitBreakerGRPC_Report_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthDataReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CircuitBreakerGRPCServer).Report(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/polaris.metric.v2.CircuitBreakerGRPC/Report",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CircuitBreakerGRPCServer).Report(ctx, req.(*HealthDataReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CircuitBreakerGRPC_Query_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CircuitBreakerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CircuitBreakerGRPCServer).Query(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/polaris.metric.v2.CircuitBreakerGRPC/Query",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CircuitBreakerGRPCServer).Query(ctx, req.(*CircuitBreakerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _CircuitBreakerGRPC_serviceDesc = grpc.ServiceDesc{
	ServiceName: "polaris.metric.v2.CircuitBreakerGRPC",
	HandlerType: (*CircuitBreakerGRPCServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Report",
			Handler:    _CircuitBreakerGRPC_Report_Handler,
		},
		{
			MethodName: "Query",
			Handler:    _CircuitBreakerGRPC_Query_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "metric/v2/circuit_breaker_api.proto",
}
