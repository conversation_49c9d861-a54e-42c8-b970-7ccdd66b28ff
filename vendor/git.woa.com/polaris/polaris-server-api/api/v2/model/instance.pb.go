// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v2/model/instance.proto

package model

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 健康检查类型
type HealthCheck_HealthCheckType int32

const (
	HealthCheck_UNKNOWN   HealthCheck_HealthCheckType = 0
	HealthCheck_HEARTBEAT HealthCheck_HealthCheckType = 1
)

// Enum value maps for HealthCheck_HealthCheckType.
var (
	HealthCheck_HealthCheckType_name = map[int32]string{
		0: "UNKNOWN",
		1: "HEARTBEAT",
	}
	HealthCheck_HealthCheckType_value = map[string]int32{
		"UNKNOWN":   0,
		"HEARTBEAT": 1,
	}
)

func (x HealthCheck_HealthCheckType) Enum() *HealthCheck_HealthCheckType {
	p := new(HealthCheck_HealthCheckType)
	*p = x
	return p
}

func (x HealthCheck_HealthCheckType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HealthCheck_HealthCheckType) Descriptor() protoreflect.EnumDescriptor {
	return file_v2_model_instance_proto_enumTypes[0].Descriptor()
}

func (HealthCheck_HealthCheckType) Type() protoreflect.EnumType {
	return &file_v2_model_instance_proto_enumTypes[0]
}

func (x HealthCheck_HealthCheckType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HealthCheck_HealthCheckType.Descriptor instead.
func (HealthCheck_HealthCheckType) EnumDescriptor() ([]byte, []int) {
	return file_v2_model_instance_proto_rawDescGZIP(), []int{1, 0}
}

// Instance V2版本的实例定义
// DiscoverType = INSTANCE返回的字段：
// vpc_id,host,port,protocol,version,priority,weight,healthy,isolate,location,metadata,mtime,iid
type Instance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 服务实例id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// vpc_id
	VpcId string `protobuf:"bytes,2,opt,name=vpc_id,proto3" json:"vpc_id,omitempty"`
	// ip/域名
	Host string `protobuf:"bytes,3,opt,name=host,proto3" json:"host,omitempty"`
	// 端口
	Port uint32 `protobuf:"varint,4,opt,name=port,proto3" json:"port,omitempty"`
	// 协议
	Protocol string `protobuf:"bytes,5,opt,name=protocol,proto3" json:"protocol,omitempty"`
	// 版本
	Version string `protobuf:"bytes,6,opt,name=version,proto3" json:"version,omitempty"`
	// 优先级
	Priority uint32 `protobuf:"varint,7,opt,name=priority,proto3" json:"priority,omitempty"`
	// 权重
	Weight uint32 `protobuf:"varint,8,opt,name=weight,proto3" json:"weight,omitempty"`
	// 是否开启健康检查
	EnableHealthCheck bool `protobuf:"varint,9,opt,name=enable_health_check,json=enableHealthCheck,proto3" json:"enable_health_check,omitempty"`
	// 健康检查类型
	HealthCheck *HealthCheck `protobuf:"bytes,10,opt,name=health_check,json=healthCheck,proto3" json:"health_check,omitempty"`
	// 是否健康
	Healthy bool `protobuf:"varint,11,opt,name=healthy,proto3" json:"healthy,omitempty"`
	// 是否隔离
	Isolate bool `protobuf:"varint,12,opt,name=isolate,proto3" json:"isolate,omitempty"`
	// 位置信息
	Location *Location `protobuf:"bytes,13,opt,name=location,proto3" json:"location,omitempty"`
	// 元数据
	Metadata map[string]string `protobuf:"bytes,14,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 逻辑分区
	LogicSet string `protobuf:"bytes,15,opt,name=logic_set,proto3" json:"logic_set,omitempty"`
	// 创建时间
	Ctime int64 `protobuf:"varint,16,opt,name=ctime,proto3" json:"ctime,omitempty"`
	// 修改时间
	Mtime int64 `protobuf:"varint,17,opt,name=mtime,proto3" json:"mtime,omitempty"`
	// 版本号
	Revision string `protobuf:"bytes,18,opt,name=revision,proto3" json:"revision,omitempty"`
	// 服务内的实例ID 服务内全局唯一，根据CRC32(Instance.id)生成，有冲突自动往下顺延
	Iid int32 `protobuf:"varint,19,opt,name=iid,proto3" json:"iid,omitempty"`
	// 实例动态权重
	DynamicWeight uint32 `protobuf:"varint,20,opt,name=dynamic_weight,proto3" json:"dynamic_weight,omitempty"`
	// 服务注解
	Annotation map[string]string `protobuf:"bytes,21,rep,name=annotation,proto3" json:"annotation,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Instance) Reset() {
	*x = Instance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v2_model_instance_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Instance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Instance) ProtoMessage() {}

func (x *Instance) ProtoReflect() protoreflect.Message {
	mi := &file_v2_model_instance_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Instance.ProtoReflect.Descriptor instead.
func (*Instance) Descriptor() ([]byte, []int) {
	return file_v2_model_instance_proto_rawDescGZIP(), []int{0}
}

func (x *Instance) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Instance) GetVpcId() string {
	if x != nil {
		return x.VpcId
	}
	return ""
}

func (x *Instance) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Instance) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Instance) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *Instance) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Instance) GetPriority() uint32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *Instance) GetWeight() uint32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *Instance) GetEnableHealthCheck() bool {
	if x != nil {
		return x.EnableHealthCheck
	}
	return false
}

func (x *Instance) GetHealthCheck() *HealthCheck {
	if x != nil {
		return x.HealthCheck
	}
	return nil
}

func (x *Instance) GetHealthy() bool {
	if x != nil {
		return x.Healthy
	}
	return false
}

func (x *Instance) GetIsolate() bool {
	if x != nil {
		return x.Isolate
	}
	return false
}

func (x *Instance) GetLocation() *Location {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *Instance) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Instance) GetLogicSet() string {
	if x != nil {
		return x.LogicSet
	}
	return ""
}

func (x *Instance) GetCtime() int64 {
	if x != nil {
		return x.Ctime
	}
	return 0
}

func (x *Instance) GetMtime() int64 {
	if x != nil {
		return x.Mtime
	}
	return 0
}

func (x *Instance) GetRevision() string {
	if x != nil {
		return x.Revision
	}
	return ""
}

func (x *Instance) GetIid() int32 {
	if x != nil {
		return x.Iid
	}
	return 0
}

func (x *Instance) GetDynamicWeight() uint32 {
	if x != nil {
		return x.DynamicWeight
	}
	return 0
}

func (x *Instance) GetAnnotation() map[string]string {
	if x != nil {
		return x.Annotation
	}
	return nil
}

type HealthCheck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type      HealthCheck_HealthCheckType `protobuf:"varint,1,opt,name=type,proto3,enum=api.v2.model.HealthCheck_HealthCheckType" json:"type,omitempty"`
	Heartbeat *HeartbeatHealthCheck       `protobuf:"bytes,2,opt,name=heartbeat,proto3" json:"heartbeat,omitempty"`
}

func (x *HealthCheck) Reset() {
	*x = HealthCheck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v2_model_instance_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthCheck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheck) ProtoMessage() {}

func (x *HealthCheck) ProtoReflect() protoreflect.Message {
	mi := &file_v2_model_instance_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheck.ProtoReflect.Descriptor instead.
func (*HealthCheck) Descriptor() ([]byte, []int) {
	return file_v2_model_instance_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheck) GetType() HealthCheck_HealthCheckType {
	if x != nil {
		return x.Type
	}
	return HealthCheck_UNKNOWN
}

func (x *HealthCheck) GetHeartbeat() *HeartbeatHealthCheck {
	if x != nil {
		return x.Heartbeat
	}
	return nil
}

type HeartbeatHealthCheck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ttl uint32 `protobuf:"varint,1,opt,name=ttl,proto3" json:"ttl,omitempty"`
}

func (x *HeartbeatHealthCheck) Reset() {
	*x = HeartbeatHealthCheck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v2_model_instance_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeartbeatHealthCheck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatHealthCheck) ProtoMessage() {}

func (x *HeartbeatHealthCheck) ProtoReflect() protoreflect.Message {
	mi := &file_v2_model_instance_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatHealthCheck.ProtoReflect.Descriptor instead.
func (*HeartbeatHealthCheck) Descriptor() ([]byte, []int) {
	return file_v2_model_instance_proto_rawDescGZIP(), []int{2}
}

func (x *HeartbeatHealthCheck) GetTtl() uint32 {
	if x != nil {
		return x.Ttl
	}
	return 0
}

var File_v2_model_instance_proto protoreflect.FileDescriptor

var file_v2_model_instance_proto_rawDesc = []byte{
	0x0a, 0x17, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x17, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xc0, 0x06, 0x0a, 0x08, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x76, 0x70, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x76,
	0x70, 0x63, 0x5f, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12,
	0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x48, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x3c, 0x0a, 0x0c, 0x68, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x0b, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x12,
	0x18, 0x0a, 0x07, 0x69, 0x73, 0x6f, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x69, 0x73, 0x6f, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x08, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a,
	0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x5f, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x69, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12,
	0x46, 0x0a, 0x0a, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x41, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3d, 0x0a, 0x0f, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xbd, 0x01, 0x0a, 0x0b, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x12, 0x3d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x2e, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x40, 0x0a, 0x09, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x48, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x09, 0x68, 0x65, 0x61, 0x72, 0x74,
	0x62, 0x65, 0x61, 0x74, 0x22, 0x2d, 0x0a, 0x0f, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x48, 0x45, 0x41, 0x52, 0x54, 0x42, 0x45, 0x41,
	0x54, 0x10, 0x01, 0x22, 0x28, 0x0a, 0x14, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74,
	0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x74,
	0x74, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x74, 0x74, 0x6c, 0x42, 0x35, 0x5a,
	0x33, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c,
	0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v2_model_instance_proto_rawDescOnce sync.Once
	file_v2_model_instance_proto_rawDescData = file_v2_model_instance_proto_rawDesc
)

func file_v2_model_instance_proto_rawDescGZIP() []byte {
	file_v2_model_instance_proto_rawDescOnce.Do(func() {
		file_v2_model_instance_proto_rawDescData = protoimpl.X.CompressGZIP(file_v2_model_instance_proto_rawDescData)
	})
	return file_v2_model_instance_proto_rawDescData
}

var file_v2_model_instance_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_v2_model_instance_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_v2_model_instance_proto_goTypes = []interface{}{
	(HealthCheck_HealthCheckType)(0), // 0: api.v2.model.HealthCheck.HealthCheckType
	(*Instance)(nil),                 // 1: api.v2.model.Instance
	(*HealthCheck)(nil),              // 2: api.v2.model.HealthCheck
	(*HeartbeatHealthCheck)(nil),     // 3: api.v2.model.HeartbeatHealthCheck
	nil,                              // 4: api.v2.model.Instance.MetadataEntry
	nil,                              // 5: api.v2.model.Instance.AnnotationEntry
	(*Location)(nil),                 // 6: api.v2.model.Location
}
var file_v2_model_instance_proto_depIdxs = []int32{
	2, // 0: api.v2.model.Instance.health_check:type_name -> api.v2.model.HealthCheck
	6, // 1: api.v2.model.Instance.location:type_name -> api.v2.model.Location
	4, // 2: api.v2.model.Instance.metadata:type_name -> api.v2.model.Instance.MetadataEntry
	5, // 3: api.v2.model.Instance.annotation:type_name -> api.v2.model.Instance.AnnotationEntry
	0, // 4: api.v2.model.HealthCheck.type:type_name -> api.v2.model.HealthCheck.HealthCheckType
	3, // 5: api.v2.model.HealthCheck.heartbeat:type_name -> api.v2.model.HeartbeatHealthCheck
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_v2_model_instance_proto_init() }
func file_v2_model_instance_proto_init() {
	if File_v2_model_instance_proto != nil {
		return
	}
	file_v2_model_location_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_v2_model_instance_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Instance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v2_model_instance_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthCheck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v2_model_instance_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeartbeatHealthCheck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v2_model_instance_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v2_model_instance_proto_goTypes,
		DependencyIndexes: file_v2_model_instance_proto_depIdxs,
		EnumInfos:         file_v2_model_instance_proto_enumTypes,
		MessageInfos:      file_v2_model_instance_proto_msgTypes,
	}.Build()
	File_v2_model_instance_proto = out.File
	file_v2_model_instance_proto_rawDesc = nil
	file_v2_model_instance_proto_goTypes = nil
	file_v2_model_instance_proto_depIdxs = nil
}
