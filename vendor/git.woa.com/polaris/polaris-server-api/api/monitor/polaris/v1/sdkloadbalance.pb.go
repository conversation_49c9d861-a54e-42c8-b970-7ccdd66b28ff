// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: monitor/polaris/v1/sdkloadbalance.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ServiceLoadBalanceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string                     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Token          *SDKToken                  `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	Namespace      string                     `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Service        string                     `protobuf:"bytes,4,opt,name=service,proto3" json:"service,omitempty"`
	Loadbalancer   string                     `protobuf:"bytes,5,opt,name=loadbalancer,proto3" json:"loadbalancer,omitempty"`
	TotalChooseNum uint32                     `protobuf:"varint,6,opt,name=total_choose_num,json=totalChooseNum,proto3" json:"total_choose_num,omitempty"`
	InstanceInfo   []*InstanceLoadBalanceInfo `protobuf:"bytes,7,rep,name=instance_info,json=instanceInfo,proto3" json:"instance_info,omitempty"`
	Signature      *MonitorSignature          `protobuf:"bytes,8,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *ServiceLoadBalanceInfo) Reset() {
	*x = ServiceLoadBalanceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkloadbalance_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceLoadBalanceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceLoadBalanceInfo) ProtoMessage() {}

func (x *ServiceLoadBalanceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkloadbalance_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceLoadBalanceInfo.ProtoReflect.Descriptor instead.
func (*ServiceLoadBalanceInfo) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkloadbalance_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceLoadBalanceInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ServiceLoadBalanceInfo) GetToken() *SDKToken {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *ServiceLoadBalanceInfo) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ServiceLoadBalanceInfo) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *ServiceLoadBalanceInfo) GetLoadbalancer() string {
	if x != nil {
		return x.Loadbalancer
	}
	return ""
}

func (x *ServiceLoadBalanceInfo) GetTotalChooseNum() uint32 {
	if x != nil {
		return x.TotalChooseNum
	}
	return 0
}

func (x *ServiceLoadBalanceInfo) GetInstanceInfo() []*InstanceLoadBalanceInfo {
	if x != nil {
		return x.InstanceInfo
	}
	return nil
}

func (x *ServiceLoadBalanceInfo) GetSignature() *MonitorSignature {
	if x != nil {
		return x.Signature
	}
	return nil
}

type InstanceLoadBalanceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip        string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	Port      uint32 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	VpcId     string `protobuf:"bytes,3,opt,name=vpc_id,json=vpcId,proto3" json:"vpc_id,omitempty"`
	ChooseNum uint32 `protobuf:"varint,4,opt,name=choose_num,json=chooseNum,proto3" json:"choose_num,omitempty"`
}

func (x *InstanceLoadBalanceInfo) Reset() {
	*x = InstanceLoadBalanceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_sdkloadbalance_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstanceLoadBalanceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstanceLoadBalanceInfo) ProtoMessage() {}

func (x *InstanceLoadBalanceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_sdkloadbalance_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstanceLoadBalanceInfo.ProtoReflect.Descriptor instead.
func (*InstanceLoadBalanceInfo) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_sdkloadbalance_proto_rawDescGZIP(), []int{1}
}

func (x *InstanceLoadBalanceInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *InstanceLoadBalanceInfo) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *InstanceLoadBalanceInfo) GetVpcId() string {
	if x != nil {
		return x.VpcId
	}
	return ""
}

func (x *InstanceLoadBalanceInfo) GetChooseNum() uint32 {
	if x != nil {
		return x.ChooseNum
	}
	return 0
}

var File_monitor_polaris_v1_sdkloadbalance_proto protoreflect.FileDescriptor

var file_monitor_polaris_v1_sdkloadbalance_proto_rawDesc = []byte{
	0x0a, 0x27, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x64, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x62, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x76, 0x31, 0x1a, 0x21, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x64, 0x6b, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x22, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc8, 0x02, 0x0a, 0x16, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x22, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x44, 0x4b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6c,
	0x6f, 0x61, 0x64, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x6c, 0x6f, 0x61, 0x64, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x12,
	0x28, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x40, 0x0a, 0x0d, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4c, 0x6f,
	0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x09, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22,
	0x73, 0x0a, 0x17, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4c, 0x6f, 0x61, 0x64, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x15,
	0x0a, 0x06, 0x76, 0x70, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x70, 0x63, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x63, 0x68, 0x6f, 0x6f, 0x73,
	0x65, 0x4e, 0x75, 0x6d, 0x42, 0x3f, 0x5a, 0x3d, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61,
	0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_monitor_polaris_v1_sdkloadbalance_proto_rawDescOnce sync.Once
	file_monitor_polaris_v1_sdkloadbalance_proto_rawDescData = file_monitor_polaris_v1_sdkloadbalance_proto_rawDesc
)

func file_monitor_polaris_v1_sdkloadbalance_proto_rawDescGZIP() []byte {
	file_monitor_polaris_v1_sdkloadbalance_proto_rawDescOnce.Do(func() {
		file_monitor_polaris_v1_sdkloadbalance_proto_rawDescData = protoimpl.X.CompressGZIP(file_monitor_polaris_v1_sdkloadbalance_proto_rawDescData)
	})
	return file_monitor_polaris_v1_sdkloadbalance_proto_rawDescData
}

var file_monitor_polaris_v1_sdkloadbalance_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_monitor_polaris_v1_sdkloadbalance_proto_goTypes = []interface{}{
	(*ServiceLoadBalanceInfo)(nil),  // 0: v1.ServiceLoadBalanceInfo
	(*InstanceLoadBalanceInfo)(nil), // 1: v1.InstanceLoadBalanceInfo
	(*SDKToken)(nil),                // 2: v1.SDKToken
	(*MonitorSignature)(nil),        // 3: v1.MonitorSignature
}
var file_monitor_polaris_v1_sdkloadbalance_proto_depIdxs = []int32{
	2, // 0: v1.ServiceLoadBalanceInfo.token:type_name -> v1.SDKToken
	1, // 1: v1.ServiceLoadBalanceInfo.instance_info:type_name -> v1.InstanceLoadBalanceInfo
	3, // 2: v1.ServiceLoadBalanceInfo.signature:type_name -> v1.MonitorSignature
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_monitor_polaris_v1_sdkloadbalance_proto_init() }
func file_monitor_polaris_v1_sdkloadbalance_proto_init() {
	if File_monitor_polaris_v1_sdkloadbalance_proto != nil {
		return
	}
	file_monitor_polaris_v1_sdktoken_proto_init()
	file_monitor_polaris_v1_signature_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_monitor_polaris_v1_sdkloadbalance_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceLoadBalanceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_sdkloadbalance_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstanceLoadBalanceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_monitor_polaris_v1_sdkloadbalance_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_monitor_polaris_v1_sdkloadbalance_proto_goTypes,
		DependencyIndexes: file_monitor_polaris_v1_sdkloadbalance_proto_depIdxs,
		MessageInfos:      file_monitor_polaris_v1_sdkloadbalance_proto_msgTypes,
	}.Build()
	File_monitor_polaris_v1_sdkloadbalance_proto = out.File
	file_monitor_polaris_v1_sdkloadbalance_proto_rawDesc = nil
	file_monitor_polaris_v1_sdkloadbalance_proto_goTypes = nil
	file_monitor_polaris_v1_sdkloadbalance_proto_depIdxs = nil
}
