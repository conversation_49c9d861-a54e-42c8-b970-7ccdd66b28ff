package trpcheadtool

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"git.code.oa.com/trpc-go/trpc-codec/qzh"
	"git.code.oa.com/trpc-go/trpc-codec/qzh/qza"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/QzonePlatform/qzone-backend/common/iptool"
)

type InitQzoneContextFromQzaOption struct {
	Qua string
}

type InitQzoneContextFromQzaOptionHanlder func(o *InitQzoneContextFromQzaOption)

func WithQua(qua string) InitQzoneContextFromQzaOptionHanlder {
	return func(o *InitQzoneContextFromQzaOption) {
		o.Qua = qua
	}
}

func InitQzoneContextWithQza(ctx context.Context, qzaHead *qza.HEAD, handlers ...InitQzoneContextFromQzaOptionHanlder) error {
	option := &InitQzoneContextFromQzaOption{}
	for _, h := range handlers {
		h(option)
	}

	msg := trpc.Message(ctx)
	md := msg.ServerMetaData()
	if md == nil {
		md = make(map[string][]byte)
	}
	ip, clientUin := qzaHead.GetClient()
	clientIP, _ := iptool.Uint2IPString(ip)
	md["is-qzone-req"] = []byte("1") // 标识空间过来的请求
	md["qzone-user-ip"] = []byte(clientIP)
	md["qzone-seq"] = []byte(fmt.Sprintf("%d", qzaHead.GetPackFlow()))
	md["qzone-auth-type"] = []byte(fmt.Sprintf("%d", qzaHead.GetAuthType())) // 空间A2 key
	md["qzone-uin"] = []byte(fmt.Sprintf("%d", clientUin))
	md["qzone-tracer-id"] = []byte(fmt.Sprintf("%d_%d_%d", clientUin, time.Now().Unix(), rand.Int() % 10000))
	md["qzone-qua"] = []byte(option.Qua)
	md["app-authinfo_uin"] = []byte(fmt.Sprintf("%d", clientUin))

	if qzaHead.GetAuthType() == qzh.EnumAuthTypeA2 {
		md["qzone-sig"] = []byte(qzaHead.GetA2Key())
	} else if qzaHead.GetAuthType() == qzh.EnumAuthTypeWeb {
		md["qzone-sig"] = []byte(qzaHead.GetSKey())
	} else {
		md["qzone-sig"] = []byte(qzaHead.GetExternalKey())
	}
	md["qzone-appid"] = []byte(fmt.Sprintf("%d", qzaHead.GetPtloginID()))
	msg.WithServerMetaData(md)
	log.WithContextFields(ctx, "traceid", GetTraceID(ctx),
		"serverip", trpc.GetIP("eth1"), "clientip", GetUserIP(ctx),
		"uin", GetLoginUINStr(ctx), "cmd", msg.ServerRPCName())

	log.DebugContextf(ctx, "A2Key: %x, skey: %x, exkey: %x", []byte(qzaHead.GetA2Key()), []byte(qzaHead.GetSKey()), []byte(qzaHead.GetExternalKey()))

	return nil
}

// InitQzoneContextFromQza ...
func InitQzoneContextFromQza(ctx context.Context, handlers ...InitQzoneContextFromQzaOptionHanlder) (err error) {
	// qza转trpc头
	qzaHead := qza.Head(ctx)
	
	return InitQzoneContextWithQza(ctx, qzaHead, handlers...)
}