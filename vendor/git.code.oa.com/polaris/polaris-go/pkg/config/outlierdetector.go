/**
 * Tencent is pleased to support the open source community by making CL5 available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */

package config

import (
	"errors"
	"fmt"
	"git.code.oa.com/polaris/polaris-go/pkg/model"
	"git.code.oa.com/polaris/polaris-go/pkg/plugin/common"
	"time"
)

//OutlierDetectionConfig实现类
type OutlierDetectionConfigImpl struct {
	//是否启动熔断
	Enable *bool `yaml:"enable,omitempty" json:"enable,omitempty"`
	// deprecated 定时探测周期，这是0.10.0版本之前的配置，现在只做兼容
	CheckPeriod *time.Duration `yaml:"checkPeriod,omitempty" json:"checkPeriod,omitempty"`
	// 定时探测周期，0.10.0版本之后，使用该配置
	Interval *time.Duration `yaml:"interval" json:"interval"`
	//熔断插件链
	Chain []string `yaml:"chain" json:"chain"`
	// 插件相关配置
	Plugin PluginConfigs `yaml:"plugin" json:"plugin"`
	//主动健康检测策略
	When *When `yaml:"when" json:"when"`
	//主动健康检测的并发数
	Concurrency *int `yaml:"concurrency" json:"concurrency"`
}

//是否启用熔断
func (o *OutlierDetectionConfigImpl) IsEnable() bool {
	return *o.When != HealthCheckNever
}

//设置是否启用探测，用于兼容旧版sdk代码, deprecated
//SetEnable(false)相当于SetWhen(HealthCheckNever)
//SetEnable(true)相当于SetWhen(HealthCheckOnRecover)
func (o *OutlierDetectionConfigImpl) SetEnable(enable bool) {
	o.When = new(When)
	o.Enable = &enable
	if enable {
		// 如果启动的话，策略为HealthCheckOnRecover，按照旧版本sdk策略，启动探测协程为1
		*o.When = HealthCheckOnRecover
		o.Concurrency = new(int)
		*o.Concurrency = DefaultOutlierDetectConcurrency
	} else {
		*o.When = HealthCheckNever
	}
}

//熔断器插件链
func (o *OutlierDetectionConfigImpl) GetChain() []string {
	return o.Chain
}

//设置熔断器插件链
func (o *OutlierDetectionConfigImpl) SetChain(chain []string) {
	o.Chain = chain
}

//健康探测定时检测时间, deprecated
func (o *OutlierDetectionConfigImpl) GetCheckPeriod() time.Duration {
	return *o.Interval
}

//设置健康探测定时检测时间, deprecated
func (o *OutlierDetectionConfigImpl) SetCheckPeriod(period time.Duration) {
	o.Interval = &period
}

//健康探测定时检测时间
func (o *OutlierDetectionConfigImpl) GetInterval() time.Duration {
	return *o.Interval
}

//设置健康探测定时检测时间
func (o *OutlierDetectionConfigImpl) SetInterval(period time.Duration) {
	o.Interval = &period
}

// GetConcurrency 返回健康检查的并发协程数量
func (o *OutlierDetectionConfigImpl) GetConcurrency() int {
	return *o.Concurrency
}

// SetConcurrency 设置健康检查的并发协程数量
func (o *OutlierDetectionConfigImpl) SetConcurrency(value int) {
	o.Concurrency = &value
}

// GetWhen 获取健康检测的策略
func (o *OutlierDetectionConfigImpl) GetWhen() When {
	return *o.When
}

// SetWhen 设置健康检测的策略
func (o *OutlierDetectionConfigImpl) SetWhen(policy When) {
	o.When = &policy
}

//获取插件配置
func (o *OutlierDetectionConfigImpl) GetPluginConfig(pluginName string) BaseConfig {
	cfgValue, ok := o.Plugin[pluginName]
	if !ok {
		return nil
	}
	return cfgValue.(BaseConfig)
}

//设置单独插件配置
func (o *OutlierDetectionConfigImpl) SetPluginConfig(pluginName string, value BaseConfig) error {
	return o.Plugin.SetPluginConfig(common.TypeOutlierDetector, pluginName, value)
}

//检验outlierDetectionConfig配置
func (o *OutlierDetectionConfigImpl) Verify() error {
	if nil == o {
		return errors.New("HealthCheckConfig is nil")
	}
	if nil != o.Interval && *o.Interval < MinOutlierDetectPeriod {
		return fmt.Errorf("consumer.healthCheck.interval should greater than %v",
			MinOutlierDetectPeriod)
	}
	if nil == o.When {
		return fmt.Errorf("consumer.healthCheck.when must not be nil")
	}
	if *o.When != HealthCheckOnRecover && *o.When != HealthCheckAlways && *o.When != HealthCheckNever {
		return fmt.Errorf("consumer.healthCheck.when must be %s, %s or %s",
			HealthCheckOnRecover, HealthCheckAlways, HealthCheckNever)
	}
	if *o.When != HealthCheckNever && len(o.Chain) == 0 {
		return fmt.Errorf("consumer.healthCheck.chain can not be empty when enabled")
	}
	if o.Concurrency == nil {
		return fmt.Errorf("consumer.healthCheck.concurrency must not be nil")
	}
	if *o.When != HealthCheckNever && *o.Concurrency <= 0 {
		return fmt.Errorf("consumer.healthCheck.concurrency must greater than 0 when enable healthCheck")
	}
	return o.Plugin.Verify()
}

//配置初始化
func (o *OutlierDetectionConfigImpl) Init() {
	o.Plugin = PluginConfigs{}
	o.Plugin.Init(common.TypeOutlierDetector)
}

//设置outlierDetectionConfig的默认值
func (o *OutlierDetectionConfigImpl) SetDefault() {
	if nil == o.Interval {
		// 如果设置了 checkPeriod，那么将 checkPeriod 的值给 interval
		if nil != o.CheckPeriod {
			o.Interval = o.CheckPeriod
			o.CheckPeriod = nil
		} else {
			//否则直接使用默认值
			o.Interval = model.ToDurationPtr(DefaultOutlierDetectPeriod)
		}
	}
	// 默认策略是只有在实例不可用的时候才进行检测
	if o.When == nil {
		o.When = new(When)
		// 如果enable为 nil 或者为 false 的话，不启动探测
		if o.Enable == nil || *o.Enable == false {
			*o.When = HealthCheckNever
		} else {
			*o.When = HealthCheckOnRecover
		}
	}
	// 根据探测策略进行并发数的设置
	if o.Concurrency == nil {
		o.Concurrency = new(int)
		switch *o.When {
		case HealthCheckNever:
			*o.Concurrency = 0
		case HealthCheckAlways:
			*o.Concurrency = DefaultOutlierDetectConcurrencyAlways
		case HealthCheckOnRecover:
			*o.Concurrency = DefaultOutlierDetectConcurrency
		}
	}
	o.Plugin.SetDefault(common.TypeOutlierDetector)
}

//获取该域下所有插件的名字
func (o *OutlierDetectionConfigImpl) GetPluginNames() model.HashSet {
	nameMap := model.HashSet{}
	for _, name := range o.Chain {
		nameMap.Add(name)
	}
	return nameMap
}
