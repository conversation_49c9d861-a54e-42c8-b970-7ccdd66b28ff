// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v1/model/fluxserverratelimit.proto

package polarismodel_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 同一服务下限流规则集合
type FluxServerRateLimitRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BzSet *BzSet `protobuf:"bytes,1,opt,name=BzSet,proto3" json:"BzSet,omitempty"`
	// setquota
	IsEnable       *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=IsEnable,proto3" json:"IsEnable,omitempty"`
	SetQuota       *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=SetQuota,proto3" json:"SetQuota,omitempty"`
	MinQuota       *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=MinQuota,proto3" json:"MinQuota,omitempty"`
	MinQuotaEnable *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=MinQuotaEnable,proto3" json:"MinQuotaEnable,omitempty"`
	WarningQuota   *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=WarningQuota,proto3" json:"WarningQuota,omitempty"`
	// keyquota集合
	KeyQuota []*FluxServerKeyQuota `protobuf:"bytes,7,rep,name=KeyQuota,proto3" json:"KeyQuota,omitempty"`
}

func (x *FluxServerRateLimitRule) Reset() {
	*x = FluxServerRateLimitRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FluxServerRateLimitRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FluxServerRateLimitRule) ProtoMessage() {}

func (x *FluxServerRateLimitRule) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FluxServerRateLimitRule.ProtoReflect.Descriptor instead.
func (*FluxServerRateLimitRule) Descriptor() ([]byte, []int) {
	return file_v1_model_fluxserverratelimit_proto_rawDescGZIP(), []int{0}
}

func (x *FluxServerRateLimitRule) GetBzSet() *BzSet {
	if x != nil {
		return x.BzSet
	}
	return nil
}

func (x *FluxServerRateLimitRule) GetIsEnable() *wrapperspb.StringValue {
	if x != nil {
		return x.IsEnable
	}
	return nil
}

func (x *FluxServerRateLimitRule) GetSetQuota() *wrapperspb.StringValue {
	if x != nil {
		return x.SetQuota
	}
	return nil
}

func (x *FluxServerRateLimitRule) GetMinQuota() *wrapperspb.StringValue {
	if x != nil {
		return x.MinQuota
	}
	return nil
}

func (x *FluxServerRateLimitRule) GetMinQuotaEnable() *wrapperspb.StringValue {
	if x != nil {
		return x.MinQuotaEnable
	}
	return nil
}

func (x *FluxServerRateLimitRule) GetWarningQuota() *wrapperspb.StringValue {
	if x != nil {
		return x.WarningQuota
	}
	return nil
}

func (x *FluxServerRateLimitRule) GetKeyQuota() []*FluxServerKeyQuota {
	if x != nil {
		return x.KeyQuota
	}
	return nil
}

// 单个key限流规则信息
type FluxServerKeyQuota struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 限流规则唯一标识
	Key *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
	// 限流规则所属业务
	Business *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=Business,proto3" json:"Business,omitempty"`
	// 限流规则配额
	Quota *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=Quota,proto3" json:"Quota,omitempty"`
	// 限流规则开始时间
	StartTime *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=StartTime,proto3" json:"StartTime,omitempty"`
	// 限流规则结束时间
	EndTime *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=EndTime,proto3" json:"EndTime,omitempty"`
	// 限流规则告警配额
	WarningQuota *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=WarningQuota,proto3" json:"WarningQuota,omitempty"`
	// 限流规则生效开关
	IsEnable *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=IsEnable,proto3" json:"IsEnable,omitempty"`
}

func (x *FluxServerKeyQuota) Reset() {
	*x = FluxServerKeyQuota{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FluxServerKeyQuota) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FluxServerKeyQuota) ProtoMessage() {}

func (x *FluxServerKeyQuota) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FluxServerKeyQuota.ProtoReflect.Descriptor instead.
func (*FluxServerKeyQuota) Descriptor() ([]byte, []int) {
	return file_v1_model_fluxserverratelimit_proto_rawDescGZIP(), []int{1}
}

func (x *FluxServerKeyQuota) GetKey() *wrapperspb.StringValue {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *FluxServerKeyQuota) GetBusiness() *wrapperspb.StringValue {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *FluxServerKeyQuota) GetQuota() *wrapperspb.StringValue {
	if x != nil {
		return x.Quota
	}
	return nil
}

func (x *FluxServerKeyQuota) GetStartTime() *wrapperspb.StringValue {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *FluxServerKeyQuota) GetEndTime() *wrapperspb.StringValue {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *FluxServerKeyQuota) GetWarningQuota() *wrapperspb.StringValue {
	if x != nil {
		return x.WarningQuota
	}
	return nil
}

func (x *FluxServerKeyQuota) GetIsEnable() *wrapperspb.StringValue {
	if x != nil {
		return x.IsEnable
	}
	return nil
}

type BzSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 限流规则所属命名空间
	Env *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=Env,proto3" json:"Env,omitempty"`
	// 限流规则所属服务名
	ServerName *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=ServerName,proto3" json:"ServerName,omitempty"`
}

func (x *BzSet) Reset() {
	*x = BzSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BzSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BzSet) ProtoMessage() {}

func (x *BzSet) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BzSet.ProtoReflect.Descriptor instead.
func (*BzSet) Descriptor() ([]byte, []int) {
	return file_v1_model_fluxserverratelimit_proto_rawDescGZIP(), []int{2}
}

func (x *BzSet) GetEnv() *wrapperspb.StringValue {
	if x != nil {
		return x.Env
	}
	return nil
}

func (x *BzSet) GetServerName() *wrapperspb.StringValue {
	if x != nil {
		return x.ServerName
	}
	return nil
}

//sdk
type FluxSDKRateLimitRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FluxSet    *FluxSet    `protobuf:"bytes,1,opt,name=fluxSet,proto3" json:"fluxSet,omitempty"`
	MonitorSet *MonitorSet `protobuf:"bytes,2,opt,name=monitorSet,proto3" json:"monitorSet,omitempty"`
	// setquota
	SetQuota       *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=setQuota,proto3" json:"setQuota,omitempty"`
	MinQuota       *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=minQuota,proto3" json:"minQuota,omitempty"`
	MinQuotaEnable *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=minQuotaEnable,proto3" json:"minQuotaEnable,omitempty"`
	// keyquota集合
	KeyQuota []*FluxSDKKeyQuota `protobuf:"bytes,6,rep,name=keyQuota,proto3" json:"keyQuota,omitempty"`
}

func (x *FluxSDKRateLimitRule) Reset() {
	*x = FluxSDKRateLimitRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FluxSDKRateLimitRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FluxSDKRateLimitRule) ProtoMessage() {}

func (x *FluxSDKRateLimitRule) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FluxSDKRateLimitRule.ProtoReflect.Descriptor instead.
func (*FluxSDKRateLimitRule) Descriptor() ([]byte, []int) {
	return file_v1_model_fluxserverratelimit_proto_rawDescGZIP(), []int{3}
}

func (x *FluxSDKRateLimitRule) GetFluxSet() *FluxSet {
	if x != nil {
		return x.FluxSet
	}
	return nil
}

func (x *FluxSDKRateLimitRule) GetMonitorSet() *MonitorSet {
	if x != nil {
		return x.MonitorSet
	}
	return nil
}

func (x *FluxSDKRateLimitRule) GetSetQuota() *wrapperspb.StringValue {
	if x != nil {
		return x.SetQuota
	}
	return nil
}

func (x *FluxSDKRateLimitRule) GetMinQuota() *wrapperspb.StringValue {
	if x != nil {
		return x.MinQuota
	}
	return nil
}

func (x *FluxSDKRateLimitRule) GetMinQuotaEnable() *wrapperspb.StringValue {
	if x != nil {
		return x.MinQuotaEnable
	}
	return nil
}

func (x *FluxSDKRateLimitRule) GetKeyQuota() []*FluxSDKKeyQuota {
	if x != nil {
		return x.KeyQuota
	}
	return nil
}

// 单个key限流规则信息
type FluxSDKKeyQuota struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 限流规则唯一标识
	Key *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// 限流规则所属业务
	Business *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=business,proto3" json:"business,omitempty"`
	// 限流规则配额
	Quota *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=quota,proto3" json:"quota,omitempty"`
}

func (x *FluxSDKKeyQuota) Reset() {
	*x = FluxSDKKeyQuota{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FluxSDKKeyQuota) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FluxSDKKeyQuota) ProtoMessage() {}

func (x *FluxSDKKeyQuota) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FluxSDKKeyQuota.ProtoReflect.Descriptor instead.
func (*FluxSDKKeyQuota) Descriptor() ([]byte, []int) {
	return file_v1_model_fluxserverratelimit_proto_rawDescGZIP(), []int{4}
}

func (x *FluxSDKKeyQuota) GetKey() *wrapperspb.StringValue {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *FluxSDKKeyQuota) GetBusiness() *wrapperspb.StringValue {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *FluxSDKKeyQuota) GetQuota() *wrapperspb.StringValue {
	if x != nil {
		return x.Quota
	}
	return nil
}

type FluxSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 限流规则所属服务名
	ServerName *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=serverName,proto3" json:"serverName,omitempty"`
	// 限流规则所属命名空间
	Env *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=env,proto3" json:"env,omitempty"`
}

func (x *FluxSet) Reset() {
	*x = FluxSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FluxSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FluxSet) ProtoMessage() {}

func (x *FluxSet) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FluxSet.ProtoReflect.Descriptor instead.
func (*FluxSet) Descriptor() ([]byte, []int) {
	return file_v1_model_fluxserverratelimit_proto_rawDescGZIP(), []int{5}
}

func (x *FluxSet) GetServerName() *wrapperspb.StringValue {
	if x != nil {
		return x.ServerName
	}
	return nil
}

func (x *FluxSet) GetEnv() *wrapperspb.StringValue {
	if x != nil {
		return x.Env
	}
	return nil
}

type MonitorSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 限流规则所属服务名
	ServerName *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=serverName,proto3" json:"serverName,omitempty"`
	// 限流规则所属命名空间
	Env *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=env,proto3" json:"env,omitempty"`
}

func (x *MonitorSet) Reset() {
	*x = MonitorSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonitorSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorSet) ProtoMessage() {}

func (x *MonitorSet) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorSet.ProtoReflect.Descriptor instead.
func (*MonitorSet) Descriptor() ([]byte, []int) {
	return file_v1_model_fluxserverratelimit_proto_rawDescGZIP(), []int{6}
}

func (x *MonitorSet) GetServerName() *wrapperspb.StringValue {
	if x != nil {
		return x.ServerName
	}
	return nil
}

func (x *MonitorSet) GetEnv() *wrapperspb.StringValue {
	if x != nil {
		return x.Env
	}
	return nil
}

// flux控制台限流规则
type FluxConsoleRateLimitRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 规则id
	Id *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 规则版本
	Revision *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=revision,proto3" json:"revision,omitempty"`
	// 规则名
	Name *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 规则描述
	Description *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// 被调服务名称
	CalleeServiceName *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=calleeServiceName,proto3" json:"calleeServiceName,omitempty"`
	// 被调服务环境
	CalleeServiceEnv *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=calleeServiceEnv,proto3" json:"calleeServiceEnv,omitempty"`
	// 被调服务id
	CalleeServiceId *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=calleeServiceId,proto3" json:"calleeServiceId,omitempty"`
	// 主调服务业务
	CallerServiceBusiness *wrapperspb.StringValue `protobuf:"bytes,8,opt,name=callerServiceBusiness,proto3" json:"callerServiceBusiness,omitempty"`
	// set限流key
	SetKey *wrapperspb.StringValue `protobuf:"bytes,9,opt,name=setKey,proto3" json:"setKey,omitempty"`
	// set告警qps
	SetAlertQps *wrapperspb.StringValue `protobuf:"bytes,10,opt,name=setAlertQps,proto3" json:"setAlertQps,omitempty"`
	// set预警qps
	SetWarningQps *wrapperspb.StringValue `protobuf:"bytes,11,opt,name=setWarningQps,proto3" json:"setWarningQps,omitempty"`
	// set限流备注
	SetRemark *wrapperspb.StringValue `protobuf:"bytes,12,opt,name=setRemark,proto3" json:"setRemark,omitempty"`
	// 兜底限流key
	DefaultKey *wrapperspb.StringValue `protobuf:"bytes,13,opt,name=defaultKey,proto3" json:"defaultKey,omitempty"`
	// 兜底告警qps
	DefaultAlertQps *wrapperspb.StringValue `protobuf:"bytes,14,opt,name=defaultAlertQps,proto3" json:"defaultAlertQps,omitempty"`
	// 兜底预警qps
	DefaultWarningQps *wrapperspb.StringValue `protobuf:"bytes,15,opt,name=defaultWarningQps,proto3" json:"defaultWarningQps,omitempty"`
	// 兜底限流备注
	DefaultRemark *wrapperspb.StringValue `protobuf:"bytes,16,opt,name=defaultRemark,proto3" json:"defaultRemark,omitempty"`
	// 创建人
	Creator *wrapperspb.StringValue `protobuf:"bytes,17,opt,name=creator,proto3" json:"creator,omitempty"`
	// 更新人
	Updater *wrapperspb.StringValue `protobuf:"bytes,18,opt,name=updater,proto3" json:"updater,omitempty"`
	// 状态 0-未生效 1-已生效
	Status *wrapperspb.UInt32Value `protobuf:"bytes,19,opt,name=status,proto3" json:"status,omitempty"`
	// 规则创建时间
	Ctime *wrapperspb.StringValue `protobuf:"bytes,20,opt,name=ctime,proto3" json:"ctime,omitempty"`
	// 规则更新时间
	Mtime *wrapperspb.StringValue `protobuf:"bytes,21,opt,name=mtime,proto3" json:"mtime,omitempty"`
	// 服务的TOKEN信息，仅用于控制台
	ServiceToken *wrapperspb.StringValue `protobuf:"bytes,22,opt,name=service_token,proto3" json:"service_token,omitempty"`
	// 规则类型
	Type *wrapperspb.UInt32Value `protobuf:"bytes,23,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *FluxConsoleRateLimitRule) Reset() {
	*x = FluxConsoleRateLimitRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FluxConsoleRateLimitRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FluxConsoleRateLimitRule) ProtoMessage() {}

func (x *FluxConsoleRateLimitRule) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_fluxserverratelimit_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FluxConsoleRateLimitRule.ProtoReflect.Descriptor instead.
func (*FluxConsoleRateLimitRule) Descriptor() ([]byte, []int) {
	return file_v1_model_fluxserverratelimit_proto_rawDescGZIP(), []int{7}
}

func (x *FluxConsoleRateLimitRule) GetId() *wrapperspb.StringValue {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetRevision() *wrapperspb.StringValue {
	if x != nil {
		return x.Revision
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetName() *wrapperspb.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetDescription() *wrapperspb.StringValue {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetCalleeServiceName() *wrapperspb.StringValue {
	if x != nil {
		return x.CalleeServiceName
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetCalleeServiceEnv() *wrapperspb.StringValue {
	if x != nil {
		return x.CalleeServiceEnv
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetCalleeServiceId() *wrapperspb.StringValue {
	if x != nil {
		return x.CalleeServiceId
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetCallerServiceBusiness() *wrapperspb.StringValue {
	if x != nil {
		return x.CallerServiceBusiness
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetSetKey() *wrapperspb.StringValue {
	if x != nil {
		return x.SetKey
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetSetAlertQps() *wrapperspb.StringValue {
	if x != nil {
		return x.SetAlertQps
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetSetWarningQps() *wrapperspb.StringValue {
	if x != nil {
		return x.SetWarningQps
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetSetRemark() *wrapperspb.StringValue {
	if x != nil {
		return x.SetRemark
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetDefaultKey() *wrapperspb.StringValue {
	if x != nil {
		return x.DefaultKey
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetDefaultAlertQps() *wrapperspb.StringValue {
	if x != nil {
		return x.DefaultAlertQps
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetDefaultWarningQps() *wrapperspb.StringValue {
	if x != nil {
		return x.DefaultWarningQps
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetDefaultRemark() *wrapperspb.StringValue {
	if x != nil {
		return x.DefaultRemark
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetCreator() *wrapperspb.StringValue {
	if x != nil {
		return x.Creator
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetUpdater() *wrapperspb.StringValue {
	if x != nil {
		return x.Updater
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetStatus() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetCtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Ctime
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetMtime() *wrapperspb.StringValue {
	if x != nil {
		return x.Mtime
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetServiceToken() *wrapperspb.StringValue {
	if x != nil {
		return x.ServiceToken
	}
	return nil
}

func (x *FluxConsoleRateLimitRule) GetType() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Type
	}
	return nil
}

var File_v1_model_fluxserverratelimit_proto protoreflect.FileDescriptor

var file_v1_model_fluxserverratelimit_proto_rawDesc = []byte{
	0x0a, 0x22, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x66, 0x6c, 0x75, 0x78, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x72, 0x61, 0x74, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xb8, 0x03, 0x0a, 0x17, 0x46, 0x6c, 0x75, 0x78, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x29,
	0x0a, 0x05, 0x42, 0x7a, 0x53, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x42, 0x7a, 0x53,
	0x65, 0x74, 0x52, 0x05, 0x42, 0x7a, 0x53, 0x65, 0x74, 0x12, 0x38, 0x0a, 0x08, 0x49, 0x73, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x49, 0x73, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x53, 0x65, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x08, 0x53, 0x65, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x12, 0x38, 0x0a,
	0x08, 0x4d, 0x69, 0x6e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x4d,
	0x69, 0x6e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x12, 0x44, 0x0a, 0x0e, 0x4d, 0x69, 0x6e, 0x51, 0x75,
	0x6f, 0x74, 0x61, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x4d,
	0x69, 0x6e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x40, 0x0a,
	0x0c, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0c, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x12,
	0x3c, 0x0a, 0x08, 0x4b, 0x65, 0x79, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x46, 0x6c, 0x75, 0x78, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4b, 0x65, 0x79, 0x51, 0x75,
	0x6f, 0x74, 0x61, 0x52, 0x08, 0x4b, 0x65, 0x79, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x22, 0xa2, 0x03,
	0x0a, 0x12, 0x46, 0x6c, 0x75, 0x78, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4b, 0x65, 0x79, 0x51,
	0x75, 0x6f, 0x74, 0x61, 0x12, 0x2e, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x03, 0x4b, 0x65, 0x79, 0x12, 0x38, 0x0a, 0x08, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x32,
	0x0a, 0x05, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x51, 0x75, 0x6f,
	0x74, 0x61, 0x12, 0x3a, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x36,
	0x0a, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x45,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0c, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e,
	0x67, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x57, 0x61, 0x72, 0x6e,
	0x69, 0x6e, 0x67, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x12, 0x38, 0x0a, 0x08, 0x49, 0x73, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x49, 0x73, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x22, 0x75, 0x0a, 0x05, 0x42, 0x7a, 0x53, 0x65, 0x74, 0x12, 0x2e, 0x0a, 0x03, 0x45,
	0x6e, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x03, 0x45, 0x6e, 0x76, 0x12, 0x3c, 0x0a, 0x0a, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xf6, 0x02, 0x0a, 0x14, 0x46, 0x6c,
	0x75, 0x78, 0x53, 0x44, 0x4b, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75,
	0x6c, 0x65, 0x12, 0x2f, 0x0a, 0x07, 0x66, 0x6c, 0x75, 0x78, 0x53, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x46, 0x6c, 0x75, 0x78, 0x53, 0x65, 0x74, 0x52, 0x07, 0x66, 0x6c, 0x75, 0x78,
	0x53, 0x65, 0x74, 0x12, 0x38, 0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x65,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x65,
	0x74, 0x52, 0x0a, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x65, 0x74, 0x12, 0x38, 0x0a,
	0x08, 0x73, 0x65, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x73,
	0x65, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x12, 0x38, 0x0a, 0x08, 0x6d, 0x69, 0x6e, 0x51, 0x75,
	0x6f, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x51, 0x75, 0x6f, 0x74,
	0x61, 0x12, 0x44, 0x0a, 0x0e, 0x6d, 0x69, 0x6e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x6d, 0x69, 0x6e, 0x51, 0x75, 0x6f, 0x74,
	0x61, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x51, 0x75,
	0x6f, 0x74, 0x61, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x46, 0x6c, 0x75, 0x78, 0x53, 0x44, 0x4b,
	0x4b, 0x65, 0x79, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x51, 0x75, 0x6f,
	0x74, 0x61, 0x22, 0xaf, 0x01, 0x0a, 0x0f, 0x46, 0x6c, 0x75, 0x78, 0x53, 0x44, 0x4b, 0x4b, 0x65,
	0x79, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x12, 0x2e, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x38, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x12, 0x32, 0x0a, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x71,
	0x75, 0x6f, 0x74, 0x61, 0x22, 0x77, 0x0a, 0x07, 0x46, 0x6c, 0x75, 0x78, 0x53, 0x65, 0x74, 0x12,
	0x3c, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a,
	0x03, 0x65, 0x6e, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x22, 0x7a, 0x0a,
	0x0a, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x65, 0x74, 0x12, 0x3c, 0x0a, 0x0a, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x03, 0x65, 0x6e, 0x76,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x22, 0xb6, 0x0b, 0x0a, 0x18, 0x46, 0x6c,
	0x75, 0x78, 0x43, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x30,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x3e, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x4a, 0x0a, 0x11, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x11, 0x63, 0x61, 0x6c, 0x6c, 0x65,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x10,
	0x63, 0x61, 0x6c, 0x6c, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x76,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x45, 0x6e, 0x76, 0x12, 0x46, 0x0a, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x63,
	0x61, 0x6c, 0x6c, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x52,
	0x0a, 0x15, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x15, 0x63, 0x61, 0x6c,
	0x6c, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x12, 0x34, 0x0a, 0x06, 0x73, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x06, 0x73, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x3e, 0x0a, 0x0b, 0x73, 0x65, 0x74, 0x41,
	0x6c, 0x65, 0x72, 0x74, 0x51, 0x70, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x74,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x51, 0x70, 0x73, 0x12, 0x42, 0x0a, 0x0d, 0x73, 0x65, 0x74, 0x57,
	0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x51, 0x70, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x73,
	0x65, 0x74, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x51, 0x70, 0x73, 0x12, 0x3a, 0x0a, 0x09,
	0x73, 0x65, 0x74, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x73,
	0x65, 0x74, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x3c, 0x0a, 0x0a, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x4b, 0x65, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x46, 0x0a, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x51, 0x70, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x51, 0x70, 0x73, 0x12, 0x4a,
	0x0a, 0x11, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67,
	0x51, 0x70, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x11, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x51, 0x70, 0x73, 0x12, 0x42, 0x0a, 0x0d, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x36,
	0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x36, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x34,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x0d,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x3b, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_v1_model_fluxserverratelimit_proto_rawDescOnce sync.Once
	file_v1_model_fluxserverratelimit_proto_rawDescData = file_v1_model_fluxserverratelimit_proto_rawDesc
)

func file_v1_model_fluxserverratelimit_proto_rawDescGZIP() []byte {
	file_v1_model_fluxserverratelimit_proto_rawDescOnce.Do(func() {
		file_v1_model_fluxserverratelimit_proto_rawDescData = protoimpl.X.CompressGZIP(file_v1_model_fluxserverratelimit_proto_rawDescData)
	})
	return file_v1_model_fluxserverratelimit_proto_rawDescData
}

var file_v1_model_fluxserverratelimit_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_v1_model_fluxserverratelimit_proto_goTypes = []interface{}{
	(*FluxServerRateLimitRule)(nil),  // 0: api.v1.model.FluxServerRateLimitRule
	(*FluxServerKeyQuota)(nil),       // 1: api.v1.model.FluxServerKeyQuota
	(*BzSet)(nil),                    // 2: api.v1.model.BzSet
	(*FluxSDKRateLimitRule)(nil),     // 3: api.v1.model.FluxSDKRateLimitRule
	(*FluxSDKKeyQuota)(nil),          // 4: api.v1.model.FluxSDKKeyQuota
	(*FluxSet)(nil),                  // 5: api.v1.model.FluxSet
	(*MonitorSet)(nil),               // 6: api.v1.model.MonitorSet
	(*FluxConsoleRateLimitRule)(nil), // 7: api.v1.model.FluxConsoleRateLimitRule
	(*wrapperspb.StringValue)(nil),   // 8: google.protobuf.StringValue
	(*wrapperspb.UInt32Value)(nil),   // 9: google.protobuf.UInt32Value
}
var file_v1_model_fluxserverratelimit_proto_depIdxs = []int32{
	2,  // 0: api.v1.model.FluxServerRateLimitRule.BzSet:type_name -> api.v1.model.BzSet
	8,  // 1: api.v1.model.FluxServerRateLimitRule.IsEnable:type_name -> google.protobuf.StringValue
	8,  // 2: api.v1.model.FluxServerRateLimitRule.SetQuota:type_name -> google.protobuf.StringValue
	8,  // 3: api.v1.model.FluxServerRateLimitRule.MinQuota:type_name -> google.protobuf.StringValue
	8,  // 4: api.v1.model.FluxServerRateLimitRule.MinQuotaEnable:type_name -> google.protobuf.StringValue
	8,  // 5: api.v1.model.FluxServerRateLimitRule.WarningQuota:type_name -> google.protobuf.StringValue
	1,  // 6: api.v1.model.FluxServerRateLimitRule.KeyQuota:type_name -> api.v1.model.FluxServerKeyQuota
	8,  // 7: api.v1.model.FluxServerKeyQuota.Key:type_name -> google.protobuf.StringValue
	8,  // 8: api.v1.model.FluxServerKeyQuota.Business:type_name -> google.protobuf.StringValue
	8,  // 9: api.v1.model.FluxServerKeyQuota.Quota:type_name -> google.protobuf.StringValue
	8,  // 10: api.v1.model.FluxServerKeyQuota.StartTime:type_name -> google.protobuf.StringValue
	8,  // 11: api.v1.model.FluxServerKeyQuota.EndTime:type_name -> google.protobuf.StringValue
	8,  // 12: api.v1.model.FluxServerKeyQuota.WarningQuota:type_name -> google.protobuf.StringValue
	8,  // 13: api.v1.model.FluxServerKeyQuota.IsEnable:type_name -> google.protobuf.StringValue
	8,  // 14: api.v1.model.BzSet.Env:type_name -> google.protobuf.StringValue
	8,  // 15: api.v1.model.BzSet.ServerName:type_name -> google.protobuf.StringValue
	5,  // 16: api.v1.model.FluxSDKRateLimitRule.fluxSet:type_name -> api.v1.model.FluxSet
	6,  // 17: api.v1.model.FluxSDKRateLimitRule.monitorSet:type_name -> api.v1.model.MonitorSet
	8,  // 18: api.v1.model.FluxSDKRateLimitRule.setQuota:type_name -> google.protobuf.StringValue
	8,  // 19: api.v1.model.FluxSDKRateLimitRule.minQuota:type_name -> google.protobuf.StringValue
	8,  // 20: api.v1.model.FluxSDKRateLimitRule.minQuotaEnable:type_name -> google.protobuf.StringValue
	4,  // 21: api.v1.model.FluxSDKRateLimitRule.keyQuota:type_name -> api.v1.model.FluxSDKKeyQuota
	8,  // 22: api.v1.model.FluxSDKKeyQuota.key:type_name -> google.protobuf.StringValue
	8,  // 23: api.v1.model.FluxSDKKeyQuota.business:type_name -> google.protobuf.StringValue
	8,  // 24: api.v1.model.FluxSDKKeyQuota.quota:type_name -> google.protobuf.StringValue
	8,  // 25: api.v1.model.FluxSet.serverName:type_name -> google.protobuf.StringValue
	8,  // 26: api.v1.model.FluxSet.env:type_name -> google.protobuf.StringValue
	8,  // 27: api.v1.model.MonitorSet.serverName:type_name -> google.protobuf.StringValue
	8,  // 28: api.v1.model.MonitorSet.env:type_name -> google.protobuf.StringValue
	8,  // 29: api.v1.model.FluxConsoleRateLimitRule.id:type_name -> google.protobuf.StringValue
	8,  // 30: api.v1.model.FluxConsoleRateLimitRule.revision:type_name -> google.protobuf.StringValue
	8,  // 31: api.v1.model.FluxConsoleRateLimitRule.name:type_name -> google.protobuf.StringValue
	8,  // 32: api.v1.model.FluxConsoleRateLimitRule.description:type_name -> google.protobuf.StringValue
	8,  // 33: api.v1.model.FluxConsoleRateLimitRule.calleeServiceName:type_name -> google.protobuf.StringValue
	8,  // 34: api.v1.model.FluxConsoleRateLimitRule.calleeServiceEnv:type_name -> google.protobuf.StringValue
	8,  // 35: api.v1.model.FluxConsoleRateLimitRule.calleeServiceId:type_name -> google.protobuf.StringValue
	8,  // 36: api.v1.model.FluxConsoleRateLimitRule.callerServiceBusiness:type_name -> google.protobuf.StringValue
	8,  // 37: api.v1.model.FluxConsoleRateLimitRule.setKey:type_name -> google.protobuf.StringValue
	8,  // 38: api.v1.model.FluxConsoleRateLimitRule.setAlertQps:type_name -> google.protobuf.StringValue
	8,  // 39: api.v1.model.FluxConsoleRateLimitRule.setWarningQps:type_name -> google.protobuf.StringValue
	8,  // 40: api.v1.model.FluxConsoleRateLimitRule.setRemark:type_name -> google.protobuf.StringValue
	8,  // 41: api.v1.model.FluxConsoleRateLimitRule.defaultKey:type_name -> google.protobuf.StringValue
	8,  // 42: api.v1.model.FluxConsoleRateLimitRule.defaultAlertQps:type_name -> google.protobuf.StringValue
	8,  // 43: api.v1.model.FluxConsoleRateLimitRule.defaultWarningQps:type_name -> google.protobuf.StringValue
	8,  // 44: api.v1.model.FluxConsoleRateLimitRule.defaultRemark:type_name -> google.protobuf.StringValue
	8,  // 45: api.v1.model.FluxConsoleRateLimitRule.creator:type_name -> google.protobuf.StringValue
	8,  // 46: api.v1.model.FluxConsoleRateLimitRule.updater:type_name -> google.protobuf.StringValue
	9,  // 47: api.v1.model.FluxConsoleRateLimitRule.status:type_name -> google.protobuf.UInt32Value
	8,  // 48: api.v1.model.FluxConsoleRateLimitRule.ctime:type_name -> google.protobuf.StringValue
	8,  // 49: api.v1.model.FluxConsoleRateLimitRule.mtime:type_name -> google.protobuf.StringValue
	8,  // 50: api.v1.model.FluxConsoleRateLimitRule.service_token:type_name -> google.protobuf.StringValue
	9,  // 51: api.v1.model.FluxConsoleRateLimitRule.type:type_name -> google.protobuf.UInt32Value
	52, // [52:52] is the sub-list for method output_type
	52, // [52:52] is the sub-list for method input_type
	52, // [52:52] is the sub-list for extension type_name
	52, // [52:52] is the sub-list for extension extendee
	0,  // [0:52] is the sub-list for field type_name
}

func init() { file_v1_model_fluxserverratelimit_proto_init() }
func file_v1_model_fluxserverratelimit_proto_init() {
	if File_v1_model_fluxserverratelimit_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_v1_model_fluxserverratelimit_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FluxServerRateLimitRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_fluxserverratelimit_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FluxServerKeyQuota); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_fluxserverratelimit_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BzSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_fluxserverratelimit_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FluxSDKRateLimitRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_fluxserverratelimit_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FluxSDKKeyQuota); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_fluxserverratelimit_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FluxSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_fluxserverratelimit_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonitorSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_fluxserverratelimit_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FluxConsoleRateLimitRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_model_fluxserverratelimit_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v1_model_fluxserverratelimit_proto_goTypes,
		DependencyIndexes: file_v1_model_fluxserverratelimit_proto_depIdxs,
		MessageInfos:      file_v1_model_fluxserverratelimit_proto_msgTypes,
	}.Build()
	File_v1_model_fluxserverratelimit_proto = out.File
	file_v1_model_fluxserverratelimit_proto_rawDesc = nil
	file_v1_model_fluxserverratelimit_proto_goTypes = nil
	file_v1_model_fluxserverratelimit_proto_depIdxs = nil
}
