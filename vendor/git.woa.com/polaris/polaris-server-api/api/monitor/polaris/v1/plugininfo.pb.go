// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: monitor/polaris/v1/plugininfo.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PluginAPIResultType int32

const (
	PluginAPIResultType_UnknownResult PluginAPIResultType = 0
	PluginAPIResultType_APISuccess    PluginAPIResultType = 1
	PluginAPIResultType_APIFail       PluginAPIResultType = 2
)

// Enum value maps for PluginAPIResultType.
var (
	PluginAPIResultType_name = map[int32]string{
		0: "UnknownResult",
		1: "APISuccess",
		2: "APIFail",
	}
	PluginAPIResultType_value = map[string]int32{
		"UnknownResult": 0,
		"APISuccess":    1,
		"APIFail":       2,
	}
)

func (x PluginAPIResultType) Enum() *PluginAPIResultType {
	p := new(PluginAPIResultType)
	*p = x
	return p
}

func (x PluginAPIResultType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PluginAPIResultType) Descriptor() protoreflect.EnumDescriptor {
	return file_monitor_polaris_v1_plugininfo_proto_enumTypes[0].Descriptor()
}

func (PluginAPIResultType) Type() protoreflect.EnumType {
	return &file_monitor_polaris_v1_plugininfo_proto_enumTypes[0]
}

func (x PluginAPIResultType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PluginAPIResultType.Descriptor instead.
func (PluginAPIResultType) EnumDescriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_plugininfo_proto_rawDescGZIP(), []int{0}
}

type PluginAPIStatistics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Token     *SDKToken          `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	PluginApi *PluginAPIKey      `protobuf:"bytes,3,opt,name=plugin_api,json=pluginApi,proto3" json:"plugin_api,omitempty"`
	Results   []*PluginAPIResult `protobuf:"bytes,4,rep,name=results,proto3" json:"results,omitempty"`
	Signature *MonitorSignature  `protobuf:"bytes,5,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *PluginAPIStatistics) Reset() {
	*x = PluginAPIStatistics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_plugininfo_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PluginAPIStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginAPIStatistics) ProtoMessage() {}

func (x *PluginAPIStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_plugininfo_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginAPIStatistics.ProtoReflect.Descriptor instead.
func (*PluginAPIStatistics) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_plugininfo_proto_rawDescGZIP(), []int{0}
}

func (x *PluginAPIStatistics) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PluginAPIStatistics) GetToken() *SDKToken {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *PluginAPIStatistics) GetPluginApi() *PluginAPIKey {
	if x != nil {
		return x.PluginApi
	}
	return nil
}

func (x *PluginAPIStatistics) GetResults() []*PluginAPIResult {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *PluginAPIStatistics) GetSignature() *MonitorSignature {
	if x != nil {
		return x.Signature
	}
	return nil
}

type PluginAPIKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginType   string `protobuf:"bytes,1,opt,name=plugin_type,json=pluginType,proto3" json:"plugin_type,omitempty"`
	PluginName   string `protobuf:"bytes,2,opt,name=plugin_name,json=pluginName,proto3" json:"plugin_name,omitempty"`
	PluginMethod string `protobuf:"bytes,3,opt,name=plugin_method,json=pluginMethod,proto3" json:"plugin_method,omitempty"`
}

func (x *PluginAPIKey) Reset() {
	*x = PluginAPIKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_plugininfo_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PluginAPIKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginAPIKey) ProtoMessage() {}

func (x *PluginAPIKey) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_plugininfo_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginAPIKey.ProtoReflect.Descriptor instead.
func (*PluginAPIKey) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_plugininfo_proto_rawDescGZIP(), []int{1}
}

func (x *PluginAPIKey) GetPluginType() string {
	if x != nil {
		return x.PluginType
	}
	return ""
}

func (x *PluginAPIKey) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *PluginAPIKey) GetPluginMethod() string {
	if x != nil {
		return x.PluginMethod
	}
	return ""
}

type PluginAPIResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RetCode                string              `protobuf:"bytes,1,opt,name=ret_code,json=retCode,proto3" json:"ret_code,omitempty"`
	TotalRequestsPerMinute uint32              `protobuf:"varint,2,opt,name=total_requests_per_minute,json=totalRequestsPerMinute,proto3" json:"total_requests_per_minute,omitempty"`
	Type                   PluginAPIResultType `protobuf:"varint,3,opt,name=type,proto3,enum=v1.PluginAPIResultType" json:"type,omitempty"`
	DelayRange             string              `protobuf:"bytes,4,opt,name=delay_range,json=delayRange,proto3" json:"delay_range,omitempty"`
}

func (x *PluginAPIResult) Reset() {
	*x = PluginAPIResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_plugininfo_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PluginAPIResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginAPIResult) ProtoMessage() {}

func (x *PluginAPIResult) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_plugininfo_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginAPIResult.ProtoReflect.Descriptor instead.
func (*PluginAPIResult) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_plugininfo_proto_rawDescGZIP(), []int{2}
}

func (x *PluginAPIResult) GetRetCode() string {
	if x != nil {
		return x.RetCode
	}
	return ""
}

func (x *PluginAPIResult) GetTotalRequestsPerMinute() uint32 {
	if x != nil {
		return x.TotalRequestsPerMinute
	}
	return 0
}

func (x *PluginAPIResult) GetType() PluginAPIResultType {
	if x != nil {
		return x.Type
	}
	return PluginAPIResultType_UnknownResult
}

func (x *PluginAPIResult) GetDelayRange() string {
	if x != nil {
		return x.DelayRange
	}
	return ""
}

var File_monitor_polaris_v1_plugininfo_proto protoreflect.FileDescriptor

var file_monitor_polaris_v1_plugininfo_proto_rawDesc = []byte{
	0x0a, 0x23, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x69, 0x6e, 0x66, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x76, 0x31, 0x1a, 0x21, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x64,
	0x6b, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xdd, 0x01, 0x0a, 0x13, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x41, 0x50, 0x49, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x44, 0x4b,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2f, 0x0a, 0x0a,
	0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x41, 0x50, 0x49, 0x4b,
	0x65, 0x79, 0x52, 0x09, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x41, 0x70, 0x69, 0x12, 0x2d, 0x0a,
	0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x41, 0x50, 0x49, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x32, 0x0a, 0x09,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x22, 0x75, 0x0a, 0x0c, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x41, 0x50, 0x49, 0x4b, 0x65, 0x79,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x22, 0xb5, 0x01, 0x0a, 0x0f, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x41, 0x50, 0x49, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x72,
	0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72,
	0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x69, 0x6e,
	0x75, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x16, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x50, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x75, 0x74,
	0x65, 0x12, 0x2b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x17, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x41, 0x50, 0x49, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x2a,
	0x45, 0x0a, 0x13, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x41, 0x50, 0x49, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x50, 0x49,
	0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x50, 0x49,
	0x46, 0x61, 0x69, 0x6c, 0x10, 0x02, 0x42, 0x3f, 0x5a, 0x3d, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c,
	0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_monitor_polaris_v1_plugininfo_proto_rawDescOnce sync.Once
	file_monitor_polaris_v1_plugininfo_proto_rawDescData = file_monitor_polaris_v1_plugininfo_proto_rawDesc
)

func file_monitor_polaris_v1_plugininfo_proto_rawDescGZIP() []byte {
	file_monitor_polaris_v1_plugininfo_proto_rawDescOnce.Do(func() {
		file_monitor_polaris_v1_plugininfo_proto_rawDescData = protoimpl.X.CompressGZIP(file_monitor_polaris_v1_plugininfo_proto_rawDescData)
	})
	return file_monitor_polaris_v1_plugininfo_proto_rawDescData
}

var file_monitor_polaris_v1_plugininfo_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_monitor_polaris_v1_plugininfo_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_monitor_polaris_v1_plugininfo_proto_goTypes = []interface{}{
	(PluginAPIResultType)(0),    // 0: v1.PluginAPIResultType
	(*PluginAPIStatistics)(nil), // 1: v1.PluginAPIStatistics
	(*PluginAPIKey)(nil),        // 2: v1.PluginAPIKey
	(*PluginAPIResult)(nil),     // 3: v1.PluginAPIResult
	(*SDKToken)(nil),            // 4: v1.SDKToken
	(*MonitorSignature)(nil),    // 5: v1.MonitorSignature
}
var file_monitor_polaris_v1_plugininfo_proto_depIdxs = []int32{
	4, // 0: v1.PluginAPIStatistics.token:type_name -> v1.SDKToken
	2, // 1: v1.PluginAPIStatistics.plugin_api:type_name -> v1.PluginAPIKey
	3, // 2: v1.PluginAPIStatistics.results:type_name -> v1.PluginAPIResult
	5, // 3: v1.PluginAPIStatistics.signature:type_name -> v1.MonitorSignature
	0, // 4: v1.PluginAPIResult.type:type_name -> v1.PluginAPIResultType
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_monitor_polaris_v1_plugininfo_proto_init() }
func file_monitor_polaris_v1_plugininfo_proto_init() {
	if File_monitor_polaris_v1_plugininfo_proto != nil {
		return
	}
	file_monitor_polaris_v1_sdktoken_proto_init()
	file_monitor_polaris_v1_signature_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_monitor_polaris_v1_plugininfo_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PluginAPIStatistics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_plugininfo_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PluginAPIKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_polaris_v1_plugininfo_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PluginAPIResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_monitor_polaris_v1_plugininfo_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_monitor_polaris_v1_plugininfo_proto_goTypes,
		DependencyIndexes: file_monitor_polaris_v1_plugininfo_proto_depIdxs,
		EnumInfos:         file_monitor_polaris_v1_plugininfo_proto_enumTypes,
		MessageInfos:      file_monitor_polaris_v1_plugininfo_proto_msgTypes,
	}.Build()
	File_monitor_polaris_v1_plugininfo_proto = out.File
	file_monitor_polaris_v1_plugininfo_proto_rawDesc = nil
	file_monitor_polaris_v1_plugininfo_proto_goTypes = nil
	file_monitor_polaris_v1_plugininfo_proto_depIdxs = nil
}
