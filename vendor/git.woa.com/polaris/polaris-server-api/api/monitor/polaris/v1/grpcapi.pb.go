// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: monitor/polaris/v1/grpcapi.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_monitor_polaris_v1_grpcapi_proto protoreflect.FileDescriptor

var file_monitor_polaris_v1_grpcapi_proto_rawDesc = []byte{
	0x0a, 0x20, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x02, 0x76, 0x31, 0x1a, 0x20, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x64, 0x6b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x64, 0x6b, 0x63, 0x61,
	0x63, 0x68, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x69,
	0x72, 0x63, 0x75, 0x69, 0x74, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x23, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x69, 0x6e, 0x66, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70,
	0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x64, 0x6b, 0x6c, 0x6f, 0x61,
	0x64, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x72, 0x61, 0x74, 0x65, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0xeb, 0x07, 0x0a, 0x07, 0x47, 0x72, 0x70, 0x63, 0x41, 0x50, 0x49, 0x12, 0x47, 0x0a, 0x17,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x14, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x1a, 0x10, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x47, 0x0a, 0x17, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x53, 0x44, 0x4b, 0x41, 0x50, 0x49, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x12, 0x14, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x44, 0x4b, 0x41, 0x50, 0x49, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x1a, 0x10, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x49,
	0x0a, 0x18, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x15, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x73, 0x1a, 0x10, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x3c, 0x0a, 0x17, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x53, 0x44, 0x4b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0d, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x44, 0x4b, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x1a, 0x10, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3a, 0x0a, 0x0f, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x53, 0x44, 0x4b, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x0f, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x10, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x28,
	0x01, 0x30, 0x01, 0x12, 0x46, 0x0a, 0x13, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x69,
	0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x12, 0x17, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x62, 0x72,
	0x65, 0x61, 0x6b, 0x1a, 0x10, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x4a, 0x0a, 0x17, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x17, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x41, 0x50, 0x49, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x1a,
	0x10, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x4c, 0x0a, 0x16, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1a, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x6f,
	0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x10, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x45, 0x0a, 0x16, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x13, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x1a, 0x10, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x44, 0x0a, 0x12,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x12, 0x16, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x1a, 0x10, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x28, 0x01,
	0x30, 0x01, 0x12, 0x39, 0x0a, 0x11, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x73,
	0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x0c, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x10, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x43, 0x0a,
	0x13, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x73, 0x68, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x14, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x68, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x10, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x28, 0x01,
	0x30, 0x01, 0x12, 0x47, 0x0a, 0x1a, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x12, 0x15, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x1a, 0x10, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x1f, 0x41,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x1a,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x1a, 0x10, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x3f,
	0x5a, 0x3d, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_monitor_polaris_v1_grpcapi_proto_goTypes = []interface{}{
	(*ServerStatistics)(nil),       // 0: v1.ServerStatistics
	(*SDKAPIStatistics)(nil),       // 1: v1.SDKAPIStatistics
	(*ServiceStatistics)(nil),      // 2: v1.ServiceStatistics
	(*SDKConfig)(nil),              // 3: v1.SDKConfig
	(*ServiceInfo)(nil),            // 4: v1.ServiceInfo
	(*ServiceCircuitbreak)(nil),    // 5: v1.ServiceCircuitbreak
	(*PluginAPIStatistics)(nil),    // 6: v1.PluginAPIStatistics
	(*ServiceLoadBalanceInfo)(nil), // 7: v1.ServiceLoadBalanceInfo
	(*RateLimitRecord)(nil),        // 8: v1.RateLimitRecord
	(*ServiceRouteRecord)(nil),     // 9: v1.ServiceRouteRecord
	(*MeshInfo)(nil),               // 10: v1.MeshInfo
	(*MeshResourceInfo)(nil),       // 11: v1.MeshResourceInfo
	(*BatchServiceStatistics)(nil), // 12: v1.BatchServiceStatistics
	(*StatResponse)(nil),           // 13: v1.StatResponse
}
var file_monitor_polaris_v1_grpcapi_proto_depIdxs = []int32{
	0,  // 0: v1.GrpcAPI.CollectServerStatistics:input_type -> v1.ServerStatistics
	1,  // 1: v1.GrpcAPI.CollectSDKAPIStatistics:input_type -> v1.SDKAPIStatistics
	2,  // 2: v1.GrpcAPI.CollectServiceStatistics:input_type -> v1.ServiceStatistics
	3,  // 3: v1.GrpcAPI.CollectSDKConfiguration:input_type -> v1.SDKConfig
	4,  // 4: v1.GrpcAPI.CollectSDKCache:input_type -> v1.ServiceInfo
	5,  // 5: v1.GrpcAPI.CollectCircuitBreak:input_type -> v1.ServiceCircuitbreak
	6,  // 6: v1.GrpcAPI.CollectPluginStatistics:input_type -> v1.PluginAPIStatistics
	7,  // 7: v1.GrpcAPI.CollectLoadBalanceInfo:input_type -> v1.ServiceLoadBalanceInfo
	8,  // 8: v1.GrpcAPI.CollectRateLimitRecord:input_type -> v1.RateLimitRecord
	9,  // 9: v1.GrpcAPI.CollectRouteRecord:input_type -> v1.ServiceRouteRecord
	10, // 10: v1.GrpcAPI.CollectMeshConfig:input_type -> v1.MeshInfo
	11, // 11: v1.GrpcAPI.CollectMeshResource:input_type -> v1.MeshResourceInfo
	2,  // 12: v1.GrpcAPI.AggregateServiceStatistics:input_type -> v1.ServiceStatistics
	12, // 13: v1.GrpcAPI.AggregateBatchServiceStatistics:input_type -> v1.BatchServiceStatistics
	13, // 14: v1.GrpcAPI.CollectServerStatistics:output_type -> v1.StatResponse
	13, // 15: v1.GrpcAPI.CollectSDKAPIStatistics:output_type -> v1.StatResponse
	13, // 16: v1.GrpcAPI.CollectServiceStatistics:output_type -> v1.StatResponse
	13, // 17: v1.GrpcAPI.CollectSDKConfiguration:output_type -> v1.StatResponse
	13, // 18: v1.GrpcAPI.CollectSDKCache:output_type -> v1.StatResponse
	13, // 19: v1.GrpcAPI.CollectCircuitBreak:output_type -> v1.StatResponse
	13, // 20: v1.GrpcAPI.CollectPluginStatistics:output_type -> v1.StatResponse
	13, // 21: v1.GrpcAPI.CollectLoadBalanceInfo:output_type -> v1.StatResponse
	13, // 22: v1.GrpcAPI.CollectRateLimitRecord:output_type -> v1.StatResponse
	13, // 23: v1.GrpcAPI.CollectRouteRecord:output_type -> v1.StatResponse
	13, // 24: v1.GrpcAPI.CollectMeshConfig:output_type -> v1.StatResponse
	13, // 25: v1.GrpcAPI.CollectMeshResource:output_type -> v1.StatResponse
	13, // 26: v1.GrpcAPI.AggregateServiceStatistics:output_type -> v1.StatResponse
	13, // 27: v1.GrpcAPI.AggregateBatchServiceStatistics:output_type -> v1.StatResponse
	14, // [14:28] is the sub-list for method output_type
	0,  // [0:14] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_monitor_polaris_v1_grpcapi_proto_init() }
func file_monitor_polaris_v1_grpcapi_proto_init() {
	if File_monitor_polaris_v1_grpcapi_proto != nil {
		return
	}
	file_monitor_polaris_v1_request_proto_init()
	file_monitor_polaris_v1_response_proto_init()
	file_monitor_polaris_v1_sdkconfiguration_proto_init()
	file_monitor_polaris_v1_sdkcache_proto_init()
	file_monitor_polaris_v1_circuitbreak_proto_init()
	file_monitor_polaris_v1_plugininfo_proto_init()
	file_monitor_polaris_v1_sdkloadbalance_proto_init()
	file_monitor_polaris_v1_serviceratelimit_proto_init()
	file_monitor_polaris_v1_serviceroute_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_monitor_polaris_v1_grpcapi_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_monitor_polaris_v1_grpcapi_proto_goTypes,
		DependencyIndexes: file_monitor_polaris_v1_grpcapi_proto_depIdxs,
	}.Build()
	File_monitor_polaris_v1_grpcapi_proto = out.File
	file_monitor_polaris_v1_grpcapi_proto_rawDesc = nil
	file_monitor_polaris_v1_grpcapi_proto_goTypes = nil
	file_monitor_polaris_v1_grpcapi_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// GrpcAPIClient is the client API for GrpcAPI service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GrpcAPIClient interface {
	// Collect开头的方法，用于接收sdk的数据
	CollectServerStatistics(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectServerStatisticsClient, error)
	CollectSDKAPIStatistics(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectSDKAPIStatisticsClient, error)
	CollectServiceStatistics(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectServiceStatisticsClient, error)
	CollectSDKConfiguration(ctx context.Context, in *SDKConfig, opts ...grpc.CallOption) (*StatResponse, error)
	CollectSDKCache(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectSDKCacheClient, error)
	CollectCircuitBreak(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectCircuitBreakClient, error)
	CollectPluginStatistics(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectPluginStatisticsClient, error)
	CollectLoadBalanceInfo(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectLoadBalanceInfoClient, error)
	CollectRateLimitRecord(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectRateLimitRecordClient, error)
	CollectRouteRecord(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectRouteRecordClient, error)
	CollectMeshConfig(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectMeshConfigClient, error)
	CollectMeshResource(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectMeshResourceClient, error)
	// Aggregate开头的方法，用于接收来自上层monitor的数据，进行进一步聚合
	AggregateServiceStatistics(ctx context.Context, in *ServiceStatistics, opts ...grpc.CallOption) (*StatResponse, error)
	AggregateBatchServiceStatistics(ctx context.Context, in *BatchServiceStatistics, opts ...grpc.CallOption) (*StatResponse, error)
}

type grpcAPIClient struct {
	cc grpc.ClientConnInterface
}

func NewGrpcAPIClient(cc grpc.ClientConnInterface) GrpcAPIClient {
	return &grpcAPIClient{cc}
}

func (c *grpcAPIClient) CollectServerStatistics(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectServerStatisticsClient, error) {
	stream, err := c.cc.NewStream(ctx, &_GrpcAPI_serviceDesc.Streams[0], "/v1.GrpcAPI/CollectServerStatistics", opts...)
	if err != nil {
		return nil, err
	}
	x := &grpcAPICollectServerStatisticsClient{stream}
	return x, nil
}

type GrpcAPI_CollectServerStatisticsClient interface {
	Send(*ServerStatistics) error
	Recv() (*StatResponse, error)
	grpc.ClientStream
}

type grpcAPICollectServerStatisticsClient struct {
	grpc.ClientStream
}

func (x *grpcAPICollectServerStatisticsClient) Send(m *ServerStatistics) error {
	return x.ClientStream.SendMsg(m)
}

func (x *grpcAPICollectServerStatisticsClient) Recv() (*StatResponse, error) {
	m := new(StatResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *grpcAPIClient) CollectSDKAPIStatistics(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectSDKAPIStatisticsClient, error) {
	stream, err := c.cc.NewStream(ctx, &_GrpcAPI_serviceDesc.Streams[1], "/v1.GrpcAPI/CollectSDKAPIStatistics", opts...)
	if err != nil {
		return nil, err
	}
	x := &grpcAPICollectSDKAPIStatisticsClient{stream}
	return x, nil
}

type GrpcAPI_CollectSDKAPIStatisticsClient interface {
	Send(*SDKAPIStatistics) error
	Recv() (*StatResponse, error)
	grpc.ClientStream
}

type grpcAPICollectSDKAPIStatisticsClient struct {
	grpc.ClientStream
}

func (x *grpcAPICollectSDKAPIStatisticsClient) Send(m *SDKAPIStatistics) error {
	return x.ClientStream.SendMsg(m)
}

func (x *grpcAPICollectSDKAPIStatisticsClient) Recv() (*StatResponse, error) {
	m := new(StatResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *grpcAPIClient) CollectServiceStatistics(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectServiceStatisticsClient, error) {
	stream, err := c.cc.NewStream(ctx, &_GrpcAPI_serviceDesc.Streams[2], "/v1.GrpcAPI/CollectServiceStatistics", opts...)
	if err != nil {
		return nil, err
	}
	x := &grpcAPICollectServiceStatisticsClient{stream}
	return x, nil
}

type GrpcAPI_CollectServiceStatisticsClient interface {
	Send(*ServiceStatistics) error
	Recv() (*StatResponse, error)
	grpc.ClientStream
}

type grpcAPICollectServiceStatisticsClient struct {
	grpc.ClientStream
}

func (x *grpcAPICollectServiceStatisticsClient) Send(m *ServiceStatistics) error {
	return x.ClientStream.SendMsg(m)
}

func (x *grpcAPICollectServiceStatisticsClient) Recv() (*StatResponse, error) {
	m := new(StatResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *grpcAPIClient) CollectSDKConfiguration(ctx context.Context, in *SDKConfig, opts ...grpc.CallOption) (*StatResponse, error) {
	out := new(StatResponse)
	err := c.cc.Invoke(ctx, "/v1.GrpcAPI/CollectSDKConfiguration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *grpcAPIClient) CollectSDKCache(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectSDKCacheClient, error) {
	stream, err := c.cc.NewStream(ctx, &_GrpcAPI_serviceDesc.Streams[3], "/v1.GrpcAPI/CollectSDKCache", opts...)
	if err != nil {
		return nil, err
	}
	x := &grpcAPICollectSDKCacheClient{stream}
	return x, nil
}

type GrpcAPI_CollectSDKCacheClient interface {
	Send(*ServiceInfo) error
	Recv() (*StatResponse, error)
	grpc.ClientStream
}

type grpcAPICollectSDKCacheClient struct {
	grpc.ClientStream
}

func (x *grpcAPICollectSDKCacheClient) Send(m *ServiceInfo) error {
	return x.ClientStream.SendMsg(m)
}

func (x *grpcAPICollectSDKCacheClient) Recv() (*StatResponse, error) {
	m := new(StatResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *grpcAPIClient) CollectCircuitBreak(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectCircuitBreakClient, error) {
	stream, err := c.cc.NewStream(ctx, &_GrpcAPI_serviceDesc.Streams[4], "/v1.GrpcAPI/CollectCircuitBreak", opts...)
	if err != nil {
		return nil, err
	}
	x := &grpcAPICollectCircuitBreakClient{stream}
	return x, nil
}

type GrpcAPI_CollectCircuitBreakClient interface {
	Send(*ServiceCircuitbreak) error
	Recv() (*StatResponse, error)
	grpc.ClientStream
}

type grpcAPICollectCircuitBreakClient struct {
	grpc.ClientStream
}

func (x *grpcAPICollectCircuitBreakClient) Send(m *ServiceCircuitbreak) error {
	return x.ClientStream.SendMsg(m)
}

func (x *grpcAPICollectCircuitBreakClient) Recv() (*StatResponse, error) {
	m := new(StatResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *grpcAPIClient) CollectPluginStatistics(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectPluginStatisticsClient, error) {
	stream, err := c.cc.NewStream(ctx, &_GrpcAPI_serviceDesc.Streams[5], "/v1.GrpcAPI/CollectPluginStatistics", opts...)
	if err != nil {
		return nil, err
	}
	x := &grpcAPICollectPluginStatisticsClient{stream}
	return x, nil
}

type GrpcAPI_CollectPluginStatisticsClient interface {
	Send(*PluginAPIStatistics) error
	Recv() (*StatResponse, error)
	grpc.ClientStream
}

type grpcAPICollectPluginStatisticsClient struct {
	grpc.ClientStream
}

func (x *grpcAPICollectPluginStatisticsClient) Send(m *PluginAPIStatistics) error {
	return x.ClientStream.SendMsg(m)
}

func (x *grpcAPICollectPluginStatisticsClient) Recv() (*StatResponse, error) {
	m := new(StatResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *grpcAPIClient) CollectLoadBalanceInfo(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectLoadBalanceInfoClient, error) {
	stream, err := c.cc.NewStream(ctx, &_GrpcAPI_serviceDesc.Streams[6], "/v1.GrpcAPI/CollectLoadBalanceInfo", opts...)
	if err != nil {
		return nil, err
	}
	x := &grpcAPICollectLoadBalanceInfoClient{stream}
	return x, nil
}

type GrpcAPI_CollectLoadBalanceInfoClient interface {
	Send(*ServiceLoadBalanceInfo) error
	Recv() (*StatResponse, error)
	grpc.ClientStream
}

type grpcAPICollectLoadBalanceInfoClient struct {
	grpc.ClientStream
}

func (x *grpcAPICollectLoadBalanceInfoClient) Send(m *ServiceLoadBalanceInfo) error {
	return x.ClientStream.SendMsg(m)
}

func (x *grpcAPICollectLoadBalanceInfoClient) Recv() (*StatResponse, error) {
	m := new(StatResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *grpcAPIClient) CollectRateLimitRecord(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectRateLimitRecordClient, error) {
	stream, err := c.cc.NewStream(ctx, &_GrpcAPI_serviceDesc.Streams[7], "/v1.GrpcAPI/CollectRateLimitRecord", opts...)
	if err != nil {
		return nil, err
	}
	x := &grpcAPICollectRateLimitRecordClient{stream}
	return x, nil
}

type GrpcAPI_CollectRateLimitRecordClient interface {
	Send(*RateLimitRecord) error
	Recv() (*StatResponse, error)
	grpc.ClientStream
}

type grpcAPICollectRateLimitRecordClient struct {
	grpc.ClientStream
}

func (x *grpcAPICollectRateLimitRecordClient) Send(m *RateLimitRecord) error {
	return x.ClientStream.SendMsg(m)
}

func (x *grpcAPICollectRateLimitRecordClient) Recv() (*StatResponse, error) {
	m := new(StatResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *grpcAPIClient) CollectRouteRecord(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectRouteRecordClient, error) {
	stream, err := c.cc.NewStream(ctx, &_GrpcAPI_serviceDesc.Streams[8], "/v1.GrpcAPI/CollectRouteRecord", opts...)
	if err != nil {
		return nil, err
	}
	x := &grpcAPICollectRouteRecordClient{stream}
	return x, nil
}

type GrpcAPI_CollectRouteRecordClient interface {
	Send(*ServiceRouteRecord) error
	Recv() (*StatResponse, error)
	grpc.ClientStream
}

type grpcAPICollectRouteRecordClient struct {
	grpc.ClientStream
}

func (x *grpcAPICollectRouteRecordClient) Send(m *ServiceRouteRecord) error {
	return x.ClientStream.SendMsg(m)
}

func (x *grpcAPICollectRouteRecordClient) Recv() (*StatResponse, error) {
	m := new(StatResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *grpcAPIClient) CollectMeshConfig(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectMeshConfigClient, error) {
	stream, err := c.cc.NewStream(ctx, &_GrpcAPI_serviceDesc.Streams[9], "/v1.GrpcAPI/CollectMeshConfig", opts...)
	if err != nil {
		return nil, err
	}
	x := &grpcAPICollectMeshConfigClient{stream}
	return x, nil
}

type GrpcAPI_CollectMeshConfigClient interface {
	Send(*MeshInfo) error
	Recv() (*StatResponse, error)
	grpc.ClientStream
}

type grpcAPICollectMeshConfigClient struct {
	grpc.ClientStream
}

func (x *grpcAPICollectMeshConfigClient) Send(m *MeshInfo) error {
	return x.ClientStream.SendMsg(m)
}

func (x *grpcAPICollectMeshConfigClient) Recv() (*StatResponse, error) {
	m := new(StatResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *grpcAPIClient) CollectMeshResource(ctx context.Context, opts ...grpc.CallOption) (GrpcAPI_CollectMeshResourceClient, error) {
	stream, err := c.cc.NewStream(ctx, &_GrpcAPI_serviceDesc.Streams[10], "/v1.GrpcAPI/CollectMeshResource", opts...)
	if err != nil {
		return nil, err
	}
	x := &grpcAPICollectMeshResourceClient{stream}
	return x, nil
}

type GrpcAPI_CollectMeshResourceClient interface {
	Send(*MeshResourceInfo) error
	Recv() (*StatResponse, error)
	grpc.ClientStream
}

type grpcAPICollectMeshResourceClient struct {
	grpc.ClientStream
}

func (x *grpcAPICollectMeshResourceClient) Send(m *MeshResourceInfo) error {
	return x.ClientStream.SendMsg(m)
}

func (x *grpcAPICollectMeshResourceClient) Recv() (*StatResponse, error) {
	m := new(StatResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *grpcAPIClient) AggregateServiceStatistics(ctx context.Context, in *ServiceStatistics, opts ...grpc.CallOption) (*StatResponse, error) {
	out := new(StatResponse)
	err := c.cc.Invoke(ctx, "/v1.GrpcAPI/AggregateServiceStatistics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *grpcAPIClient) AggregateBatchServiceStatistics(ctx context.Context, in *BatchServiceStatistics, opts ...grpc.CallOption) (*StatResponse, error) {
	out := new(StatResponse)
	err := c.cc.Invoke(ctx, "/v1.GrpcAPI/AggregateBatchServiceStatistics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GrpcAPIServer is the server API for GrpcAPI service.
type GrpcAPIServer interface {
	// Collect开头的方法，用于接收sdk的数据
	CollectServerStatistics(GrpcAPI_CollectServerStatisticsServer) error
	CollectSDKAPIStatistics(GrpcAPI_CollectSDKAPIStatisticsServer) error
	CollectServiceStatistics(GrpcAPI_CollectServiceStatisticsServer) error
	CollectSDKConfiguration(context.Context, *SDKConfig) (*StatResponse, error)
	CollectSDKCache(GrpcAPI_CollectSDKCacheServer) error
	CollectCircuitBreak(GrpcAPI_CollectCircuitBreakServer) error
	CollectPluginStatistics(GrpcAPI_CollectPluginStatisticsServer) error
	CollectLoadBalanceInfo(GrpcAPI_CollectLoadBalanceInfoServer) error
	CollectRateLimitRecord(GrpcAPI_CollectRateLimitRecordServer) error
	CollectRouteRecord(GrpcAPI_CollectRouteRecordServer) error
	CollectMeshConfig(GrpcAPI_CollectMeshConfigServer) error
	CollectMeshResource(GrpcAPI_CollectMeshResourceServer) error
	// Aggregate开头的方法，用于接收来自上层monitor的数据，进行进一步聚合
	AggregateServiceStatistics(context.Context, *ServiceStatistics) (*StatResponse, error)
	AggregateBatchServiceStatistics(context.Context, *BatchServiceStatistics) (*StatResponse, error)
}

// UnimplementedGrpcAPIServer can be embedded to have forward compatible implementations.
type UnimplementedGrpcAPIServer struct {
}

func (*UnimplementedGrpcAPIServer) CollectServerStatistics(GrpcAPI_CollectServerStatisticsServer) error {
	return status.Errorf(codes.Unimplemented, "method CollectServerStatistics not implemented")
}
func (*UnimplementedGrpcAPIServer) CollectSDKAPIStatistics(GrpcAPI_CollectSDKAPIStatisticsServer) error {
	return status.Errorf(codes.Unimplemented, "method CollectSDKAPIStatistics not implemented")
}
func (*UnimplementedGrpcAPIServer) CollectServiceStatistics(GrpcAPI_CollectServiceStatisticsServer) error {
	return status.Errorf(codes.Unimplemented, "method CollectServiceStatistics not implemented")
}
func (*UnimplementedGrpcAPIServer) CollectSDKConfiguration(context.Context, *SDKConfig) (*StatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CollectSDKConfiguration not implemented")
}
func (*UnimplementedGrpcAPIServer) CollectSDKCache(GrpcAPI_CollectSDKCacheServer) error {
	return status.Errorf(codes.Unimplemented, "method CollectSDKCache not implemented")
}
func (*UnimplementedGrpcAPIServer) CollectCircuitBreak(GrpcAPI_CollectCircuitBreakServer) error {
	return status.Errorf(codes.Unimplemented, "method CollectCircuitBreak not implemented")
}
func (*UnimplementedGrpcAPIServer) CollectPluginStatistics(GrpcAPI_CollectPluginStatisticsServer) error {
	return status.Errorf(codes.Unimplemented, "method CollectPluginStatistics not implemented")
}
func (*UnimplementedGrpcAPIServer) CollectLoadBalanceInfo(GrpcAPI_CollectLoadBalanceInfoServer) error {
	return status.Errorf(codes.Unimplemented, "method CollectLoadBalanceInfo not implemented")
}
func (*UnimplementedGrpcAPIServer) CollectRateLimitRecord(GrpcAPI_CollectRateLimitRecordServer) error {
	return status.Errorf(codes.Unimplemented, "method CollectRateLimitRecord not implemented")
}
func (*UnimplementedGrpcAPIServer) CollectRouteRecord(GrpcAPI_CollectRouteRecordServer) error {
	return status.Errorf(codes.Unimplemented, "method CollectRouteRecord not implemented")
}
func (*UnimplementedGrpcAPIServer) CollectMeshConfig(GrpcAPI_CollectMeshConfigServer) error {
	return status.Errorf(codes.Unimplemented, "method CollectMeshConfig not implemented")
}
func (*UnimplementedGrpcAPIServer) CollectMeshResource(GrpcAPI_CollectMeshResourceServer) error {
	return status.Errorf(codes.Unimplemented, "method CollectMeshResource not implemented")
}
func (*UnimplementedGrpcAPIServer) AggregateServiceStatistics(context.Context, *ServiceStatistics) (*StatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AggregateServiceStatistics not implemented")
}
func (*UnimplementedGrpcAPIServer) AggregateBatchServiceStatistics(context.Context, *BatchServiceStatistics) (*StatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AggregateBatchServiceStatistics not implemented")
}

func RegisterGrpcAPIServer(s *grpc.Server, srv GrpcAPIServer) {
	s.RegisterService(&_GrpcAPI_serviceDesc, srv)
}

func _GrpcAPI_CollectServerStatistics_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(GrpcAPIServer).CollectServerStatistics(&grpcAPICollectServerStatisticsServer{stream})
}

type GrpcAPI_CollectServerStatisticsServer interface {
	Send(*StatResponse) error
	Recv() (*ServerStatistics, error)
	grpc.ServerStream
}

type grpcAPICollectServerStatisticsServer struct {
	grpc.ServerStream
}

func (x *grpcAPICollectServerStatisticsServer) Send(m *StatResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *grpcAPICollectServerStatisticsServer) Recv() (*ServerStatistics, error) {
	m := new(ServerStatistics)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _GrpcAPI_CollectSDKAPIStatistics_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(GrpcAPIServer).CollectSDKAPIStatistics(&grpcAPICollectSDKAPIStatisticsServer{stream})
}

type GrpcAPI_CollectSDKAPIStatisticsServer interface {
	Send(*StatResponse) error
	Recv() (*SDKAPIStatistics, error)
	grpc.ServerStream
}

type grpcAPICollectSDKAPIStatisticsServer struct {
	grpc.ServerStream
}

func (x *grpcAPICollectSDKAPIStatisticsServer) Send(m *StatResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *grpcAPICollectSDKAPIStatisticsServer) Recv() (*SDKAPIStatistics, error) {
	m := new(SDKAPIStatistics)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _GrpcAPI_CollectServiceStatistics_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(GrpcAPIServer).CollectServiceStatistics(&grpcAPICollectServiceStatisticsServer{stream})
}

type GrpcAPI_CollectServiceStatisticsServer interface {
	Send(*StatResponse) error
	Recv() (*ServiceStatistics, error)
	grpc.ServerStream
}

type grpcAPICollectServiceStatisticsServer struct {
	grpc.ServerStream
}

func (x *grpcAPICollectServiceStatisticsServer) Send(m *StatResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *grpcAPICollectServiceStatisticsServer) Recv() (*ServiceStatistics, error) {
	m := new(ServiceStatistics)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _GrpcAPI_CollectSDKConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SDKConfig)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GrpcAPIServer).CollectSDKConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/v1.GrpcAPI/CollectSDKConfiguration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GrpcAPIServer).CollectSDKConfiguration(ctx, req.(*SDKConfig))
	}
	return interceptor(ctx, in, info, handler)
}

func _GrpcAPI_CollectSDKCache_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(GrpcAPIServer).CollectSDKCache(&grpcAPICollectSDKCacheServer{stream})
}

type GrpcAPI_CollectSDKCacheServer interface {
	Send(*StatResponse) error
	Recv() (*ServiceInfo, error)
	grpc.ServerStream
}

type grpcAPICollectSDKCacheServer struct {
	grpc.ServerStream
}

func (x *grpcAPICollectSDKCacheServer) Send(m *StatResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *grpcAPICollectSDKCacheServer) Recv() (*ServiceInfo, error) {
	m := new(ServiceInfo)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _GrpcAPI_CollectCircuitBreak_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(GrpcAPIServer).CollectCircuitBreak(&grpcAPICollectCircuitBreakServer{stream})
}

type GrpcAPI_CollectCircuitBreakServer interface {
	Send(*StatResponse) error
	Recv() (*ServiceCircuitbreak, error)
	grpc.ServerStream
}

type grpcAPICollectCircuitBreakServer struct {
	grpc.ServerStream
}

func (x *grpcAPICollectCircuitBreakServer) Send(m *StatResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *grpcAPICollectCircuitBreakServer) Recv() (*ServiceCircuitbreak, error) {
	m := new(ServiceCircuitbreak)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _GrpcAPI_CollectPluginStatistics_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(GrpcAPIServer).CollectPluginStatistics(&grpcAPICollectPluginStatisticsServer{stream})
}

type GrpcAPI_CollectPluginStatisticsServer interface {
	Send(*StatResponse) error
	Recv() (*PluginAPIStatistics, error)
	grpc.ServerStream
}

type grpcAPICollectPluginStatisticsServer struct {
	grpc.ServerStream
}

func (x *grpcAPICollectPluginStatisticsServer) Send(m *StatResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *grpcAPICollectPluginStatisticsServer) Recv() (*PluginAPIStatistics, error) {
	m := new(PluginAPIStatistics)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _GrpcAPI_CollectLoadBalanceInfo_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(GrpcAPIServer).CollectLoadBalanceInfo(&grpcAPICollectLoadBalanceInfoServer{stream})
}

type GrpcAPI_CollectLoadBalanceInfoServer interface {
	Send(*StatResponse) error
	Recv() (*ServiceLoadBalanceInfo, error)
	grpc.ServerStream
}

type grpcAPICollectLoadBalanceInfoServer struct {
	grpc.ServerStream
}

func (x *grpcAPICollectLoadBalanceInfoServer) Send(m *StatResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *grpcAPICollectLoadBalanceInfoServer) Recv() (*ServiceLoadBalanceInfo, error) {
	m := new(ServiceLoadBalanceInfo)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _GrpcAPI_CollectRateLimitRecord_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(GrpcAPIServer).CollectRateLimitRecord(&grpcAPICollectRateLimitRecordServer{stream})
}

type GrpcAPI_CollectRateLimitRecordServer interface {
	Send(*StatResponse) error
	Recv() (*RateLimitRecord, error)
	grpc.ServerStream
}

type grpcAPICollectRateLimitRecordServer struct {
	grpc.ServerStream
}

func (x *grpcAPICollectRateLimitRecordServer) Send(m *StatResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *grpcAPICollectRateLimitRecordServer) Recv() (*RateLimitRecord, error) {
	m := new(RateLimitRecord)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _GrpcAPI_CollectRouteRecord_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(GrpcAPIServer).CollectRouteRecord(&grpcAPICollectRouteRecordServer{stream})
}

type GrpcAPI_CollectRouteRecordServer interface {
	Send(*StatResponse) error
	Recv() (*ServiceRouteRecord, error)
	grpc.ServerStream
}

type grpcAPICollectRouteRecordServer struct {
	grpc.ServerStream
}

func (x *grpcAPICollectRouteRecordServer) Send(m *StatResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *grpcAPICollectRouteRecordServer) Recv() (*ServiceRouteRecord, error) {
	m := new(ServiceRouteRecord)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _GrpcAPI_CollectMeshConfig_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(GrpcAPIServer).CollectMeshConfig(&grpcAPICollectMeshConfigServer{stream})
}

type GrpcAPI_CollectMeshConfigServer interface {
	Send(*StatResponse) error
	Recv() (*MeshInfo, error)
	grpc.ServerStream
}

type grpcAPICollectMeshConfigServer struct {
	grpc.ServerStream
}

func (x *grpcAPICollectMeshConfigServer) Send(m *StatResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *grpcAPICollectMeshConfigServer) Recv() (*MeshInfo, error) {
	m := new(MeshInfo)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _GrpcAPI_CollectMeshResource_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(GrpcAPIServer).CollectMeshResource(&grpcAPICollectMeshResourceServer{stream})
}

type GrpcAPI_CollectMeshResourceServer interface {
	Send(*StatResponse) error
	Recv() (*MeshResourceInfo, error)
	grpc.ServerStream
}

type grpcAPICollectMeshResourceServer struct {
	grpc.ServerStream
}

func (x *grpcAPICollectMeshResourceServer) Send(m *StatResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *grpcAPICollectMeshResourceServer) Recv() (*MeshResourceInfo, error) {
	m := new(MeshResourceInfo)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _GrpcAPI_AggregateServiceStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServiceStatistics)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GrpcAPIServer).AggregateServiceStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/v1.GrpcAPI/AggregateServiceStatistics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GrpcAPIServer).AggregateServiceStatistics(ctx, req.(*ServiceStatistics))
	}
	return interceptor(ctx, in, info, handler)
}

func _GrpcAPI_AggregateBatchServiceStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchServiceStatistics)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GrpcAPIServer).AggregateBatchServiceStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/v1.GrpcAPI/AggregateBatchServiceStatistics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GrpcAPIServer).AggregateBatchServiceStatistics(ctx, req.(*BatchServiceStatistics))
	}
	return interceptor(ctx, in, info, handler)
}

var _GrpcAPI_serviceDesc = grpc.ServiceDesc{
	ServiceName: "v1.GrpcAPI",
	HandlerType: (*GrpcAPIServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CollectSDKConfiguration",
			Handler:    _GrpcAPI_CollectSDKConfiguration_Handler,
		},
		{
			MethodName: "AggregateServiceStatistics",
			Handler:    _GrpcAPI_AggregateServiceStatistics_Handler,
		},
		{
			MethodName: "AggregateBatchServiceStatistics",
			Handler:    _GrpcAPI_AggregateBatchServiceStatistics_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "CollectServerStatistics",
			Handler:       _GrpcAPI_CollectServerStatistics_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "CollectSDKAPIStatistics",
			Handler:       _GrpcAPI_CollectSDKAPIStatistics_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "CollectServiceStatistics",
			Handler:       _GrpcAPI_CollectServiceStatistics_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "CollectSDKCache",
			Handler:       _GrpcAPI_CollectSDKCache_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "CollectCircuitBreak",
			Handler:       _GrpcAPI_CollectCircuitBreak_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "CollectPluginStatistics",
			Handler:       _GrpcAPI_CollectPluginStatistics_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "CollectLoadBalanceInfo",
			Handler:       _GrpcAPI_CollectLoadBalanceInfo_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "CollectRateLimitRecord",
			Handler:       _GrpcAPI_CollectRateLimitRecord_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "CollectRouteRecord",
			Handler:       _GrpcAPI_CollectRouteRecord_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "CollectMeshConfig",
			Handler:       _GrpcAPI_CollectMeshConfig_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "CollectMeshResource",
			Handler:       _GrpcAPI_CollectMeshResource_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "monitor/polaris/v1/grpcapi.proto",
}
