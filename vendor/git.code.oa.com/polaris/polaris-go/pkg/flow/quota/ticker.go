/**
 * <PERSON><PERSON> is pleased to support the open source community by making CL5 available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */

package quota

import (
	"git.code.oa.com/polaris/polaris-go/pkg/algorithm/rand"
	"git.code.oa.com/polaris/polaris-go/pkg/config"
	"git.code.oa.com/polaris/polaris-go/pkg/flow/data"
	"git.code.oa.com/polaris/polaris-go/pkg/log"
	"git.code.oa.com/polaris/polaris-go/pkg/model"
	"git.code.oa.com/polaris/polaris-go/pkg/plugin"
	"git.code.oa.com/polaris/polaris-go/pkg/plugin/localregistry"
	"git.code.oa.com/polaris/polaris-go/pkg/plugin/serverconnector"
	"time"
)

//远程配额查询任务
type RemoteQuotaCallBack struct {
	registry             localregistry.InstancesRegistry
	asyncRLimitConnector serverconnector.AsyncRateLimitConnector
	engine               model.Engine
	scalableRand         *rand.ScalableRand
}

//创建查询任务
func NewRemoteQuotaCallback(cfg config.Configuration, supplier plugin.Supplier,
	engine model.Engine) (*RemoteQuotaCallBack, error) {
	registry, err := data.GetRegistry(cfg, supplier)
	if nil != err {
		return nil, err
	}
	connector, err := data.GetServerConnector(cfg, supplier)
	if nil != err {
		return nil, err
	}
	return &RemoteQuotaCallBack{
		scalableRand:         rand.NewScalableRand(),
		registry:             registry,
		asyncRLimitConnector: connector.GetAsyncRateLimitConnector(),
		engine:               engine}, nil
}

const (
	intervalMinMilli   = 30
	intervalRangeMilli = 20
)

//处理远程配额查询任务
func (r *RemoteQuotaCallBack) Process(
	taskKey interface{}, taskValue interface{}, lastProcessTime time.Time) model.TaskResult {
	rateLimitWindow := taskValue.(*RateLimitWindow)
	reportInterval := int64(r.scalableRand.Intn(intervalRangeMilli) + intervalMinMilli)
	nowMilli := model.CurrentMillisecond()
	lastProcessMilli := lastProcessTime.UnixNano() / 1e6
	// 如果是Initializing状态，不退出。查看是否有发初始化请求，没有的话立刻发请求
	if lastProcessMilli > 0 && nowMilli-lastProcessMilli < reportInterval &&
		rateLimitWindow.GetStatus() != Initializing {
		return model.SKIP
	}
	//尝试触发一次清理
	rateLimitWindow.WindowSet.PurgeWindows(nowMilli)
	//访问缓存一次，触发一次过期校验
	taskWindow := rateLimitWindow.WindowSet.GetRateWindowByID(rateLimitWindow.ID)
	//规则变更触发的删除
	if taskWindow == nil {
		log.GetBaseLogger().Infof("[RateLimit]window %s of id %d deleted, start terminate task",
			rateLimitWindow.uniqueKey, rateLimitWindow.ID)
		return model.TERMINATE
	}

	remoteStatus := rateLimitWindow.AsyncRateLimitConnector().
		CheckRemoteWindowStatus(rateLimitWindow.communicationRecord, nowMilli)

	switch remoteStatus {
	// 如果需要重新初始化，那么设置 window 的状态为Initializing
	case serverconnector.NeedInitialization:
		rateLimitWindow.SetStatus(Initializing)
		// 如果要继续等待 server 的回应，直接返回
	case serverconnector.WaitingResponse:
		return model.CONTINUE
	}

	//状态机
	switch rateLimitWindow.GetStatus() {
	case Created:
		break
	case Deleted:
		break
	case Initializing:
		rateLimitWindow.DoAsyncRemoteInit()
	default:
		if err := rateLimitWindow.DoAsyncRemoteAcquire(); nil != err {
			rateLimitWindow.SetStatus(Initializing)
		}
	}
	return model.CONTINUE
}

//OnTaskEvent 任务事件回调
func (a *RemoteQuotaCallBack) OnTaskEvent(event model.TaskEvent) {

}
