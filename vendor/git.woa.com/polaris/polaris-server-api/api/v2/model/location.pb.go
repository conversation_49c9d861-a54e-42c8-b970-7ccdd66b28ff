// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v2/model/location.proto

package model

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 位置信息
type Location struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region   string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	Zone     string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	Campus   string `protobuf:"bytes,3,opt,name=campus,proto3" json:"campus,omitempty"`
	RegionID uint32 `protobuf:"varint,4,opt,name=regionID,proto3" json:"regionID,omitempty"`
	ZoneID   uint32 `protobuf:"varint,5,opt,name=zoneID,proto3" json:"zoneID,omitempty"`
	CampusID uint32 `protobuf:"varint,6,opt,name=campusID,proto3" json:"campusID,omitempty"`
}

func (x *Location) Reset() {
	*x = Location{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v2_model_location_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Location) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Location) ProtoMessage() {}

func (x *Location) ProtoReflect() protoreflect.Message {
	mi := &file_v2_model_location_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Location.ProtoReflect.Descriptor instead.
func (*Location) Descriptor() ([]byte, []int) {
	return file_v2_model_location_proto_rawDescGZIP(), []int{0}
}

func (x *Location) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *Location) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *Location) GetCampus() string {
	if x != nil {
		return x.Campus
	}
	return ""
}

func (x *Location) GetRegionID() uint32 {
	if x != nil {
		return x.RegionID
	}
	return 0
}

func (x *Location) GetZoneID() uint32 {
	if x != nil {
		return x.ZoneID
	}
	return 0
}

func (x *Location) GetCampusID() uint32 {
	if x != nil {
		return x.CampusID
	}
	return 0
}

var File_v2_model_location_proto protoreflect.FileDescriptor

var file_v2_model_location_proto_rawDesc = []byte{
	0x0a, 0x17, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x9e, 0x01, 0x0a, 0x08, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x70, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x61, 0x6d, 0x70, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x7a, 0x6f, 0x6e, 0x65, 0x49, 0x44, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x7a, 0x6f, 0x6e, 0x65, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x61, 0x6d, 0x70, 0x75, 0x73, 0x49, 0x44, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x63, 0x61, 0x6d, 0x70, 0x75, 0x73, 0x49, 0x44, 0x42, 0x35, 0x5a, 0x33, 0x67, 0x69, 0x74, 0x2e,
	0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v2_model_location_proto_rawDescOnce sync.Once
	file_v2_model_location_proto_rawDescData = file_v2_model_location_proto_rawDesc
)

func file_v2_model_location_proto_rawDescGZIP() []byte {
	file_v2_model_location_proto_rawDescOnce.Do(func() {
		file_v2_model_location_proto_rawDescData = protoimpl.X.CompressGZIP(file_v2_model_location_proto_rawDescData)
	})
	return file_v2_model_location_proto_rawDescData
}

var file_v2_model_location_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_v2_model_location_proto_goTypes = []interface{}{
	(*Location)(nil), // 0: api.v2.model.Location
}
var file_v2_model_location_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_v2_model_location_proto_init() }
func file_v2_model_location_proto_init() {
	if File_v2_model_location_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_v2_model_location_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Location); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v2_model_location_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v2_model_location_proto_goTypes,
		DependencyIndexes: file_v2_model_location_proto_depIdxs,
		MessageInfos:      file_v2_model_location_proto_msgTypes,
	}.Build()
	File_v2_model_location_proto = out.File
	file_v2_model_location_proto_rawDesc = nil
	file_v2_model_location_proto_goTypes = nil
	file_v2_model_location_proto_depIdxs = nil
}
