// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v1/model/dynamicweightapi.proto

package polarismodel_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DynamicWeightKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Service   string `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
}

func (x *DynamicWeightKey) Reset() {
	*x = DynamicWeightKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_dynamicweightapi_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicWeightKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicWeightKey) ProtoMessage() {}

func (x *DynamicWeightKey) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_dynamicweightapi_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicWeightKey.ProtoReflect.Descriptor instead.
func (*DynamicWeightKey) Descriptor() ([]byte, []int) {
	return file_v1_model_dynamicweightapi_proto_rawDescGZIP(), []int{0}
}

func (x *DynamicWeightKey) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *DynamicWeightKey) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

type DynamicWeightNodeKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host  string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Port  uint32 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	VpcId string `protobuf:"bytes,3,opt,name=vpcId,proto3" json:"vpcId,omitempty"`
}

func (x *DynamicWeightNodeKey) Reset() {
	*x = DynamicWeightNodeKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_dynamicweightapi_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicWeightNodeKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicWeightNodeKey) ProtoMessage() {}

func (x *DynamicWeightNodeKey) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_dynamicweightapi_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicWeightNodeKey.ProtoReflect.Descriptor instead.
func (*DynamicWeightNodeKey) Descriptor() ([]byte, []int) {
	return file_v1_model_dynamicweightapi_proto_rawDescGZIP(), []int{1}
}

func (x *DynamicWeightNodeKey) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *DynamicWeightNodeKey) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *DynamicWeightNodeKey) GetVpcId() string {
	if x != nil {
		return x.VpcId
	}
	return ""
}

type DynamicWeight struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   *DynamicWeightNodeKey `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value uint32                `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *DynamicWeight) Reset() {
	*x = DynamicWeight{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_dynamicweightapi_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicWeight) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicWeight) ProtoMessage() {}

func (x *DynamicWeight) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_dynamicweightapi_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicWeight.ProtoReflect.Descriptor instead.
func (*DynamicWeight) Descriptor() ([]byte, []int) {
	return file_v1_model_dynamicweightapi_proto_rawDescGZIP(), []int{2}
}

func (x *DynamicWeight) GetKey() *DynamicWeightNodeKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *DynamicWeight) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type DynamicWeightReportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgId      *wrapperspb.UInt64Value `protobuf:"bytes,1,opt,name=msgId,proto3" json:"msgId,omitempty"`
	Key        *DynamicWeightKey       `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	NodeKey    *DynamicWeightNodeKey   `protobuf:"bytes,3,opt,name=nodeKey,proto3" json:"nodeKey,omitempty"`
	Metrics    map[string]string       `protobuf:"bytes,4,rep,name=metrics,proto3" json:"metrics,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Token      string                  `protobuf:"bytes,5,opt,name=token,proto3" json:"token,omitempty"`
	InstanceId string                  `protobuf:"bytes,6,opt,name=instanceId,proto3" json:"instanceId,omitempty"`
}

func (x *DynamicWeightReportRequest) Reset() {
	*x = DynamicWeightReportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_dynamicweightapi_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicWeightReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicWeightReportRequest) ProtoMessage() {}

func (x *DynamicWeightReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_dynamicweightapi_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicWeightReportRequest.ProtoReflect.Descriptor instead.
func (*DynamicWeightReportRequest) Descriptor() ([]byte, []int) {
	return file_v1_model_dynamicweightapi_proto_rawDescGZIP(), []int{3}
}

func (x *DynamicWeightReportRequest) GetMsgId() *wrapperspb.UInt64Value {
	if x != nil {
		return x.MsgId
	}
	return nil
}

func (x *DynamicWeightReportRequest) GetKey() *DynamicWeightKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *DynamicWeightReportRequest) GetNodeKey() *DynamicWeightNodeKey {
	if x != nil {
		return x.NodeKey
	}
	return nil
}

func (x *DynamicWeightReportRequest) GetMetrics() map[string]string {
	if x != nil {
		return x.Metrics
	}
	return nil
}

func (x *DynamicWeightReportRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *DynamicWeightReportRequest) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

type DynamicWeightQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgId *wrapperspb.UInt64Value `protobuf:"bytes,1,opt,name=msgId,proto3" json:"msgId,omitempty"`
	Key   *DynamicWeightKey       `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *DynamicWeightQueryRequest) Reset() {
	*x = DynamicWeightQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_dynamicweightapi_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicWeightQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicWeightQueryRequest) ProtoMessage() {}

func (x *DynamicWeightQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_dynamicweightapi_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicWeightQueryRequest.ProtoReflect.Descriptor instead.
func (*DynamicWeightQueryRequest) Descriptor() ([]byte, []int) {
	return file_v1_model_dynamicweightapi_proto_rawDescGZIP(), []int{4}
}

func (x *DynamicWeightQueryRequest) GetMsgId() *wrapperspb.UInt64Value {
	if x != nil {
		return x.MsgId
	}
	return nil
}

func (x *DynamicWeightQueryRequest) GetKey() *DynamicWeightKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type DynamicWeightResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgId   *wrapperspb.UInt64Value `protobuf:"bytes,1,opt,name=msgId,proto3" json:"msgId,omitempty"`
	Code    *wrapperspb.UInt32Value `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	Info    *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	Key     *DynamicWeightKey       `protobuf:"bytes,4,opt,name=key,proto3" json:"key,omitempty"`
	Weights []*DynamicWeight        `protobuf:"bytes,5,rep,name=weights,proto3" json:"weights,omitempty"`
}

func (x *DynamicWeightResponse) Reset() {
	*x = DynamicWeightResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_dynamicweightapi_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicWeightResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicWeightResponse) ProtoMessage() {}

func (x *DynamicWeightResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_dynamicweightapi_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicWeightResponse.ProtoReflect.Descriptor instead.
func (*DynamicWeightResponse) Descriptor() ([]byte, []int) {
	return file_v1_model_dynamicweightapi_proto_rawDescGZIP(), []int{5}
}

func (x *DynamicWeightResponse) GetMsgId() *wrapperspb.UInt64Value {
	if x != nil {
		return x.MsgId
	}
	return nil
}

func (x *DynamicWeightResponse) GetCode() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Code
	}
	return nil
}

func (x *DynamicWeightResponse) GetInfo() *wrapperspb.StringValue {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *DynamicWeightResponse) GetKey() *DynamicWeightKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *DynamicWeightResponse) GetWeights() []*DynamicWeight {
	if x != nil {
		return x.Weights
	}
	return nil
}

var File_v1_model_dynamicweightapi_proto protoreflect.FileDescriptor

var file_v1_model_dynamicweightapi_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a,
	0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x4a, 0x0a, 0x10, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x4b, 0x65, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x22, 0x54, 0x0a, 0x14, 0x44,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x4e, 0x6f, 0x64, 0x65,
	0x4b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x70, 0x63, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x70, 0x63, 0x49,
	0x64, 0x22, 0x5b, 0x0a, 0x0d, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x57, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x12, 0x34, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x4e, 0x6f, 0x64, 0x65,
	0x4b, 0x65, 0x79, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x83,
	0x03, 0x0a, 0x1a, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a,
	0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49,
	0x64, 0x12, 0x30, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x4b, 0x65, 0x79, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x3c, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x4b, 0x65, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x57, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x4e, 0x6f, 0x64, 0x65, 0x4b, 0x65, 0x79, 0x52, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x4b, 0x65,
	0x79, 0x12, 0x4f, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x1a, 0x3a, 0x0a, 0x0c, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x81, 0x01, 0x0a, 0x19, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x32, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x4b, 0x65, 0x79, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0x98, 0x02, 0x0a, 0x15, 0x44, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x57, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x35, 0x0a, 0x07,
	0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x07, 0x77, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72,
	0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x3b, 0x70, 0x6f, 0x6c, 0x61, 0x72,
	0x69, 0x73, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_v1_model_dynamicweightapi_proto_rawDescOnce sync.Once
	file_v1_model_dynamicweightapi_proto_rawDescData = file_v1_model_dynamicweightapi_proto_rawDesc
)

func file_v1_model_dynamicweightapi_proto_rawDescGZIP() []byte {
	file_v1_model_dynamicweightapi_proto_rawDescOnce.Do(func() {
		file_v1_model_dynamicweightapi_proto_rawDescData = protoimpl.X.CompressGZIP(file_v1_model_dynamicweightapi_proto_rawDescData)
	})
	return file_v1_model_dynamicweightapi_proto_rawDescData
}

var file_v1_model_dynamicweightapi_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_v1_model_dynamicweightapi_proto_goTypes = []interface{}{
	(*DynamicWeightKey)(nil),           // 0: api.v1.model.DynamicWeightKey
	(*DynamicWeightNodeKey)(nil),       // 1: api.v1.model.DynamicWeightNodeKey
	(*DynamicWeight)(nil),              // 2: api.v1.model.DynamicWeight
	(*DynamicWeightReportRequest)(nil), // 3: api.v1.model.DynamicWeightReportRequest
	(*DynamicWeightQueryRequest)(nil),  // 4: api.v1.model.DynamicWeightQueryRequest
	(*DynamicWeightResponse)(nil),      // 5: api.v1.model.DynamicWeightResponse
	nil,                                // 6: api.v1.model.DynamicWeightReportRequest.MetricsEntry
	(*wrapperspb.UInt64Value)(nil),     // 7: google.protobuf.UInt64Value
	(*wrapperspb.UInt32Value)(nil),     // 8: google.protobuf.UInt32Value
	(*wrapperspb.StringValue)(nil),     // 9: google.protobuf.StringValue
}
var file_v1_model_dynamicweightapi_proto_depIdxs = []int32{
	1,  // 0: api.v1.model.DynamicWeight.key:type_name -> api.v1.model.DynamicWeightNodeKey
	7,  // 1: api.v1.model.DynamicWeightReportRequest.msgId:type_name -> google.protobuf.UInt64Value
	0,  // 2: api.v1.model.DynamicWeightReportRequest.key:type_name -> api.v1.model.DynamicWeightKey
	1,  // 3: api.v1.model.DynamicWeightReportRequest.nodeKey:type_name -> api.v1.model.DynamicWeightNodeKey
	6,  // 4: api.v1.model.DynamicWeightReportRequest.metrics:type_name -> api.v1.model.DynamicWeightReportRequest.MetricsEntry
	7,  // 5: api.v1.model.DynamicWeightQueryRequest.msgId:type_name -> google.protobuf.UInt64Value
	0,  // 6: api.v1.model.DynamicWeightQueryRequest.key:type_name -> api.v1.model.DynamicWeightKey
	7,  // 7: api.v1.model.DynamicWeightResponse.msgId:type_name -> google.protobuf.UInt64Value
	8,  // 8: api.v1.model.DynamicWeightResponse.code:type_name -> google.protobuf.UInt32Value
	9,  // 9: api.v1.model.DynamicWeightResponse.info:type_name -> google.protobuf.StringValue
	0,  // 10: api.v1.model.DynamicWeightResponse.key:type_name -> api.v1.model.DynamicWeightKey
	2,  // 11: api.v1.model.DynamicWeightResponse.weights:type_name -> api.v1.model.DynamicWeight
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_v1_model_dynamicweightapi_proto_init() }
func file_v1_model_dynamicweightapi_proto_init() {
	if File_v1_model_dynamicweightapi_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_v1_model_dynamicweightapi_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicWeightKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_dynamicweightapi_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicWeightNodeKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_dynamicweightapi_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicWeight); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_dynamicweightapi_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicWeightReportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_dynamicweightapi_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicWeightQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_dynamicweightapi_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicWeightResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_model_dynamicweightapi_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v1_model_dynamicweightapi_proto_goTypes,
		DependencyIndexes: file_v1_model_dynamicweightapi_proto_depIdxs,
		MessageInfos:      file_v1_model_dynamicweightapi_proto_msgTypes,
	}.Build()
	File_v1_model_dynamicweightapi_proto = out.File
	file_v1_model_dynamicweightapi_proto_rawDesc = nil
	file_v1_model_dynamicweightapi_proto_goTypes = nil
	file_v1_model_dynamicweightapi_proto_depIdxs = nil
}
