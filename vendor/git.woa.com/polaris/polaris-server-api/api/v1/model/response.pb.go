// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v1/model/response.proto

package polarismodel_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DiscoverResponse_DiscoverResponseType int32

const (
	DiscoverResponse_UNKNOWN            DiscoverResponse_DiscoverResponseType = 0
	DiscoverResponse_INSTANCE           DiscoverResponse_DiscoverResponseType = 1
	DiscoverResponse_CLUSTER            DiscoverResponse_DiscoverResponseType = 2
	DiscoverResponse_ROUTING            DiscoverResponse_DiscoverResponseType = 3
	DiscoverResponse_RATE_LIMIT         DiscoverResponse_DiscoverResponseType = 4
	DiscoverResponse_CIRCUIT_BREAKER    DiscoverResponse_DiscoverResponseType = 5
	DiscoverResponse_SERVICES           DiscoverResponse_DiscoverResponseType = 6
	DiscoverResponse_MESH               DiscoverResponse_DiscoverResponseType = 7
	DiscoverResponse_MESH_CONFIG        DiscoverResponse_DiscoverResponseType = 8
	DiscoverResponse_FLUX_DBREFRESH     DiscoverResponse_DiscoverResponseType = 9
	DiscoverResponse_FLUX_SDK           DiscoverResponse_DiscoverResponseType = 10
	DiscoverResponse_FLUX_SERVER        DiscoverResponse_DiscoverResponseType = 11
	DiscoverResponse_CIRCUIT_BREAKER_V2 DiscoverResponse_DiscoverResponseType = 13
	DiscoverResponse_INSTANCE_RAW       DiscoverResponse_DiscoverResponseType = 14
)

// Enum value maps for DiscoverResponse_DiscoverResponseType.
var (
	DiscoverResponse_DiscoverResponseType_name = map[int32]string{
		0:  "UNKNOWN",
		1:  "INSTANCE",
		2:  "CLUSTER",
		3:  "ROUTING",
		4:  "RATE_LIMIT",
		5:  "CIRCUIT_BREAKER",
		6:  "SERVICES",
		7:  "MESH",
		8:  "MESH_CONFIG",
		9:  "FLUX_DBREFRESH",
		10: "FLUX_SDK",
		11: "FLUX_SERVER",
		13: "CIRCUIT_BREAKER_V2",
		14: "INSTANCE_RAW",
	}
	DiscoverResponse_DiscoverResponseType_value = map[string]int32{
		"UNKNOWN":            0,
		"INSTANCE":           1,
		"CLUSTER":            2,
		"ROUTING":            3,
		"RATE_LIMIT":         4,
		"CIRCUIT_BREAKER":    5,
		"SERVICES":           6,
		"MESH":               7,
		"MESH_CONFIG":        8,
		"FLUX_DBREFRESH":     9,
		"FLUX_SDK":           10,
		"FLUX_SERVER":        11,
		"CIRCUIT_BREAKER_V2": 13,
		"INSTANCE_RAW":       14,
	}
)

func (x DiscoverResponse_DiscoverResponseType) Enum() *DiscoverResponse_DiscoverResponseType {
	p := new(DiscoverResponse_DiscoverResponseType)
	*p = x
	return p
}

func (x DiscoverResponse_DiscoverResponseType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DiscoverResponse_DiscoverResponseType) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_model_response_proto_enumTypes[0].Descriptor()
}

func (DiscoverResponse_DiscoverResponseType) Type() protoreflect.EnumType {
	return &file_v1_model_response_proto_enumTypes[0]
}

func (x DiscoverResponse_DiscoverResponseType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DiscoverResponse_DiscoverResponseType.Descriptor instead.
func (DiscoverResponse_DiscoverResponseType) EnumDescriptor() ([]byte, []int) {
	return file_v1_model_response_proto_rawDescGZIP(), []int{4, 0}
}

type SimpleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code *wrapperspb.UInt32Value `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Info *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *SimpleResponse) Reset() {
	*x = SimpleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_response_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimpleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimpleResponse) ProtoMessage() {}

func (x *SimpleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_response_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimpleResponse.ProtoReflect.Descriptor instead.
func (*SimpleResponse) Descriptor() ([]byte, []int) {
	return file_v1_model_response_proto_rawDescGZIP(), []int{0}
}

func (x *SimpleResponse) GetCode() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Code
	}
	return nil
}

func (x *SimpleResponse) GetInfo() *wrapperspb.StringValue {
	if x != nil {
		return x.Info
	}
	return nil
}

type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code                     *wrapperspb.UInt32Value   `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Info                     *wrapperspb.StringValue   `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	Client                   *Client                   `protobuf:"bytes,3,opt,name=client,proto3" json:"client,omitempty"`
	Namespace                *Namespace                `protobuf:"bytes,4,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Service                  *Service                  `protobuf:"bytes,5,opt,name=service,proto3" json:"service,omitempty"`
	Instance                 *Instance                 `protobuf:"bytes,6,opt,name=instance,proto3" json:"instance,omitempty"`
	Routing                  *Routing                  `protobuf:"bytes,7,opt,name=routing,proto3" json:"routing,omitempty"`
	Alias                    *ServiceAlias             `protobuf:"bytes,8,opt,name=alias,proto3" json:"alias,omitempty"`
	RateLimit                *Rule                     `protobuf:"bytes,9,opt,name=rateLimit,proto3" json:"rateLimit,omitempty"`
	CircuitBreaker           *CircuitBreaker           `protobuf:"bytes,10,opt,name=circuitBreaker,proto3" json:"circuitBreaker,omitempty"`
	ConfigRelease            *ConfigRelease            `protobuf:"bytes,11,opt,name=configRelease,proto3" json:"configRelease,omitempty"`
	Mesh                     *Mesh                     `protobuf:"bytes,12,opt,name=mesh,proto3" json:"mesh,omitempty"`
	MeshResource             *MeshResource             `protobuf:"bytes,13,opt,name=meshResource,proto3" json:"meshResource,omitempty"`
	MeshService              *MeshService              `protobuf:"bytes,14,opt,name=meshService,proto3" json:"meshService,omitempty"`
	Platform                 *Platform                 `protobuf:"bytes,15,opt,name=platform,proto3" json:"platform,omitempty"`
	FluxServerRateLimitRule  *FluxServerRateLimitRule  `protobuf:"bytes,16,opt,name=fluxServerRateLimitRule,proto3" json:"fluxServerRateLimitRule,omitempty"`
	FluxSDKRateLimitRule     *FluxSDKRateLimitRule     `protobuf:"bytes,17,opt,name=fluxSDKRateLimitRule,proto3" json:"fluxSDKRateLimitRule,omitempty"`
	FluxConsoleRateLimitRule *FluxConsoleRateLimitRule `protobuf:"bytes,18,opt,name=fluxConsoleRateLimitRule,proto3" json:"fluxConsoleRateLimitRule,omitempty"`
	ServiceDomain            *ServiceDomain            `protobuf:"bytes,19,opt,name=serviceDomain,proto3" json:"serviceDomain,omitempty"`
	CircuitBreakerV2         *CircuitBreakerV2         `protobuf:"bytes,25,opt,name=circuit_breaker_v2,json=circuitBreakerV2,proto3" json:"circuit_breaker_v2,omitempty"`
}

func (x *Response) Reset() {
	*x = Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_response_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_response_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_v1_model_response_proto_rawDescGZIP(), []int{1}
}

func (x *Response) GetCode() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Code
	}
	return nil
}

func (x *Response) GetInfo() *wrapperspb.StringValue {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *Response) GetClient() *Client {
	if x != nil {
		return x.Client
	}
	return nil
}

func (x *Response) GetNamespace() *Namespace {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *Response) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *Response) GetInstance() *Instance {
	if x != nil {
		return x.Instance
	}
	return nil
}

func (x *Response) GetRouting() *Routing {
	if x != nil {
		return x.Routing
	}
	return nil
}

func (x *Response) GetAlias() *ServiceAlias {
	if x != nil {
		return x.Alias
	}
	return nil
}

func (x *Response) GetRateLimit() *Rule {
	if x != nil {
		return x.RateLimit
	}
	return nil
}

func (x *Response) GetCircuitBreaker() *CircuitBreaker {
	if x != nil {
		return x.CircuitBreaker
	}
	return nil
}

func (x *Response) GetConfigRelease() *ConfigRelease {
	if x != nil {
		return x.ConfigRelease
	}
	return nil
}

func (x *Response) GetMesh() *Mesh {
	if x != nil {
		return x.Mesh
	}
	return nil
}

func (x *Response) GetMeshResource() *MeshResource {
	if x != nil {
		return x.MeshResource
	}
	return nil
}

func (x *Response) GetMeshService() *MeshService {
	if x != nil {
		return x.MeshService
	}
	return nil
}

func (x *Response) GetPlatform() *Platform {
	if x != nil {
		return x.Platform
	}
	return nil
}

func (x *Response) GetFluxServerRateLimitRule() *FluxServerRateLimitRule {
	if x != nil {
		return x.FluxServerRateLimitRule
	}
	return nil
}

func (x *Response) GetFluxSDKRateLimitRule() *FluxSDKRateLimitRule {
	if x != nil {
		return x.FluxSDKRateLimitRule
	}
	return nil
}

func (x *Response) GetFluxConsoleRateLimitRule() *FluxConsoleRateLimitRule {
	if x != nil {
		return x.FluxConsoleRateLimitRule
	}
	return nil
}

func (x *Response) GetServiceDomain() *ServiceDomain {
	if x != nil {
		return x.ServiceDomain
	}
	return nil
}

func (x *Response) GetCircuitBreakerV2() *CircuitBreakerV2 {
	if x != nil {
		return x.CircuitBreakerV2
	}
	return nil
}

type BatchWriteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      *wrapperspb.UInt32Value `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Info      *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	Size      *wrapperspb.UInt32Value `protobuf:"bytes,3,opt,name=size,proto3" json:"size,omitempty"`
	Responses []*Response             `protobuf:"bytes,4,rep,name=responses,proto3" json:"responses,omitempty"`
}

func (x *BatchWriteResponse) Reset() {
	*x = BatchWriteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_response_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchWriteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchWriteResponse) ProtoMessage() {}

func (x *BatchWriteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_response_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchWriteResponse.ProtoReflect.Descriptor instead.
func (*BatchWriteResponse) Descriptor() ([]byte, []int) {
	return file_v1_model_response_proto_rawDescGZIP(), []int{2}
}

func (x *BatchWriteResponse) GetCode() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Code
	}
	return nil
}

func (x *BatchWriteResponse) GetInfo() *wrapperspb.StringValue {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *BatchWriteResponse) GetSize() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Size
	}
	return nil
}

func (x *BatchWriteResponse) GetResponses() []*Response {
	if x != nil {
		return x.Responses
	}
	return nil
}

type BatchQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code                      *wrapperspb.UInt32Value     `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Info                      *wrapperspb.StringValue     `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	Amount                    *wrapperspb.UInt32Value     `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
	Size                      *wrapperspb.UInt32Value     `protobuf:"bytes,4,opt,name=size,proto3" json:"size,omitempty"`
	Namespaces                []*Namespace                `protobuf:"bytes,5,rep,name=namespaces,proto3" json:"namespaces,omitempty"`
	Services                  []*Service                  `protobuf:"bytes,6,rep,name=services,proto3" json:"services,omitempty"`
	Instances                 []*Instance                 `protobuf:"bytes,7,rep,name=instances,proto3" json:"instances,omitempty"`
	Routings                  []*Routing                  `protobuf:"bytes,8,rep,name=routings,proto3" json:"routings,omitempty"`
	Aliases                   []*ServiceAlias             `protobuf:"bytes,9,rep,name=aliases,proto3" json:"aliases,omitempty"`
	RateLimits                []*Rule                     `protobuf:"bytes,10,rep,name=rateLimits,proto3" json:"rateLimits,omitempty"`
	ConfigWithServices        []*ConfigWithService        `protobuf:"bytes,11,rep,name=configWithServices,proto3" json:"configWithServices,omitempty"`
	Meshes                    []*Mesh                     `protobuf:"bytes,12,rep,name=meshes,proto3" json:"meshes,omitempty"`
	MeshResources             []*MeshResource             `protobuf:"bytes,13,rep,name=meshResources,proto3" json:"meshResources,omitempty"`
	MeshServices              []*MeshService              `protobuf:"bytes,14,rep,name=meshServices,proto3" json:"meshServices,omitempty"`
	Platforms                 []*Platform                 `protobuf:"bytes,15,rep,name=platforms,proto3" json:"platforms,omitempty"`
	FluxConsoleRateLimitRules []*FluxConsoleRateLimitRule `protobuf:"bytes,16,rep,name=fluxConsoleRateLimitRules,proto3" json:"fluxConsoleRateLimitRules,omitempty"`
	ServiceDomains            []*ServiceDomain            `protobuf:"bytes,17,rep,name=serviceDomains,proto3" json:"serviceDomains,omitempty"`
}

func (x *BatchQueryResponse) Reset() {
	*x = BatchQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_response_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchQueryResponse) ProtoMessage() {}

func (x *BatchQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_response_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchQueryResponse.ProtoReflect.Descriptor instead.
func (*BatchQueryResponse) Descriptor() ([]byte, []int) {
	return file_v1_model_response_proto_rawDescGZIP(), []int{3}
}

func (x *BatchQueryResponse) GetCode() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Code
	}
	return nil
}

func (x *BatchQueryResponse) GetInfo() *wrapperspb.StringValue {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *BatchQueryResponse) GetAmount() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *BatchQueryResponse) GetSize() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Size
	}
	return nil
}

func (x *BatchQueryResponse) GetNamespaces() []*Namespace {
	if x != nil {
		return x.Namespaces
	}
	return nil
}

func (x *BatchQueryResponse) GetServices() []*Service {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *BatchQueryResponse) GetInstances() []*Instance {
	if x != nil {
		return x.Instances
	}
	return nil
}

func (x *BatchQueryResponse) GetRoutings() []*Routing {
	if x != nil {
		return x.Routings
	}
	return nil
}

func (x *BatchQueryResponse) GetAliases() []*ServiceAlias {
	if x != nil {
		return x.Aliases
	}
	return nil
}

func (x *BatchQueryResponse) GetRateLimits() []*Rule {
	if x != nil {
		return x.RateLimits
	}
	return nil
}

func (x *BatchQueryResponse) GetConfigWithServices() []*ConfigWithService {
	if x != nil {
		return x.ConfigWithServices
	}
	return nil
}

func (x *BatchQueryResponse) GetMeshes() []*Mesh {
	if x != nil {
		return x.Meshes
	}
	return nil
}

func (x *BatchQueryResponse) GetMeshResources() []*MeshResource {
	if x != nil {
		return x.MeshResources
	}
	return nil
}

func (x *BatchQueryResponse) GetMeshServices() []*MeshService {
	if x != nil {
		return x.MeshServices
	}
	return nil
}

func (x *BatchQueryResponse) GetPlatforms() []*Platform {
	if x != nil {
		return x.Platforms
	}
	return nil
}

func (x *BatchQueryResponse) GetFluxConsoleRateLimitRules() []*FluxConsoleRateLimitRule {
	if x != nil {
		return x.FluxConsoleRateLimitRules
	}
	return nil
}

func (x *BatchQueryResponse) GetServiceDomains() []*ServiceDomain {
	if x != nil {
		return x.ServiceDomains
	}
	return nil
}

type DiscoverResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code                    *wrapperspb.UInt32Value               `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Info                    *wrapperspb.StringValue               `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	Type                    DiscoverResponse_DiscoverResponseType `protobuf:"varint,3,opt,name=type,proto3,enum=api.v1.model.DiscoverResponse_DiscoverResponseType" json:"type,omitempty"`
	Service                 *Service                              `protobuf:"bytes,4,opt,name=service,proto3" json:"service,omitempty"`
	Instances               []*Instance                           `protobuf:"bytes,5,rep,name=instances,proto3" json:"instances,omitempty"`
	Routing                 *Routing                              `protobuf:"bytes,6,opt,name=routing,proto3" json:"routing,omitempty"`
	RateLimit               *RateLimit                            `protobuf:"bytes,7,opt,name=rateLimit,proto3" json:"rateLimit,omitempty"`
	CircuitBreaker          *CircuitBreaker                       `protobuf:"bytes,8,opt,name=circuitBreaker,proto3" json:"circuitBreaker,omitempty"`
	Services                []*Service                            `protobuf:"bytes,9,rep,name=services,proto3" json:"services,omitempty"`
	Mesh                    *Mesh                                 `protobuf:"bytes,10,opt,name=mesh,proto3" json:"mesh,omitempty"`
	MeshConfig              *MeshConfig                           `protobuf:"bytes,11,opt,name=meshConfig,proto3" json:"meshConfig,omitempty"`
	FluxSDKRateLimitRule    []*FluxSDKRateLimitRule               `protobuf:"bytes,12,rep,name=fluxSDKRateLimitRule,proto3" json:"fluxSDKRateLimitRule,omitempty"`
	FluxServerRateLimitRule []*FluxServerRateLimitRule            `protobuf:"bytes,13,rep,name=fluxServerRateLimitRule,proto3" json:"fluxServerRateLimitRule,omitempty"`
	CircuitBreakerV2        *CircuitBreakerV2                     `protobuf:"bytes,15,opt,name=circuit_breaker_v2,json=circuitBreakerV2,proto3" json:"circuit_breaker_v2,omitempty"`
}

func (x *DiscoverResponse) Reset() {
	*x = DiscoverResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v1_model_response_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiscoverResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscoverResponse) ProtoMessage() {}

func (x *DiscoverResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_model_response_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscoverResponse.ProtoReflect.Descriptor instead.
func (*DiscoverResponse) Descriptor() ([]byte, []int) {
	return file_v1_model_response_proto_rawDescGZIP(), []int{4}
}

func (x *DiscoverResponse) GetCode() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Code
	}
	return nil
}

func (x *DiscoverResponse) GetInfo() *wrapperspb.StringValue {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *DiscoverResponse) GetType() DiscoverResponse_DiscoverResponseType {
	if x != nil {
		return x.Type
	}
	return DiscoverResponse_UNKNOWN
}

func (x *DiscoverResponse) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *DiscoverResponse) GetInstances() []*Instance {
	if x != nil {
		return x.Instances
	}
	return nil
}

func (x *DiscoverResponse) GetRouting() *Routing {
	if x != nil {
		return x.Routing
	}
	return nil
}

func (x *DiscoverResponse) GetRateLimit() *RateLimit {
	if x != nil {
		return x.RateLimit
	}
	return nil
}

func (x *DiscoverResponse) GetCircuitBreaker() *CircuitBreaker {
	if x != nil {
		return x.CircuitBreaker
	}
	return nil
}

func (x *DiscoverResponse) GetServices() []*Service {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *DiscoverResponse) GetMesh() *Mesh {
	if x != nil {
		return x.Mesh
	}
	return nil
}

func (x *DiscoverResponse) GetMeshConfig() *MeshConfig {
	if x != nil {
		return x.MeshConfig
	}
	return nil
}

func (x *DiscoverResponse) GetFluxSDKRateLimitRule() []*FluxSDKRateLimitRule {
	if x != nil {
		return x.FluxSDKRateLimitRule
	}
	return nil
}

func (x *DiscoverResponse) GetFluxServerRateLimitRule() []*FluxServerRateLimitRule {
	if x != nil {
		return x.FluxServerRateLimitRule
	}
	return nil
}

func (x *DiscoverResponse) GetCircuitBreakerV2() *CircuitBreakerV2 {
	if x != nil {
		return x.CircuitBreakerV2
	}
	return nil
}

var File_v1_model_response_proto protoreflect.FileDescriptor

var file_v1_model_response_proto_rawDesc = []byte{
	0x0a, 0x17, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x16, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18,
	0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x72, 0x61, 0x74, 0x65, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2f, 0x63, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x63, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72,
	0x5f, 0x76, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x76, 0x31, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x66, 0x6c, 0x75, 0x78, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x72, 0x61, 0x74, 0x65,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x31, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6d,
	0x65, 0x73, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x76, 0x31, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x74, 0x0a, 0x0e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0xdd, 0x09, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x2f, 0x0a, 0x07,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x32, 0x0a,
	0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x12, 0x2f, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x30, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x52, 0x05, 0x61,
	0x6c, 0x69, 0x61, 0x73, 0x12, 0x30, 0x0a, 0x09, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x09, 0x72, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x44, 0x0a, 0x0e, 0x63, 0x69, 0x72, 0x63, 0x75, 0x69,
	0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69,
	0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x0e, 0x63, 0x69,
	0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0d,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12,
	0x26, 0x0a, 0x04, 0x6d, 0x65, 0x73, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x73,
	0x68, 0x52, 0x04, 0x6d, 0x65, 0x73, 0x68, 0x12, 0x3e, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x68, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x73,
	0x68, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0c, 0x6d, 0x65, 0x73, 0x68, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x6d, 0x65, 0x73, 0x68, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x73, 0x68,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x68, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x32, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x08,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x5f, 0x0a, 0x17, 0x66, 0x6c, 0x75, 0x78,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52,
	0x75, 0x6c, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x46, 0x6c, 0x75, 0x78, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x17, 0x66, 0x6c, 0x75, 0x78, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x56, 0x0a, 0x14, 0x66, 0x6c, 0x75,
	0x78, 0x53, 0x44, 0x4b, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c,
	0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x46, 0x6c, 0x75, 0x78, 0x53, 0x44, 0x4b, 0x52, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x14, 0x66, 0x6c, 0x75,
	0x78, 0x53, 0x44, 0x4b, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c,
	0x65, 0x12, 0x62, 0x0a, 0x18, 0x66, 0x6c, 0x75, 0x78, 0x43, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65,
	0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x46, 0x6c, 0x75, 0x78, 0x43, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x52, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x18, 0x66, 0x6c, 0x75,
	0x78, 0x43, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x4c, 0x0a, 0x12, 0x63, 0x69, 0x72, 0x63,
	0x75, 0x69, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x76, 0x32, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b,
	0x65, 0x72, 0x56, 0x32, 0x52, 0x10, 0x63, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65,
	0x61, 0x6b, 0x65, 0x72, 0x56, 0x32, 0x22, 0xe0, 0x01, 0x0a, 0x12, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x57, 0x72, 0x69, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x30, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x69, 0x6e, 0x66,
	0x6f, 0x12, 0x30, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x34, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x09,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x22, 0xfe, 0x07, 0x0a, 0x12, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x30, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04,
	0x69, 0x6e, 0x66, 0x6f, 0x12, 0x34, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x37, 0x0a, 0x0a,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x73, 0x12, 0x31, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x08,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x09, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x31,
	0x0a, 0x08, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x08, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x12, 0x34, 0x0a, 0x07, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x52, 0x07,
	0x61, 0x6c, 0x69, 0x61, 0x73, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x0a, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x4f, 0x0a, 0x12, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x57, 0x69, 0x74, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x57, 0x69, 0x74,
	0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x12, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x57, 0x69, 0x74, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x06,
	0x6d, 0x65, 0x73, 0x68, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x73, 0x68,
	0x52, 0x06, 0x6d, 0x65, 0x73, 0x68, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x0d, 0x6d, 0x65, 0x73, 0x68,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d,
	0x65, 0x73, 0x68, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0d, 0x6d, 0x65, 0x73,
	0x68, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x6d, 0x65,
	0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x4d, 0x65, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x0c, 0x6d, 0x65, 0x73,
	0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x09, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x73, 0x12,
	0x64, 0x0a, 0x19, 0x66, 0x6c, 0x75, 0x78, 0x43, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x52, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x46, 0x6c, 0x75, 0x78, 0x43, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x52, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x19, 0x66, 0x6c, 0x75, 0x78,
	0x43, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x65, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x43, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x22, 0xe9, 0x08, 0x0a, 0x10, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x30, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x30, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x69,
	0x6e, 0x66, 0x6f, 0x12, 0x47, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x07,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x34, 0x0a,
	0x09, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x72, 0x6f, 0x75,
	0x74, 0x69, 0x6e, 0x67, 0x12, 0x35, 0x0a, 0x09, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x52, 0x09, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x44, 0x0a, 0x0e, 0x63,
	0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65,
	0x72, 0x52, 0x0e, 0x63, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65,
	0x72, 0x12, 0x31, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x04, 0x6d, 0x65, 0x73, 0x68, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x4d, 0x65, 0x73, 0x68, 0x52, 0x04, 0x6d, 0x65, 0x73, 0x68, 0x12, 0x38, 0x0a, 0x0a,
	0x6d, 0x65, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x4d, 0x65, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x6d, 0x65, 0x73, 0x68,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x56, 0x0a, 0x14, 0x66, 0x6c, 0x75, 0x78, 0x53, 0x44,
	0x4b, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x46, 0x6c, 0x75, 0x78, 0x53, 0x44, 0x4b, 0x52, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x14, 0x66, 0x6c, 0x75, 0x78, 0x53, 0x44,
	0x4b, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x5f,
	0x0a, 0x17, 0x66, 0x6c, 0x75, 0x78, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x46,
	0x6c, 0x75, 0x78, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x17, 0x66, 0x6c, 0x75, 0x78, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12,
	0x4c, 0x0a, 0x12, 0x63, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b,
	0x65, 0x72, 0x5f, 0x76, 0x32, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75,
	0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x56, 0x32, 0x52, 0x10, 0x63, 0x69, 0x72,
	0x63, 0x75, 0x69, 0x74, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x56, 0x32, 0x22, 0xf6, 0x01,
	0x0a, 0x14, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x4c, 0x55, 0x53, 0x54, 0x45, 0x52, 0x10, 0x02, 0x12, 0x0b,
	0x0a, 0x07, 0x52, 0x4f, 0x55, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x52,
	0x41, 0x54, 0x45, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x43,
	0x49, 0x52, 0x43, 0x55, 0x49, 0x54, 0x5f, 0x42, 0x52, 0x45, 0x41, 0x4b, 0x45, 0x52, 0x10, 0x05,
	0x12, 0x0c, 0x0a, 0x08, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x53, 0x10, 0x06, 0x12, 0x08,
	0x0a, 0x04, 0x4d, 0x45, 0x53, 0x48, 0x10, 0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x45, 0x53, 0x48,
	0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10, 0x08, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x4c, 0x55,
	0x58, 0x5f, 0x44, 0x42, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48, 0x10, 0x09, 0x12, 0x0c, 0x0a,
	0x08, 0x46, 0x4c, 0x55, 0x58, 0x5f, 0x53, 0x44, 0x4b, 0x10, 0x0a, 0x12, 0x0f, 0x0a, 0x0b, 0x46,
	0x4c, 0x55, 0x58, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x10, 0x0b, 0x12, 0x16, 0x0a, 0x12,
	0x43, 0x49, 0x52, 0x43, 0x55, 0x49, 0x54, 0x5f, 0x42, 0x52, 0x45, 0x41, 0x4b, 0x45, 0x52, 0x5f,
	0x56, 0x32, 0x10, 0x0d, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45,
	0x5f, 0x52, 0x41, 0x57, 0x10, 0x0e, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x3b, 0x70, 0x6f,
	0x6c, 0x61, 0x72, 0x69, 0x73, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v1_model_response_proto_rawDescOnce sync.Once
	file_v1_model_response_proto_rawDescData = file_v1_model_response_proto_rawDesc
)

func file_v1_model_response_proto_rawDescGZIP() []byte {
	file_v1_model_response_proto_rawDescOnce.Do(func() {
		file_v1_model_response_proto_rawDescData = protoimpl.X.CompressGZIP(file_v1_model_response_proto_rawDescData)
	})
	return file_v1_model_response_proto_rawDescData
}

var file_v1_model_response_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_v1_model_response_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_v1_model_response_proto_goTypes = []interface{}{
	(DiscoverResponse_DiscoverResponseType)(0), // 0: api.v1.model.DiscoverResponse.DiscoverResponseType
	(*SimpleResponse)(nil),                     // 1: api.v1.model.SimpleResponse
	(*Response)(nil),                           // 2: api.v1.model.Response
	(*BatchWriteResponse)(nil),                 // 3: api.v1.model.BatchWriteResponse
	(*BatchQueryResponse)(nil),                 // 4: api.v1.model.BatchQueryResponse
	(*DiscoverResponse)(nil),                   // 5: api.v1.model.DiscoverResponse
	(*wrapperspb.UInt32Value)(nil),             // 6: google.protobuf.UInt32Value
	(*wrapperspb.StringValue)(nil),             // 7: google.protobuf.StringValue
	(*Client)(nil),                             // 8: api.v1.model.Client
	(*Namespace)(nil),                          // 9: api.v1.model.Namespace
	(*Service)(nil),                            // 10: api.v1.model.Service
	(*Instance)(nil),                           // 11: api.v1.model.Instance
	(*Routing)(nil),                            // 12: api.v1.model.Routing
	(*ServiceAlias)(nil),                       // 13: api.v1.model.ServiceAlias
	(*Rule)(nil),                               // 14: api.v1.model.Rule
	(*CircuitBreaker)(nil),                     // 15: api.v1.model.CircuitBreaker
	(*ConfigRelease)(nil),                      // 16: api.v1.model.ConfigRelease
	(*Mesh)(nil),                               // 17: api.v1.model.Mesh
	(*MeshResource)(nil),                       // 18: api.v1.model.MeshResource
	(*MeshService)(nil),                        // 19: api.v1.model.MeshService
	(*Platform)(nil),                           // 20: api.v1.model.Platform
	(*FluxServerRateLimitRule)(nil),            // 21: api.v1.model.FluxServerRateLimitRule
	(*FluxSDKRateLimitRule)(nil),               // 22: api.v1.model.FluxSDKRateLimitRule
	(*FluxConsoleRateLimitRule)(nil),           // 23: api.v1.model.FluxConsoleRateLimitRule
	(*ServiceDomain)(nil),                      // 24: api.v1.model.ServiceDomain
	(*CircuitBreakerV2)(nil),                   // 25: api.v1.model.CircuitBreakerV2
	(*ConfigWithService)(nil),                  // 26: api.v1.model.ConfigWithService
	(*RateLimit)(nil),                          // 27: api.v1.model.RateLimit
	(*MeshConfig)(nil),                         // 28: api.v1.model.MeshConfig
}
var file_v1_model_response_proto_depIdxs = []int32{
	6,  // 0: api.v1.model.SimpleResponse.code:type_name -> google.protobuf.UInt32Value
	7,  // 1: api.v1.model.SimpleResponse.info:type_name -> google.protobuf.StringValue
	6,  // 2: api.v1.model.Response.code:type_name -> google.protobuf.UInt32Value
	7,  // 3: api.v1.model.Response.info:type_name -> google.protobuf.StringValue
	8,  // 4: api.v1.model.Response.client:type_name -> api.v1.model.Client
	9,  // 5: api.v1.model.Response.namespace:type_name -> api.v1.model.Namespace
	10, // 6: api.v1.model.Response.service:type_name -> api.v1.model.Service
	11, // 7: api.v1.model.Response.instance:type_name -> api.v1.model.Instance
	12, // 8: api.v1.model.Response.routing:type_name -> api.v1.model.Routing
	13, // 9: api.v1.model.Response.alias:type_name -> api.v1.model.ServiceAlias
	14, // 10: api.v1.model.Response.rateLimit:type_name -> api.v1.model.Rule
	15, // 11: api.v1.model.Response.circuitBreaker:type_name -> api.v1.model.CircuitBreaker
	16, // 12: api.v1.model.Response.configRelease:type_name -> api.v1.model.ConfigRelease
	17, // 13: api.v1.model.Response.mesh:type_name -> api.v1.model.Mesh
	18, // 14: api.v1.model.Response.meshResource:type_name -> api.v1.model.MeshResource
	19, // 15: api.v1.model.Response.meshService:type_name -> api.v1.model.MeshService
	20, // 16: api.v1.model.Response.platform:type_name -> api.v1.model.Platform
	21, // 17: api.v1.model.Response.fluxServerRateLimitRule:type_name -> api.v1.model.FluxServerRateLimitRule
	22, // 18: api.v1.model.Response.fluxSDKRateLimitRule:type_name -> api.v1.model.FluxSDKRateLimitRule
	23, // 19: api.v1.model.Response.fluxConsoleRateLimitRule:type_name -> api.v1.model.FluxConsoleRateLimitRule
	24, // 20: api.v1.model.Response.serviceDomain:type_name -> api.v1.model.ServiceDomain
	25, // 21: api.v1.model.Response.circuit_breaker_v2:type_name -> api.v1.model.CircuitBreakerV2
	6,  // 22: api.v1.model.BatchWriteResponse.code:type_name -> google.protobuf.UInt32Value
	7,  // 23: api.v1.model.BatchWriteResponse.info:type_name -> google.protobuf.StringValue
	6,  // 24: api.v1.model.BatchWriteResponse.size:type_name -> google.protobuf.UInt32Value
	2,  // 25: api.v1.model.BatchWriteResponse.responses:type_name -> api.v1.model.Response
	6,  // 26: api.v1.model.BatchQueryResponse.code:type_name -> google.protobuf.UInt32Value
	7,  // 27: api.v1.model.BatchQueryResponse.info:type_name -> google.protobuf.StringValue
	6,  // 28: api.v1.model.BatchQueryResponse.amount:type_name -> google.protobuf.UInt32Value
	6,  // 29: api.v1.model.BatchQueryResponse.size:type_name -> google.protobuf.UInt32Value
	9,  // 30: api.v1.model.BatchQueryResponse.namespaces:type_name -> api.v1.model.Namespace
	10, // 31: api.v1.model.BatchQueryResponse.services:type_name -> api.v1.model.Service
	11, // 32: api.v1.model.BatchQueryResponse.instances:type_name -> api.v1.model.Instance
	12, // 33: api.v1.model.BatchQueryResponse.routings:type_name -> api.v1.model.Routing
	13, // 34: api.v1.model.BatchQueryResponse.aliases:type_name -> api.v1.model.ServiceAlias
	14, // 35: api.v1.model.BatchQueryResponse.rateLimits:type_name -> api.v1.model.Rule
	26, // 36: api.v1.model.BatchQueryResponse.configWithServices:type_name -> api.v1.model.ConfigWithService
	17, // 37: api.v1.model.BatchQueryResponse.meshes:type_name -> api.v1.model.Mesh
	18, // 38: api.v1.model.BatchQueryResponse.meshResources:type_name -> api.v1.model.MeshResource
	19, // 39: api.v1.model.BatchQueryResponse.meshServices:type_name -> api.v1.model.MeshService
	20, // 40: api.v1.model.BatchQueryResponse.platforms:type_name -> api.v1.model.Platform
	23, // 41: api.v1.model.BatchQueryResponse.fluxConsoleRateLimitRules:type_name -> api.v1.model.FluxConsoleRateLimitRule
	24, // 42: api.v1.model.BatchQueryResponse.serviceDomains:type_name -> api.v1.model.ServiceDomain
	6,  // 43: api.v1.model.DiscoverResponse.code:type_name -> google.protobuf.UInt32Value
	7,  // 44: api.v1.model.DiscoverResponse.info:type_name -> google.protobuf.StringValue
	0,  // 45: api.v1.model.DiscoverResponse.type:type_name -> api.v1.model.DiscoverResponse.DiscoverResponseType
	10, // 46: api.v1.model.DiscoverResponse.service:type_name -> api.v1.model.Service
	11, // 47: api.v1.model.DiscoverResponse.instances:type_name -> api.v1.model.Instance
	12, // 48: api.v1.model.DiscoverResponse.routing:type_name -> api.v1.model.Routing
	27, // 49: api.v1.model.DiscoverResponse.rateLimit:type_name -> api.v1.model.RateLimit
	15, // 50: api.v1.model.DiscoverResponse.circuitBreaker:type_name -> api.v1.model.CircuitBreaker
	10, // 51: api.v1.model.DiscoverResponse.services:type_name -> api.v1.model.Service
	17, // 52: api.v1.model.DiscoverResponse.mesh:type_name -> api.v1.model.Mesh
	28, // 53: api.v1.model.DiscoverResponse.meshConfig:type_name -> api.v1.model.MeshConfig
	22, // 54: api.v1.model.DiscoverResponse.fluxSDKRateLimitRule:type_name -> api.v1.model.FluxSDKRateLimitRule
	21, // 55: api.v1.model.DiscoverResponse.fluxServerRateLimitRule:type_name -> api.v1.model.FluxServerRateLimitRule
	25, // 56: api.v1.model.DiscoverResponse.circuit_breaker_v2:type_name -> api.v1.model.CircuitBreakerV2
	57, // [57:57] is the sub-list for method output_type
	57, // [57:57] is the sub-list for method input_type
	57, // [57:57] is the sub-list for extension type_name
	57, // [57:57] is the sub-list for extension extendee
	0,  // [0:57] is the sub-list for field type_name
}

func init() { file_v1_model_response_proto_init() }
func file_v1_model_response_proto_init() {
	if File_v1_model_response_proto != nil {
		return
	}
	file_v1_model_service_proto_init()
	file_v1_model_routing_proto_init()
	file_v1_model_client_proto_init()
	file_v1_model_ratelimit_proto_init()
	file_v1_model_circuitbreaker_proto_init()
	file_v1_model_circuitbreaker_v2_proto_init()
	file_v1_model_configrelease_proto_init()
	file_v1_model_fluxserverratelimit_proto_init()
	file_v1_model_platform_proto_init()
	file_v1_model_mesh_proto_init()
	file_v1_model_domain_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_v1_model_response_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SimpleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_response_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_response_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchWriteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_response_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v1_model_response_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiscoverResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_model_response_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v1_model_response_proto_goTypes,
		DependencyIndexes: file_v1_model_response_proto_depIdxs,
		EnumInfos:         file_v1_model_response_proto_enumTypes,
		MessageInfos:      file_v1_model_response_proto_msgTypes,
	}.Build()
	File_v1_model_response_proto = out.File
	file_v1_model_response_proto_rawDesc = nil
	file_v1_model_response_proto_goTypes = nil
	file_v1_model_response_proto_depIdxs = nil
}
