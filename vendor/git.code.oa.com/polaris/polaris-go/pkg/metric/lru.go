package metric

import (
	"container/list"
	"fmt"
	"git.code.oa.com/polaris/polaris-go/pkg/model"
	"sync"
)

var KeyNotFoundError = fmt.Errorf("Key not found.")

type EvictedFunc func(interface{}, interface{})

type ExpiredFunc func(interface{}) bool

// Discards the least recently used items first.
type LRUCache struct {
	size        int
	mu          sync.RWMutex
	items       map[interface{}]*list.Element
	evictedFunc EvictedFunc
	expiredFunc ExpiredFunc
	evictList   *list.List
}

func NewLRUCache(size int, evictedFunc EvictedFunc, expiredFunc ExpiredFunc) *LRUCache {
	cache := &LRUCache{
		size:        size,
		evictedFunc: evictedFunc,
		expiredFunc: expiredFunc,
	}
	cache.init()
	return cache
}

func (c *LRUCache) init() {
	c.evictList = list.New()
	c.items = make(map[interface{}]*list.Element, c.size+1)
}

func (c *LRUCache) set(key, value interface{}) (interface{}, error) {
	// Check for existing item
	var item *lruItem
	if it, ok := c.items[key]; ok {
		c.evictList.MoveToFront(it)
		item = it.Value.(*lruItem)
		item.value = value
	} else {
		// Verify size not exceeded
		if c.evictList.Len() >= c.size {
			c.evict(1)
		}
		item = &lruItem{
			key:   key,
			value: value,
		}
		c.items[key] = c.evictList.PushFront(item)
	}

	return item, nil
}

// set a new key-value pair
func (c *LRUCache) Set(key, value interface{}) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	_, err := c.set(key, value)
	return err
}

// Get a value from cache pool using key if it exists.
// If it dose not exists key and has LoaderFunc,
// generate a value using `LoaderFunc` method returns value.
func (c *LRUCache) Get(key interface{}) (interface{}, error) {
	return c.get(key, false)
}

// 获取一个缓存，并刷新过期时间
//func (c *LRUCache) GetWithRefresh(key interface{}) (interface{}, error) {
//	return c.get(key, true)
//}

func (c *LRUCache) get(key interface{}, refresh bool) (interface{}, error) {
	v, err := c.getValue(key)
	if err != nil {
		return nil, err
	}
	return v, nil
}

func (c *LRUCache) getValue(key interface{}) (interface{}, error) {
	c.mu.Lock()
	item, ok := c.items[key]
	if ok {
		it := item.Value.(*lruItem)
		if !c.IsExpired(it) {
			c.evictList.MoveToFront(item)
			v := it.value
			c.mu.Unlock()
			return v, nil
		}
		c.removeElement(item)
	}
	c.mu.Unlock()
	return nil, KeyNotFoundError
}

// evict removes the oldest item from the cache.
func (c *LRUCache) evict(count int) {
	for i := 0; i < count; i++ {
		ent := c.evictList.Back()
		if ent == nil {
			return
		} else {
			c.removeElement(ent)
		}
	}
}

// Has checks if key exists in cache
func (c *LRUCache) Has(key interface{}) bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.has(key, model.CurrentMillisecond())
}

func (c *LRUCache) has(key interface{}, nowMilli int64) bool {
	item, ok := c.items[key]
	if !ok {
		return false
	}
	return !c.IsExpired(item.Value.(*lruItem))
}

// Remove removes the provided key from the cache.
func (c *LRUCache) Remove(key interface{}) bool {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.remove(key)
}

func (c *LRUCache) remove(key interface{}) bool {
	if ent, ok := c.items[key]; ok {
		c.removeElement(ent)
		return true
	}
	return false
}

func (c *LRUCache) removeElement(e *list.Element) {
	c.evictList.Remove(e)
	entry := e.Value.(*lruItem)
	delete(c.items, entry.key)
	if c.evictedFunc != nil {
		entry := e.Value.(*lruItem)
		c.evictedFunc(entry.key, entry.value)
	}
}

func (c *LRUCache) keys() []interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()
	keys := make([]interface{}, len(c.items))
	var i = 0
	for k := range c.items {
		keys[i] = k
		i++
	}
	return keys
}

// GetALL returns all key-value pairs in the cache.
func (c *LRUCache) GetALL(checkExpired bool) map[interface{}]interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()
	items := make(map[interface{}]interface{}, len(c.items))
	now := model.CurrentMillisecond()
	for k, item := range c.items {
		if !checkExpired || c.has(k, now) {
			items[k] = item.Value.(*lruItem).value
		}
	}
	return items
}

// Keys returns a slice of the keys in the cache.
func (c *LRUCache) Keys(checkExpired bool) []interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()
	keys := make([]interface{}, 0, len(c.items))
	now := model.CurrentMillisecond()
	for k := range c.items {
		if !checkExpired || c.has(k, now) {
			keys = append(keys, k)
		}
	}
	return keys
}

// Len returns the number of items in the cache.
func (c *LRUCache) Len(checkExpired bool) int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	if !checkExpired {
		return len(c.items)
	}
	var length int
	now := model.CurrentMillisecond()
	for k := range c.items {
		if c.has(k, now) {
			length++
		}
	}
	return length
}

// Completely clear the cache
func (c *LRUCache) Purge() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.init()
}

func (c *LRUCache) IsExpired(it *lruItem) bool {
	if c.expiredFunc == nil {
		return false
	}
	return c.expiredFunc(it.value)
}

type lruItem struct {
	key   interface{}
	value interface{}
}

// RemoveExpiredElement remove all expired elements
func (c *LRUCache) RemoveExpiredElement() {
	c.mu.Lock()
	for _, v := range c.items {
		if c.IsExpired(v.Value.(*lruItem)) {
			c.removeElement(v)
		}
	}
	c.mu.Unlock()
}
