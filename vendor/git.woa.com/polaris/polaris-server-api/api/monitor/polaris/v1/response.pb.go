// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: monitor/polaris/v1/response.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StatResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`     // 唯一标识
	Code *wrapperspb.UInt32Value `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"` // 返回码
	Info *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"` // 返回信息
}

func (x *StatResponse) Reset() {
	*x = StatResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_polaris_v1_response_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatResponse) ProtoMessage() {}

func (x *StatResponse) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_polaris_v1_response_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatResponse.ProtoReflect.Descriptor instead.
func (*StatResponse) Descriptor() ([]byte, []int) {
	return file_monitor_polaris_v1_response_proto_rawDescGZIP(), []int{0}
}

func (x *StatResponse) GetId() *wrapperspb.StringValue {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *StatResponse) GetCode() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Code
	}
	return nil
}

func (x *StatResponse) GetInfo() *wrapperspb.StringValue {
	if x != nil {
		return x.Info
	}
	return nil
}

var File_monitor_polaris_v1_response_proto protoreflect.FileDescriptor

var file_monitor_polaris_v1_response_proto_rawDesc = []byte{
	0x0a, 0x21, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x02, 0x76, 0x31, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa0, 0x01, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x3f, 0x5a, 0x3d, 0x67, 0x69,
	0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69,
	0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_monitor_polaris_v1_response_proto_rawDescOnce sync.Once
	file_monitor_polaris_v1_response_proto_rawDescData = file_monitor_polaris_v1_response_proto_rawDesc
)

func file_monitor_polaris_v1_response_proto_rawDescGZIP() []byte {
	file_monitor_polaris_v1_response_proto_rawDescOnce.Do(func() {
		file_monitor_polaris_v1_response_proto_rawDescData = protoimpl.X.CompressGZIP(file_monitor_polaris_v1_response_proto_rawDescData)
	})
	return file_monitor_polaris_v1_response_proto_rawDescData
}

var file_monitor_polaris_v1_response_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_monitor_polaris_v1_response_proto_goTypes = []interface{}{
	(*StatResponse)(nil),           // 0: v1.StatResponse
	(*wrapperspb.StringValue)(nil), // 1: google.protobuf.StringValue
	(*wrapperspb.UInt32Value)(nil), // 2: google.protobuf.UInt32Value
}
var file_monitor_polaris_v1_response_proto_depIdxs = []int32{
	1, // 0: v1.StatResponse.id:type_name -> google.protobuf.StringValue
	2, // 1: v1.StatResponse.code:type_name -> google.protobuf.UInt32Value
	1, // 2: v1.StatResponse.info:type_name -> google.protobuf.StringValue
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_monitor_polaris_v1_response_proto_init() }
func file_monitor_polaris_v1_response_proto_init() {
	if File_monitor_polaris_v1_response_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_monitor_polaris_v1_response_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_monitor_polaris_v1_response_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_monitor_polaris_v1_response_proto_goTypes,
		DependencyIndexes: file_monitor_polaris_v1_response_proto_depIdxs,
		MessageInfos:      file_monitor_polaris_v1_response_proto_msgTypes,
	}.Build()
	File_monitor_polaris_v1_response_proto = out.File
	file_monitor_polaris_v1_response_proto_rawDesc = nil
	file_monitor_polaris_v1_response_proto_goTypes = nil
	file_monitor_polaris_v1_response_proto_depIdxs = nil
}
