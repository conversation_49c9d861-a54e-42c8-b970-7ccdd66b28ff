# git.code.oa.com/trpc-go/trpc-go v0.20.0
## explicit; go 1.18
git.code.oa.com/trpc-go/trpc-go
git.code.oa.com/trpc-go/trpc-go/admin
git.code.oa.com/trpc-go/trpc-go/client
git.code.oa.com/trpc-go/trpc-go/codec
git.code.oa.com/trpc-go/trpc-go/config
git.code.oa.com/trpc-go/trpc-go/errs
git.code.oa.com/trpc-go/trpc-go/filter
git.code.oa.com/trpc-go/trpc-go/healthcheck
git.code.oa.com/trpc-go/trpc-go/http
git.code.oa.com/trpc-go/trpc-go/internal/addrutil
git.code.oa.com/trpc-go/trpc-go/internal/atomic
git.code.oa.com/trpc-go/trpc-go/internal/attachment
git.code.oa.com/trpc-go/trpc-go/internal/bytes
git.code.oa.com/trpc-go/trpc-go/internal/codec
git.code.oa.com/trpc-go/trpc-go/internal/context
git.code.oa.com/trpc-go/trpc-go/internal/env
git.code.oa.com/trpc-go/trpc-go/internal/error
git.code.oa.com/trpc-go/trpc-go/internal/expandenv
git.code.oa.com/trpc-go/trpc-go/internal/graceful
git.code.oa.com/trpc-go/trpc-go/internal/graceful/internal
git.code.oa.com/trpc-go/trpc-go/internal/http/config
git.code.oa.com/trpc-go/trpc-go/internal/http/fastop
git.code.oa.com/trpc-go/trpc-go/internal/httprule
git.code.oa.com/trpc-go/trpc-go/internal/keeporder
git.code.oa.com/trpc-go/trpc-go/internal/keeporder/actor
git.code.oa.com/trpc-go/trpc-go/internal/local/inprocess
git.code.oa.com/trpc-go/trpc-go/internal/local/server
git.code.oa.com/trpc-go/trpc-go/internal/lru
git.code.oa.com/trpc-go/trpc-go/internal/naming
git.code.oa.com/trpc-go/trpc-go/internal/net
git.code.oa.com/trpc-go/trpc-go/internal/packetbuffer
git.code.oa.com/trpc-go/trpc-go/internal/protocol
git.code.oa.com/trpc-go/trpc-go/internal/queue
git.code.oa.com/trpc-go/trpc-go/internal/random
git.code.oa.com/trpc-go/trpc-go/internal/reflect
git.code.oa.com/trpc-go/trpc-go/internal/reflection
git.code.oa.com/trpc-go/trpc-go/internal/report
git.code.oa.com/trpc-go/trpc-go/internal/ring
git.code.oa.com/trpc-go/trpc-go/internal/rpcz
git.code.oa.com/trpc-go/trpc-go/internal/rpczenable
git.code.oa.com/trpc-go/trpc-go/internal/scope
git.code.oa.com/trpc-go/trpc-go/internal/stack
git.code.oa.com/trpc-go/trpc-go/internal/stream
git.code.oa.com/trpc-go/trpc-go/internal/tls
git.code.oa.com/trpc-go/trpc-go/internal/writev
git.code.oa.com/trpc-go/trpc-go/log
git.code.oa.com/trpc-go/trpc-go/log/internal/timeunit
git.code.oa.com/trpc-go/trpc-go/log/rollwriter
git.code.oa.com/trpc-go/trpc-go/metrics
git.code.oa.com/trpc-go/trpc-go/naming/bannednodes
git.code.oa.com/trpc-go/trpc-go/naming/circuitbreaker
git.code.oa.com/trpc-go/trpc-go/naming/discovery
git.code.oa.com/trpc-go/trpc-go/naming/loadbalance
git.code.oa.com/trpc-go/trpc-go/naming/registry
git.code.oa.com/trpc-go/trpc-go/naming/selector
git.code.oa.com/trpc-go/trpc-go/naming/servicerouter
git.code.oa.com/trpc-go/trpc-go/overloadctrl
git.code.oa.com/trpc-go/trpc-go/plugin
git.code.oa.com/trpc-go/trpc-go/pool/connpool
git.code.oa.com/trpc-go/trpc-go/pool/httppool
git.code.oa.com/trpc-go/trpc-go/pool/multiplexed
git.code.oa.com/trpc-go/trpc-go/pool/objectpool
git.code.oa.com/trpc-go/trpc-go/restful
git.code.oa.com/trpc-go/trpc-go/restful/dat
git.code.oa.com/trpc-go/trpc-go/restful/errors
git.code.oa.com/trpc-go/trpc-go/rpcz
git.code.oa.com/trpc-go/trpc-go/server
git.code.oa.com/trpc-go/trpc-go/transport
git.code.oa.com/trpc-go/trpc-go/transport/internal/bufio
git.code.oa.com/trpc-go/trpc-go/transport/internal/dialer
git.code.oa.com/trpc-go/trpc-go/transport/internal/errs
git.code.oa.com/trpc-go/trpc-go/transport/internal/frame
git.code.oa.com/trpc-go/trpc-go/transport/internal/msg
git.code.oa.com/trpc-go/trpc-go/transport/internal/pool
git.code.oa.com/trpc-go/trpc-go/transport/tnet
git.code.oa.com/trpc-go/trpc-go/transport/tnet/multiplexed
# git.woa.com/jce/jce v1.2.0
## explicit; go 1.17
git.woa.com/jce/jce
# git.woa.com/trpc-go/go_reuseport v1.7.0
## explicit; go 1.17
git.woa.com/trpc-go/go_reuseport
# git.woa.com/trpc-go/tnet v0.1.0
## explicit; go 1.18
git.woa.com/trpc-go/tnet
git.woa.com/trpc-go/tnet/internal/asynctimer
git.woa.com/trpc-go/tnet/internal/autopostpone
git.woa.com/trpc-go/tnet/internal/buffer
git.woa.com/trpc-go/tnet/internal/cache/mcache
git.woa.com/trpc-go/tnet/internal/cache/systype
git.woa.com/trpc-go/tnet/internal/iovec
git.woa.com/trpc-go/tnet/internal/locker
git.woa.com/trpc-go/tnet/internal/netutil
git.woa.com/trpc-go/tnet/internal/poller
git.woa.com/trpc-go/tnet/internal/poller/event
git.woa.com/trpc-go/tnet/internal/safejob
git.woa.com/trpc-go/tnet/internal/stat
git.woa.com/trpc-go/tnet/internal/timer
git.woa.com/trpc-go/tnet/log
git.woa.com/trpc-go/tnet/metrics
git.woa.com/trpc-go/tnet/tls
# github.com/BurntSushi/toml v0.3.1
## explicit
github.com/BurntSushi/toml
# github.com/andybalholm/brotli v1.1.0
## explicit; go 1.13
github.com/andybalholm/brotli
github.com/andybalholm/brotli/matchfinder
# github.com/fsnotify/fsnotify v1.7.0
## explicit; go 1.17
github.com/fsnotify/fsnotify
# github.com/go-playground/form/v4 v4.2.1
## explicit; go 1.13
github.com/go-playground/form/v4
# github.com/golang/mock v1.6.0
## explicit; go 1.11
github.com/golang/mock/gomock
# github.com/golang/protobuf v1.5.4
## explicit; go 1.17
github.com/golang/protobuf/jsonpb
github.com/golang/protobuf/proto
# github.com/golang/snappy v0.0.4
## explicit
github.com/golang/snappy
# github.com/google/flatbuffers v24.3.25+incompatible
## explicit
github.com/google/flatbuffers/go
# github.com/hashicorp/errwrap v1.1.0
## explicit
github.com/hashicorp/errwrap
# github.com/hashicorp/go-multierror v1.1.1
## explicit; go 1.13
github.com/hashicorp/go-multierror
# github.com/jinzhu/copier v0.4.0
## explicit; go 1.13
github.com/jinzhu/copier
# github.com/json-iterator/go v1.1.12
## explicit; go 1.12
github.com/json-iterator/go
# github.com/klauspost/compress v1.17.6
## explicit; go 1.19
github.com/klauspost/compress/flate
github.com/klauspost/compress/gzip
github.com/klauspost/compress/zlib
# github.com/lestrrat-go/strftime v1.0.6
## explicit; go 1.13
github.com/lestrrat-go/strftime
github.com/lestrrat-go/strftime/internal/errors
# github.com/mitchellh/mapstructure v1.5.0
## explicit; go 1.14
github.com/mitchellh/mapstructure
# github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd
## explicit
github.com/modern-go/concurrent
# github.com/modern-go/reflect2 v1.0.2
## explicit; go 1.12
github.com/modern-go/reflect2
# github.com/panjf2000/ants/v2 v2.10.0
## explicit; go 1.13
github.com/panjf2000/ants/v2
github.com/panjf2000/ants/v2/internal/sync
# github.com/pierrec/lz4/v4 v4.1.21
## explicit; go 1.14
github.com/pierrec/lz4/v4
github.com/pierrec/lz4/v4/internal/lz4block
github.com/pierrec/lz4/v4/internal/lz4errors
github.com/pierrec/lz4/v4/internal/lz4stream
github.com/pierrec/lz4/v4/internal/xxh32
# github.com/pkg/errors v0.9.1
## explicit
github.com/pkg/errors
# github.com/r3labs/sse/v2 v2.10.0
## explicit; go 1.13
github.com/r3labs/sse/v2
# github.com/spf13/cast v1.6.0
## explicit; go 1.19
github.com/spf13/cast
# github.com/valyala/bytebufferpool v1.0.0
## explicit
github.com/valyala/bytebufferpool
# github.com/valyala/fasthttp v1.52.0
## explicit; go 1.20
github.com/valyala/fasthttp
github.com/valyala/fasthttp/fasthttputil
github.com/valyala/fasthttp/stackless
# go.uber.org/atomic v1.11.0
## explicit; go 1.18
go.uber.org/atomic
# go.uber.org/automaxprocs v1.5.4-0.20240213192314-8553d3bb2149
## explicit; go 1.20
go.uber.org/automaxprocs/internal/cgroups
go.uber.org/automaxprocs/internal/runtime
go.uber.org/automaxprocs/maxprocs
# go.uber.org/multierr v1.7.0
## explicit; go 1.14
go.uber.org/multierr
# go.uber.org/zap v1.24.0
## explicit; go 1.19
go.uber.org/zap
go.uber.org/zap/buffer
go.uber.org/zap/internal
go.uber.org/zap/internal/bufferpool
go.uber.org/zap/internal/color
go.uber.org/zap/internal/exit
go.uber.org/zap/zapcore
# golang.org/x/net v0.23.0
## explicit; go 1.18
golang.org/x/net/context
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/h2c
golang.org/x/net/http2/hpack
golang.org/x/net/idna
# golang.org/x/sync v0.7.0
## explicit; go 1.18
golang.org/x/sync/errgroup
golang.org/x/sync/singleflight
# golang.org/x/sys v0.22.0
## explicit; go 1.18
golang.org/x/sys/cpu
golang.org/x/sys/unix
golang.org/x/sys/windows
# golang.org/x/text v0.14.0
## explicit; go 1.18
golang.org/x/text/secure/bidirule
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
# google.golang.org/protobuf v1.36.6
## explicit; go 1.22
google.golang.org/protobuf/encoding/protojson
google.golang.org/protobuf/encoding/prototext
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/descfmt
google.golang.org/protobuf/internal/descopts
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/editiondefaults
google.golang.org/protobuf/internal/editionssupport
google.golang.org/protobuf/internal/encoding/defval
google.golang.org/protobuf/internal/encoding/json
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/encoding/tag
google.golang.org/protobuf/internal/encoding/text
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/filedesc
google.golang.org/protobuf/internal/filetype
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/impl
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/protolazy
google.golang.org/protobuf/internal/set
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/internal/version
google.golang.org/protobuf/proto
google.golang.org/protobuf/reflect/protodesc
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
google.golang.org/protobuf/runtime/protoimpl
google.golang.org/protobuf/types/descriptorpb
google.golang.org/protobuf/types/gofeaturespb
google.golang.org/protobuf/types/known/durationpb
google.golang.org/protobuf/types/known/emptypb
google.golang.org/protobuf/types/known/fieldmaskpb
google.golang.org/protobuf/types/known/timestamppb
google.golang.org/protobuf/types/known/wrapperspb
# gopkg.in/cenkalti/backoff.v1 v1.1.0
## explicit
gopkg.in/cenkalti/backoff.v1
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
