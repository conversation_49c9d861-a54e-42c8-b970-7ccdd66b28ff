// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.3
// source: v2/model/service.proto

package model

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Service V2服务信息
// DiscoverType=INSTANCE返回的字段：
// name,namespace,metadata,mtime,revision
type Service struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 服务名
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 命名空间
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 服务元数据
	Metadata map[string]string `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 服务的端口，兼容存量系统
	Ports string `protobuf:"bytes,4,opt,name=ports,proto3" json:"ports,omitempty"`
	// 业务
	Business string `protobuf:"bytes,5,opt,name=business,proto3" json:"business,omitempty"`
	// 部门
	Department string `protobuf:"bytes,6,opt,name=department,proto3" json:"department,omitempty"`
	// 一级cmdb模块
	CmdbMod1 string `protobuf:"bytes,7,opt,name=cmdb_mod1,proto3" json:"cmdb_mod1,omitempty"`
	// 二级cmdb模块
	CmdbMod2 string `protobuf:"bytes,8,opt,name=cmdb_mod2,proto3" json:"cmdb_mod2,omitempty"`
	// 三级cmdb模块
	CmdbMod3 string `protobuf:"bytes,9,opt,name=cmdb_mod3,proto3" json:"cmdb_mod3,omitempty"`
	// 描述
	Comment string `protobuf:"bytes,10,opt,name=comment,proto3" json:"comment,omitempty"`
	// 负责人
	Owners string `protobuf:"bytes,11,opt,name=owners,proto3" json:"owners,omitempty"`
	// 创建时间
	Ctime int64 `protobuf:"varint,12,opt,name=ctime,proto3" json:"ctime,omitempty"`
	// 修改时间
	Mtime int64 `protobuf:"varint,13,opt,name=mtime,proto3" json:"mtime,omitempty"`
	// 版本号
	Revision string `protobuf:"bytes,14,opt,name=revision,proto3" json:"revision,omitempty"`
	// 平台ID
	PlatformId string `protobuf:"bytes,15,opt,name=platform_id,proto3" json:"platform_id,omitempty"`
	// 增量实例信息
	IncrementInstance *IncrementInstance `protobuf:"bytes,16,opt,name=increment_instance,proto3" json:"increment_instance,omitempty"`
}

func (x *Service) Reset() {
	*x = Service{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v2_model_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service) ProtoMessage() {}

func (x *Service) ProtoReflect() protoreflect.Message {
	mi := &file_v2_model_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service.ProtoReflect.Descriptor instead.
func (*Service) Descriptor() ([]byte, []int) {
	return file_v2_model_service_proto_rawDescGZIP(), []int{0}
}

func (x *Service) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Service) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *Service) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Service) GetPorts() string {
	if x != nil {
		return x.Ports
	}
	return ""
}

func (x *Service) GetBusiness() string {
	if x != nil {
		return x.Business
	}
	return ""
}

func (x *Service) GetDepartment() string {
	if x != nil {
		return x.Department
	}
	return ""
}

func (x *Service) GetCmdbMod1() string {
	if x != nil {
		return x.CmdbMod1
	}
	return ""
}

func (x *Service) GetCmdbMod2() string {
	if x != nil {
		return x.CmdbMod2
	}
	return ""
}

func (x *Service) GetCmdbMod3() string {
	if x != nil {
		return x.CmdbMod3
	}
	return ""
}

func (x *Service) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *Service) GetOwners() string {
	if x != nil {
		return x.Owners
	}
	return ""
}

func (x *Service) GetCtime() int64 {
	if x != nil {
		return x.Ctime
	}
	return 0
}

func (x *Service) GetMtime() int64 {
	if x != nil {
		return x.Mtime
	}
	return 0
}

func (x *Service) GetRevision() string {
	if x != nil {
		return x.Revision
	}
	return ""
}

func (x *Service) GetPlatformId() string {
	if x != nil {
		return x.PlatformId
	}
	return ""
}

func (x *Service) GetIncrementInstance() *IncrementInstance {
	if x != nil {
		return x.IncrementInstance
	}
	return nil
}

// IncrementInstance 增量实例信息
type IncrementInstance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//本次请求携带的tag id
	OldTag string `protobuf:"bytes,1,opt,name=old_tag,proto3" json:"old_tag,omitempty"`
	//下次请求携带的tag id
	NewTag string `protobuf:"bytes,2,opt,name=new_tag,proto3" json:"new_tag,omitempty"`
	//service本身的revision
	ServiceRevision string `protobuf:"bytes,3,opt,name=service_revision,proto3" json:"service_revision,omitempty"`
}

func (x *IncrementInstance) Reset() {
	*x = IncrementInstance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_v2_model_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncrementInstance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncrementInstance) ProtoMessage() {}

func (x *IncrementInstance) ProtoReflect() protoreflect.Message {
	mi := &file_v2_model_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncrementInstance.ProtoReflect.Descriptor instead.
func (*IncrementInstance) Descriptor() ([]byte, []int) {
	return file_v2_model_service_proto_rawDescGZIP(), []int{1}
}

func (x *IncrementInstance) GetOldTag() string {
	if x != nil {
		return x.OldTag
	}
	return ""
}

func (x *IncrementInstance) GetNewTag() string {
	if x != nil {
		return x.NewTag
	}
	return ""
}

func (x *IncrementInstance) GetServiceRevision() string {
	if x != nil {
		return x.ServiceRevision
	}
	return ""
}

var File_v2_model_service_proto protoreflect.FileDescriptor

var file_v2_model_service_proto_rawDesc = []byte{
	0x0a, 0x16, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0xd2, 0x04, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6d, 0x64, 0x62, 0x5f,
	0x6d, 0x6f, 0x64, 0x31, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6d, 0x64, 0x62,
	0x5f, 0x6d, 0x6f, 0x64, 0x31, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6d, 0x64, 0x62, 0x5f, 0x6d, 0x6f,
	0x64, 0x32, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6d, 0x64, 0x62, 0x5f, 0x6d,
	0x6f, 0x64, 0x32, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6d, 0x64, 0x62, 0x5f, 0x6d, 0x6f, 0x64, 0x33,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6d, 0x64, 0x62, 0x5f, 0x6d, 0x6f, 0x64,
	0x33, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x12, 0x4f, 0x0a,
	0x12, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x32, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x12, 0x69, 0x6e, 0x63, 0x72,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x3b,
	0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x73, 0x0a, 0x11, 0x49,
	0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6f, 0x6c, 0x64, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x6c, 0x64, 0x5f, 0x74, 0x61, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65,
	0x77, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x77,
	0x5f, 0x74, 0x61, 0x67, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x42, 0x35, 0x5a, 0x33, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2f, 0x70, 0x6f, 0x6c, 0x61, 0x72, 0x69, 0x73, 0x2d,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x32, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v2_model_service_proto_rawDescOnce sync.Once
	file_v2_model_service_proto_rawDescData = file_v2_model_service_proto_rawDesc
)

func file_v2_model_service_proto_rawDescGZIP() []byte {
	file_v2_model_service_proto_rawDescOnce.Do(func() {
		file_v2_model_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_v2_model_service_proto_rawDescData)
	})
	return file_v2_model_service_proto_rawDescData
}

var file_v2_model_service_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_v2_model_service_proto_goTypes = []interface{}{
	(*Service)(nil),           // 0: api.v2.model.Service
	(*IncrementInstance)(nil), // 1: api.v2.model.IncrementInstance
	nil,                       // 2: api.v2.model.Service.MetadataEntry
}
var file_v2_model_service_proto_depIdxs = []int32{
	2, // 0: api.v2.model.Service.metadata:type_name -> api.v2.model.Service.MetadataEntry
	1, // 1: api.v2.model.Service.increment_instance:type_name -> api.v2.model.IncrementInstance
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_v2_model_service_proto_init() }
func file_v2_model_service_proto_init() {
	if File_v2_model_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_v2_model_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_v2_model_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncrementInstance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v2_model_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v2_model_service_proto_goTypes,
		DependencyIndexes: file_v2_model_service_proto_depIdxs,
		MessageInfos:      file_v2_model_service_proto_msgTypes,
	}.Build()
	File_v2_model_service_proto = out.File
	file_v2_model_service_proto_rawDesc = nil
	file_v2_model_service_proto_goTypes = nil
	file_v2_model_service_proto_depIdxs = nil
}
