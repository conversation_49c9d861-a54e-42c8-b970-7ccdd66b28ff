syntax = "proto3";

package trpc.zy_socail.qq_photos;

option go_package = "git.woa.com/trpcprotocol/zy_socail/qq_photos";

//定义服务接口
service QzonePhotoTool{
    rpc RawFixTool(RawFixToolRequest) returns (RawFixToolReply);
}
//请求参数
message RawFixToolRequest{
    repeated uint64 uids = 1;
    int32 concurrency = 2; //一次请求的并发数
}
//响应参数
message RawFixToolReply{
    repeated PhotoErrorInfo errorList = 1; //原图和大图不一样的的图片的列表
    repeated uint64 unprocessedUids = 2; //未处理或处理时出错的qq号列表。拉取qq号所属的图片info列表出错、或用户提前停止批处理 触发
    repeated PhotoUnprocessedInfo photoUnprocessedList = 3; //未处理的图片列表，但这个uid下的其他图片处理了。下载图片时出错、phash计算出错 触发
}

message PhotoUnprocessedInfo{
    string uin = 1;
    string albumId = 2;
    string lloc = 3;
    string bigPicUrl = 4; //大图url
    string rawPicUrl = 5; //原图url
}

message PhotoErrorInfo{
    string uin = 1;
    string albumId = 2;
    string lloc = 3;
}