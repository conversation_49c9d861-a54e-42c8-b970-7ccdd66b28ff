// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.21.2
// source: oidb_head.proto

package oidb

import (
	sso_protos "git.code.oa.com/PCG-MQQ-QQService-DevGroup2/SSO-Proj/sso_protos"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoiface "google.golang.org/protobuf/runtime/protoiface"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Oidb2ServerSsoInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uint32Seq             *uint32 `protobuf:"varint,1,opt,name=uint32_seq,json=uint32Seq" json:"uint32_seq,omitempty"`                                        //sso的seq
	Uint32AppId           *uint32 `protobuf:"varint,2,opt,name=uint32_app_id,json=uint32AppId" json:"uint32_app_id,omitempty"`                                //appid, 由SSO的LC的前4位获取
	BytesImei             []byte  `protobuf:"bytes,3,opt,name=bytes_imei,json=bytesImei" json:"bytes_imei,omitempty"`                                         //手机IMEI
	BytesClientVersion    []byte  `protobuf:"bytes,4,opt,name=bytes_client_version,json=bytesClientVersion" json:"bytes_client_version,omitempty"`            //客户端协议版本号(客户端上传, 兼容旧版本)
	BytesSsoClientVersion []byte  `protobuf:"bytes,5,opt,name=bytes_sso_client_version,json=bytesSsoClientVersion" json:"bytes_sso_client_version,omitempty"` //客户端协议版本号(SSO添加, 使用这个字段)
	Uint32SsoBid          *uint32 `protobuf:"varint,6,opt,name=uint32_sso_bid,json=uint32SsoBid" json:"uint32_sso_bid,omitempty"`                             //SSO包头的BID(即客户端带上来的AppId)
	Uint32ApnType         *uint32 `protobuf:"varint,7,opt,name=uint32_apn_type,json=uint32ApnType" json:"uint32_apn_type,omitempty"`                          // 用户网络类型
	// optional Tencent.QQService.SSO.UserData msg_sso_user_data = 8;    // SSO原生态的UserData数据
	StrCrossData           *string `protobuf:"bytes,9,opt,name=str_cross_data,json=strCrossData" json:"str_cross_data,omitempty"`                                   // web请求时cookie内的cross_data透传字段的数据
	Uint32SsoClientVersion *uint32 `protobuf:"varint,10,opt,name=uint32_sso_client_version,json=uint32SsoClientVersion" json:"uint32_sso_client_version,omitempty"` //OIDB不填此字段的!!!! 后端业务可使用Agent将版本(A/I)XX.YY.ZZZZZ转换成数字的形式传给后续的业务做版本控制用.
	//qqhead link: http://git.woa.com/PCG-MQQ-QQService-DevGroup2/SSO-Proj/sso_protos.git
	MsgQqHead      *sso_protos.QQHead `protobuf:"bytes,11,opt,name=msg_qq_head,json=msgQqHead" json:"msg_qq_head,omitempty"`                  // SSO原生态的qqHead数据
	MsgWebLoginSig *LoginSig          `protobuf:"bytes,12,opt,name=msg_web_login_sig,json=msgWebLoginSig" json:"msg_web_login_sig,omitempty"` // SSO jsapi传递过来的登录态，取值等同于oidb登录态
	WebContext     *string            `protobuf:"bytes,13,opt,name=web_context,json=webContext" json:"web_context,omitempty"`                 // 业务拓展字段
}

func (x *Oidb2ServerSsoInfo) Reset() {
	*x = Oidb2ServerSsoInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oidb_head_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Oidb2ServerSsoInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Oidb2ServerSsoInfo) ProtoMessage() {}

func (x *Oidb2ServerSsoInfo) ProtoReflect() protoreflect.Message {
	mi := &file_oidb_head_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Oidb2ServerSsoInfo.ProtoReflect.Descriptor instead.
func (*Oidb2ServerSsoInfo) Descriptor() ([]byte, []int) {
	return file_oidb_head_proto_rawDescGZIP(), []int{0}
}

func (x *Oidb2ServerSsoInfo) GetUint32Seq() uint32 {
	if x != nil && x.Uint32Seq != nil {
		return *x.Uint32Seq
	}
	return 0
}

func (x *Oidb2ServerSsoInfo) GetUint32AppId() uint32 {
	if x != nil && x.Uint32AppId != nil {
		return *x.Uint32AppId
	}
	return 0
}

func (x *Oidb2ServerSsoInfo) GetBytesImei() []byte {
	if x != nil {
		return x.BytesImei
	}
	return nil
}

func (x *Oidb2ServerSsoInfo) GetBytesClientVersion() []byte {
	if x != nil {
		return x.BytesClientVersion
	}
	return nil
}

func (x *Oidb2ServerSsoInfo) GetBytesSsoClientVersion() []byte {
	if x != nil {
		return x.BytesSsoClientVersion
	}
	return nil
}

func (x *Oidb2ServerSsoInfo) GetUint32SsoBid() uint32 {
	if x != nil && x.Uint32SsoBid != nil {
		return *x.Uint32SsoBid
	}
	return 0
}

func (x *Oidb2ServerSsoInfo) GetUint32ApnType() uint32 {
	if x != nil && x.Uint32ApnType != nil {
		return *x.Uint32ApnType
	}
	return 0
}

func (x *Oidb2ServerSsoInfo) GetStrCrossData() string {
	if x != nil && x.StrCrossData != nil {
		return *x.StrCrossData
	}
	return ""
}

func (x *Oidb2ServerSsoInfo) GetUint32SsoClientVersion() uint32 {
	if x != nil && x.Uint32SsoClientVersion != nil {
		return *x.Uint32SsoClientVersion
	}
	return 0
}

func (x *Oidb2ServerSsoInfo) GetMsgQqHead() *sso_protos.QQHead {
	if x != nil {
		return x.MsgQqHead
	}
	return nil
}

func (x *Oidb2ServerSsoInfo) GetMsgWebLoginSig() *LoginSig {
	if x != nil {
		return x.MsgWebLoginSig
	}
	return nil
}

func (x *Oidb2ServerSsoInfo) GetWebContext() string {
	if x != nil && x.WebContext != nil {
		return *x.WebContext
	}
	return ""
}

type Oidb2ServerPCInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uint32Version    *uint32 `protobuf:"varint,1,opt,name=uint32_version,json=uint32Version" json:"uint32_version,omitempty"`            //客户端版本号
	Uint32ClientType *uint32 `protobuf:"varint,2,opt,name=uint32_client_type,json=uint32ClientType" json:"uint32_client_type,omitempty"` //客户端类型
	Uint32PubNo      *uint32 `protobuf:"varint,3,opt,name=uint32_pub_no,json=uint32PubNo" json:"uint32_pub_no,omitempty"`                //pub_no
	Uint32Instanceid *uint32 `protobuf:"varint,4,opt,name=uint32_instanceid,json=uint32Instanceid" json:"uint32_instanceid,omitempty"`   //实例id
}

func (x *Oidb2ServerPCInfo) Reset() {
	*x = Oidb2ServerPCInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oidb_head_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Oidb2ServerPCInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Oidb2ServerPCInfo) ProtoMessage() {}

func (x *Oidb2ServerPCInfo) ProtoReflect() protoreflect.Message {
	mi := &file_oidb_head_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Oidb2ServerPCInfo.ProtoReflect.Descriptor instead.
func (*Oidb2ServerPCInfo) Descriptor() ([]byte, []int) {
	return file_oidb_head_proto_rawDescGZIP(), []int{1}
}

func (x *Oidb2ServerPCInfo) GetUint32Version() uint32 {
	if x != nil && x.Uint32Version != nil {
		return *x.Uint32Version
	}
	return 0
}

func (x *Oidb2ServerPCInfo) GetUint32ClientType() uint32 {
	if x != nil && x.Uint32ClientType != nil {
		return *x.Uint32ClientType
	}
	return 0
}

func (x *Oidb2ServerPCInfo) GetUint32PubNo() uint32 {
	if x != nil && x.Uint32PubNo != nil {
		return *x.Uint32PubNo
	}
	return 0
}

func (x *Oidb2ServerPCInfo) GetUint32Instanceid() uint32 {
	if x != nil && x.Uint32Instanceid != nil {
		return *x.Uint32Instanceid
	}
	return 0
}

type LoginSig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uint32Type  *uint32 `protobuf:"varint,1,opt,name=uint32_type,json=uint32Type" json:"uint32_type,omitempty"`    //登录态类型
	BytesSig    []byte  `protobuf:"bytes,2,opt,name=bytes_sig,json=bytesSig" json:"bytes_sig,omitempty"`           //登录态内容
	Uint32Appid *uint32 `protobuf:"varint,3,opt,name=uint32_appid,json=uint32Appid" json:"uint32_appid,omitempty"` //第三方调用的appid
}

func (x *LoginSig) Reset() {
	*x = LoginSig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oidb_head_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginSig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginSig) ProtoMessage() {}

func (x *LoginSig) ProtoReflect() protoreflect.Message {
	mi := &file_oidb_head_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginSig.ProtoReflect.Descriptor instead.
func (*LoginSig) Descriptor() ([]byte, []int) {
	return file_oidb_head_proto_rawDescGZIP(), []int{2}
}

func (x *LoginSig) GetUint32Type() uint32 {
	if x != nil && x.Uint32Type != nil {
		return *x.Uint32Type
	}
	return 0
}

func (x *LoginSig) GetBytesSig() []byte {
	if x != nil {
		return x.BytesSig
	}
	return nil
}

func (x *LoginSig) GetUint32Appid() uint32 {
	if x != nil && x.Uint32Appid != nil {
		return *x.Uint32Appid
	}
	return 0
}

//透传数据
type TransInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Caller  []byte        `protobuf:"bytes,1,opt,name=caller" json:"caller,omitempty"`                  //主调服务的名称 trpc协议下的规范格式: trpc.应用名.服务名.pb的service名
	Callee  []byte        `protobuf:"bytes,2,opt,name=callee" json:"callee,omitempty"`                  //被调服务的路由名称，trpc协议下的规范格式，trpc.应用名.服务名.pb的service名[.接口名]
	TraceId []byte        `protobuf:"bytes,3,opt,name=trace_id,json=traceId" json:"trace_id,omitempty"` //trace_id
	Params  []*TransParam `protobuf:"bytes,4,rep,name=params" json:"params,omitempty"`                  //自定义参数
}

func (x *TransInfo) Reset() {
	*x = TransInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oidb_head_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransInfo) ProtoMessage() {}

func (x *TransInfo) ProtoReflect() protoreflect.Message {
	mi := &file_oidb_head_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransInfo.ProtoReflect.Descriptor instead.
func (*TransInfo) Descriptor() ([]byte, []int) {
	return file_oidb_head_proto_rawDescGZIP(), []int{3}
}

func (x *TransInfo) GetCaller() []byte {
	if x != nil {
		return x.Caller
	}
	return nil
}

func (x *TransInfo) GetCallee() []byte {
	if x != nil {
		return x.Callee
	}
	return nil
}

func (x *TransInfo) GetTraceId() []byte {
	if x != nil {
		return x.TraceId
	}
	return nil
}

func (x *TransInfo) GetParams() []*TransParam {
	if x != nil {
		return x.Params
	}
	return nil
}

//透传参数
type TransParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   *string `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`     //参数key
	Value []byte  `protobuf:"bytes,2,opt,name=value" json:"value,omitempty"` //参数value
}

func (x *TransParam) Reset() {
	*x = TransParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oidb_head_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransParam) ProtoMessage() {}

func (x *TransParam) ProtoReflect() protoreflect.Message {
	mi := &file_oidb_head_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransParam.ProtoReflect.Descriptor instead.
func (*TransParam) Descriptor() ([]byte, []int) {
	return file_oidb_head_proto_rawDescGZIP(), []int{4}
}

func (x *TransParam) GetKey() string {
	if x != nil && x.Key != nil {
		return *x.Key
	}
	return ""
}

func (x *TransParam) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

type UsrAccountMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uint64Uin  *uint64 `protobuf:"varint,1,opt,name=uint64_uin,json=uint64Uin" json:"uint64_uin,omitempty"`
	BytesUsrId []byte  `protobuf:"bytes,2,opt,name=bytes_usr_id,json=bytesUsrId" json:"bytes_usr_id,omitempty"` // 非Uin帐号，包括openid/小程序sessionKey/uid等
}

func (x *UsrAccountMap) Reset() {
	*x = UsrAccountMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oidb_head_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsrAccountMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsrAccountMap) ProtoMessage() {}

func (x *UsrAccountMap) ProtoReflect() protoreflect.Message {
	mi := &file_oidb_head_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsrAccountMap.ProtoReflect.Descriptor instead.
func (*UsrAccountMap) Descriptor() ([]byte, []int) {
	return file_oidb_head_proto_rawDescGZIP(), []int{5}
}

func (x *UsrAccountMap) GetUint64Uin() uint64 {
	if x != nil && x.Uint64Uin != nil {
		return *x.Uint64Uin
	}
	return 0
}

func (x *UsrAccountMap) GetBytesUsrId() []byte {
	if x != nil {
		return x.BytesUsrId
	}
	return nil
}

type OIDBHead struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Uint64Uin         *uint64             `protobuf:"varint,1,opt,name=uint64_uin,json=uint64Uin" json:"uint64_uin,omitempty"`                           //QQ号码
	Uint32Command     *uint32             `protobuf:"varint,2,opt,name=uint32_command,json=uint32Command" json:"uint32_command,omitempty"`               //主命令号, 即协议号
	Uint32ServiceType *uint32             `protobuf:"varint,3,opt,name=uint32_service_type,json=uint32ServiceType" json:"uint32_service_type,omitempty"` //子命令号, 即在门户上申请的业务类型
	Uint32Seq         *uint32             `protobuf:"varint,4,opt,name=uint32_seq,json=uint32Seq" json:"uint32_seq,omitempty"`                           //序列值, 用于给请求方区分不同的请求回包
	Uint32ClientAddr  *uint32             `protobuf:"fixed32,5,opt,name=uint32_client_addr,json=uint32ClientAddr" json:"uint32_client_addr,omitempty"`   //客户端IP -- 发起请求的Uin的IP, 指触发请求的用户的外网IP, 网络序,  可以使用inet_addr()函数生成, struct in_addr格式
	BytesClientAddr   []byte              `protobuf:"bytes,15,opt,name=bytes_client_addr,json=bytesClientAddr" json:"bytes_client_addr,omitempty"`       //客户端IP，ipv6存储格式，16个字节，如果是ipv4ip采用兼容ipv6方式存储。
	Uint32ServerAddr  *uint32             `protobuf:"fixed32,6,opt,name=uint32_server_addr,json=uint32ServerAddr" json:"uint32_server_addr,omitempty"`   //服务端IP -- 最前端与用户交互的服务器IP, 如果触发请求是通过Cs通道的, 则为Conn透传后的第一台服务器, 如果触发请求是通过Web, 则为Cgi所在的服务器. 网络序, 可以使用inet_addr()函数生成, struct in_addr格式
	BytesServerAddr   []byte              `protobuf:"bytes,16,opt,name=bytes_server_addr,json=bytesServerAddr" json:"bytes_server_addr,omitempty"`       //服务端IP，ipv6存储格式，16个字节，如果是ipv4ip采用兼容ipv6方式存储。
	Uint32Result      *uint32             `protobuf:"varint,7,opt,name=uint32_result,json=uint32Result" json:"uint32_result,omitempty"`                  //返回值: 0--处理正确
	StrErrorMsg       *string             `protobuf:"bytes,8,opt,name=str_error_msg,json=strErrorMsg" json:"str_error_msg,omitempty"`                    //错误描述 -- 给返回值非0的描述
	MsgLoginSig       *LoginSig           `protobuf:"bytes,9,opt,name=msg_login_sig,json=msgLoginSig" json:"msg_login_sig,omitempty"`                    //登录态, 指Server给用户派发的签名, 用于校验用户的合法性, 详细请见门户上的"开发指南"->"使用帮助"->"OIDB接口说明书"
	StrUserName       *string             `protobuf:"bytes,10,opt,name=str_user_name,json=strUserName" json:"str_user_name,omitempty"`                   //申请权限时的用户名
	StrServiceName    *string             `protobuf:"bytes,11,opt,name=str_service_name,json=strServiceName" json:"str_service_name,omitempty"`          //申请权限时的业务名
	Uint32Flag        *uint32             `protobuf:"varint,12,opt,name=uint32_flag,json=uint32Flag" json:"uint32_flag,omitempty"`                       //标志(某些业务可能需要调用方填写标志, 如群)
	Uint32FromAddr    *uint32             `protobuf:"varint,13,opt,name=uint32_from_addr,json=uint32FromAddr" json:"uint32_from_addr,omitempty"`         //发起请求的IP, 安全业务用, 网络序, struct in_addr格式
	Uint32LocalAddr   *uint32             `protobuf:"varint,14,opt,name=uint32_local_addr,json=uint32LocalAddr" json:"uint32_local_addr,omitempty"`      //收到请求的IP, 安全业务用, 网络序, struct in_addr格式
	Uint32Moduleid    *uint32             `protobuf:"varint,17,opt,name=uint32_moduleid,json=uint32Moduleid" json:"uint32_moduleid,omitempty"`           //模块id，使用模块鉴权时用到
	Reserved          []byte              `protobuf:"bytes,18,opt,name=reserved" json:"reserved,omitempty"`                                              //reserved
	Uint32LocaleId    *uint32             `protobuf:"varint,19,opt,name=uint32_locale_id,json=uint32LocaleId" json:"uint32_locale_id,omitempty"`         //语言ID
	MsgSsoInfo        *Oidb2ServerSsoInfo `protobuf:"bytes,20,opt,name=msg_sso_info,json=msgSsoInfo" json:"msg_sso_info,omitempty"`                      //SSO相关信息
	Uint64LongSeq     *uint64             `protobuf:"varint,21,opt,name=uint64_long_seq,json=uint64LongSeq" json:"uint64_long_seq,omitempty"`            //uint64的Seq, 有些业务使用uint64作为Session的
	MsgPcInfo         *Oidb2ServerPCInfo  `protobuf:"bytes,25,opt,name=msg_pc_info,json=msgPcInfo" json:"msg_pc_info,omitempty"`                         // PCQQ相关信息
	//tag 26~31废弃不用, 直接使用QQHead.proto里面相应的字段
	//optional uint32 uint32_route_id = 26;   //联调环境转包id
	//optional bytes  bytes_trace_id = 28; // 全链路跟踪上下文(trace_id+span_id+parent_id)
	//optional bytes  bytes_span_id = 29;
	//optional bytes  bytes_parent_id = 30;
	//optional bytes  bytes_client_guid = 31;  //Guid
	Uint64ApplyId               *uint64          `protobuf:"varint,32,opt,name=uint64_apply_id,json=uint64ApplyId" json:"uint64_apply_id,omitempty"`                                               //权限单id，oidb查到之后传递给后端用，返回时会抹除
	Uint32AccountType           *uint32          `protobuf:"varint,33,opt,name=uint32_account_type,json=uint32AccountType" json:"uint32_account_type,omitempty"`                                   // 包头的帐号类型, 不填或者0:uin, 1:uid, 2:openid, 3:小程序sessionkey
	Uint32OriginalAccountType   *uint32          `protobuf:"varint,34,opt,name=uint32_original_account_type,json=uint32OriginalAccountType" json:"uint32_original_account_type,omitempty"`         // oidb内部使用, 业务不要动
	RptMsgUsrAccountMap         []*UsrAccountMap `protobuf:"bytes,35,rep,name=rpt_msg_usr_account_map,json=rptMsgUsrAccountMap" json:"rpt_msg_usr_account_map,omitempty"`                          // uin和非uin映射
	StrKnocknockId              *string          `protobuf:"bytes,36,opt,name=str_knocknock_id,json=strKnocknockId" json:"str_knocknock_id,omitempty"`                                             // oidb上游的knocknock server id
	Uint32OpenAppId             *uint32          `protobuf:"varint,37,opt,name=uint32_open_app_id,json=uint32OpenAppId" json:"uint32_open_app_id,omitempty"`                                       // openid/小程序登录态的appid，主要openid登录态时使用
	BytesAuthInfo               []byte           `protobuf:"bytes,40,opt,name=bytes_auth_info,json=bytesAuthInfo" json:"bytes_auth_info,omitempty"`                                                // 模块鉴权字段，oidb识别上游&oidb访问下游使用
	Uint64ToUin                 *uint64          `protobuf:"varint,100,opt,name=uint64_to_uin,json=uint64ToUin" json:"uint64_to_uin,omitempty"`                                                    //业务to uin，安全业务用
	BytesIgnoreHeadUinSignature []byte           `protobuf:"bytes,101,opt,name=bytes_ignore_head_uin_signature,json=bytesIgnoreHeadUinSignature" json:"bytes_ignore_head_uin_signature,omitempty"` //小微项目(forestli)用，测试环境识别出这个标志之后，就不对包头uin(din)进行检查
	Uint64Seq                   *uint64          `protobuf:"varint,102,opt,name=uint64_seq,json=uint64Seq" json:"uint64_seq,omitempty"`                                                            //seq uint64版本
	BytesServiceInfo            []byte           `protobuf:"bytes,2000,opt,name=bytes_service_info,json=bytesServiceInfo" json:"bytes_service_info,omitempty"`                                     // oidb 0xc56 增加，透传给 imagnet User
}

func (x *OIDBHead) Reset() {
	*x = OIDBHead{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oidb_head_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OIDBHead) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OIDBHead) ProtoMessage() {}

func (x *OIDBHead) ProtoReflect() protoreflect.Message {
	mi := &file_oidb_head_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OIDBHead.ProtoReflect.Descriptor instead.
func (*OIDBHead) Descriptor() ([]byte, []int) {
	return file_oidb_head_proto_rawDescGZIP(), []int{6}
}

var extRange_OIDBHead = []protoiface.ExtensionRangeV1{
	{Start: 1000, End: 1999},
}

// Deprecated: Use OIDBHead.ProtoReflect.Descriptor.ExtensionRanges instead.
func (*OIDBHead) ExtensionRangeArray() []protoiface.ExtensionRangeV1 {
	return extRange_OIDBHead
}

func (x *OIDBHead) GetUint64Uin() uint64 {
	if x != nil && x.Uint64Uin != nil {
		return *x.Uint64Uin
	}
	return 0
}

func (x *OIDBHead) GetUint32Command() uint32 {
	if x != nil && x.Uint32Command != nil {
		return *x.Uint32Command
	}
	return 0
}

func (x *OIDBHead) GetUint32ServiceType() uint32 {
	if x != nil && x.Uint32ServiceType != nil {
		return *x.Uint32ServiceType
	}
	return 0
}

func (x *OIDBHead) GetUint32Seq() uint32 {
	if x != nil && x.Uint32Seq != nil {
		return *x.Uint32Seq
	}
	return 0
}

func (x *OIDBHead) GetUint32ClientAddr() uint32 {
	if x != nil && x.Uint32ClientAddr != nil {
		return *x.Uint32ClientAddr
	}
	return 0
}

func (x *OIDBHead) GetBytesClientAddr() []byte {
	if x != nil {
		return x.BytesClientAddr
	}
	return nil
}

func (x *OIDBHead) GetUint32ServerAddr() uint32 {
	if x != nil && x.Uint32ServerAddr != nil {
		return *x.Uint32ServerAddr
	}
	return 0
}

func (x *OIDBHead) GetBytesServerAddr() []byte {
	if x != nil {
		return x.BytesServerAddr
	}
	return nil
}

func (x *OIDBHead) GetUint32Result() uint32 {
	if x != nil && x.Uint32Result != nil {
		return *x.Uint32Result
	}
	return 0
}

func (x *OIDBHead) GetStrErrorMsg() string {
	if x != nil && x.StrErrorMsg != nil {
		return *x.StrErrorMsg
	}
	return ""
}

func (x *OIDBHead) GetMsgLoginSig() *LoginSig {
	if x != nil {
		return x.MsgLoginSig
	}
	return nil
}

func (x *OIDBHead) GetStrUserName() string {
	if x != nil && x.StrUserName != nil {
		return *x.StrUserName
	}
	return ""
}

func (x *OIDBHead) GetStrServiceName() string {
	if x != nil && x.StrServiceName != nil {
		return *x.StrServiceName
	}
	return ""
}

func (x *OIDBHead) GetUint32Flag() uint32 {
	if x != nil && x.Uint32Flag != nil {
		return *x.Uint32Flag
	}
	return 0
}

func (x *OIDBHead) GetUint32FromAddr() uint32 {
	if x != nil && x.Uint32FromAddr != nil {
		return *x.Uint32FromAddr
	}
	return 0
}

func (x *OIDBHead) GetUint32LocalAddr() uint32 {
	if x != nil && x.Uint32LocalAddr != nil {
		return *x.Uint32LocalAddr
	}
	return 0
}

func (x *OIDBHead) GetUint32Moduleid() uint32 {
	if x != nil && x.Uint32Moduleid != nil {
		return *x.Uint32Moduleid
	}
	return 0
}

func (x *OIDBHead) GetReserved() []byte {
	if x != nil {
		return x.Reserved
	}
	return nil
}

func (x *OIDBHead) GetUint32LocaleId() uint32 {
	if x != nil && x.Uint32LocaleId != nil {
		return *x.Uint32LocaleId
	}
	return 0
}

func (x *OIDBHead) GetMsgSsoInfo() *Oidb2ServerSsoInfo {
	if x != nil {
		return x.MsgSsoInfo
	}
	return nil
}

func (x *OIDBHead) GetUint64LongSeq() uint64 {
	if x != nil && x.Uint64LongSeq != nil {
		return *x.Uint64LongSeq
	}
	return 0
}

func (x *OIDBHead) GetMsgPcInfo() *Oidb2ServerPCInfo {
	if x != nil {
		return x.MsgPcInfo
	}
	return nil
}

func (x *OIDBHead) GetUint64ApplyId() uint64 {
	if x != nil && x.Uint64ApplyId != nil {
		return *x.Uint64ApplyId
	}
	return 0
}

func (x *OIDBHead) GetUint32AccountType() uint32 {
	if x != nil && x.Uint32AccountType != nil {
		return *x.Uint32AccountType
	}
	return 0
}

func (x *OIDBHead) GetUint32OriginalAccountType() uint32 {
	if x != nil && x.Uint32OriginalAccountType != nil {
		return *x.Uint32OriginalAccountType
	}
	return 0
}

func (x *OIDBHead) GetRptMsgUsrAccountMap() []*UsrAccountMap {
	if x != nil {
		return x.RptMsgUsrAccountMap
	}
	return nil
}

func (x *OIDBHead) GetStrKnocknockId() string {
	if x != nil && x.StrKnocknockId != nil {
		return *x.StrKnocknockId
	}
	return ""
}

func (x *OIDBHead) GetUint32OpenAppId() uint32 {
	if x != nil && x.Uint32OpenAppId != nil {
		return *x.Uint32OpenAppId
	}
	return 0
}

func (x *OIDBHead) GetBytesAuthInfo() []byte {
	if x != nil {
		return x.BytesAuthInfo
	}
	return nil
}

func (x *OIDBHead) GetUint64ToUin() uint64 {
	if x != nil && x.Uint64ToUin != nil {
		return *x.Uint64ToUin
	}
	return 0
}

func (x *OIDBHead) GetBytesIgnoreHeadUinSignature() []byte {
	if x != nil {
		return x.BytesIgnoreHeadUinSignature
	}
	return nil
}

func (x *OIDBHead) GetUint64Seq() uint64 {
	if x != nil && x.Uint64Seq != nil {
		return *x.Uint64Seq
	}
	return 0
}

func (x *OIDBHead) GetBytesServiceInfo() []byte {
	if x != nil {
		return x.BytesServiceInfo
	}
	return nil
}

var File_oidb_head_proto protoreflect.FileDescriptor

var file_oidb_head_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x6f, 0x69, 0x64, 0x62, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6d, 0x2e, 0x6f, 0x69,
	0x64, 0x62, 0x1a, 0x0c, 0x51, 0x51, 0x48, 0x65, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xb9, 0x04, 0x0a, 0x12, 0x4f, 0x69, 0x64, 0x62, 0x32, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x53, 0x73, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x75, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x53, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x0d, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x75,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x79,
	0x74, 0x65, 0x73, 0x5f, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09,
	0x62, 0x79, 0x74, 0x65, 0x73, 0x49, 0x6d, 0x65, 0x69, 0x12, 0x30, 0x0a, 0x14, 0x62, 0x79, 0x74,
	0x65, 0x73, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x12, 0x62, 0x79, 0x74, 0x65, 0x73, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x18, 0x62,
	0x79, 0x74, 0x65, 0x73, 0x5f, 0x73, 0x73, 0x6f, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x15, 0x62,
	0x79, 0x74, 0x65, 0x73, 0x53, 0x73, 0x6f, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x73,
	0x73, 0x6f, 0x5f, 0x62, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x75, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x53, 0x73, 0x6f, 0x42, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x75, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x5f, 0x61, 0x70, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0d, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x41, 0x70, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x74, 0x72, 0x5f, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x74, 0x72, 0x43,
	0x72, 0x6f, 0x73, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x19, 0x75, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x5f, 0x73, 0x73, 0x6f, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x16, 0x75, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x53, 0x73, 0x6f, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x0b, 0x6d, 0x73, 0x67, 0x5f, 0x71, 0x71, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x54, 0x65, 0x6e, 0x63, 0x65,
	0x6e, 0x74, 0x2e, 0x51, 0x51, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x51, 0x51, 0x48, 0x65, 0x61, 0x64, 0x52, 0x09, 0x6d, 0x73, 0x67, 0x51,
	0x71, 0x48, 0x65, 0x61, 0x64, 0x12, 0x44, 0x0a, 0x11, 0x6d, 0x73, 0x67, 0x5f, 0x77, 0x65, 0x62,
	0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6d, 0x2e, 0x6f, 0x69,
	0x64, 0x62, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x53, 0x69, 0x67, 0x52, 0x0e, 0x6d, 0x73, 0x67,
	0x57, 0x65, 0x62, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x53, 0x69, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x77,
	0x65, 0x62, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x77, 0x65, 0x62, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0xb9, 0x01, 0x0a,
	0x11, 0x4f, 0x69, 0x64, 0x62, 0x32, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x50, 0x43, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x25, 0x0a, 0x0e, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x75, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x75, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x5f, 0x70, 0x75, 0x62, 0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b,
	0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x50, 0x75, 0x62, 0x4e, 0x6f, 0x12, 0x2b, 0x0a, 0x11, 0x75,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x69, 0x64, 0x22, 0x6b, 0x0a, 0x08, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x53, 0x69, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x73,
	0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x62, 0x79, 0x74, 0x65, 0x73, 0x53,
	0x69, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x61, 0x70, 0x70,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x41, 0x70, 0x70, 0x69, 0x64, 0x22, 0x8b, 0x01, 0x0a, 0x09, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x63,
	0x61, 0x6c, 0x6c, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x63, 0x61, 0x6c,
	0x6c, 0x65, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x33,
	0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6d, 0x2e, 0x6f, 0x69, 0x64, 0x62,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x06, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x22, 0x34, 0x0a, 0x0a, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x50, 0x0a, 0x0d, 0x55, 0x73, 0x72,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x5f, 0x75, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x55, 0x69, 0x6e, 0x12, 0x20, 0x0a, 0x0c, 0x62, 0x79, 0x74,
	0x65, 0x73, 0x5f, 0x75, 0x73, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0a, 0x62, 0x79, 0x74, 0x65, 0x73, 0x55, 0x73, 0x72, 0x49, 0x64, 0x22, 0xf0, 0x0b, 0x0a, 0x08,
	0x4f, 0x49, 0x44, 0x42, 0x48, 0x65, 0x61, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x5f, 0x75, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x75, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x55, 0x69, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0d, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x2e,
	0x0a, 0x13, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x75, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x65, 0x71, 0x12, 0x2c, 0x0a,
	0x12, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x07, 0x52, 0x10, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x62,
	0x79, 0x74, 0x65, 0x73, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x07, 0x52, 0x10, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x41, 0x64, 0x64, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x0f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x12, 0x23, 0x0a, 0x0d, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x74, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x3d, 0x0a, 0x0d, 0x6d, 0x73,
	0x67, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6d, 0x2e, 0x6f,
	0x69, 0x64, 0x62, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x53, 0x69, 0x67, 0x52, 0x0b, 0x6d, 0x73,
	0x67, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x53, 0x69, 0x67, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x74, 0x72,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x74, 0x72, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a,
	0x10, 0x73, 0x74, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x72, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x75, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x28, 0x0a, 0x10, 0x75, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0e, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x72, 0x6f, 0x6d, 0x41, 0x64,
	0x64, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x75,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x12, 0x27,
	0x0a, 0x0f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x69,
	0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x6c, 0x6f,
	0x63, 0x61, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x75,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x45, 0x0a,
	0x0c, 0x6d, 0x73, 0x67, 0x5f, 0x73, 0x73, 0x6f, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6d,
	0x2e, 0x6f, 0x69, 0x64, 0x62, 0x2e, 0x4f, 0x69, 0x64, 0x62, 0x32, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x53, 0x73, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x6d, 0x73, 0x67, 0x53, 0x73, 0x6f,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x6c,
	0x6f, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x15, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x75,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x4c, 0x6f, 0x6e, 0x67, 0x53, 0x65, 0x71, 0x12, 0x42, 0x0a, 0x0b,
	0x6d, 0x73, 0x67, 0x5f, 0x70, 0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6d, 0x2e, 0x6f,
	0x69, 0x64, 0x62, 0x2e, 0x4f, 0x69, 0x64, 0x62, 0x32, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x50,
	0x43, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6d, 0x73, 0x67, 0x50, 0x63, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x26, 0x0a, 0x0f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x20, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x75, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x75, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x21, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x1c, 0x75, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x19,
	0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x54, 0x0a, 0x17, 0x72, 0x70, 0x74,
	0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x75, 0x73, 0x72, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x6d, 0x61, 0x70, 0x18, 0x23, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x65, 0x6e,
	0x63, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6d, 0x2e, 0x6f, 0x69, 0x64, 0x62, 0x2e, 0x55, 0x73, 0x72,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x13, 0x72, 0x70, 0x74, 0x4d,
	0x73, 0x67, 0x55, 0x73, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x12,
	0x28, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x5f, 0x6b, 0x6e, 0x6f, 0x63, 0x6b, 0x6e, 0x6f, 0x63, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x72, 0x4b, 0x6e,
	0x6f, 0x63, 0x6b, 0x6e, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x12, 0x75, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x25, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x4f, 0x70, 0x65,
	0x6e, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f,
	0x61, 0x75, 0x74, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x28, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0d, 0x62, 0x79, 0x74, 0x65, 0x73, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22,
	0x0a, 0x0d, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x69, 0x6e, 0x18,
	0x64, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x54, 0x6f, 0x55,
	0x69, 0x6e, 0x12, 0x44, 0x0a, 0x1f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x69, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x1b, 0x62, 0x79, 0x74,
	0x65, 0x73, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x48, 0x65, 0x61, 0x64, 0x55, 0x69, 0x6e, 0x53,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x66, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x75, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x53, 0x65, 0x71, 0x12, 0x2d, 0x0a, 0x12, 0x62, 0x79, 0x74, 0x65, 0x73,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0xd0, 0x0f,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x10, 0x62, 0x79, 0x74, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2a, 0x06, 0x08, 0xe8, 0x07, 0x10, 0xd0, 0x0f, 0x42, 0x29,
	0x5a, 0x27, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x6f, 0x61, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x74, 0x72, 0x70, 0x63, 0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x72, 0x70, 0x63, 0x2d, 0x63,
	0x6f, 0x64, 0x65, 0x63, 0x2f, 0x6f, 0x69, 0x64, 0x62,
}

var (
	file_oidb_head_proto_rawDescOnce sync.Once
	file_oidb_head_proto_rawDescData = file_oidb_head_proto_rawDesc
)

func file_oidb_head_proto_rawDescGZIP() []byte {
	file_oidb_head_proto_rawDescOnce.Do(func() {
		file_oidb_head_proto_rawDescData = protoimpl.X.CompressGZIP(file_oidb_head_proto_rawDescData)
	})
	return file_oidb_head_proto_rawDescData
}

var file_oidb_head_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_oidb_head_proto_goTypes = []interface{}{
	(*Oidb2ServerSsoInfo)(nil), // 0: tencent.im.oidb.Oidb2ServerSsoInfo
	(*Oidb2ServerPCInfo)(nil),  // 1: tencent.im.oidb.Oidb2ServerPCInfo
	(*LoginSig)(nil),           // 2: tencent.im.oidb.LoginSig
	(*TransInfo)(nil),          // 3: tencent.im.oidb.TransInfo
	(*TransParam)(nil),         // 4: tencent.im.oidb.TransParam
	(*UsrAccountMap)(nil),      // 5: tencent.im.oidb.UsrAccountMap
	(*OIDBHead)(nil),           // 6: tencent.im.oidb.OIDBHead
	(*sso_protos.QQHead)(nil),  // 7: Tencent.QQService.Common.QQHead
}
var file_oidb_head_proto_depIdxs = []int32{
	7, // 0: tencent.im.oidb.Oidb2ServerSsoInfo.msg_qq_head:type_name -> Tencent.QQService.Common.QQHead
	2, // 1: tencent.im.oidb.Oidb2ServerSsoInfo.msg_web_login_sig:type_name -> tencent.im.oidb.LoginSig
	4, // 2: tencent.im.oidb.TransInfo.params:type_name -> tencent.im.oidb.TransParam
	2, // 3: tencent.im.oidb.OIDBHead.msg_login_sig:type_name -> tencent.im.oidb.LoginSig
	0, // 4: tencent.im.oidb.OIDBHead.msg_sso_info:type_name -> tencent.im.oidb.Oidb2ServerSsoInfo
	1, // 5: tencent.im.oidb.OIDBHead.msg_pc_info:type_name -> tencent.im.oidb.Oidb2ServerPCInfo
	5, // 6: tencent.im.oidb.OIDBHead.rpt_msg_usr_account_map:type_name -> tencent.im.oidb.UsrAccountMap
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_oidb_head_proto_init() }
func file_oidb_head_proto_init() {
	if File_oidb_head_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_oidb_head_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Oidb2ServerSsoInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_oidb_head_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Oidb2ServerPCInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_oidb_head_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginSig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_oidb_head_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_oidb_head_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_oidb_head_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsrAccountMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_oidb_head_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OIDBHead); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_oidb_head_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_oidb_head_proto_goTypes,
		DependencyIndexes: file_oidb_head_proto_depIdxs,
		MessageInfos:      file_oidb_head_proto_msgTypes,
	}.Build()
	File_oidb_head_proto = out.File
	file_oidb_head_proto_rawDesc = nil
	file_oidb_head_proto_goTypes = nil
	file_oidb_head_proto_depIdxs = nil
}
